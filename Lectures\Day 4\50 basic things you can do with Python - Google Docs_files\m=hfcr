"use strict";this.default_IdentityRotateCookiesHttp=this.default_IdentityRotateCookiesHttp||{};(function(_){var window=this;
try{
_._F_toggles_initialize=function(a){(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:this)._F_toggles=a||[]};(0,_._F_toggles_initialize)([0x6000, ]);
/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var aa=function(a){p.setTimeout(function(){throw a;},0)},ba=function(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b},q=function(a){a=Error(a);ba(a,"warning");return a},r=function(a,b,c){return typeof Symbol==="function"&&typeof Symbol()==="symbol"?(c===void 0?0:c)&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol():b},y=function(a,b){u||w in a||ca(a,da);a[w]|=b},z=function(a,b){u||w in a||ca(a,da);a[w]=b},A=function(){return typeof BigInt===
"function"},ea=function(a,b){return b===void 0?a.s!==C&&!!(2&(a.j[w]|0)):!!(2&b)&&a.s!==C},D=function(a){a.K=!0;return a},F=function(a){var b=a;if(fa(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if(ha(b)&&!Number.isSafeInteger(b))throw Error(String(b));return E?BigInt(a):a=ia(a)?a?"1":"0":fa(a)?a.trim()||"0":String(a)},ja=function(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var f=a[c],e=b[c];if(f>e)return!1;if(f<e)return!0}},
ka=function(a){var b=a>>>0;G=b;H=(a-b)/4294967296>>>0},K=function(a){if(a<0){ka(-a);var b=I(J(G,H));a=b.next().value;b=b.next().value;G=a>>>0;H=b>>>0}else ka(a)},L=function(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else A()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+la(c)+la(a));return c},la=function(a){a=String(a);return"0000000".slice(a.length)+
a},ma=function(){var a=G,b=H;b&2147483648?A()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=I(J(a,b)),a=b.next().value,b=b.next().value,a="-"+L(a,b)):a=L(a,b);return a},J=function(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]},pa=function(a){switch(typeof a){case "bigint":return!0;case "number":return na(a);case "string":return oa.test(a);default:return!1}},va=function(a){var b=0;b=b===void 0?0:b;if(!pa(a))throw q("int64");var c=typeof a;switch(b){case 512:switch(c){case "string":return qa(a);case "bigint":return String(M(64,
a));default:return ra(a)}case 1024:switch(c){case "string":return sa(a);case "bigint":return F(M(64,a));default:return ta(a)}case 0:switch(c){case "string":return qa(a);case "bigint":return F(M(64,a));default:return ua(a)}default:throw Error("Unknown format requested type for int64");}},wa=function(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337},xa=function(a){if(wa(a))return a;if(a.length<16)K(Number(a));else if(A())a=
BigInt(a),G=Number(a&BigInt(4294967295))>>>0,H=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");H=G=0;for(var c=a.length,f=b,e=(c-b)%6+b;e<=c;f=e,e+=6)f=Number(a.slice(f,e)),H*=1E6,G=G*1E6+f,G>=4294967296&&(H+=Math.trunc(G/4294967296),H>>>=0,G>>>=0);b&&(b=I(J(G,H)),a=b.next().value,b=b.next().value,G=a,H=b)}return ma()},ua=function(a){a=N(a);if(!O(a)){K(a);var b=G,c=H;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);var f=c*4294967296+(b>>>0);b=Number.isSafeInteger(f)?f:L(b,
c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a},ra=function(a){a=N(a);if(O(a))a=String(a);else{var b=String(a);wa(b)?a=b:(K(a),a=ma())}return a},qa=function(a){var b=N(Number(a));if(O(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return xa(a)},sa=function(a){var b=N(Number(a));if(O(b))return F(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return A()?F(M(64,BigInt(a))):F(xa(a))},ta=function(a){return O(a)?F(ua(a)):F(ra(a))},ya=function(a){return a},P=function(a,b,c,f){var e=
f!==void 0;f=!!f;var l=[],d=a.length,g=4294967295,h=!1,k=!!(b&64),n=k?b&128?0:-1:void 0;if(!(b&1)){var t=d&&a[d-1];t!=null&&typeof t==="object"&&t.constructor===Object?(d--,g=d):t=void 0;if(k&&!(b&128)&&!e){h=!0;var m;g=((m=za)!=null?m:ya)(g-n,n,a,t)+n}}b=void 0;for(e=0;e<d;e++)if(m=a[e],m!=null&&(m=c(m,f))!=null)if(k&&e>=g){var v=e-n,B=void 0;((B=b)!=null?B:b={})[v]=m}else l[e]=m;if(t)for(var x in t)a=t[x],a!=null&&(a=c(a,f))!=null&&(d=+x,e=void 0,k&&!Number.isNaN(d)&&(e=d+n)<g?l[e]=a:(d=void 0,
((d=b)!=null?d:b={})[x]=a));b&&(h?l.push(b):l[g]=b);return l},Ba=function(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return Aa(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[w]|0;return a.length===0&&b&1?void 0:P(a,b,Ba)}if(a!=null&&a[Q]===R)return S(a);return}return a},S=function(a){a=a.j;return P(a,a[w]|0,Ba)},T=function(a,b,c){var f=f===void 0?0:f;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-8380417|(b&1023)<<
13)}else{if(!Array.isArray(a))throw Error("g");e=a[w]|0;2048&e&&!(2&e)&&Ca();if(e&256)throw Error("i");if(e&64)return f!==0||e&2048||z(a,e|2048),a;if(c&&(e|=128,c!==a[0]))throw Error("j");a:{c=a;e|=64;var l=c.length;if(l){var d=l-1,g=c[d];if(g!=null&&typeof g==="object"&&g.constructor===Object){b=e&128?0:-1;d-=b;if(d>=1024)throw Error("l");for(var h in g)l=+h,l<d&&(c[l+b]=g[h],delete g[h]);e=e&-8380417|(d&1023)<<13;break a}}if(b){h=Math.max(b,l-(e&128?0:-1));if(h>1024)throw Error("m");e=e&-8380417|
(h&1023)<<13}}}e|=64;f===0&&(e|=2048);z(a,e);return a},Ca=function(){if(Da!=null){var a;var b=(a=Ea)!=null?a:Ea={};a=b[Da]||0;a>=5||(b[Da]=a+1,b=Error(),ba(b,"incident"),aa(b))}},Ga=function(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[w]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=Fa(a,c,!1,b&&!(c&16)):(y(a,34),c&4&&Object.freeze(a)));return a}if(a!=null&&a[Q]===R){b=a.j;var f=b[w]|0;ea(a,f)?c=a:c=Fa(b,f);return c}},Fa=function(a,b,c,f){f!=null||(f=!!(34&b));a=P(a,
b,Ga,f);f=32;c&&(f|=2);b=b&8380609|f;z(a,b);return a},Ha=function(a){return Aa(a)?Number(a):String(a)},Ia=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},Ja=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},Ka=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&
global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");},U=Ka(this),V=function(a,b){if(b)a:{var c=U;a=a.split(".");for(var f=0;f<a.length-1;f++){var e=a[f];if(!(e in c))break a;c=c[e]}a=a[a.length-1];f=c[a];b=b(f);b!=f&&b!=null&&Ja(c,a,{configurable:!0,writable:!0,value:b})}},La=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},Ma;
if(typeof Object.setPrototypeOf=="function")Ma=Object.setPrototypeOf;else{var Na;a:{var Oa={a:!0},Pa={};try{Pa.__proto__=Oa;Na=Pa.a;break a}catch(a){}Na=!1}Ma=Na?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError("b`"+a);return a}:null}
var Qa=Ma,Ra=function(a,b){a.prototype=La(b.prototype);a.prototype.constructor=a;if(Qa)Qa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var f=Object.getOwnPropertyDescriptor(b,c);f&&Object.defineProperty(a,c,f)}else a[c]=b[c];a.N=b.prototype},I=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:Ia(a)};throw Error("c`"+String(a));};
V("Promise",function(a){function b(){this.g=null}function c(d){return d instanceof e?d:new e(function(g){g(d)})}if(a)return a;b.prototype.h=function(d){if(this.g==null){this.g=[];var g=this;this.i(function(){g.m()})}this.g.push(d)};var f=U.setTimeout;b.prototype.i=function(d){f(d,0)};b.prototype.m=function(){for(;this.g&&this.g.length;){var d=this.g;this.g=[];for(var g=0;g<d.length;++g){var h=d[g];d[g]=null;try{h()}catch(k){this.l(k)}}}this.g=null};b.prototype.l=function(d){this.i(function(){throw d;
})};var e=function(d){this.h=0;this.i=void 0;this.g=[];this.v=!1;var g=this.l();try{d(g.resolve,g.reject)}catch(h){g.reject(h)}};e.prototype.l=function(){function d(k){return function(n){h||(h=!0,k.call(g,n))}}var g=this,h=!1;return{resolve:d(this.C),reject:d(this.m)}};e.prototype.C=function(d){if(d===this)this.m(new TypeError("d"));else if(d instanceof e)this.F(d);else{a:switch(typeof d){case "object":var g=d!=null;break a;case "function":g=!0;break a;default:g=!1}g?this.B(d):this.u(d)}};e.prototype.B=
function(d){var g=void 0;try{g=d.then}catch(h){this.m(h);return}typeof g=="function"?this.G(g,d):this.u(d)};e.prototype.m=function(d){this.A(2,d)};e.prototype.u=function(d){this.A(1,d)};e.prototype.A=function(d,g){if(this.h!=0)throw Error("e`"+d+"`"+g+"`"+this.h);this.h=d;this.i=g;this.h===2&&this.D();this.I()};e.prototype.D=function(){var d=this;f(function(){if(d.J()){var g=U.console;typeof g!=="undefined"&&g.error(d.i)}},1)};e.prototype.J=function(){if(this.v)return!1;var d=U.CustomEvent,g=U.Event,
h=U.dispatchEvent;if(typeof h==="undefined")return!0;typeof d==="function"?d=new d("unhandledrejection",{cancelable:!0}):typeof g==="function"?d=new g("unhandledrejection",{cancelable:!0}):(d=U.document.createEvent("CustomEvent"),d.initCustomEvent("unhandledrejection",!1,!0,d));d.promise=this;d.reason=this.i;return h(d)};e.prototype.I=function(){if(this.g!=null){for(var d=0;d<this.g.length;++d)l.h(this.g[d]);this.g=null}};var l=new b;e.prototype.F=function(d){var g=this.l();d.o(g.resolve,g.reject)};
e.prototype.G=function(d,g){var h=this.l();try{d.call(g,h.resolve,h.reject)}catch(k){h.reject(k)}};e.prototype.then=function(d,g){function h(m,v){return typeof m=="function"?function(B){try{k(m(B))}catch(x){n(x)}}:v}var k,n,t=new e(function(m,v){k=m;n=v});this.o(h(d,k),h(g,n));return t};e.prototype.catch=function(d){return this.then(void 0,d)};e.prototype.o=function(d,g){function h(){switch(k.h){case 1:d(k.i);break;case 2:g(k.i);break;default:throw Error("f`"+k.h);}}var k=this;this.g==null?l.h(h):
this.g.push(h);this.v=!0};e.resolve=c;e.reject=function(d){return new e(function(g,h){h(d)})};e.race=function(d){return new e(function(g,h){for(var k=I(d),n=k.next();!n.done;n=k.next())c(n.value).o(g,h)})};e.all=function(d){var g=I(d),h=g.next();return h.done?c([]):new e(function(k,n){function t(B){return function(x){m[B]=x;v--;v==0&&k(m)}}var m=[],v=0;do m.push(void 0),v++,c(h.value).o(t(m.length-1),n),h=g.next();while(!h.done)})};return e});
V("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});V("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});V("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});V("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});V("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});
V("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});V("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});var p=this||self;var Ea=void 0;var u=typeof Symbol==="function"&&typeof Symbol()==="symbol",Sa=r("jas",void 0,!0),Da=r(void 0,"0actk"),Q=r("m_m","L",!0);var da={H:{value:0,configurable:!0,writable:!0,enumerable:!1}},ca=Object.defineProperties,w=u?Sa:"H";var R={},C={},Ta={};var ha=D(function(a){return typeof a==="number"}),fa=D(function(a){return typeof a==="string"}),ia=D(function(a){return typeof a==="boolean"});var E=typeof p.BigInt==="function"&&typeof p.BigInt(0)==="bigint";var Aa=D(function(a){return E?a>=Ua&&a<=Va:a[0]==="-"?ja(a,Wa):ja(a,Xa)}),Wa=Number.MIN_SAFE_INTEGER.toString(),Ua=E?BigInt(Number.MIN_SAFE_INTEGER):void 0,Xa=Number.MAX_SAFE_INTEGER.toString(),Va=E?BigInt(Number.MAX_SAFE_INTEGER):void 0;var G=0,H=0;var M=typeof BigInt==="function"?BigInt.asIntN:void 0,O=Number.isSafeInteger,na=Number.isFinite,N=Math.trunc,oa=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;var za;var Ya=F(0),Za=function(a,b,c){if(a.s===C){var f=a.j;f=Fa(f,f[w]|0);y(f,2048);a.j=f;a.s=void 0;a.M=void 0;f=!0}else f=!1;if(!f&&ea(a,a.j[w]|0))throw Error();f=a.j;a:{var e=f[w]|0,l=b+-1,d=f.length-1;if(d>=0&&l>=d){var g=f[d];if(g!=null&&typeof g==="object"&&g.constructor===Object){g[b]=c;break a}}l<=d?f[l]=c:c!==void 0&&(e=(e!=null?e:f[w]|0)>>13&1023||536870912,b>=e?c!=null&&(l={},f[e+-1]=(l[b]=c,l)):f[l]=c)}return a},$a=function(a){var b=void 0;b=b===void 0?Ya:b;var c=a.j;a=1+(Ta?0:-1);var f=c.length-
1;f<1+(Ta?0:-1)?a=void 0:a>=f?(c=c[f],c!=null&&typeof c==="object"&&c.constructor===Object?a=c[1]:a===f?a=c:a=void 0):a=c[a];a=a!==null?a:void 0;f=typeof a;a=a==null?a:f==="bigint"?F(M(64,a)):pa(a)?f==="string"?sa(a):ta(a):void 0;return a!=null?a:b},ab=function(a,b,c){if(c!=null){if(typeof c!=="number")throw q("int32");if(!na(c))throw q("int32");c|=0}Za(a,b,c)};var W=function(a,b,c){this.j=T(a,b,c)};W.prototype.toJSON=function(){return S(this)};W.prototype[Q]=R;W.prototype.toString=function(){return this.j.toString()};var bb=function(a){this.j=T(a)};Ra(bb,W);var cb=function(a){this.j=T(a,0,"identity.hfcr")};Ra(cb,W);var db=function(a){return function(b){if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("n");y(b,32);b=new a(b)}return b}}(cb);var eb=function(a,b,c,f,e){this.m=a;this.i=b;this.l=c;this.h=f;this.g=e};eb.prototype.start=function(){var a=this;if(typeof fetch!=="undefined")if(fb()){var b=gb(),c=Date.now();b&&b>c+this.g*1E3&&(b=Date.now()+this.g*1E3,X(b));var f=function(){hb(a).then(function(){setTimeout(f,a.g*1E3)})};setTimeout(function(){f()},b&&b>c?b-c:0)}else ib(this)};
var ib=function(a){jb(a).then(function(){var b=function(){jb(a).then(function(){setTimeout(b,a.g*1E3)})};setTimeout(function(){b()},a.g*1E3)})},jb=function(a){var b=kb(a);return lb(b).then(function(c){c=mb(Ha($a(c)));c!==a.g&&(a.g=c)}).catch(function(){a.g*=2})},hb=function(a){var b=gb();if(!b||Date.now()>=b){var c=Math.floor(Math.random()*1E3);return new Promise(function(f){setTimeout(function(){var e=gb();!e||Date.now()>=e?f(nb(a)):f()},c)})}return Promise.resolve()},lb=function(a){a={method:"POST",
credentials:"same-origin",cache:"no-store",mode:"same-origin",headers:{"Content-Type":"application/json"},body:JSON.stringify(S(a))};if(typeof AbortController!=="undefined"){var b=new AbortController;setTimeout(function(){b.abort()},3E4);a.signal=b.signal}return fetch(new Request("/RotateCookies",a)).then(function(c){return c.text()}).then(function(c){return db(JSON.stringify(JSON.parse(c.substring(5))[0]))})},kb=function(a){var b=new bb;var c=a.m;c=c==null?c:va(c);b=Za(b,2,c);a.i!==0&&ab(b,1,a.i);
a.l!==0&&ab(b,3,a.l);a.h!==0&&ab(b,4,a.h);return b},nb=function(a){X(Date.now()+a.g*1E3);var b=kb(a);return lb(b).then(function(c){c=mb(Ha($a(c)));c!==a.g&&(X(Date.now()+c*1E3),a.g=c)}).catch(function(){a.g*=2;X(Date.now()+a.g*1E3)})},fb=function(){try{var a=window.localStorage;if(!a)return!1;a.setItem("cookieRotationStorageAccessTest","1");a.removeItem("cookieRotationStorageAccessTest");return!0}catch(b){return!1}},mb=function(a){a<60&&(a=60);return a},gb=function(){try{var a=window.localStorage.getItem("nextRotationAttemptTs");
if(!a)return null;var b=Math.floor(Number(a));return Number.isNaN(b)?null:b}catch(c){return null}},X=function(a){try{window.localStorage.setItem("nextRotationAttemptTs",a.toString())}catch(b){}};for(var ob=function(a,b,c,f,e){(new eb(a,b,c,f,e)).start()},pb=["init"],Y=p,Z;pb.length&&(Z=pb.shift());)pb.length||ob===void 0?Y[Z]&&Y[Z]!==Object.prototype[Z]?Y=Y[Z]:Y=Y[Z]={}:Y[Z]=ob;
}catch(e){_._DumpException(e)}
}).call(this,this.default_IdentityRotateCookiesHttp);
// Google Inc.
