# Question 13: Triangle Type Checker
# This program determines the type of triangle based on side lengths.

a = float(input("Enter first side length: "))
b = float(input("Enter second side length: "))
c = float(input("Enter third side length: "))
if a + b > c and a + c > b and b + c > a:
    if a == b == c:
        print("Equilateral")
    elif a == b or b == c or a == c:
        print("Isosceles")
    else:
        print("Scalene")
else:
    print("Invalid Triangle")