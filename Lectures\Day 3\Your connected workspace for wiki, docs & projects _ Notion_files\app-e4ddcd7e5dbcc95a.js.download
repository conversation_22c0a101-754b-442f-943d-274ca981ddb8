(()=>{"use strict";var e,t,n,o,a,r={2881:(e,t,n)=>{n.r(t)},3243:(e,t,n)=>{n.d(t,{tq:()=>a,v3:()=>s,vc:()=>i,zF:()=>r});var o=()=>n(645873);const a={TeamHome:new(o().O2)("teamHome",(()=>Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(9304),n.e(77848),n.e(55571),n.e(93282),n.e(22970),n.e(14310),n.e(93552),n.e(30322),n.e(47934),n.e(35266),n.e(28464),n.e(67608),n.e(41720),n.e(89041)]).then(n.bind(n,533741)))),TeamHomeMoreMenu:new(o().O2)("TeamHomeMoreMenu",(()=>Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(51363)]).then(n.bind(n,583273)))),TeamJoinLeaveButton:new(o().O2)("TeamJoinLeaveButton",(()=>Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(209)]).then(n.bind(n,341974))))},r=(0,o()._h)(a.TeamHome,(e=>e.default)),i=(0,o()._h)(a.TeamHomeMoreMenu,(e=>e.default)),s=(0,o()._h)(a.TeamJoinLeaveButton,(e=>e.TeamJoinLeaveButton))},7615:(e,t,n)=>{n.d(t,{Ay:()=>i,Jm:()=>a,Yq:()=>r});var o=()=>n(292588);const a="space_entitlement_usage",r=n(638681).literals("responses","tokens","seconds"),i={table:a,columnTypes:{id:o().A.UUID,version:o().A.Number,last_version:o().A.Number,created_at:o().A.Number,updated_at:o().A.Number,space_id:o().A.UUID,type:o().A.String,usage:o().A.Number,unit:o().A.String},requiredColumns:{id:!0,version:!0,created_at:!0,updated_at:!0,space_id:!0,type:!0,usage:!0,unit:!0},model:(0,n(152853).P)({})}},11048:(e,t,n)=>{n.d(t,{A:()=>i});var o=n(296540),a=()=>n(872994),r=n(474848);function i(e){let{name:t,children:n}=e;const i=(0,a().y)(),c=(0,o.useRef)({displayName:`MCE(${t})`});return(0,o.useInsertionEffect)((()=>{i&&i.domLock.lockAfterRender(c.current)})),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(s,{token:c}),(0,r.jsx)(r.Fragment,{children:n})]})}function s(e){let{token:t}=e;const n=(0,a().y)();return(0,o.useInsertionEffect)((()=>{n&&n.domLock.unlockForRender(t.current)})),null}},11113:(e,t,n)=>{n.d(t,{Id:()=>a,oY:()=>r});n(944114);var o=()=>n(534177);function a(e){const t=[{type:"home",value:""}];e.crumbs&&t.push(...function(e){const t=e.split(","),n=[];for(const a of t){const[e,t]=a.split(":");if(i(e)&&t)switch(e){case"cat":n.push({type:"category",value:t});break;case"cre":"all"===t?n.push({type:"creators",value:""}):n.push({type:"creator",value:t});break;case"ser":n.push({type:"search",value:t});break;case"col":"all"===t?n.push({type:"collections",value:""}):n.push({type:"collection",value:t});break;default:(0,o().HB)(e)}}return n}(e.crumbs));const n=function(e){const{pageType:t,slug:n,query:a}=e,r=t??"";if(function(e){return"creators"===e||"search"===e||"categories"===e||"templates"===e||"collections"===e}(r))switch(r){case"creators":return n?{type:"creator",value:n}:{type:"creators",value:""};case"categories":return n?{type:"category",value:n}:void 0;case"search":return a?{type:"search",value:a}:void 0;case"templates":return n?{type:"template",value:n}:void 0;case"collections":return n?{type:"collection",value:n}:{type:"collections",value:""};default:(0,o().HB)(r)}return}(e);return n&&t.push(n),t}function r(e,t){const n=function(e){return"creators"===e||"search"===e||"categories"===e||"collections"===e}(e);if(n)switch(e){case"categories":return t?`cat:${t}`:void 0;case"creators":return t?`cre:${t}`:"cre:all";case"search":return t?`ser:${t}`:void 0;case"collections":return t?`col:${t}`:"col:all";default:(0,o().HB)(e)}}function i(e){return"cre"===e||"ser"===e||"cat"===e||"col"===e}},15623:(e,t,n)=>{n.d(t,{e6:()=>a,yc:()=>r});var o=()=>n(496603);function a(e){const t=localStorage.getItem(function(e){return`featureGateOverride-${e}`}(e));return"true"===t||"false"!==t&&void 0}function r(e){const t=localStorage.getItem(function(e){return`experimentOverrideKey-${e}`}(e))||void 0;if(!(0,o().Im)(t))return t}},20765:(e,t,n)=>{n.d(t,{loadCss:()=>o});async function o(){await Promise.all([Promise.resolve().then(n.bind(n,2881)),Promise.resolve().then(n.bind(n,49360)),Promise.resolve().then(n.bind(n,53334)),Promise.resolve().then(n.bind(n,669528)),Promise.resolve().then(n.bind(n,261642)),Promise.resolve().then(n.bind(n,841126)),Promise.resolve().then(n.bind(n,640254)),Promise.resolve().then(n.bind(n,692186)),Promise.resolve().then(n.bind(n,209881)),Promise.resolve().then(n.bind(n,787212)),Promise.resolve().then(n.bind(n,862114)),Promise.resolve().then(n.bind(n,328058)),Promise.resolve().then(n.bind(n,192188)),Promise.resolve().then(n.bind(n,468928))])}},49360:(e,t,n)=>{n.r(t)},52523:(e,t,n)=>{n.d(t,{default:()=>a});var o=()=>n(529543);class a{constructor(e){this.environment=void 0,this.getCookieWithoutPermissionCheck=e=>o().getCookieWithoutPermissionCheck(this.environment,e),this.removeCookie=(e,t)=>(o().removeCookie(e,t),Promise.resolve(void 0)),this.isMobileNative=()=>this.environment.device.isMobileNative,this.environment=e}}},53334:(e,t,n)=>{n.r(t)},56222:(e,t,n)=>{n.d(t,{A:()=>o});n(16280);const o=new class{constructor(){this.sdkInstance=void 0}get sdk(){if(void 0===this.sdkInstance)throw new Error("The Sentry SDK was accessed before `sentryInitializeLight` was called! This should never happen.");return this.sdkInstance}set sdk(e){this.sdkInstance=e}}},61844:(e,t,n)=>{n.d(t,{FN:()=>a,Wx:()=>o});function o(e){return"upwork"===e||"perfmark"===e}const a=["perfmark","reverse","reverse_mm_ent","business_reverse","stacked_business_trial",...n(258037).Kl];n(902006).My},68336:(e,t,n)=>{n.d(t,{F4:()=>v});n(16280),n(581454),n(944114),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(898992),n(803949);let o;function a(e,t){const{sql:n,tagName:a,className:r}=e,i=(null==t?void 0:t.stylize)??(e=>e),s=function(e){try{return o?o.highlight(e):e}catch{return e}}(n),c=s.split("\n"),l=[r?i(`(${r}) `,"undefined"):"",i(a??"sql","special"),i("`","string")];return c.length>2?(n.startsWith("\n")||l.push("\n"),l.push(c.map((e=>`  ${e}`)).join("\n")),n.endsWith("\n")||l.push("\n")):l.push(s),l.push(i("`","string")),l.join("")}function r(e,t){for(const n of t)e.push(n)}const i=Symbol("QueryArg");class s{constructor(e){this.value=e}}const c=new Set(["IS NULL","IS NOT NULL","IS TRUE","IS FALSE","=","!=","<","<=",">",">=","IS","IN","NOT IN","LIKE","NOT LIKE","MATCH"]);function l(e){if(c.has(e))return e;throw new Error(`Not a SQL operator: "${e}"`)}const d={createForDialect:e=>function(t){for(var n=arguments.length,o=new Array(n>1?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];return(new e).appendTemplate(t,...o)}};class u{constructor(){this.chunks=[],this.args=[]}appendTemplate(e){if(!Array.isArray(e)||!Array.isArray(e.raw))throw new Error("sql`` can only be used as a template literal tag");for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];for(const a of e)if(this.appendRaw_DANGEROUS(a),n.length){const e=n.shift();if(e instanceof s){this.appendIdentifier(e);continue}if(e instanceof u){this.append(e);continue}this.appendArg(e)}return this}append(e){return r(this.chunks,e.chunks),r(this.args,e.args),this}appendArg(e){return this.chunks.push(i),this.args.push(e),this}appendRaw_DANGEROUS(e){return this.chunks.push(e),this}appendIdentifier(e){const t=e instanceof s?e.value:e;return this.appendRaw_DANGEROUS(this.escapeIdentifier(t))}escapeIdentifier(e){if(/^[\w]+$/.test(e))return`"${e}"`;throw new Error(`Unexpected SQL identifier format: ${e}`)}sql(){let e=0;return this.chunks.map((t=>t===i?"$"+ ++e:t)).join("")}toString(){const e=JSON.stringify(this.args),t=this.constructor;return`${t===u?"Sql":t.name||t.TagName}(\`${this.sql()}\`, ${e})`}DEBUG_ONLY_getInterpolatedQuery(){let e=0;return this.chunks.map((t=>{if(t===i){const t=this.args[e];return e++,function(e){if(Array.isArray(e)){let t=!1;const n=e.map((e=>"string"==typeof e?b(e):("number"==typeof e||(t=!0),e)));if(!t)return`ARRAY[${n.join(",")}]`}if(null==e)return"NULL";switch(typeof e){case"string":case"symbol":return b(String(e));case"number":case"bigint":case"boolean":return String(e);case"function":case"object":case"undefined":return b(JSON.stringify(e))}}(t)}return t})).join("")}}if(u.TagName="sql","undefined"==typeof window){u.prototype[Symbol.for("nodejs.util.inspect.custom")]=function(e,t,n){return a({sql:this.DEBUG_ONLY_getInterpolatedQuery(),tagName:this.constructor.TagName||"sql",className:this.constructor===u?void 0:this.constructor.name},t)}}function p(e,t){if(t instanceof u)return t;if("string"==typeof t){if(!/^[ \t]*$/.test(t))throw new Error(`Unexpected indent format ${t}`);return e.raw_DANGEROUS(t)}if(t<0)return;const n="  ".repeat(t);return e`\n`.appendRaw_DANGEROUS(`${n}`)}function m(e,t,n){const o=e``;return t.forEach(((e,a)=>{o.append(e),a!==t.length-1&&o.append(n)})),o}const f=(new u).appendRaw_DANGEROUS(" "),g=new u;function b(e){let t=!1,n="'";for(let o=0;o<e.length;o++){const a=e[o];"'"===a?n+=a+a:"\\"===a?(n+=a+a,t=!0):n+=a}return n+="'",!0===t&&(n=` E${n}`),n}var h=()=>n(430476);class _ extends u{static fromColumnType(e){if(!(0,n(732524).tx)(e))throw new Error(`Not a valid Sqlite column type: "${e}"`);return v.raw_DANGEROUS(e)}sql(){return this.chunks.map((e=>e===i?"?":e)).join("")}asRead(){return{sql:this.sql(),args:this.args,getData:!0}}asWrite(){return{sql:this.sql(),args:this.args}}async all(e,t){return(0,h().qU)({connection:e,sql:this.sql(),args:this.args,queryName:t})}async first(e){return(await this.all(e))[0]}async run(e){return(0,h().kx)({connection:e,sql:this.sql(),args:this.args})}}_.TagName="sqlite";const v=function(e){const t=d.createForDialect(e),n=t;return n.raw_DANGEROUS=t=>(new e).appendRaw_DANGEROUS(t),n.ident=t=>(new e).appendIdentifier(t),n.op=t=>(new e).appendRaw_DANGEROUS(l(t)),n.expr=(t,n,o)=>{const a=new e;if("string"==typeof t){const e=t.split(".");for(let t=0;t<e.length;t++)0!==t&&a.appendRaw_DANGEROUS("."),a.appendIdentifier(e[t])}else a.append(t);return a.appendRaw_DANGEROUS(` ${l(n)} `),void 0!==o&&a.append(o),a},n.join=(e,t)=>m(n,e,t),n.and=(e,t)=>function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1;if(0===t.length)return e`TRUE`;if(1===t.length)return e`(${t[0]})`;const o=p(e,n),a=m(e,t,e`${o??f}AND `);return e`(${o??g}${a})`}(n,e,t),n.or=(e,t)=>function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1;if(0===t.length)return e`FALSE`;if(1===t.length)return e`(${t[0]})`;const o=p(e,n),a=m(e,t,e`${o??f}OR `);return e`(${o??g}${a})`}(n,e,t),n.comma=(e,t)=>function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1;return m(e,t,e`,${p(e,n)??f}`)}(n,e,t),n.newline=e=>p(n,e??0)??t`\n`,n.comment=t=>(new e).appendRaw_DANGEROUS(`/* ${t.replace(/\/(?=\*)|\*(?=\/)/g,"$& ")} */`),n}(_)},94442:(e,t,n)=>{async function o(e){const{loadCurrentUserId:t}=await Promise.resolve().then(n.bind(n,479954));return await t(e)}n.d(t,{loadCurrentUserId:()=>o})},96689:(e,t,n)=>{n.d(t,{Ay:()=>f,GQ:()=>c,NR:()=>s,NX:()=>u,Q4:()=>l,Zz:()=>g,zX:()=>i});var o=()=>n(638681),a=()=>n(313127),r=()=>n(292588);const i=65,s="doc_notes",c="wiki",l="project_management",d=o().object({required:{enabled:o().boolean(),start_ts:o().number(),reason:o().literals("migration")},optional:{}}),u="space",p={table:u,columnTypes:{id:r().A.UUID,version:r().A.Number,last_version:r().A.Number,name:r().A.String,email_domains:r().A.StringArray,pages:r().A.StringArray,icon:r().A.String,disable_public_access:r().A.Boolean,disable_public_access_requests:r().A.Boolean,disable_guests:r().A.Boolean,disable_move_to_space:r().A.Boolean,disable_export:r().A.Boolean,disable_space_page_edits:r().A.Boolean,disable_team_creation:r().A.Boolean,beta_enabled:r().A.Boolean,created_time:r().A.Number,last_edited_time:r().A.Number,deleted_by:r().A.UUID,permanently_deleted_time:r().A.Number,created_by_table:r().A.String,created_by_id:r().A.UUID,last_edited_by_table:r().A.String,last_edited_by_id:r().A.UUID,admin_disable_public_access:r().A.Boolean,space_pages:r().A.UUIDArray,plan_type:r().A.String,subscription_tier:r().A.String,invite_link_enabled:r().A.Boolean,initial_use_cases:r().A.String,public_home_page:r().A.String,bot_settings:r().A.JSON,settings:r().A.JSON,overdue_subscription:r().A.JSON,short_id:r().A.Number,short_id_str:r().A.String},model:(0,n(152853).P)({RecordStore:!0,properties:{icon:{getMethod:"getRawIcon",getKeyStoreMethod:"getIconStore"},subscription_tier:{defaultOnRead:"free"},settings:{defaultOnRead:{}}}})},m=o().object({required:{feature:o().literals("ai"),id:o().literals(...n(928217).nD),received_at:o().number()},optional:{}}),f=(o().object({required:{},optional:{is_teams_enabled:o().boolean(),migrated_to_teams_at:o().number(),space_request:o().union([o().object({required:{type:o().literals("personal_external_transfer"),status:o().literals("requested","started","completed"),requested_time:o().number(),requested_from_space_id:o().string(),requested_by_user_id:o().string(),contact_email:o().string()},optional:{start_time:o().number(),target_user_id:o().string(),completed_time:o().number()}}),o().object({required:{type:o().literals("organization_personal_external_transfer"),status:o().literals("requested","started","completed"),requested_time:o().number(),requested_by_organization_id:o().string(),requested_by_user_id:o().string(),contact_email:o().string()},optional:{start_time:o().number(),target_user_id:o().string(),completed_time:o().number()}}),o().object({required:{type:o().literal("team_claim_upgrade"),status:o().literals("requested","started","added_and_downgraded_users","completed"),requested_time:o().number(),requested_by_space_id:o().string(),target_space_admin_ids:o().array(o().string()),requested_by_id:o().string(),requested_by_table:o().literals("bot","notion_user")},optional:{started_time:o().number(),completed_time:o().number()}}),o().object({required:{type:o().literal("organization_claiming_space"),status:o().literals("requested","added_space_to_organization","completed"),requested_time:o().number(),requested_by_organization_id:o().string(),requested_by_id:o().string(),requested_by_table:o().literal("notion_user")},optional:{started_time:o().number(),completed_time:o().number()}}),o().object({required:{type:o().literal("domain_claim_space_deletion"),status:o().literals("started","canceled"),requested_time:o().number(),started_time:o().number(),requested_by_space_id:o().string(),requested_by_id:o().string(),requested_by_table:o().literals("bot","notion_user")},optional:{contact_email:o().string(),last_delayed_time:o().number(),canceled_time:o().number(),canceled_by_id:o().string(),canceled_by_table:o().literals("bot","notion_user")}}),o().object({required:{type:o().literal("organization_domain_claim_space_deletion"),status:o().literals("started","canceled"),requested_time:o().number(),started_time:o().number(),requested_by_organization_id:o().string(),requested_by_id:o().string(),requested_by_table:o().literals("bot","notion_user")},optional:{contact_email:o().string(),last_delayed_time:o().number(),canceled_time:o().number(),canceled_by_id:o().string(),canceled_by_table:o().literals("bot","notion_user")}}),o().object({required:{type:o().literal("space_content_duplication"),status:o().literals("started","completed","failed","dry_run_completed","failed_with_closed_source","pending_deletion"),started_time:o().number(),target_space_id:o().string(),requested_by_id:o().string(),requested_by_table:o().literals("bot","notion_user"),contact_email:o().string()},optional:{completed_time:o().number(),failed_time:o().number()}})]),in_ai_program:o().boolean(),is_assistant_experience_enabled:o().boolean(),all_users_ai_enabled:o().boolean(),database_sync_row_limit:o().number(),disable_membership_requests:o().boolean(),disable_member_upgrade_requests:o().boolean(),disable_external_membership_requests:o().boolean(),enable_guest_invite_requests:o().boolean(),seen_membership_requests:o().boolean(),disable_guest_membership_requests:o().boolean(),seen_guest_membership_requests:o().boolean(),first_invited_member_time:o().number(),hide_sites_banner:o().boolean(),disable_ai_feature:o().boolean(),enable_ai_feature:o().boolean(),disable_page_analytics:o().boolean(),disable_team_guests:o().boolean(),grant_awards:o().array(m),crdt_status:o().literals("off","downgrading","prefer_off","prefer_on","migrating","on"),subscription_elapsed_date:o().number(),space_survey_data:o().object({required:{},optional:{intent:o().object({required:{value:o().string(),version:o().number(),collected_at:o().number(),collected_from:o().literals(...a().dH)},optional:{}}),use_cases:o().object({required:{value:o().array(o().string()),version:o().number(),collected_at:o().number(),collected_from:o().literals(...a().dH)},optional:{}}),company_size:o().object({required:{value:o().string(),version:o().number(),collected_at:o().number(),collected_from:o().literals(...a().dH)},optional:{}}),collaborative_intent:o().object({required:{value:o().string(),version:o().number(),collected_at:o().number(),collected_from:o().literals(...a().dH)},optional:{}})}}),seen_block_limit_reduction:o().boolean(),seen_block_limit_reduction_v2:o().boolean(),seen_block_limit_increase:o().boolean(),seen_block_limit_earlier:o().boolean(),exposed_to_timed_trials_in_onboarding:o().boolean(),exposed_to_timed_trials_in_onboarding_v2:o().boolean(),exposed_to_hide_block_limit_counter:o().boolean(),exposed_to_block_management:o().boolean(),reach_block_limit_time:o().number(),zero_retention_enabled:o().boolean(),embedding_index_is_live:o().boolean(),embedding_index_generation:o().number(),sharded_entitlement_usage_tables:o().boolean(),are_scim_invite_emails_suppressed:o().boolean(),disable_notion_calendar:o().boolean(),custom_emoji_creation_only_admin:o().boolean(),self_serve_enterprise_eligible:o().boolean(),was_in_personal_home_default_treatment:o().boolean(),is_personal_home_enabled:o().boolean(),show_home_virtual_views:o().boolean(),enable_ai_training:o().boolean(),manage_internal_domains:o().union([o().literal("auto"),o().literal("notion_admin_override")]),premium_feature_overrides:o().record(o().string(),o().union([o().literal("unlimited"),o().number(),o().boolean()])),delete_from_trash_delay_seconds:o().number(),purge_delay_seconds:o().number(),free_charts:o().array(o().string()),hipaa_compliancy_data:o().object({required:{last_edited_by_id:o().string(),last_edited_time:o().number()},optional:{}}),disallow_webhook_automation_action:o().boolean(),domain_link_sharing_enabled:o().boolean(),freeze:d,exposed_to_stackranking_experiment:o().object({required:{},optional:{adoption_hide_features_v1:o().boolean()}})}}),p),g="e12b42ac-4e54-476f-a4f5-7d6bdb1e61e2"},99895:(e,t,n)=>{n.d(t,{G:()=>o});class o extends Map{constructor(e,t){super(t),this.defaultFactory=void 0,this.defaultFactory=e}get(e){if(!super.has(e)){const t=this.defaultFactory(e);super.set(e,t)}return super.get(e)}update(e,t){const n=t(this.get(e));return this.set(e,n),n}toMap(){return new Map(this)}}},100875:(e,t,n)=>{n.d(t,{S9:()=>s,cq:()=>a,hr:()=>r,qm:()=>i});var o=()=>n(994310);function a(e){const{spaceView:t,preference:n}=e;if(!t)return;const a=s({userId:t.getUserId(),spaceId:t.getSpaceId()});a&&o().A.set(a,n)}function r(e){const{spaceView:t,pageId:n}=e;if(!t)return;const a=i({spaceId:t.getSpaceId(),userId:t.getUserId()});a&&o().A.set(a,n)}function i(e){const{userId:t,spaceId:n}=e;return t&&n?`firstPageInSidebar:${t}:${n}`:null}function s(e){const{userId:t,spaceId:n}=e;return t&&n?`onAppStartPreference:${t}:${n}`:null}},108873:(e,t,n)=>{n.d(t,{initOPFS:()=>a});var o=()=>n(780054);async function a(e){let{userId:t,environment:a}=e;const{OPFSBootupRegistry:r}=await Promise.resolve().then(n.bind(n,865085));if(!r.isEnabled)return;if(!r.pageCache)return;const{parseRoute:i}=await Promise.resolve().then(n.bind(n,861017)),s=i({url:window.location.href,isMobile:a.device.isMobile,baseUrl:o().A.domainBaseUrl,publicDomainName:o().A.publicDomainName,protocol:o().A.protocol,currentUrl:window.location.href});"page"===s.name&&s.blockId&&(r.pageRecordMapBufferPromise=r.pageCache.readBuffer(t,s.blockId).then((e=>(e.buffer.byteLength>0&&(r.isPageInCache=!0),r.updateInitialMetricData({get_handle_duration:e.metrics.getHandle,get_file_duration:e.metrics.getFile,load_content_duration:e.metrics.getBuffer,read_duration:e.metrics.total}),e))),r.pageRecordMapBufferPromise.catch((e=>{})))}},117296:(e,t,n)=>{n.d(t,{KM:()=>a,VJ:()=>r,Ww:()=>i,Y2:()=>c,Y8:()=>s});var o=()=>n(645873);const a={personalHome:new(o().O2)("personalHome",(()=>Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(96346),n.e(58550),n.e(16471),n.e(62475),n.e(3151),n.e(44711),n.e(98629),n.e(53321),n.e(90825),n.e(55373),n.e(37353),n.e(29151),n.e(78191),n.e(77848),n.e(55571),n.e(92845),n.e(71718),n.e(5605),n.e(48486),n.e(95794),n.e(13677),n.e(56952),n.e(74562),n.e(48632),n.e(27711),n.e(59794),n.e(4422),n.e(11341),n.e(34359),n.e(28721),n.e(28271),n.e(46414),n.e(90625),n.e(17254),n.e(15566),n.e(65487),n.e(34877),n.e(46418),n.e(78708),n.e(76959),n.e(68452),n.e(24587),n.e(81768),n.e(2233),n.e(26483),n.e(31276),n.e(9375),n.e(71104),n.e(67435),n.e(98659),n.e(82116),n.e(2517),n.e(69849),n.e(29459),n.e(9562),n.e(51127),n.e(25065),n.e(90978),n.e(93282),n.e(22970),n.e(90832),n.e(14310),n.e(93552),n.e(27506),n.e(45620),n.e(88177),n.e(3184),n.e(35014),n.e(86840),n.e(24735),n.e(55435),n.e(44802)]).then(n.bind(n,122172)))),CustomDBPanelEmptyState:new(o().O2)("CustomDBPanelEmptyState",(async()=>await Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(69224)]).then(n.bind(n,52372)))),TipsInAppModal:new(o().O2)("TipsInAppModal",(async()=>Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(58204)]).then(n.bind(n,493068)))),personalHomeLearnHelpers:new(o().O2)("personalHomeLearnHelpers",(async()=>Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(41135)]).then(n.bind(n,539780)))),RecentsCachingListener:new(o().O2)("RecentsCachingListener",(async()=>Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(62516)]).then(n.bind(n,437879))))},r=(0,o()._h)(a.personalHome,(e=>e.PersonalHomeContainer)),i=(0,o()._h)(a.personalHome,(e=>e.SetupPendingHomeTile)),s=(0,o().jQ)(a.CustomDBPanelEmptyState,(e=>e.default)),c=(0,o()._h)(a.TipsInAppModal,(e=>e.TipsInAppModal))},118165:(e,t,n)=>{n.d(t,{initializeReactivityVersion:()=>o});function o(){return 1}},140283:(e,t,n)=>{n.d(t,{A:()=>o,B:()=>a});const o=1024,a=1048576},140583:(e,t,n)=>{n.d(t,{E:()=>a});n(581454);var o=n.n(n(877232));function a(e,t){const n=e.navigator,a=new(o().UAParser)(t);function r(){return/Yandex/.test(t)}function i(){return/Windows.*Edge/i.test(t)}function s(){return!i()&&!r()&&(/Chrome/i.test(t)||/CriOS/i.test(t))}function c(){return/Android/i.test(t)||"Android"===a.getOS().name}function l(){return!c()&&(/iPad/i.test(t)||"MacIntel"===n.platform&&"ontouchend"in document)}function d(){return!c()&&(/iPhone|iPod/i.test(t)||l())}function u(){return d()||c()||/Windows Phone/i.test(t)}function p(){return/Mac/i.test(t)&&!u()}function m(){return/Windows/i.test(t)&&!u()}function f(){return/CrOS/i.test(t)}function g(){return(/Linux/i.test(t)||f())&&!u()}function b(){return`${a.getBrowser().name}`}return{isYandex:r,isEdgeHTML:i,isChrome:s,isSafari:function(){return!i()&&!r()&&!s()&&/Safari/i.test(t)},isFirefox:function(){return/Firefox/i.test(t)},isAndroid:c,isIpad:l,isIOS:d,isMobile:u,isMobileNative:function(){return/ReactNative/.test(t)||/MobileNative/.test(t)},isMobileNativeCalendar:function(){return/NotionCalendar/.test(t)},isDesktop:function(){return!u()},isMac:p,isWindows:m,isChromebook:f,isLinux:g,isRetina:function(){return!!e.matchMedia&&e.matchMedia("(-webkit-min-device-pixel-ratio: 1.5),\t\t\t\t\t\t(min--moz-device-pixel-ratio: 1.5),\t\t\t\t\t\t(-o-min-device-pixel-ratio: 3/2),\t\t\t\t\t\t(min-resolution: 1.5dppx)").matches},getDeviceOS:function(){return p()?"mac":m()?"windows":d()?"ios":c()?"android":s()?"chrome":g()?"linux":"unknown"},getBrowserName:b,getBrowserVersion:function(){return`${a.getBrowser().version}`},getDeviceOSVersion:function(){const e=a.getOS().version;return e?e.split(".").map((e=>parseInt(e,10))):[]},getDoNotTrackEnabled:function(){return"1"===e.doNotTrack||("yes"===n.doNotTrack||"1"===n.doNotTrack||("1"===n.msDoNotTrack||!!(e.external&&e.external.msTrackingProtectionEnabled&&e.external.msTrackingProtectionEnabled())))},getIsBannedGoogleSSOUserAgent:function(){return!!/AppleWebKit.*LinkedInApp/i.test(t)||(!!/AppleWebKit.*BytedanceWebview/i.test(t)||!("Facebook"!==b()||!/AppleWebKit/i.test(t)))}}}},141625:(e,t,n)=>{n.d(t,{createDevice:()=>c});n(581454);var o=()=>n(496506),a=()=>n(757695),r=()=>n(140583),i=()=>n(496603),s=()=>n(780054);function c(e,t){var n;const c=(0,r().E)(e,(null==t?void 0:t.userAgentOverride)??e.navigator.userAgent),l=Boolean(e.__isElectron),d=Boolean(e.__isElectronMail),u=Boolean(e.__isElectronCalendar),p=l||d||u,m=l&&"darwin"===e.__platform,f=l&&"win32"===e.__platform,g=l?function(){const e=/Electron\/(\w+\.\w+.\w+)/.exec(navigator.userAgent);if(e)return e[1].split(".").map((e=>parseInt(e,10)));return}():void 0;let b,h;var _,v;l&&(h=null===(_=e.__desktopConfig)||void 0===_?void 0:_.desktopAppId,b=null===(v=e.__desktopConfig)||void 0===v?void 0:v.targetPlatform);const y=c.isMobileNative(),w=c.isMobileNativeCalendar(),k=l||y,S=!k,A="https:"===e.location.protocol||"http:"===e.location.protocol,C=c.isYandex(),I=c.isEdgeHTML(),P=c.isChrome(),T=c.isSafari(),E=c.isFirefox(),R=c.isAndroid(),D=c.isIpad(),q=c.isIOS(),M=c.isMobile();null==t||null===(n=t.horizontalSizeClassStore)||void 0===n||n.setState(q?D?"unknown":"compact":"unknown");const O=a().Store.createValue(e.innerHeight,{name:"windowHeightStore"}),N=a().Store.createValue(e.innerWidth,{name:"windowWidthStore"});e.addEventListener("resize",i().sg((()=>{O.setState(e.innerHeight),N.setState(e.innerWidth)}),300));let B=Math.max(N.state,O.state);const L=new(o().ComputedStore)((()=>{if(B=Math.max(N.state,O.state),R){const e=600,t=969,n=N.state===B?t:e;return N.state>=n}if(!q)return!1;if(null==t||!t.horizontalSizeClassStore)return!1;switch(null==t?void 0:t.horizontalSizeClassStore.state){case"compact":return!1;case"regular":return!0;case"unknown":return!!D&&N.state>680}}),{debugName:"isTablet"}),x=c.isDesktop(),U=c.isMac(),F=c.isWindows(),V=c.isChromebook(),j=c.isLinux(),J=c.isRetina(),H=M&&S,$=x&&S,W="undefined"!=typeof chrome&&void 0!==chrome.tabs,z=s().A.version,Z=c.getDeviceOS(),G=c.getDeviceOSVersion(),K=l?"electron":y?"react-native":"browser",Q=(()=>{if(l||u||d)return m?"mac-desktop":"windows-desktop";if(y){if(R)return"android";if(q)return"ios"}return"web"})(),X=(()=>{if(l)return m?"mac-desktop":"windows-desktop";if(y){if(R)return"android";if(q)return"ios"}return M?"web-mobile":"web-desktop"})(),Y=c.getBrowserName(),ee=c.getBrowserVersion(),te=c.getDoNotTrackEnabled(),ne=c.getIsBannedGoogleSSOUserAgent(),oe=matchMedia("(prefers-reduced-motion: reduce)").matches;return{isElectron:l,isElectronMail:d,isElectronCalendar:u,isElectronAny:p,isElectronMac:m,isElectronWindows:f,desktopAppId:h,electronVersion:g,isMobileNative:y,isMobileBeta:!1,isMobileNativeCalendar:w,isNative:k,isBrowser:S,isHttpApp:A,isYandex:C,isEdgeHTML:I,isChrome:P,isSafari:T,isFirefox:E,isAndroid:R,isIOS:q,isIpad:D,isMobile:M,get isTablet(){return L.state},get isPhone(){return M&&(!L.state||q&&!D)},get isSmallPhone(){return M&&N.state<=320},isDesktop:x,isMac:U,get isApple(){return U||q},isWindows:F,isChromebook:V,isLinux:j,isRetina:J,isMobileBrowser:H,isDesktopBrowser:$,isChromeExtension:W,isIPhoneX:!1,version:z,desktopAppVersion:undefined,mobileAppVersion:undefined,os:Z,osVersion:G,platform:K,auditLogPlatform:Q,browserName:Y,browserVersion:ee,doNotTrackEnabled:te,isBannedGoogleSSOUserAgent:ne,get prefersDarkInterface(){return matchMedia("(prefers-color-scheme: dark)").matches},ramSizeInGB:undefined,deviceType:X,prefersReducedMotion:oe,desktopTargetPlatform:b}}},149208:(e,t,n)=>{n.d(t,{n:()=>o});const o="__notion_html_async"},150749:(e,t,n)=>{n.d(t,{getProfilingToolForSession:()=>r});var o=()=>n(165162);let a;function r(){var e;if(void 0!==a)return a;const t=(null===(e=(0,o().Pr)("sentry"))||void 0===e?void 0:e.get("profilesSessionSampleRate",0))||0;return Math.random()<t?(a="sentry",a):(a="none",a)}},152853:(e,t,n)=>{n.d(t,{P:()=>o});Symbol("Model: default value disabled");function o(e){return e}},155959:(e,t,n)=>{n.d(t,{T:()=>a});const o="__mobileAppFeatures";function a(e){const t="undefined"==typeof window?{}:window[o];return void 0!==t&&!0===t[e]}},165162:(e,t,n)=>{n.d(t,{Pr:()=>_,StatsigInitializer:()=>l,getDeviceAttributesForStatsigUser:()=>p,getOrCreateStableID:()=>y,gg:()=>h,localUserOnlyGateCheck:()=>f,statsigClientLoader:()=>u,tX:()=>m});n(898992),n(803949),n(581454);var o=()=>n(758363),a=(o(),()=>n(219319)),r=(a(),()=>n(246529)),i=(r(),()=>n(871386)),s=()=>n(15623);const c="STATSIG_LOCAL_STORAGE_INTERNAL_STORE_OVERRIDES_V3",l={initializePromise:void 0,isComplete:!1,error:void 0,stableID:void 0,statsigStageTwoUser:void 0,environment:void 0};class d extends r().LocalOverrideAdapter{constructor(){super(...arguments),this.preSaveFunction=void 0}setPreSaveFunction(e){this.preSaveFunction||(this.preSaveFunction=e)}getAllOverrides(){const e=super.getAllOverrides();Object.keys((null==e?void 0:e.experiment)??{}).map(o()._DJB2).forEach((t=>{null==e||delete e.experiment[t]}));return Object.keys((null==e?void 0:e.gate)??{}).map(o()._DJB2).forEach((t=>{null==e||delete e.gate[t]})),e}overrideExperiment(e,t){super.overrideExperiment(e,t),this.saveConfig()}overrideGate(e,t){super.overrideGate(e,t),this.saveConfig()}removeExperimentOverride(e){super.removeExperimentOverride(e),this.saveConfig()}removeGateOverride(e){super.removeGateOverride(e),this.saveConfig()}removeAllOverrides(){super.removeAllOverrides(),this.saveConfig()}loadConfig(){const e=localStorage.getItem(c);try{let t;if(e&&(t=JSON.parse(e)),!t)return;void 0!==t.gates&&Object.entries(t.gates).forEach((e=>{let[t,n]=e;"boolean"==typeof n&&super.overrideGate(t,n)}));const n=e=>"object"==typeof e&&null!==e;void 0!==t.configs&&Object.entries(t.configs).forEach((e=>{let[t,o]=e;n(o)&&super.overrideExperiment(t,o)}))}catch(t){}}saveConfig(){var e;null===(e=this.preSaveFunction)||void 0===e||e.call(this);const t=super.getAllOverrides();t&&localStorage.setItem(c,JSON.stringify({gates:t.gate??{},configs:t.experiment??{},layers:t.layer??{}}))}}const u=new class{constructor(){this.completedStageOne=void 0,this.completedStageTwo=void 0,this.overrideAdapter=void 0}getUninitializedStatsigClient(e){const{statsigUser:t,initialValues:o,overrideStableID:r}=e;r&&(t.customIDs=t.customIDs||{},t.customIDs.stableID=r);const i=new d;this.overrideAdapter=i;const s=new(a().StatsigClient)(n(780054).A.statsig.apiKey,t,{environment:{tier:"production"},networkConfig:{api:"https://exp.notion.so/v1/"},overrideAdapter:i,disableEvaluationMemoization:!1});return"string"==typeof o&&s.dataAdapter.setData(o),s}loadStageOne(e){const{currentUserId:t,device:n,overrideStableID:o,browserId:a,deviceId:r}=e,i=m({userId:t,device:n,stableId:o,browserId:a,deviceId:r}),s=this.getUninitializedStatsigClient({statsigUser:i,initialValues:void 0,overrideStableID:o});return s.initializeSync(),localStorage.setItem(v,o),this.completedStageOne=s,s}loadStageTwo(e){var t,n;const{statsigUser:o}=e,a="object"==typeof e.initialValues?JSON.stringify(e.initialValues):e.initialValues,r="stableID"in e?e.stableID:null===(t=e.initialValues)||void 0===t?void 0:t.evaluated_keys.customIDs.stableID;let i;return r&&(o.customIDs=o.customIDs||{},o.customIDs.stableID=r),this.completedStageOne?(i=this.completedStageOne,a&&i.dataAdapter.setData(a),i.updateUserSync(o)):i=this.getUninitializedStatsigClient({statsigUser:o,initialValues:a,overrideStableID:r}),null===(n=this.overrideAdapter)||void 0===n||n.loadConfig(),l.statsigStageTwoUser=o,this.completedStageTwo=i,i}async getClient(){return await l.initializePromise,this.getClientSync()}getClientSync(){return this.completedStageTwo?this.completedStageTwo:this.completedStageOne}getLocalUserOnlyClient(){return this.completedStageOne}};function p(e){var t;return{clientVersion:e.version,mobileVersion:e.mobileAppVersion,desktopVersion:null===(t=e.electronVersion)||void 0===t?void 0:t.join("."),isElectron:e.isElectron,isMobileNative:e.isMobileNative,isMobileBeta:e.isMobileBeta,isMobileBrowser:e.isMobileBrowser,isBrowser:e.isBrowser,isMobile:e.isMobile,isDesktopBrowser:e.isDesktopBrowser,isTablet:e.isTablet}}function m(e){var t;const{userId:n,device:o}=e,a={locale:"locale"in e?e.locale:void 0,...p(o),spaceCreatedTime:"spaceCreatedTime"in e?e.spaceCreatedTime:void 0,spaceCreatedDate:"spaceCreatedDate"in e?e.spaceCreatedDate:void 0,spaceSubscriptionTier:"subscriptionTier"in e?e.subscriptionTier:void 0,isSalesAssistedPlan:"isSalesAssistedPlan"in e?e.isSalesAssistedPlan:void 0,userSignupTime:"signupTime"in e?e.signupTime:void 0,domainType:"domainType"in e?e.domainType:void 0,persona:"persona"in e?e.persona:void 0,teamRole:"teamRole"in e?e.teamRole:void 0,useCases:"useCases"in e?e.useCases:void 0,companySize:"companySize"in e?e.companySize:void 0,planType:"planType"in e?e.planType:void 0,browserId:"browserId"in e?e.browserId:void 0,stableID:"stableId"in e?e.stableId:void 0,hasBrowserId:"browserId"in e&&Boolean(null===(t=e.browserId)||void 0===t?void 0:t.length)};"spaceId"in e&&e.spaceId&&(a.spaceId=e.spaceId),"deviceId"in e&&e.deviceId&&(a.deviceId=e.deviceId);return{userID:n,custom:a,customIDs:{..."deviceId"in e&&e.deviceId&&{deviceId:e.deviceId},..."stableId"in e&&e.stableId&&{stableID:e.stableId},..."spaceId"in e&&e.spaceId&&{spaceId:e.spaceId}},privateAttributes:{...!1}}}function f(e){const{gateName:t,disableExposureLogging:n}=e;if(Boolean(l.initializePromise)&&!l.error)try{const e=(0,s().e6)(t);if(void 0!==e)return e;const o=u.getLocalUserOnlyClient();return o?o.checkGate(t,{disableExposureLog:n}):null}catch{return null}return null}const g="group",b="_unassigned";function h(e){const{experimentId:t,param:n=g,disableExposureLogging:o}=e;if(Boolean(l.initializePromise)&&!l.error){let e;try{const a=u.getLocalUserOnlyClient();if(!a)return;e=o?a.getExperiment(t,{disableExposureLog:!0}):a.getExperiment(t);const r=e.get(n,b);if(r&&r!==b)return r}catch{}}}function _(e){if(Boolean(l.initializePromise)&&!l.error)try{const t=u.getLocalUserOnlyClient();return t?t.getDynamicConfig(e,{disableExposureLog:!0}):void 0}catch{return}}const v="STATSIG_LOCAL_STORAGE_STABLE_ID";function y(){if(l.stableID)return l.stableID;let e;try{e=localStorage.getItem(v)??void 0}catch(t){}return e||(e=(0,i().A)()),l.stableID=e,e}},179034:(e,t,n)=>{n.d(t,{Ay:()=>c,Ky:()=>i,NL:()=>r,ev:()=>s,nd:()=>a});var o=()=>n(292588);const a=[...["dmca","graphic_or_harmful","exploitation","harassment_abuse_or_threat","hateful","ip_infringement","deceptive_or_malicious","personal_or_confidential","other","verified"]],r={block:!0,collection:!0,space:!0,comment:!0,team:!0,automation_action:!0,skill:!0,collection_view:!0,layout:!0,channel:!0,assistant_chat_session:!0},i={to_do:!0,header:!0,sub_header:!0,sub_sub_header:!0,toggle:!0,quote:!0,bulleted_list:!0,divider:!0,numbered_list:!0,code:!0,text:!1,encrypted_text:!1,page:!1,personal_home_page:!1,factory:!1,button:!1,column_list:!1,column:!1,embed:!1,framer:!1,tweet:!1,gist:!1,drive:!1,audio:!1,maps:!1,invision:!1,mixpanel:!1,image:!1,video:!1,file:!1,bookmark:!1,equation:!1,collection_view:!1,collection_view_page:!1,form:!1,breadcrumb:!1,copy_indicator:!1,link_to_page:!1,link_to_collection:!1,figma:!1,loom:!1,typeform:!1,codepen:!1,pdf:!1,callout:!1,table_of_contents:!1,whimsical:!1,miro:!1,abstract:!1,sketch:!1,excalidraw:!1,replit:!1,hex:!1,deepnote:!1,ai_block:!1,drawing:!1,slide:!1,post:!1,transclusion_container:!1,transclusion_reference:!1,external_object_instance:!1,table:!1,table_row:!1,tab:!1,external_object_instance_page:!1,alias:!1,workflow:!1,transcription:!1,person_profile:!1},s="block",c={table:s,columnTypes:{id:o().A.UUID,space_id:o().A.UUID,version:o().A.Number,last_version:o().A.Number,type:o().A.String,properties:o().A.JSON,content:o().A.StringArray,non_content_children:o().A.UUIDArray,discussions:o().A.StringArray,view_ids:o().A.StringArray,collection_id:o().A.UUID,permissions:o().A.JSON,created_time:o().A.Number,last_edited_time:o().A.Number,copied_from:o().A.UUID,file_ids:o().A.StringArray,ignore_block_count:o().A.Boolean,is_template:o().A.Boolean,parent_id:o().A.UUID,parent_table:o().A.String,alive:o().A.Boolean,moved:o().A.JSON,format:o().A.JSON,created_by:o().A.UUID,last_edited_by:o().A.UUID,created_by_table:o().A.String,created_by_id:o().A.UUID,last_edited_by_table:o().A.String,last_edited_by_id:o().A.UUID,content_classification:o().A.String,moved_to_trash_table:o().A.String,moved_to_trash_id:o().A.UUID,moved_to_trash_time:o().A.Number,deleted_from_trash_time:o().A.Number,deleted_from_trash_table:o().A.String,deleted_from_trash_id:o().A.UUID,crdt_data:o().A.JSON,crdt_format_version:o().A.Number},requiredColumns:{parent_id:!0,parent_table:!0,alive:!0,type:!0,space_id:!0},defaultColumnValues:{alive:!1},model:(0,n(152853).P)({RecordStore:!0,properties:{content:{getMethod:"getContentIds",getKeyStoreMethod:"getContentStore"},view_ids:{getMethod:"getCollectionViewIds",getKeyStoreMethod:"getCollectionViewsStore"},properties:{defaultOnRead:{}},format:{defaultOnRead:{}},copied_from:{getMethod:!1},collection_id:{getMethod:!1}}})}},183558:(e,t,n)=>{n.d(t,{Ay:()=>c,Mb:()=>r,Ut:()=>i});n(16280),n(898992),n(823215);var o=()=>n(534177);const a=[":",";","<","=",">","?","@","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","[","\\","]","^","_","`","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","{","|","}","~"],r=["!",'"',"#","$","%","&","'","(",")","*","+",",","-",".","/","0","1","2","3","4","5","6","7","8","9",...a];function i(e){return e.length>0&&[...e].every((e=>(0,o().Xk)(r,e)))}function s(){return a[Math.floor(Math.random()*a.length)]}function c(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:4;if(e<1)throw new Error("ShortID length must be at least 1");let t=s();for(;t.length<e;)t+=s();return t}},187174:(e,t,n)=>{n.d(t,{BC:()=>u,V$:()=>l,Yb:()=>d,fQ:()=>p});n(898992),n(823215),n(672577);var o=n(296540),a=()=>n(319625),r=()=>n(534177),i=()=>n(558842),s=()=>n(591779),c=()=>n(336811);function l(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{debugName:r}=n;(0,o.useDebugValue)(r);const i=(0,o.useRef)(0),s=(0,c().T)(),[l,d]=(0,o.useState)({status:"idle"}),u=(0,o.useCallback)((()=>i.current++),[i]),p=(0,o.useMemo)((()=>async function(){i.current++;const t=i.current;d((e=>({...e,status:"pending"})));try{const n=await e(...arguments);return t===i.current&&s.current&&d((e=>({...e,status:"resolved",value:n,error:void 0}))),n}catch(n){return t===i.current&&s.current&&d((e=>({...e,status:"rejected",error:(0,a().A)(n),value:void 0}))),Promise.reject(n)}}),[s,...t]);return[l,p,u]}function d(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const{debounce:a,throttle:r,interval:i,debugName:c}=n;(0,o.useDebugValue)(c);const d=(0,s().lW)(t,a,s().MR),u=(0,s().wb)(d,r,s().MR),[p,m,f]=l(e,u,n);return(0,o.useEffect)((function(){function e(){m().catch((e=>function(e,t){console.error("useAsync: promise rejected",t,e)}(e,c)))}if(void 0!==i){let t;function n(){t&&(window.clearInterval(t),t=void 0)}function o(){!document.hidden?t=window.setInterval(e,i):n()}return document.addEventListener("visibilitychange",o),o(),e(),()=>{document.removeEventListener("visibilitychange",o),n(),f()}}return e(),f}),[m,f,i,c]),[p,m,f]}function u(e){let{state:t,render:n,forceRenderLoading:o,spinAfterMs:a=300}=e;const r="resolved"!==t.status||(o??!1),c=(0,s().b_)(r,!1,a,Object.is);return r?(0,i().Du)(n(c)):null}function p(e){const t=Object.values(e),n=(0,o.useMemo)((()=>{const t={};for(const[n,o]of(0,r().WP)(e)){if(!o.value&&"resolved"!==o.status)return;t[n]=o.value}return t}),t);return(0,o.useMemo)((()=>{if(t.every((e=>"resolved"===e.status)))return{status:"resolved",value:n};if(t.every((e=>"idle"===e.status)))return{status:"idle",value:n};const e=t.find((e=>"rejected"===e.status));return e||{status:"pending",value:n}}),t)}},192188:(e,t,n)=>{n.r(t)},206267:(e,t,n)=>{n.d(t,{Dt:()=>i,JW:()=>r,gB:()=>a});var o=()=>n(498212);const a=o().gB,r=o().lZ,i=o().lZ},209199:(e,t,n)=>{n.d(t,{Hj:()=>g,Hx:()=>w,Tf:()=>d,Yr:()=>r,Zv:()=>I,fy:()=>i,gI:()=>_,hL:()=>S,iK:()=>k,nG:()=>m,o_:()=>y,p5:()=>R,q:()=>C,ur:()=>f,v9:()=>P,vL:()=>A,vR:()=>E});n(898992),n(354520),n(672577),n(581454),n(908872);var o=()=>n(534177);const a={"en-US":{marketing:!0,preferred:!0,routing:!0,language:!1,development:!1,beta:!1},"ko-KR":{marketing:!0,preferred:!0,routing:!1,language:!1,development:!1,beta:!1},"ja-JP":{marketing:!0,preferred:!0,routing:!1,beta:!0,language:!1,development:!1},"fr-FR":{marketing:!0,preferred:!0,routing:!1,language:!1,development:!1,beta:!1},"de-DE":{marketing:!0,preferred:!0,routing:!1,language:!1,development:!1,beta:!1},"es-ES":{marketing:!0,preferred:!0,routing:!0,language:!1,development:!1,beta:!1},"es-LA":{marketing:!0,preferred:!0,routing:!1,language:!1,development:!1,beta:!1},"pt-BR":{marketing:!0,preferred:!0,routing:!1,language:!1,development:!1,beta:!1},"fi-FI":{marketing:!1,preferred:!0,routing:!0,beta:!0,language:!1,development:!1},"da-DK":{marketing:!1,preferred:!0,routing:!0,beta:!0,language:!1,development:!1},"nl-NL":{marketing:!1,preferred:!0,routing:!0,beta:!0,language:!1,development:!1},"nb-NO":{marketing:!1,preferred:!0,routing:!0,beta:!0,language:!1,development:!1},"sv-SE":{marketing:!1,preferred:!0,routing:!0,beta:!0,language:!1,development:!1},"zh-CN":{marketing:!0,preferred:!0,routing:!0,development:!1,beta:!0,language:!1},"zh-TW":{marketing:!0,preferred:!0,routing:!0,development:!1,beta:!0,language:!1},es:{marketing:!0,preferred:!1,routing:!0,language:!0,development:!1,beta:!1},fr:{marketing:!0,preferred:!1,routing:!0,language:!0,development:!1,beta:!1},pt:{marketing:!0,preferred:!1,routing:!0,language:!0,development:!1,beta:!1},ko:{marketing:!0,preferred:!1,routing:!0,language:!0,development:!1,beta:!1},ja:{marketing:!0,preferred:!1,routing:!0,language:!0,development:!1,beta:!1},de:{marketing:!0,preferred:!1,routing:!0,language:!0,development:!1,beta:!1}},r=(0,o().WP)(a).filter((e=>{let[t,{preferred:n,marketing:o}]=e;return n&&o})).map((e=>{let[t]=e;return t})),i=(0,o().WP)(a).filter((e=>{let[t,{preferred:n,development:o}]=e;return n&&!o})).map((e=>{let[t]=e;return t})),s=(0,o().WP)(a).filter((e=>{let[t,{preferred:n,beta:o}]=e;return n&&o})).map((e=>{let[t]=e;return t})),c=((0,o().WP)(a).filter((e=>{let[t,{routing:n,development:o}]=e;return n&&!o})).map((e=>{let[t]=e;return t})),(0,o().WP)(a).filter((e=>{let[t,{routing:n,marketing:o}]=e;return n&&o})).map((e=>{let[t]=e;return t}))),l=["pseudo"],d=[...l,...(0,o().WP)(a).filter((e=>{let[t,{preferred:n,development:o}]=e;return n&&o})).map((e=>{let[t]=e;return t}))],u=((0,o().WP)(a).filter((e=>{let[t,{routing:n,development:o}]=e;return n&&o})).map((e=>{let[t]=e;return t})),[...i,...s,...d]),p=(0,o().WP)(a).filter((e=>{let[t,{language:n}]=e;return n})).map((e=>{let[t]=e;return t})),m=d.map((e=>({[e.split("-").join("")]:`/${e.toLocaleLowerCase()}`}))).reduce(((e,t)=>Object.assign(e,t)),{}),f=u.map((e=>({[e.split("-").join("")]:`/${e.toLocaleLowerCase()}`}))).reduce(((e,t)=>Object.assign(e,t)),{}),g=p.map((e=>({[e.toLocaleLowerCase()]:`/${e.toLocaleLowerCase()}`}))).reduce(((e,t)=>Object.assign(e,t)),{}),b={"es-LA":"es-419","zh-TW":"zh-Hant-TW",pseudo:"yav"},h=["en","ko","ja","fr","de","es","pt","fi","da","nl","nb","sv","zh"],_={de:"de-DE",ko:"ko-KR",en:"en-US",es:"es-LA",fr:"fr-FR",ja:"ja-JP",pt:"pt-BR",fi:"fi-FI",da:"da-DK",nl:"nl-NL",nb:"nb-NO",sv:"sv-SE",zh:"zh-CN"},v={"es-ES":"es","es-LA":"es","en-US":"en","pt-BR":"pt","fr-FR":"fr","de-DE":"de","ja-JP":"ja","da-DK":"da","sv-SE":"sv","zh-CN":"zh","zh-TW":"zh","ko-KR":"ko","nb-NO":"nb","nl-NL":"nl","fi-FI":"fi",pseudo:"en"};function y(e){return v[e]}function w(e){return(0,o().Xk)(s,e)}function k(e){return(0,o().Xk)(i,e)}function S(e){return(0,o().Xk)(h,e)}function A(e){return k(e)||w(e)||function(e){return(0,o().Xk)(d,e)}(e)}const C="en-US";function I(e){var t;if(!e)return C;let n=e;const a=e.toLowerCase();return function(e){return(0,o().Xk)(c,e)}(a)&&(n=T[a]),null===(t=n)||void 0===t||null===(t=t.replace(/(\-[a-z])\w+/g,(e=>e.toUpperCase())))||void 0===t?void 0:t.replace(/([A-Z]*[A-Z]\-)+/gm,(e=>e.toLocaleLowerCase()))}u.map((e=>b[e]??e)).filter((function(e){return!(e in b)}));function P(e){const t=I(e);return[...i,...d,...s].includes(t)?b[t]||t:C}const T={"en-US":"en-US",ko:"ko-KR",ja:"ja-JP",fr:"fr-FR",de:"de-DE","es-ES":"es-ES",es:"es-LA",pt:"pt-BR","zh-CN":"zh-CN","zh-TW":"zh-TW"};function E(e){return(0,o().Xk)(r,e)}function R(e){if(e)return E(e)?e:void 0}},209881:(e,t,n)=>{n.r(t)},219187:(e,t,n)=>{n.r(t),n.d(t,{electronApi:()=>a().electronApi,electronCalendarApi:()=>r,electronMailApi:()=>i,getAndCacheDesktopVersionAsync:()=>l,getDesktopVersion:()=>c});var o=()=>n(711059),a=()=>n(675742);const r=window.__electronCalendarApi,i=window.__electronMailApi;let s;function c(){if(s)return(0,o().parseVersion)(s)}async function l(){return!s&&a().electronApi&&(s=await a().electronApi.getAppVersion()),c()}},227440:(e,t,n)=>{n.d(t,{A:()=>r,e:()=>i});class o extends(()=>n(757695))().Store{getInitialState(){return{online:!0,syncing:!1,lastOfflineTimestamp:void 0,mobileConnectivityType:void 0}}}const a=new o,r=a;(0,n(604341).exposeDebugValue)("ConnectionStore",a);const i=new(n(496506).ComputedStore)((()=>a.state.online),{debugName:"connectionStoreIsOnlineStore"})},236005:(e,t,n)=>{n.d(t,{H2:()=>m,Kr:()=>l,Mq:()=>f});n(898992),n(737550);var o=()=>n(427704);const a=["de-de","es-la","fr-fr","ja-jp","ko-kr","pt-br","zh-cn","zh-tw","pseudo","de","en-us","es","es-es","fr","ja","ko","pt"];function r(e){const{path:t,route:n}=e;if(t.toLowerCase()===n.toLowerCase())return!0;const o=n.endsWith("/")?n:`${n}/`;return!!t.startsWith(o)}function i(e){const{path:t,routes:n,locales:o}=e;for(const a of n){if(r({path:t,route:a}))return{route:a};for(const e of o){if(r({path:t,route:`/${e}${a}`}))return{route:a,locale:e}}}}const s=[o().JZ.downloadWindows,o().JZ.downloadWindowsArm,o().JZ.downloadMacAppleSilicon,o().JZ.downloadMacIntel,o().JZ.downloadMacUniversal,o().JZ.downloadMac,o().JZ.downloadCalendarMacAppleSilicon,o().JZ.downloadCalendarMacIntel,o().JZ.downloadCalendarMacUniversal,o().JZ.downloadCalendarMac,o().JZ.downloadCalendarWindows,o().JZ.downloadMailMac],c=[/^\/desktop\/Notion.*\.(dmg|exe)$/],l="fredir";Object.assign(o().Bf,n(209199).nG);const d=Object.values(o().zK),u=Object.values(o().eR),p=Object.values(o().Bf);function m(e,t){var n;return null===(n=f(e,t))||void 0===n?void 0:n[0]}function f(e,t){if(void 0!==e&&!i({path:e,routes:s,locales:[]})&&!c.some((t=>Boolean(e.match(t)))))return i({path:e,routes:d,locales:[]})?["internal",e]:i({path:e,routes:p,locales:a})?["devOnly",e]:i({path:e,routes:u,locales:a})?["normal",e]:"/"!==e||t?a.some((t=>e.toLowerCase()===`/${t.toLowerCase()}`))?["root",e]:e.startsWith(o().eR.templateCreator)?["normal","/@:templateCreator"]:void 0:["root",e]}},239391:(e,t,n)=>{n.d(t,{J:()=>a,q:()=>r});var o=()=>n(645873);const a=new(o().O2)("BlockPropertyRouter",(()=>Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(34359),n.e(65487),n.e(22970),n.e(14310),n.e(93552),n.e(47934),n.e(35266),n.e(28464),n.e(22542),n.e(95803),n.e(36264),n.e(46990)]).then(n.bind(n,536264)))),r=(0,o()._h)(a,(e=>e.BlockPropertyRouter))},249229:(e,t,n)=>{async function o(e){let{environment:t,currentUserId:o}=e;const{performPrefetchRequests:a}=await Promise.resolve().then(n.bind(n,669484));return a({environment:t,currentUserId:o})}n.d(t,{prefetchRequests:()=>o})},249386:(e,t,n)=>{n.d(t,{BM:()=>o,HM:()=>a});const o="n",a="null"},258037:(e,t,n)=>{n.d(t,{Kl:()=>r,bC:()=>i,wJ:()=>a});n(581454);const o={"3m":{type:"trial",campaign:"admin_3m",duration:{days:90},subscriptionTier:"plus"},"6m":{type:"trial",campaign:"admin_6m",duration:{days:180},subscriptionTier:"plus"},"12m":{type:"trial",campaign:"admin_12m",duration:{days:365},subscriptionTier:"plus"},"3m - SMB":{type:"trial",campaign:"admin_3m_smb",duration:{days:90},subscriptionTier:"plus"},"6m - SMB":{type:"trial",campaign:"admin_6m_smb",duration:{days:180},subscriptionTier:"plus"},"12m - Lenny":{type:"trial",campaign:"admin_12m_lenny",duration:{days:365},subscriptionTier:"plus"},"12m - Lenny Business":{type:"trial",campaign:"admin_12m_lenny_business",duration:{days:365},subscriptionTier:"business"},"1m - Business":{type:"trial",campaign:"admin_1m_startups_business",duration:{days:30},subscriptionTier:"business"},"3m - Business":{type:"trial",campaign:"admin_3m_startups_business",duration:{days:90},subscriptionTier:"business"},"6m - Business":{type:"trial",campaign:"admin_6m_startups_business",duration:{days:180},subscriptionTier:"business"},"12m - Business":{type:"trial",campaign:"admin_12m_startups_business",duration:{days:365},subscriptionTier:"business"},creator_6m:{type:"trial",campaign:"creator_6m",duration:{days:180},subscriptionTier:"plus"},creator_12m:{type:"trial",campaign:"creator_12m",duration:{days:365},subscriptionTier:"plus"},creator_12m_no_ai:{type:"trial",campaign:"creator_12m_no_ai",duration:{days:365},subscriptionTier:"plus"},admin_6m_business:{type:"trial",campaign:"admin_6m_business",duration:{days:180},subscriptionTier:"business"},plus_bundle_1m:{type:"trial",campaign:"plus_bundle_1m",duration:{days:30},subscriptionTier:"plus"},business_1m:{type:"trial",campaign:"business_1m",duration:{days:30},subscriptionTier:"business"}},a=(0,n(534177).uv)(o),r=Object.values(o).map((e=>{let{campaign:t}=e;return t})),i=[{type:"coupon",campaign:"team_three_months",percentOff:50,duration:{months:3}},{type:"coupon",campaign:"team_annual",percentOff:10,duration:{months:12}},{type:"coupon",campaign:"business_three_months",percentOff:10,duration:{months:3}},{type:"coupon",campaign:"business_annual",percentOff:10,duration:{months:12}},{type:"coupon",campaign:"enterprise_three_months",percentOff:10,duration:{months:3}},{type:"coupon",campaign:"enterprise_annual",percentOff:10,duration:{months:12}},{type:"coupon",campaign:"business_three_months",percentOff:10,duration:{months:3}},{type:"coupon",campaign:"ai_fifty_percent_upgrade",percentOff:50,duration:{months:12}},{type:"coupon",campaign:"ai_fifty_percent_downgrade",percentOff:50,duration:{months:3}},{type:"coupon",campaign:"resurrection_offer",percentOff:50,duration:{months:3}},{type:"coupon",campaign:"lic_25",percentOff:25,duration:{months:12}},{type:"coupon",campaign:"lic_50",percentOff:50,duration:{months:12}},{type:"coupon",campaign:"new_year_2025",percentOff:50,duration:{months:3}},{type:"coupon",campaign:"biz_upgrade_2025_8",percentOff:8,duration:{months:12}},{type:"coupon",campaign:"biz_upgrade_2025_10",percentOff:10,duration:{months:12}},{type:"trial",campaign:"default",duration:{days:14},subscriptionTier:"plus"},{type:"trial",campaign:"enterprise",duration:{days:30},subscriptionTier:"plus"},{type:"trial",campaign:"mm_ent",duration:{days:30},subscriptionTier:"plus"},{type:"trial",campaign:"upwork",duration:{days:30},subscriptionTier:"plus"},{type:"trial",campaign:"perfmark",duration:{days:30},subscriptionTier:"plus"},{type:"trial",campaign:"reverse",duration:{days:14},subscriptionTier:"plus"},{type:"trial",campaign:"reverse_mm_ent",duration:{days:30},subscriptionTier:"plus"},{type:"trial",campaign:"business_reverse",duration:{days:30},subscriptionTier:"business"},{type:"trial",campaign:"business_cc",duration:{days:30},subscriptionTier:"business"},{type:"trial",campaign:"stacked_business_trial",duration:{days:30},subscriptionTier:"business"},...Object.values(o)]},258595:(e,t,n)=>{n.d(t,{Ay:()=>a,Ko:()=>r,WS:()=>u});n(16280),n(944114);var o=()=>n(56222);function a(e){o().A.sdk=function(e){return{onInjectSDK:e.onInjectSDK,isFullSDK:!1,addBreadcrumb:function(){s({f:"addBreadcrumb",a:arguments})},captureMessage:function(){return s({f:"captureMessage",a:arguments}),""},captureException:function(){return s({f:"captureException",a:arguments}),""},captureEvent:function(){return s({f:"captureEvent",a:arguments}),""},configureScope:function(){s({f:"configureScope",a:arguments})},withScope:function(){s({f:"withScope",a:arguments})},showReportDialog:function(){s({f:"showReportDialog",a:arguments})}}}(e),window.addEventListener("error",c),window.addEventListener("unhandledrejection",l)}function r(){window.removeEventListener("error",c),window.removeEventListener("unhandledrejection",l)}const i=[];function s(e){("e"in e||"p"in e||e.f&&e.f.indexOf("capture")>-1||e.f&&e.f.indexOf("showReportDialog")>-1)&&async function(){if(d)return;d=!0;const e=o().A.sdk;if(e.isFullSDK)throw new Error("Failed to report error to Sentry");await e.onInjectSDK()}(),i.push(e)}function c(){s({e:[].slice.call(arguments)})}function l(e){s({p:"reason"in e?e.reason:"detail"in e&&"reason"in e.detail?e.detail.reason:e})}let d=!1;function u(e){try{for(const o of i)if("f"in o){const{f:t,a:n}=o;e[t].apply(e,n)}const t=window.onerror,n=window.onunhandledrejection;for(const e of i)"e"in e&&t?t.apply(window,e.e):"p"in e&&n&&n.apply(window,[e.p])}catch(t){console.error(t)}}},261642:(e,t,n)=>{n.r(t)},277942:(e,t,n)=>{n.d(t,{J1:()=>d,M4:()=>m,NV:()=>f,_:()=>u,ev:()=>g,rm:()=>p,s7:()=>s});var o=()=>n(645873);const a=new(o().O2)("FullPageAI",(()=>Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(96346),n.e(58550),n.e(16471),n.e(62475),n.e(3151),n.e(44711),n.e(98629),n.e(53321),n.e(90825),n.e(55373),n.e(37353),n.e(29151),n.e(78191),n.e(77848),n.e(55571),n.e(92845),n.e(71718),n.e(5605),n.e(48486),n.e(95794),n.e(13677),n.e(56952),n.e(74562),n.e(48632),n.e(27711),n.e(59794),n.e(4422),n.e(11341),n.e(34359),n.e(28721),n.e(28271),n.e(46414),n.e(90625),n.e(17254),n.e(15566),n.e(65487),n.e(34877),n.e(46418),n.e(78708),n.e(76959),n.e(68452),n.e(24587),n.e(81768),n.e(2233),n.e(26483),n.e(31276),n.e(9375),n.e(71104),n.e(67435),n.e(98659),n.e(82116),n.e(2517),n.e(69849),n.e(29459),n.e(9562),n.e(51127),n.e(25065),n.e(90978),n.e(93282),n.e(22970),n.e(90832),n.e(14310),n.e(93552),n.e(95699)]).then(n.bind(n,130145)))),r=new(o().O2)("AIModePicker",(()=>Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(96346),n.e(58550),n.e(16471),n.e(62475),n.e(3151),n.e(44711),n.e(98629),n.e(53321),n.e(90825),n.e(55373),n.e(37353),n.e(29151),n.e(78191),n.e(77848),n.e(55571),n.e(92845),n.e(71718),n.e(5605),n.e(48486),n.e(95794),n.e(13677),n.e(56952),n.e(74562),n.e(48632),n.e(27711),n.e(59794),n.e(4422),n.e(11341),n.e(34359),n.e(28721),n.e(28271),n.e(46414),n.e(90625),n.e(17254),n.e(15566),n.e(65487),n.e(34877),n.e(46418),n.e(78708),n.e(76959),n.e(68452),n.e(24587),n.e(81768),n.e(2233),n.e(26483),n.e(31276),n.e(9375),n.e(71104),n.e(67435),n.e(98659),n.e(82116),n.e(2517),n.e(69849),n.e(29459),n.e(9562),n.e(51127),n.e(25065),n.e(90978),n.e(93282),n.e(22970),n.e(90832),n.e(14310),n.e(93552),n.e(82926)]).then(n.bind(n,546716)))),i=new(o().O2)("AgentModelPicker",(()=>Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(8243)]).then(n.bind(n,959223)))),s=new(o().O2)("AIChatView",(()=>Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(96346),n.e(58550),n.e(16471),n.e(62475),n.e(3151),n.e(44711),n.e(98629),n.e(53321),n.e(90825),n.e(55373),n.e(37353),n.e(29151),n.e(78191),n.e(77848),n.e(55571),n.e(92845),n.e(71718),n.e(5605),n.e(48486),n.e(95794),n.e(13677),n.e(56952),n.e(74562),n.e(48632),n.e(27711),n.e(59794),n.e(4422),n.e(11341),n.e(34359),n.e(28721),n.e(28271),n.e(46414),n.e(90625),n.e(17254),n.e(15566),n.e(65487),n.e(34877),n.e(46418),n.e(78708),n.e(76959),n.e(68452),n.e(24587),n.e(81768),n.e(2233),n.e(26483),n.e(31276),n.e(9375),n.e(71104),n.e(67435),n.e(98659),n.e(82116),n.e(2517),n.e(69849),n.e(29459),n.e(9562),n.e(51127),n.e(25065),n.e(90978),n.e(93282),n.e(22970),n.e(90832),n.e(14310),n.e(93552),n.e(81374),n.e(88119),n.e(17598),n.e(47108)]).then(n.bind(n,552700)))),c=new(o().O2)("AgentChatView",(()=>Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(96346),n.e(58550),n.e(16471),n.e(62475),n.e(3151),n.e(44711),n.e(98629),n.e(53321),n.e(90825),n.e(55373),n.e(37353),n.e(29151),n.e(78191),n.e(77848),n.e(55571),n.e(92845),n.e(71718),n.e(5605),n.e(48486),n.e(95794),n.e(13677),n.e(56952),n.e(74562),n.e(48632),n.e(27711),n.e(59794),n.e(4422),n.e(11341),n.e(34359),n.e(28721),n.e(28271),n.e(46414),n.e(90625),n.e(17254),n.e(15566),n.e(65487),n.e(34877),n.e(46418),n.e(78708),n.e(76959),n.e(68452),n.e(24587),n.e(81768),n.e(2233),n.e(26483),n.e(31276),n.e(9375),n.e(71104),n.e(67435),n.e(98659),n.e(82116),n.e(2517),n.e(69849),n.e(29459),n.e(9562),n.e(51127),n.e(25065),n.e(90978),n.e(93282),n.e(22970),n.e(90832),n.e(14310),n.e(93552),n.e(46283)]).then(n.bind(n,738100)))),l=new(o().O2)("AgentChatInput",(()=>Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(96346),n.e(58550),n.e(16471),n.e(62475),n.e(3151),n.e(44711),n.e(98629),n.e(53321),n.e(90825),n.e(55373),n.e(37353),n.e(29151),n.e(78191),n.e(77848),n.e(55571),n.e(92845),n.e(71718),n.e(5605),n.e(48486),n.e(95794),n.e(13677),n.e(56952),n.e(74562),n.e(48632),n.e(27711),n.e(59794),n.e(4422),n.e(11341),n.e(34359),n.e(28721),n.e(28271),n.e(46414),n.e(90625),n.e(17254),n.e(15566),n.e(65487),n.e(34877),n.e(46418),n.e(78708),n.e(76959),n.e(68452),n.e(24587),n.e(81768),n.e(2233),n.e(26483),n.e(31276),n.e(9375),n.e(71104),n.e(67435),n.e(98659),n.e(82116),n.e(2517),n.e(69849),n.e(29459),n.e(9562),n.e(51127),n.e(25065),n.e(90978),n.e(93282),n.e(22970),n.e(90832),n.e(14310),n.e(93552),n.e(88119),n.e(17598),n.e(90068)]).then(n.bind(n,428842)))),d=(0,o()._h)(a,(e=>e.FullPageAI)),u=(0,o()._h)(r,(e=>e.AIModePicker)),p=(0,o()._h)(i,(e=>e.AgentModelPicker)),m=(0,o()._h)(s,(e=>e.AIChatView)),f=(0,o()._h)(c,(e=>e.AgentChatView)),g=(0,o()._h)(l,(e=>e.AgentChatInput))},283699:(e,t,n)=>{n.d(t,{Eo:()=>p,Hn:()=>u,R2:()=>h,T0:()=>d,TS:()=>b,bD:()=>v,hl:()=>m,nQ:()=>f,og:()=>l,q1:()=>_,v:()=>g});n(944114),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(898992),n(354520),n(803949),n(581454);var o=()=>n(534177),a=()=>n(839816);const r=!0,i=864e5,s=365*i,c={notion_cookie_consent:90*i,growSumoPartnerKey:90*i,notion_s2s_tracking_params:180*i,notion_check_cookie_consent:Number(i),csrf:Number(i),notion_calendar_csrf:Number(i),notion_cookie_sync_completed:90*i,notion_test_cookie_sync_completed:7*i,notion_sync_user_id:90*i};function l(){return a().Rv.filter((e=>!c[e]))}function d(e,t){const n=(0,o().O)(c,e)?c[e]:void 0,a=t??(n||s);return Date.now()+a}async function u(e){const{name:t,cookieService:n}=e;return p({trackingType:a().X6[t],cookieService:n})}async function p(e){const{trackingType:t,cookieService:n}=e;if("necessary"===t)return!0;if(n.isMobileNative())return!1;return function(e){const{trackingType:t,...n}=e;if("necessary"===t)return!0;if("testOnly"===t)return!0;if(n.isMobileNative)return!1;const{hasError:o}=g(n);if(o)return r;const{notion_cookie_consent:a,notion_check_cookie_consent:i}=n;return a?function(e){let{notion_cookie_consent:t,trackingType:n}=e;const o=function(e){try{return JSON.parse(e)}catch(t){return}}(t);if(!o)return r;if(!o.permission)return r;const a=o.permission[n];if("boolean"!=typeof a)return r;return a}({notion_cookie_consent:a,trackingType:t}):"true"!==i}({trackingType:t,...await m({cookieService:n})})}async function m(e){const{cookieService:t}=e,{getCookieWithoutPermissionCheck:n}=t,o=t.isMobileNative();if(o)return{isMobileNative:o,notion_check_cookie_consent:void 0,notion_cookie_consent:void 0};return{isMobileNative:o,notion_check_cookie_consent:await n("notion_check_cookie_consent"),notion_cookie_consent:await n("notion_cookie_consent")}}async function f(e){const{service:t,cookieService:n}=e;return p({trackingType:a().fr[t],cookieService:n})}function g(e){let{notion_check_cookie_consent:t,notion_cookie_consent:n}=e;if(!t)return{hasError:!0,errorType:"emptyCheckCookieConsent"};if("true"!==t&&"false"!==t)return{hasError:!0,errorType:"parseCheckCookieConsentIsNotBoolean"};if(n)try{const e=JSON.parse(n),{permission:t}=e;if(!t)return{hasError:!0,errorType:"emptyPermissionInCookieConsent"};if("boolean"!=typeof t.necessary||"boolean"!=typeof t.performance||"boolean"!=typeof t.preference||"boolean"!=typeof t.targeting)return{hasError:!0,errorType:"malformedPermissionInCookieContent"}}catch(o){return{hasError:!0,errorType:"parseCookieConsentError"}}return{hasError:!1}}function b(e){const{preference:t,performance:n,targeting:o}=e;return t&&n&&o?"accept_all":t||n||o?"partial":"deny_all"}function h(e){let{publicDomainName:t,env:n}=e;const o=window.location.host.split(":")[0];return"local"===n?o:`.${o}`}async function _(e){const{cookieService:t}=e,{removeCookie:n}=t,o=function(){const e={necessary:[],preference:[],performance:[],targeting:[]};for(const t in a().X6){const n=a().X6[t];"testOnly"!==n&&e[n].push(t)}return e}();for(const a in o){if(!(await p({trackingType:a,cookieService:t}))){const e=o[a];await Promise.all(e.map((e=>n(e))))}}}async function v(e){const{cookieService:t}=e,{removeCookie:n}=t;for(const o of a().dc){if(await p({trackingType:o,cookieService:t}))continue;const e=k(o);for(const t of e){const e=y(t);await Promise.all(e.map((e=>n(e))));w(t).forEach((e=>localStorage.removeItem(e)))}}}function y(e){return document.cookie.split(";").map((e=>e.trim())).filter((t=>t.startsWith(e))).map((e=>e.split("=")[0]))}function w(e){return Object.keys(localStorage).filter((t=>t.startsWith(e)))}function k(e){const t=[];for(const n in a().fr)a().fr[n]===e&&t.push(...a().II[n]);return t}new Set(["BE","GR","LT","PT","BG","ES","LU","RO","CZ","FR","HU","SI","DK","HR","MT","SK","DE","IT","NL","FI","EE","CY","AT","SE","IE","LV","PL","IS","NO","LI","CH","GB"])},292588:(e,t,n)=>{n.d(t,{A:()=>o});const o={UUID:"UUID",String:"String",Number:"Number",Boolean:"Boolean",StringArray:"StringArray",JSON:"JSON",XML:"XML",UUIDArray:"UUIDArray",CIDR:"CIDR",NumberArray:"NumberArray",Blob:"Blob"}},302777:(e,t,n)=>{n.d(t,{JZ:()=>o,OU:()=>r});n(898992),n(672577);Symbol("ID path");function o(e){return"object"==typeof e}function a(e,t){if(Array.isArray(e))return e.find((e=>(null==e?void 0:e.id)===t.id))}function r(e,t){let n=e;for(let i=0;i<t.length;i++){const e=t[i];if(null==n)return;var r;if(o(e))n=a(n,e);else n=null===(r=n)||void 0===r?void 0:r[e]}return n}},313127:(e,t,n)=>{n.d(t,{BQ:()=>T,De:()=>r,JE:()=>O,Tj:()=>q,_Z:()=>i,ay:()=>R,dH:()=>a,h0:()=>M,j9:()=>N,t_:()=>D});n(944114),n(898992),n(823215),n(354520),n(581454);var o=()=>n(857639);const a=["onboarding","inapp_modal"];function r(e){const{maybeFunction:t,guessedFunctionVersion:n,maybeRole:a,guessedRoleVersion:r,maybeCategories:i,guessedCategoriesVersion:p,maybeEducationRole:m,guessedEducationRoleVersion:g,maybeEducationLevel:v,guessedEducationLevelVersion:y,maybeNotionUseFrequency:w,guessedNotionUseFrequencyVersion:k,collectedFrom:I,fromMigration:P,logData:T}=e,E=e.collectedAt||(new Date).getTime(),R=void 0!==t&&function(e){if(!(4!==e.guessedFunctionVersion&&4.1!==e.guessedFunctionVersion||"unfilled"!==e.maybeFunction&&"personal"!==e.maybeFunction))return;if(4.1===e.guessedFunctionVersion&&"student"===e.maybeFunction)return;const t=(e=>{const{maybeFunction:t,collectedAt:n,collectedFrom:o,guessedFunctionVersion:a}=e,r={4:{founderCeo:"founder_or_ceo",internalCommunication:"internal_communication",itAdmin:"it_admin",knowledgeManagement:"knowledge_management",productDesign:"product_design",projectProgramMgmt:"project_or_program_management"},4.1:{internalCommunication:"internal_communication",itAdmin:"it_admin",knowledgeManagement:"knowledge_management",productDesign:"product_design",projectProgramMgmt:"project_or_program_management"}},i=a?[a]:[],s=Object.keys(b).filter((e=>b[e].created_at<=n)).reverse().map((e=>parseFloat(e)));i.push(...s);for(const c of i){if("string"==typeof t&&b[c].options.includes(t))return{value:t,version:c,collected_at:n,collected_from:o};if(r[c]&&void 0!==r[c][t])return{value:r[c][t],version:c,collected_at:n,collected_from:o}}})(e);if(t)return void 0!==e.guessedFunctionVersion&&e.guessedFunctionVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"function",unexpected_version:e.guessedFunctionVersion,field_version:t.version,field_value:t.value,from_migration:Boolean(e.fromMigration),...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"function",unexpected_value:e.maybeFunction,guessed_version:e.guessedFunctionVersion,latest_version:s,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeFunction:t,collectedFrom:I,collectedAt:E,guessedFunctionVersion:n,fromMigration:P,logData:T}),D=void 0!==a&&function(e){const t=(e=>{const{maybeRole:t,collectedAt:n,collectedFrom:o,guessedRoleVersion:a}=e,r={3:{founder_ceo:"founder_or_ceo"}},i=a?[a]:[],s=Object.keys(h).filter((e=>h[e].created_at<=n)).reverse().map((e=>parseFloat(e)));i.push(...s);for(const c of i){if("string"==typeof t&&h[c].options.includes(t))return{value:t,version:c,collected_at:n,collected_from:o};if(r[c]&&void 0!==r[c][t])return{value:r[c][t],version:c,collected_at:n,collected_from:o}}})(e);if(t)return void 0!==e.guessedRoleVersion&&e.guessedRoleVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"role",unexpected_version:e.guessedRoleVersion,field_version:t.version,field_value:t.value,from_migration:Boolean(e.fromMigration),...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"role",unexpected_value:e.maybeRole,guessed_version:e.guessedRoleVersion,latest_version:c,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeRole:a,collectedFrom:I,collectedAt:E,guessedRoleVersion:r,fromMigration:P,logData:T}),q=Array.isArray(i)&&i.length>0&&function(e){const t=(e=>{const{maybeCategories:t,collectedAt:n,collectedFrom:o,guessedCategoriesVersion:a}=e,r=a?[a]:[];for(const i of r)if(t.every((e=>_[i].options.includes(e))))return{value:t,version:i,collected_at:n,collected_from:o}})(e);if(t)return void 0!==e.guessedCategoriesVersion&&e.guessedCategoriesVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"use_cases",unexpected_version:e.guessedCategoriesVersion,field_version:t.version,field_value:t.value,from_migration:Boolean(e.fromMigration),...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"use_cases",unexpected_value:e.maybeCategories,guessed_version:e.guessedCategoriesVersion,latest_version:f,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeCategories:i,collectedFrom:I,collectedAt:E,guessedCategoriesVersion:p,fromMigration:P,logData:T}),M=void 0!==m&&function(e){const t=(e=>{const{maybeEducationRole:t,collectedAt:n,collectedFrom:o,guessedEducationRoleVersion:a}=e,r=a?[a]:[],i=Object.keys(S).filter((e=>S[e].created_at<=n)).reverse().map((e=>parseFloat(e)));r.push(...i);for(const s of r)if("string"==typeof t&&S[s].options.includes(t))return{value:t,version:s,collected_at:n,collected_from:o}})(e);if(t)return void 0!==e.guessedEducationRoleVersion&&e.guessedEducationRoleVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"education_role",unexpected_version:e.guessedEducationRoleVersion,field_version:t.version,field_value:t.value,from_migration:Boolean(e.fromMigration),...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"education_role",unexpected_value:e.maybeEducationRole,guessed_version:e.guessedEducationRoleVersion,latest_version:l,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeEducationRole:m,collectedFrom:I,collectedAt:E,guessedEducationRoleVersion:g,fromMigration:P,logData:T}),O=void 0!==v&&function(e){const t=(e=>{const{maybeEducationLevel:t,collectedAt:n,collectedFrom:o,guessedEducationLevelVersion:a}=e,r=a?[a]:[],i=Object.keys(A).filter((e=>A[e].created_at<=n)).reverse().map((e=>parseFloat(e)));r.push(...i);for(const s of r)if("string"==typeof t&&A[s].options.includes(t))return{value:t,version:s,collected_at:n,collected_from:o}})(e);if(t)return void 0!==e.guessedEducationLevelVersion&&e.guessedEducationLevelVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"education_level",unexpected_version:e.guessedEducationLevelVersion,field_version:t.version,field_value:t.value,from_migration:Boolean(e.fromMigration),...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"education_level",unexpected_value:e.maybeEducationLevel,guessed_version:e.guessedEducationLevelVersion,latest_version:d,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeEducationLevel:v,collectedFrom:I,collectedAt:E,guessedEducationLevelVersion:y,fromMigration:P,logData:T}),N=void 0!==w&&function(e){const t=(e=>{const{maybeNotionUseFrequency:t,collectedAt:n,collectedFrom:o,guessedNotionUseFrequencyVersion:a}=e,r=a?[a]:[],i=Object.keys(C).filter((e=>C[e].created_at<=n)).reverse().map((e=>parseFloat(e)));r.push(...i);for(const s of r)if("string"==typeof t&&C[s].options.includes(t))return{value:t,version:s,collected_at:n,collected_from:o}})(e);if(t)return void 0!==e.guessedNotionUseFrequencyVersion&&e.guessedNotionUseFrequencyVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"notion_use_frequency",unexpected_version:e.guessedNotionUseFrequencyVersion,field_version:t.version,field_value:t.value,from_migration:Boolean(e.fromMigration),...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateUserSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"notion_use_frequency",unexpected_value:e.maybeNotionUseFrequency,guessed_version:e.guessedNotionUseFrequencyVersion,latest_version:u,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeNotionUseFrequency:w,collectedFrom:I,collectedAt:E,guessedNotionUseFrequencyVersion:k,fromMigration:P,logData:T}),B={...R&&{function:R},...D&&{role:D},...q&&{categories:q},...M&&{education_role:M},...O&&{education_level:O},...N&&{notion_use_frequency:N}};if(Object.keys(B).length>0)return B}function i(e){const{maybeIntent:t,guessedIntentVersion:n,maybeCollaborativeIntent:a,guessedCollaborativeIntentVersion:r,maybeUseCases:i,guessedUseCasesVersion:s,maybeCompanySize:c,guessedCompanySizeVersion:l,collectedFrom:d,fromMigration:u,logData:b}=e,h=e.collectedAt||(new Date).getTime(),_="string"==typeof t&&function(e){const t=(e=>{const{maybeIntent:t,collectedAt:n,collectedFrom:o,guessedIntentVersion:a}=e,r={2:{personal:"life",team:"work"}},i=a?[a]:[],s=Object.keys(v).filter((e=>v[e].created_at<=n)).reverse().map((e=>parseFloat(e)));i.push(...s);for(const c of i){if("string"==typeof t&&v[c].options.includes(t))return{value:t,version:c,collected_at:n,collected_from:o};if(2===e.guessedIntentVersion&&"string"==typeof t&&r[c]&&void 0!==r[c][t])return{value:r[c][t],version:c,collected_at:n,collected_from:o}}})(e);if(t)return void 0!==e.guessedIntentVersion&&e.guessedIntentVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"intent",unexpected_version:e.guessedIntentVersion,field_version:t.version,field_value:t.value,fromMigration:e.fromMigration||!1,...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"intent",unexpected_value:e.maybeIntent,guessed_version:e.guessedIntentVersion,latest_version:p,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeIntent:t,collectedFrom:d,collectedAt:h,guessedIntentVersion:n,fromMigration:u}),S="string"==typeof a&&function(e){const t=(e=>{const{maybeCollaborativeIntent:t,collectedAt:n,collectedFrom:o,guessedCollaborativeIntentVersion:a}=e,r=a?[a]:[],i=Object.keys(y).filter((e=>y[e].created_at<=n)).reverse().map((e=>parseFloat(e)));r.push(...i);for(const s of r)if("string"==typeof t&&y[s].options.includes(t))return{value:t,version:s,collected_at:n,collected_from:o}})(e);if(t)return void 0!==e.guessedCollaborativeIntentVersion&&e.guessedCollaborativeIntentVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"collaborative_intent",unexpected_version:e.guessedCollaborativeIntentVersion,field_version:t.version,field_value:t.value,fromMigration:e.fromMigration||!1,...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"collaborative_intent",unexpected_value:e.maybeCollaborativeIntent,guessed_version:e.guessedCollaborativeIntentVersion,latest_version:m,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeCollaborativeIntent:a,collectedFrom:d,collectedAt:h,guessedCollaborativeIntentVersion:r,fromMigration:u}),A=Array.isArray(i)&&i.length>0&&function(e){const t=(e=>{const{maybeUseCases:t,collectedAt:n,collectedFrom:o,guessedUseCasesVersion:a}=e,r=a?[a]:[],i=Object.keys(w).filter((e=>parseFloat(e)!==a)).reverse().map((e=>parseFloat(e)));r.push(...i);for(const s of r)if(t.every((e=>w[s].options.includes(e))))return{value:t,version:s,collected_at:n,collected_from:o}})(e);if(t)return void 0!==e.guessedUseCasesVersion&&e.guessedUseCasesVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"use_cases",unexpected_version:e.guessedUseCasesVersion,field_version:t.version,field_value:t.value,from_migration:Boolean(e.fromMigration),...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"use_cases",unexpected_value:e.maybeUseCases,guessed_version:e.guessedUseCasesVersion,latest_version:f,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeUseCases:i,collectedFrom:d,collectedAt:h,guessedUseCasesVersion:s,fromMigration:u,logData:b}),C="string"==typeof c&&function(e){const t=(e=>{const{maybeCompanySize:t,collectedAt:n,collectedFrom:o,guessedCompanySizeVersion:a}=e,r=a?[a]:[],i=Object.keys(k).filter((e=>k[e].created_at<=n)).reverse().map((e=>parseFloat(e)));r.push(...i);for(const s of r)if("string"==typeof t&&k[s].options.includes(t))return{value:t,version:s,collected_at:n,collected_from:o}})(e);if(t)return void 0!==e.guessedCompanySizeVersion&&e.guessedCompanySizeVersion!==t.version&&o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"company_size",unexpected_version:e.guessedCompanySizeVersion,field_version:t.version,field_value:t.value,from_migration:Boolean(e.fromMigration),...e.logData}}}),t;o().log({level:"warning",from:"surveyDataHelpers.attemptCreateSpaceSurveyData",type:"ValidationError",data:{miscDataToConvertToString:{field:"company_size",unexpected_value:e.maybeCompanySize,guessed_version:e.guessedCompanySizeVersion,latest_version:g,from_migration:Boolean(e.fromMigration),...e.logData}}})}({maybeCompanySize:c,collectedFrom:d,collectedAt:h,guessedCompanySizeVersion:l,fromMigration:u,logData:b}),I={..._&&{intent:_},...S&&{collaborative_intent:S},...A&&{use_cases:A},...C&&{company_size:C}};if(Object.keys(I).length>0)return I}const s=4.1,c=3,l=1,d=1,u=1,p=2,m=1,f=3,g=3,b={1:{options:["personal","programmer","product_manager","designer","marketing","sales","customer_support","hr_recruiting","student","unfilled","entrepreneur","marketing_sales","operations_hr","media","freelancer","it","educator","other"],created_at:0},3:{options:["eng","design","product","marketing","sales","finance","support","hr","it","operations","student","educator","other"],created_at:164747328e4},4:{options:["creative","educator","eng","finance","founder_or_ceo","hr","internal_communication","it_admin","knowledge_management","marketing","operations","product","product_design","project_or_program_management","sales","student","support","other"],created_at:16432596e5},4.1:{options:["creative","educator","eng","finance","hr","internal_communication","it_admin","knowledge_management","marketing","operations","product","product_design","project_or_program_management","sales","support","other"],created_at:167761092e4},4.2:{options:["creative","internal_communication","eng","product_design","project_or_program_management","product","marketing","sales_or_success","support","finance","hr","it_admin","knowledge_management","operations","educator","other","student"],created_at:1682636494e3}},h={1:{options:["team_lead","org_lead","company_lead","not_lead","unfilled","personal","exec","team_manager","member"],created_at:0},2:{options:["exec","dept_lead","team_manager","member","solo","personal","other"],created_at:164747328e4},3:{options:["founder_or_ceo","exec","dept_lead","team_manager","member","solo","business_owner","other"],created_at:167761092e4},4:{options:["founder_or_ceo","exec","director","manager","individual_contributor","business_owner","freelancer","other"],created_at:1682636494e3}},_={1:{options:["planner","notes","research","clubs","teachingLessonPlans","teachingClassroomManagement","websiteBuildingForSchool","internshipApplications","groupProjects","todos","projectsTasks","habitWellness","budgetPersonalFinance","travel","hobbies","careerBuilding","foodNutrition","websiteBuildingForLife","entertainment","budget","habitTracker","journal","jobApplications","lessonPlans","classroomManagement","projectManagement","websiteBuilding"],created_at:1710198791e3}},v={1:{options:["personal","team","school"],created_at:0},2:{options:["life","work","school"],created_at:167761092e4}},y={1:{options:["singleplayer","multiplayer"],created_at:1730494772e3}},w={1:{options:["personal_notes_to_dos","team_docs_knowledge_base","team_project_management"],created_at:0},2:{options:["docs","wikis","notes","project","goals","other"],created_at:164747328e4},3:{options:["docs","wikis","notes","project","goals","ai","other"],created_at:167702934e4}},k={1:{options:["1_100","101_1000","1001+"],created_at:0},2:{options:["1_49","50_99","100_299","300_999","1000_5000","5000+"],created_at:167113674e4},3:{options:["1_10","10_49","50_99","100_299","300_999","1000_5000","5000+"],created_at:1730494772e3}},S={1:{options:["student","faculty member","staff member"],created_at:17240256e5}},A={1:{options:["k-12","undergraduate","graduate"],created_at:17240256e5}},C={1:{options:["never","a few times"],created_at:1727806707}},I=[...b[4].options,"unfilled"],P=[...h[2].options.filter((e=>"other"!==e)),"unfilled"],T=w[3].options,E=k[3].options,R=(S[1].options,A[1].options,v[1].options);y[1].options;function D(e){if(P.includes(e))return e}function q(e){if(I.includes(e))return e}function M(e){return e.map((e=>{if(T.includes(e))return e})).filter((e=>void 0!==e))}function O(e){if(E.includes(e))return e}function N(e){if(R.includes(e))return e}},319625:(e,t,n)=>{n.d(t,{A:()=>o});n(16280);function o(e){return e instanceof Error?e:"object"==typeof e&&null!==e?Object.assign(new Error("Expected error, but caught non-error object"),e):"string"==typeof e?Object.assign(new Error(e),{cause:e}):Object.assign(new Error(`Expected error, but caught \`${String(e)}\` (${typeof e})`),{cause:e})}},328058:(e,t,n)=>{n.r(t)},332393:(e,t,n)=>{n.d(t,{Ay:()=>u,C7:()=>c,Li:()=>s,SF:()=>d,Vf:()=>i,YN:()=>a,kC:()=>r,rr:()=>l});n(898992),n(354520),n(737550);var o=()=>n(292588);const a={notion_user:!0,space:!0};function r(e){return"external"===e.getType()||"guest"===e.getType()}const i=["published","unpublished"],s="integration";function c(e,t){if(e){const{developer_space_id:n,extra_testing_space_ids:o}=t.getInfo();return n===e||(o??[]).some((t=>t===e))}return!1}function l(e){return e.info.original_url_patterns&&e.info.original_url_patterns.filter((e=>{var t;return null===(t=e.additional_types)||void 0===t?void 0:t.collection})).length>0}function d(e){return!!e&&"internal"===e.type}const u={table:s,columnTypes:{id:o().A.UUID,version:o().A.Number,last_version:o().A.Number,name:o().A.String,parent_table:o().A.String,parent_id:o().A.UUID,created_at:o().A.Number,created_by_id:o().A.UUID,created_by_table:o().A.String,updated_at:o().A.Number,updated_by_id:o().A.UUID,updated_by_table:o().A.String,redirect_uris:o().A.StringArray,status:o().A.String,info:o().A.JSON,alive:o().A.Boolean,capabilities:o().A.JSON,type:o().A.String,listing_status:o().A.String},model:(0,n(152853).P)({RecordStore:!0})}},336811:(e,t,n)=>{n.d(t,{O:()=>r,T:()=>a});var o=n(296540);function a(){const e=(0,o.useRef)(!1);return(0,o.useEffect)((()=>(e.current=!0,()=>{e.current=!1})),[]),e}function r(){const e=(0,o.useRef)(!1);return(0,o.useEffect)((()=>()=>{e.current=!0}),[]),e}},347320:(e,t,n)=>{n.d(t,{A:()=>r,O:()=>a});const o="collection-prefetch-cache:",a="inline-db-lookup";function r(e){return e?`${o}${e}`:"queryCollection"}},362749:(e,t,n)=>{n.d(t,{Cj:()=>b,HK:()=>c,LL:()=>h,Nd:()=>g,Pl:()=>v,Sd:()=>p,UL:()=>i,V2:()=>d,YZ:()=>m,ck:()=>l,fu:()=>y,hO:()=>r,jj:()=>u,lA:()=>a,m5:()=>s,op:()=>_,or:()=>f});var o=()=>n(645873);const a={sidebar:new(o().O2)("sidebar",(()=>Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(96346),n.e(58550),n.e(16471),n.e(62475),n.e(3151),n.e(44711),n.e(98629),n.e(53321),n.e(90825),n.e(55373),n.e(37353),n.e(29151),n.e(78191),n.e(77848),n.e(55571),n.e(22900),n.e(49889),n.e(97727),n.e(85128),n.e(92845),n.e(71718),n.e(5605),n.e(48486),n.e(95794),n.e(13677),n.e(56952),n.e(74562),n.e(48632),n.e(27711),n.e(59794),n.e(4422),n.e(11341),n.e(34359),n.e(28721),n.e(28271),n.e(46414),n.e(90625),n.e(17254),n.e(15566),n.e(65487),n.e(34877),n.e(46418),n.e(78708),n.e(76959),n.e(68452),n.e(24587),n.e(81768),n.e(2233),n.e(26483),n.e(31276),n.e(9375),n.e(71104),n.e(67435),n.e(98659),n.e(82116),n.e(2517),n.e(69849),n.e(29459),n.e(9562),n.e(51127),n.e(25065),n.e(90978),n.e(93282),n.e(22970),n.e(90832),n.e(14310),n.e(93552),n.e(27506),n.e(45620),n.e(87598),n.e(97812),n.e(39958),n.e(93231),n.e(84341),n.e(93052),n.e(91898),n.e(41651),n.e(65922),n.e(94256),n.e(76690),n.e(81954),n.e(44763),n.e(82720),n.e(4289),n.e(78862),n.e(67378),n.e(93534),n.e(94103),n.e(48362),n.e(59426),n.e(1440),n.e(35014),n.e(29875),n.e(33194),n.e(73664),n.e(91957),n.e(45816),n.e(33616),n.e(31061)]).then(n.bind(n,839986)))),SidebarComponent:new(o().O2)("SidebarComponent",(async()=>await Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(25065),n.e(14310),n.e(93552),n.e(48362),n.e(29875),n.e(33194),n.e(33616),n.e(72982)]).then(n.bind(n,225463)))),SidebarLibraryButton:new(o().O2)("SidebarLibraryButton",(async()=>await Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(40902)]).then(n.bind(n,624959)))),SidebarTrash:new(o().O2)("SidebarTrash",(async()=>await Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(56952),n.e(14310),n.e(93552),n.e(8953)]).then(n.bind(n,386367)))),TabletSidebarButton:new(o().O2)("TabletSidebarButton",(async()=>await Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(88873)]).then(n.bind(n,871466)))),AuthSyncListener:new(o().O2)("AuthSyncListener",(async()=>await Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(21088)]).then(n.bind(n,171510)))),PublicPageSidebarContent:new(o().O2)("PublicPageSidebarContent",(async()=>Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(29875),n.e(33194),n.e(33590)]).then(n.bind(n,722032)))),SidebarMobile:new(o().O2)("SidebarMobile",(async()=>await Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(96346),n.e(58550),n.e(90825),n.e(77848),n.e(55571),n.e(22900),n.e(85128),n.e(92845),n.e(71718),n.e(5605),n.e(48486),n.e(95794),n.e(13677),n.e(56952),n.e(4422),n.e(46418),n.e(68452),n.e(9375),n.e(25065),n.e(14310),n.e(93552),n.e(87598),n.e(39958),n.e(93052),n.e(91898),n.e(41651),n.e(94256),n.e(4289),n.e(48362),n.e(29875),n.e(33194),n.e(33325),n.e(87602),n.e(35115)]).then(n.bind(n,997556)))),LockedSidebarSection:new(o().O2)("LockedSidebarSection",(async()=>await Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(29875),n.e(94412)]).then(n.bind(n,518299)))),OutlinerToggleOpenSetupModalButton:new(o().O2)("OutlinerToggleOpenSetupModalButton",(async()=>await Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(96346),n.e(58550),n.e(16471),n.e(62475),n.e(3151),n.e(44711),n.e(98629),n.e(53321),n.e(90825),n.e(55373),n.e(37353),n.e(29151),n.e(78191),n.e(77848),n.e(55571),n.e(92845),n.e(71718),n.e(5605),n.e(48486),n.e(95794),n.e(13677),n.e(56952),n.e(74562),n.e(48632),n.e(27711),n.e(59794),n.e(4422),n.e(11341),n.e(34359),n.e(28721),n.e(28271),n.e(46414),n.e(90625),n.e(17254),n.e(15566),n.e(65487),n.e(34877),n.e(46418),n.e(78708),n.e(76959),n.e(68452),n.e(24587),n.e(81768),n.e(2233),n.e(26483),n.e(31276),n.e(9375),n.e(71104),n.e(67435),n.e(98659),n.e(82116),n.e(2517),n.e(69849),n.e(29459),n.e(9562),n.e(51127),n.e(25065),n.e(90978),n.e(93282),n.e(22970),n.e(90832),n.e(14310),n.e(93552),n.e(53977)]).then(n.bind(n,794946)))),SidebarInboxButton:new(o().O2)("SidebarInboxButton",(async()=>await Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(44711),n.e(53321),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(28307)]).then(n.bind(n,84987))))},r=(0,o()._h)(a.sidebar,(e=>e.SidebarTeamBrowserContent)),i=(0,o()._h)(a.sidebar,(e=>e.SidebarSwitcherMultiAccountPopup)),s=(0,o()._h)(a.sidebar,(e=>e.SidebarFooter)),c=(0,o()._h)(a.sidebar,(e=>e.SidebarCard)),l=(0,o()._h)(a.sidebar,(e=>e.SidebarSectionWithPopup)),d=(0,o()._h)(a.SidebarTrash,(e=>e.default)),u=(0,o()._h)(a.TabletSidebarButton,(e=>e.TabletSidebarButton)),p=(0,o()._h)(n(117296).KM.RecentsCachingListener,(e=>e.default)),m=(0,o()._h)(a.SidebarComponent,(e=>e.Sidebar)),f=(0,o()._h)(a.PublicPageSidebarContent,(e=>e.PublicPageSidebarContent)),g=(0,o()._h)(a.SidebarMobile,(e=>e.default)),b=(0,o()._h)(a.LockedSidebarSection,(e=>e.LockedSidebarSection)),h=(0,o()._h)(a.OutlinerToggleOpenSetupModalButton,(e=>e.OutlinerToggleOpenSetupModalButton)),_=(0,o()._h)(a.SidebarLibraryButton,(e=>e.SidebarLibraryButton));async function v(){return(await Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(96346),n.e(77848),n.e(55571),n.e(92845),n.e(71718),n.e(14310),n.e(93552),n.e(63137)]).then(n.bind(n,375215))).createPageInTeamSyncImpl}const y=(0,o()._h)(a.SidebarInboxButton,(e=>e.SidebarInboxButton))},365902:(e,t,n)=>{async function o(){const{default:e}=await Promise.resolve().then(n.bind(n,373407));e();(await Promise.resolve().then(n.bind(n,644425))).initializeEarlyLogging()}n.d(t,{loadErrorReporting:()=>o})},373407:(e,t,n)=>{n.d(t,{default:()=>s});var o=()=>n(258595),a=()=>n(780054),r=()=>n(445155),i=()=>n(165162);function s(){(0,o().Ay)({onInjectSDK:c})}async function c(){const{getProfilingToolForSession:e}=await Promise.resolve().then(n.bind(n,150749)),{Sentry:t,sentryInitializeFull:o}=await(0,r().q)(),s=(0,i().Pr)("sentry");o({Sentry:{...t,isFullSDK:!0},config:a().A,getErrorsSampleRate:()=>(null==s?void 0:s.get("errorsSampleRate",1))||1,getTracesSampleRate:()=>(null==s?void 0:s.get("tracesSampleRate-v0",0))||0,getReplaysSessionSampleRate:()=>(null==s?void 0:s.get("replaysSessionSampleRate-v0",0))||0,getReplaysOnErrorSampleRate:()=>(null==s?void 0:s.get("replaysOnErrorSampleRate-v0",0))||0,getIsProfilingEnabled:()=>"sentry"===e(),getProfilesSampleRate:()=>(null==s?void 0:s.get("profilesSampleRate",0))||0})}},386164:(e,t,n)=>{n.d(t,{E5:()=>d,NV:()=>c,WS:()=>l});n(581454);var o=()=>n(496603),a=()=>n(498212),r=()=>n(851941),i=()=>n(870723),s=()=>n(436604);function c(e,t){if(t&&(0,i().$t)(e.table))if("space"===e.table){if(e.id!==t)return}else{if(!e.spaceId)return{...e,spaceId:t};if(e.spaceId!==t)return}return e}function l(e){const{cellId:t,recordId:n,spaceId:o,verifyShortSpaceIdVersion:i}=e,c={};let l=n?(0,r().dR)(n):void 0;if(n&&l&&i){"notion-v0"===(0,a().Iq)(n)&&(l=void 0)}return t?c[s().tx]=t:l?c[s().eG]=l.toString():o&&(c[s().B3]=o),c}function d(e){const t=new Map,n=o().$z(e,(e=>o().Ul(Object.entries(e.headers),(e=>{let[t]=e;return t})).map((e=>{let[t,n]=e;return[t,n].join(":")})).join("|")));for(const o of Object.values(n)){const[e]=o;e&&t.set(e.headers,o.map((e=>{let{headers:t,...n}=e;return n})))}return t}},402390:(e,t,n)=>{n.d(t,{A_:()=>v,CR:()=>i,H:()=>u,Nk:()=>p,_z:()=>d,dk:()=>b,g5:()=>_,q:()=>r,wW:()=>l});n(898992),n(672577);var o=()=>n(959013),a=()=>n(209199);const r="en-US";function i(e){return function(e){return e.normalize("NFD").replace(/[\u0300-\u036f]/g,"")}(e).toLowerCase()}const s=(0,o().MT)();let c=(0,o().EY)({locale:"en-US",defaultLocale:"en-US"},s);function l(){return c}function d(){return c}function u(e){return(0,a().vL)(e.locale)?e.locale:r}function p(e){return(0,a().vR)(e.locale)?e.locale:r}const m={decimal:".",integerSeparator:","};let f=c,g=h(c);function b(e){return f!==e&&(f=e,g=h(e)),g}function h(e){var t,n;const o=e.formatNumberToParts(10000.1),a=null===(t=o.find((e=>"decimal"===e.type)))||void 0===t?void 0:t.value,r=null===(n=o.find((e=>"group"===e.type)))||void 0===n?void 0:n.value;return a&&r?{decimal:a,integerSeparator:r}:m}function _(e){document.documentElement.lang=e.substring(0,2)}function v(e,t){for(const n of navigator.languages){if((0,a().iK)(n)){if(e===n)return;if(!t.includes(n)&&e!==n)return n}if((0,a().hL)(n)){const o=a().gI[n];if(e===o)return;if(!t.includes(o)&&e!==o)return o}}}},410555:(e,t,n)=>{n.d(t,{A:()=>o});const o=new(n(178624).R)({key:"openExternalLinkSettingStorageKey",namespace:n(419494).Bq,important:!0,trackingType:"necessary"})},410690:(e,t,n)=>{n.r(t),n.d(t,{appStartTimeKey:()=>a,getAppStartTime:()=>r,getPageOriginTime:()=>i});n(16280),n(898992),n(672577);var o=()=>n(449412);const a="__webStartTime";function r(){const e=window[a];if("number"==typeof e)return e;{const e=new Error("App Start Time not found. It should be in index.html.");o().Fg(e)}}function i(){const e=performance.getEntriesByType("navigation").find((e=>e instanceof PerformanceNavigationTiming));let t=(null==e?void 0:e.redirectStart)||(null==e?void 0:e.fetchStart);if("number"!=typeof t){const{redirectStart:e,fetchStart:n,navigationStart:o}=performance.timing;e?t=e-o:n&&(t=n-o)}if("number"==typeof t)return t;{const e=new Error("Page Origin Time not found");o().Fg(e)}}},416845:(e,t,n)=>{n.d(t,{AMh:()=>_,Ah6:()=>m,Cdc:()=>S,DfQ:()=>O,G4X:()=>C,IQ8:()=>k,KhW:()=>M,OwV:()=>x,QkH:()=>b,Sdl:()=>V,TXj:()=>T,USz:()=>i,Umn:()=>P,V4j:()=>w,VHT:()=>h,WXf:()=>r,X5e:()=>E,Xdn:()=>U,Xgk:()=>N,_D_:()=>F,d5r:()=>A,ico:()=>D,io_:()=>I,lV7:()=>R,m_B:()=>B,mdR:()=>L,mh_:()=>v,mvP:()=>d,op4:()=>l,pA8:()=>u,rTV:()=>y,s76:()=>q,ubK:()=>g,vP6:()=>c,yI4:()=>p});n(16280);const o=["DatastoreInfraError","PostgresConnectionError","PostgresConnectionTerminatedError","PostgresDeadlockError","PostgresServerLoginFailingError","PostgresTimeout","PgPoolWaitConnectionTimeout","PgbouncerMaxClientConnExceeded","CrdtAssertionError","CrdtIdAlreadyExistsError","CrdtOperationAlreadyAppliedError","CrdtOperationLikelyAlreadyAppliedError","EnqueueSyncTaskTimeout","EnqueueSyncTaskFailure","OfflineSyncPageNotFoundDuringVersionCheckError","DictationOpenAIError","DictationForbiddenError","DictationUpgradeRequiredError","DictationRateLimitError","DictationAudioRecorderFailedError","DictationMissingUserOrSpaceError","DictationSystemAudioFailedError","DictationStartTimeoutError","DictationSystemAudioHardwareError","AbortedError","AWSRequestHandlerError","AiSourcePickerQueryError","AiSourcePickerResponseError","AirtableError","AppleError","AsanaError","AudioProcessorError","CantAccessWorkspaceFromNetworkError","CantAccessWorkspaceFromEKMError","ChartPaywallError","ChiliPiperError","ChromeError","CohereError","CollectionSchemaOptionSizeTooLarge","CollectionSchemaTooLargeError","ConfluenceImportError","ConnectionError","ContentfulError","ContentfulNotFoundError","ContentfulUnauthorizedError","DebeziumError","DecryptionError","DiffbotError","DiffbotExtractError","DirectRequestHandlerError","DomainNameUnchangedError","Dynamo5XXError","DynamoConditionalCheckError","ElasticsearchBulkIndexError","ElasticsearchError","ElasticsearchIndexingError","ElasticsearchLtrError","EvernoteError","ExportRendererError","FailedConsolidationError","FileImportError","FileUploadError","ForbiddenError","GetStatusError","GiphyError","GlobalOauthError","GoogleAdsError","GoogleError","HttpRequestError","IframelyError","ImportError","IndexActivityEditsError","InternalApiError","InternalFetchError","InternalServerError","InvalidNameErrorData","InvalidOrExpiredTokenError","JiraError","JiraPermissionError","JiraApiError","JsonParseError","LogicalError","MarketoError","MarketoRateLimitError","MarketoServerError","MemcachedError","MessageStoreError","MessageStoreRedisError","MfaBackupCodeExpiredError","MicrosoftError","MissingSecretError","MissingStaticFileError","MissingTokenError","MondayError","MultiCellLogicalError","NetworkError","NoAvailableSearchClustersError","NoSearchResponseError","NotFoundError","NotImplementedError","NotionCalendarError","NotionMailError","OpenAIError","OperationConflictError","OrganizationNotFoundError","PandocError","PaymentRequiredError","PdfTodocxError","PostProcessingTaskError","PostgresCardinalityViolation","PostgresCollisionError","PostgresConnectionError","PostgresConnectionTerminatedError","PostgresDeadlockError","PostgresInvalidTextRepresentation","PostgresInvalidUnicodeCharacter","PostgresNullConstraintError","PostgresProgramLimitExceeded","PostgresServerLoginFailingError","PostgresTimeout","PostgresUniqueViolation","ProjectManagementImporterError","PublicAPIError","QueueProxyError","QuipError","RedisError","RequestHandlerError","RequestProxyUpstreamError","RequestTimeoutCancellationError","ResourceExpiredError","RevenueCatError","S3UploadError","S3BatchOperationError","SalesforceError","InvalidExternalIntegrationTokenError","SamlConfigurationError","ServiceUnavailableError","SlackError","SpaceFrozenError","SpaceNotFoundError","StatsigBootstrapError","StripeError","SyncedCollectionUpdateError","TaskQueueDataTooLongError","TokenRateLimitError","TokenStoreError","TooManyRequestsError","TransactionTimeout","TrelloError","TwilioError","UnauthorizedError","UnfurlResponseError","UniversalRetrievalError","UnknownCollectionServerError","UnknownError","UnprocessableEntity","UnsavedTransactionsError","UnsplashError","UnsupportedMediaTypeError","UntriedTransactionError","UserRateLimitResponse","UserValidationError","UserValidationResponse","ValidationError","WebhookError","WorkspaceConsolidationRetrySmallerBatchError","WorkspaceCreationError","ZendeskError"],a=Error;class r extends a{constructor(e){var t;super(e.message),this.level=void 0,this.statusCode=void 0,this.headers=void 0,this.name=void 0,this.data=void 0,this.error=void 0,this.body=void 0,this.retryable=void 0,this.level=e.level,this.statusCode=e.status,this.name=e.name,this.data=e.data,this.error=e.error,this.body={errorId:(0,n(498212).lZ)(),name:e.name,clientData:e.clientData,debugMessage:e.message},void 0!==e.stack&&(this.stack=e.stack),this.retryable=e.retryable||(null===(t=e.error)||void 0===t?void 0:t.retryable)||!1}}function i(e){return"object"==typeof e&&null!==e&&o.includes(e.name)}class s extends r{constructor(e){super(e),this.body=void 0;const t=this.body;this.body={...t,clientData:e.clientData}}}function c(e){return void 0!==e.clientData}function l(e){return x(e)&&c(e)}class d extends Error{constructor(e,t,n){super(e),this.data=void 0,this.clientData=void 0,this.name="QueueApiError",this.data=t,this.clientData=n}}function u(e){return Boolean(e&&"QueueApiError"===e.name&&void 0!==e.clientData)}class p extends r{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super({level:"warning",status:400,name:"ValidationError",message:e,data:t.data,error:t.error,clientData:t.clientData})}}function m(e){return Boolean(i(e)&&"ValidationError"===e.name)}class f extends r{constructor(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};super({level:"error",status:500,name:e,message:t,data:n.data,error:n.error,clientData:n.clientData})}}function g(e){return e instanceof f}class b extends f{constructor(e){super("CrdtAssertionError",e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{})}}class h extends f{constructor(e){super("CrdtIdAlreadyExistsError",e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{})}}class _ extends f{constructor(e){super("CrdtOperationAlreadyAppliedError",e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{})}}class v extends f{constructor(e){super("CrdtOperationLikelyAlreadyAppliedError",e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{})}}class y extends s{constructor(e,t){super({level:"info",status:400,name:"UserValidationError",message:e,clientData:t,...arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}})}}function w(e){return Boolean(e&&"UserValidationError"===e.name&&void 0!==e.body.clientData)}function k(e){return Boolean(e&&"UserValidationResponse"===e.name&&void 0!==e.clientData)}class S extends s{constructor(e){super({level:"info",status:403,name:"CantAccessWorkspaceFromNetworkError",message:"Unfortunately, we can't access this workspace from your network. Please try again from a different network.",clientData:e})}}function A(e){return void 0!==e&&("cant_access_workspace_from_network"===e.type||"cant_access_workspace_from_client"===e.type)}function C(e){return i(e)&&"UserRateLimitResponse"===e.name}function I(e){return i(e)&&"ForbiddenError"===e.name}class P extends r{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super({level:"error",status:400,name:"LogicalError",message:e,data:t.data,error:t.error})}}function T(e){return i(e)&&"NetworkError"===e.name}class E extends r{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super({level:"error",status:400,name:"MondayError",message:e,data:t.data,error:t.error,clientData:t.clientData})}}class R extends r{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super({level:"error",status:400,name:"TrelloError",message:e,data:t.data,error:t.error})}}class D extends s{constructor(e,t){super({level:"info",status:400,name:"ConfluenceImportError",message:e,...t})}}class q extends s{constructor(e,t){super({level:"info",status:400,name:"FileImportError",message:e,...t})}}class M extends r{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super({level:"warning",status:400,name:"OperationConflictError",message:e,data:t.data,error:t.error,clientData:t.clientData})}}class O extends r{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super({level:"error",status:400,name:"EvernoteError",message:e,data:t.data,error:t.error})}}class N extends r{constructor(e,t,n){super({level:"error",status:400,name:"WorkspaceCreationError",message:e,data:t,error:n})}}class B extends r{constructor(e){super({level:"error",status:404,name:"NotFoundError",message:(null==e?void 0:e.message)||"Resource not found.",error:null==e?void 0:e.error})}}function L(e){return Boolean(e&&"unsaved_transactions"===e.type&&e.errors.length>0)}function x(e){return"object"==typeof e&&null!==e&&e.errorId&&e.name}function U(e){return Boolean(i(e)&&"TaskQueueDataTooLongError"===e.name)}const F=402;function V(e){return Boolean(i(e)&&"PaymentRequiredError"===e.name)}},419494:(e,t,n)=>{n.d(t,{Bq:()=>l,cd:()=>c,lD:()=>d,Ay:()=>_,HW:()=>m});n(16280),n(944114),n(581454);function o(e,t,o){n(863024).add(e,t,o)}const a=[100,1e3,1e4];function r(e){return e&&e.timestamp?e.timestamp:0}function i(e,t){const n=r(e.value),o=r(t.value);return n===o?0:n>o?1:-1}class s{constructor(e){this.store=e}get(e){let{disableLRU:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=this.store.get(e);if(n)return t||this.set(e,n.value,n.important),n.value}set(e,t,n){let o;for(const r of a){if(o=this.attemptSet(e,t,n),!o)return;this.purge(r)}throw o}remove(e){this.store.remove(e)}scan(e){this.store.scan(e)}squeeze(e){const t=this.store.getSize();if(t>e){const n=t-e+Math.round(e/2);this.purge(n)}}attemptSet(e,t,n){try{this.store.set(e,{id:e,value:t,timestamp:Date.now(),important:n})}catch(o){return o}}purge(e){const t=[];this.store.scan(((n,a)=>{a.important||(o(t,{key:n,value:a},i),t.splice(e))})),t.map((e=>this.store.remove(e.key)))}}const c="LocalPreferenceStore3",l="KeyValueStore2",d="LRU:",u=["BlockCache","LocalPreferenceStore","LocalPreferenceStore2","KeyValueStore","LocalTransactionStore","LocalRecordStore","LocalRecordStore2","LRU:KeyValueStore2:offline_page_state"],p=["amplitude","intercom","STATSIG","statsig.cached.evaluations"];function m(e,t){return`${e||"guest"}:${t}`}let f=!1;function g(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new s({get:e=>{const t=localStorage.getItem(d+e);if(t)return JSON.parse(t)},set:(t,o)=>{const a=JSON.stringify(o);if(e.maxValueLength&&a.length>e.maxValueLength)throw new Error(`Value exceeds max length (${e.maxValueLength})`);try{localStorage.setItem(d+t,a)}catch(r){if(!f){f=!0;const e=Object.keys(localStorage),o=n(496603).Ul(e.map((e=>[e,JSON.stringify(localStorage[e]).length])),(e=>-e[1]));n(857639).log({level:"error",from:"LocalStorageStore",type:"set",error:(0,n(502800).convertErrorToLog)(r),data:{miscDataToConvertToString:{keysWithSizes:JSON.stringify(o),sessionHeartbeat:localStorage["LRU:KeyValueStore2:NotionSessionHeartbeat3"],setKey:d+t,setValueLength:a.length}}})}const e=[];for(let t=0;t<localStorage.length;t++)e.push(localStorage.key(t));for(const t of e){const e=p.some((e=>t.startsWith(e)));e&&localStorage.removeItem(t)}localStorage.setItem(d+t,a)}},remove:e=>{localStorage.removeItem(d+e)},scan:e=>{const t=[];for(let n=0;n<localStorage.length;n++)t.push(localStorage.key(n));for(const n of t)if(u.some((e=>n.startsWith(e))))localStorage.removeItem(n);else if(n.startsWith(d)){const t=localStorage.getItem(n);if(t){const o=JSON.parse(t);e(n.slice(d.length),o)}}},getSize:()=>localStorage.length})}function b(){return n(412215)}const h={getCookieWithoutPermissionCheck:e=>Promise.resolve(b().get(e)),removeCookie:e=>Promise.resolve(b().remove(e)),isMobileNative:()=>"undefined"!=typeof window&&(0,n(140583).E)(window,window.navigator.userAgent).isMobileNative()};const _=class{constructor(e){let{namespace:t,important:o,trackingType:a,maxValueLength:r,onHasPermissionForTrackingTypeChange:i}=e;this.namespace=void 0,this._hasPermissionForTrackingType=void 0,this._initPromise=void 0,this.lru=void 0,this.important=void 0,this.lru=g({maxValueLength:r}),this.important=o,this.namespace=t,this._hasPermissionForTrackingType="necessary"===a,this._initPromise=n(283699).Eo({trackingType:a,cookieService:h}).then((e=>{const t=this.hasPermissionForTrackingType;this.hasPermissionForTrackingType=e,t!==e&&(null==i||i())}))}async waitUntilReady(){try{await this._initPromise}catch{}}get(e){let{disableLRU:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.hasPermissionForTrackingType)return this.lru.get(this.makeKey(e),{disableLRU:t})}set(e,t){if(this.hasPermissionForTrackingType)return this.lru.set(this.makeKey(e),t,this.important)}remove(e){return this.lru.remove(this.makeKey(e))}squeeze(e){return this.lru.squeeze(e)}scan(e){this.lru.scan(((t,n)=>{t.startsWith(this.namespace)&&e(t.substring(this.namespace.length+1),n.value)}))}parseRawKeyToOwnedKey(e){const[t,n,o]=e.split(":");if(`${t}:`===d&&n===this.namespace&&o)return o}get hasPermissionForTrackingType(){return this._hasPermissionForTrackingType}set hasPermissionForTrackingType(e){this._hasPermissionForTrackingType=e}makeKey(e){return[this.namespace,e].join(":")}}},427704:(e,t,n)=>{n.d(t,{Bf:()=>s,GJ:()=>a,H9:()=>d,JZ:()=>u,eR:()=>i,zK:()=>r});n(581454);var o=()=>n(209199);const a={root:"/",onboarding:"/onboarding",make:"/make",googleOneTapRedirect:"/google-onetap-redirect",privacyCenterRedirect:"/privacy-center-redirect",login:"/login",loginCalendar:"/login/calendar",loginMail:"/login/mail",loginWithEmail:"/loginwithemail",loginSuccess:"/loginsuccess",logout:"/logout",signup:"/signup",signupCalendar:"/signup/calendar",signupMail:"/signup/mail",nativeOauthCallback:"/native/oauth2callback",nativeMailOauthCallback:"/nativemail/oauth2callback",nativeCalendarOauthCallback:"/nativecalendar/oauth2callback",desktopEmailConfirm:"/email-confirm",addAnotherAccountModal:"/?target=add_another_account",addAnotherAccount:"/add-another-account",unsubscribe:"/unsubscribe",invoice:"/invoice",admin:"/__admin",adminListData:"/__admin/data",newPage:"/new",aiFavorite:"/ai-favorite",oauthAuthorization:"/install-integration",notionCalendarAuthorization:"/install-calendar-integration",myIntegrations:"/my-integrations",creatorProfileIntegrations:"/profile/integrations",templatePreview:"/template-preview",nativeTab:"/nativetab",modal:"/modal",templateSubmission:"/submit-a-template",templateCreatorSubmission:"/submit-a-template/profile",studentGroupSignup:"/notion-for-student-orgs-apply",startupsApplication:"/startups-apply",smbsApplication:"/smbs-apply",lennyApplication:"/lenny-apply",creatorProgramApplication:"/creators",quickSearch:"/quick-search",blank:"/blank",chat:"/chat",creatorProfile:"/profile",creatorProfileTemplates:"/profile/templates",creatorProfileAnalytics:"/profile/analytics",creatorProfileCoupons:"/profile/coupons",localizedTemplates:"/localized-templates",marketplace:"/marketplace",gallery:"/gallery",home:"/home",posts:"/posts",formResponse:"/formResponse",community:"/community",consultantsRedirect:"/consultants",deprecatedGuideRedirect:"/guide",terms:"/terms",contentPolicy:"/content-policy",setupSessions:"/setup-sessions",downloadMac:"/desktop/mac/download",downloadMacUniversal:"/desktop/mac-universal/download",downloadMacIntel:"/desktop/mac-intel/download",downloadMacAppleSilicon:"/desktop/mac-apple-silicon/download",downloadWindows:"/desktop/windows/download",downloadWindowsArm:"/desktop/windows-arm/download",downloadMacInstaller:"/desktop/mac-installer/download",downloadCalendarMac:"/calendar/desktop/mac/download",downloadCalendarMacUniversal:"/calendar/desktop/mac-universal/download",downloadCalendarMacAppleSilicon:"/calendar/desktop/mac-apple-silicon/download",downloadCalendarMacIntel:"/calendar/desktop/mac-intel/download",downloadCalendarWindows:"/calendar/desktop/windows/download",downloadMailMac:"/mail/desktop/mac/download",workflowTemplates:"/build-database",notFound:"/not-found",scimApiBasePath:"/scim/v2",settingsConsoleDefault:"/settings",settingsConsoleWorkspace:"/settings/workspace",settingsConsoleOrganization:"/settings/organization",loginPasswordReset:"/loginpasswordreset",passwordResetCallback:"/passwordresetcallback",passwordChangeRedirect:"/passwordchangeredirect",applePopupRedirect:"/applepopupredirect",appleAuthCallback:"/appleauthcallback",applePopupCallback:"/applepopupcallback",nativeMailAppleAuthCallback:"/nativemail/appleauthcallback",nativeCalendarAppleAuthCallback:"/nativecalendar/appleauthcallback",googlePopupRedirect:"/googlepopupredirect",googleAuthCallback:"/oauth2callback",googlePopupCallback:"/googlepopupcallback",microsoftPopupRedirect:"/microsoftpopupredirect",microsoftAuthCallback:"/microsoftauthcallback",microsoftPopupCallback:"/microsoftpopupcallback",nativeMailMicrosoftAuthCallback:"/nativemail/microsoftauthcallback",nativeCalendarMicrosoftAuthCallback:"/nativecalendar/microsoftauthcallback",passkeyAuthVerify:"/passkeyauthverify",passkeyAuthCallback:"/passkeyauthcallback",nativeMailPasskeyAuthCallback:"/nativemail/passkeyauthcallback",nativeCalendarPasskeyAuthCallback:"/nativecalendar/passkeyauthcallback",samlAuth:"/sso/saml",samlAuthCallback:"/samlauthcallback",nativeMailSamlAuthCallback:"/nativemail/samlauthcallback",nativeCalendarSamlAuthCallback:"/nativecalendar/samlauthcallback",passkeyRegistrationRedirect:"/passkeyregistrationredirect",passkeyRegistrationVerification:"/passkeyregistrationverification",passkeyRegistrationCallback:"/passkeyregistrationcallback",globalOauthAuthorization:"/oauth2/authorize",globalOauthPostLogin:"/oauth2/postlogin",globalOauthToken:"/oauth2/token",slackPopupRedirect:"/slackpopupredirect",slackAuthCallback:"/slackoauthcallback",trelloPopupRedirect:"/trellopopupredirect",trelloAuthCallback:"/trelloauthcallback",trelloElectronAuthCallback:"/trelloelectronauthcallback",asanaPopupRedirect:"/asanapopupredirect",asanaAuthCallback:"/asanaauthcallback",evernotePopupRedirect:"/evernotepopupredirect",evernoteAuthCallback:"/evernoteoauthcallback",evernoteElectronAuthCallback:"/evernoteelectronoauthcallback",googleDrivePopupRedirect:"/googledrivepopupredirect",googleDrivePickerPopup:"/googledrivepickerpopup",verifyNoPopupBlockerHtmlAndRedirect:"/verifyNoPopupBlockerHtmlAndRedirect",externalAuthCallback:"/externalauthcallback",externalAuthProxy:"/eap",serverIntegrationsAuthProxy:"/sip",userSessionAuth:"/userSessionAuth",syncUserSessionAuthCookies:"/syncCookies",productAuth:"/productAuth",calendarAuth:"/calendarAuth",authSync:"/authSync",externalIntegrationPopupRedirect:"/externalintegrationpopupredirect",externalIntegrationAuthCallback:"/externalintegrationauthcallback",datadogAuthCallback:"/datadogauthcallback",initiateExternalAuthentication:"/initiateExternalAuthentication",initiateExternalAuthenticationFromDesktop:"/initiateExternalAuthenticationFromDesktop",externalAuthNativeCallback:"/externalauthnativecallback",githubStudentPackHome:"/githubstudentpack",githubStudentPackAuthCallback:"/githubStudentPackAuthCallback",personProfileRedirect:"/profiles/:spaceId/:userId",plansSettings:"/?target=plans",membersSettings:"/?target=members",embedPublicPages:"/ebd",authValidate:"/api/v3/authValidate",workspaceDiscovery:"/workspaceDiscovery"},r={nextJsInternal:"/_next",vercelInsightApi:"/_vercel/insights",frontApi:"/front-api",frontStatic:"/front-static",wellKnownVercel:"/.well-known/vercel",sitemap:"/sitemap.xml",robots:"/robots.txt",llms:"/llms.txt",sitemapRoot:"/sitemap-root.xml",sitemaps:"/sitemaps"},i={about:"/about",affiliates:"/affiliates",ai:"/product/ai",aiMeetingNotes:"/product/ai-meeting-notes",aiUseCases:"/product/ai/use-cases",blog:"/blog",careers:"/careers",calendarRoot:"/calendar",calendar:"/product/calendar",calendarSignup:"/product/calendar/signup",calendarDownloadDesktop:"/product/calendar/download/desktop",calendarDownload:"/product/calendar/download",calendarDownloadiOS:"/product/calendar/download/ios",calendarDownloadWindows:"/product/calendar/download/windows",calendarDownloadMac:"/product/calendar/download/mac",compareAgainst:"/compare-against",confluence:"/confluence",contactSales:"/contact-sales",contactSalesConfirmation:"/contact-sales/thank-you",customers:"/customers",download:"/download",desktop:"/desktop",docs:"/product/docs",embed:"/embed",engAndProduct:"/product/notion-for-product-development",enterprise:"/enterprise",enterpriseSearch:"/product/enterprise-search",events:"/events",evernote:"/evernote",faces:"/faces",help:"/help",helpReference:"/help/reference",helpResults:"/help/results",integrations:"/integrations",guides:"/help/guides",mail:"/product/mail",mailGA:"/product/mail/ga",mailDownload:"/product/mail/download",mobile:"/mobile",nonprofits:"/nonprofits",notes:"/notes",notionAcademy:"/help/notion-academy",offer:"/offer",pages:"/pages",partners:"/partners",personal:"/personal",pricing:"/pricing",product:"/product",projects:"/product/projects",redeem:"/redeem",releases:"/releases",releasesRSS:"/releases/rss.xml",remote:"/remote",resources:"/resources",security:"/security",startups:"/startups",sitesRoot:"/sites",sites:"/product/sites",sprigSurveyEmbed:"/embed/help/sprig-survey",charts:"/product/charts",upgradedAccount:"/upgraded-account",teams:"/teams",templates:"/templates",useCase:"/use-case",webClipper:"/web-clipper",webinars:"/webinars",wikis:"/product/wikis",forms:"/product/forms",becomeACreator:"/become-a-creator",becomeAPartner:"/become-a-partner",channelPartnerProgram:"/channel-partner-program",consultingPartnerProgram:"/consulting-partner-program",servicePartnerProgram:"/service-partner-program",servicesPartnerProgram:"/services-partner-program",solutionPartnerProgram:"/solution-partner-program",solutionsPartners:"/solutions-partners",solutionsPartnerProgram:"/solutions-partner-program",securityCompliancePartnerProgram:"/security-compliance-partner-program",technologyPartnerProgram:"/technology-partner-program",reportInappropriateContent:"/help/report-inappropriate-content",reportInappropriateForm:"/help/report-inappropriate-content#how-to-report-a-form",desktopMac:"/desktop/mac",desktopWindows:"/desktop/windows",desktopWhatsNew:"/desktop/whats-new",mobileIOS:"/mobile/ios",mobileAndroid:"/mobile/android",androidRedirect:"/android",apiBetaRedirect:"/api-beta",educatorsRedirect:"/educators",jobsRedirect:"/jobs",joinUsRedirect:"/join-us",studentsRedirect:"/students",toolsAndCraftRedirect:"/tools-and-craft",wikiRedirect:"/wiki",whyRedirect:"/why",workRedirect:"/work",guidesRedirect:"/guides",config24Redirect:"/config24",aiRedirect:"/ai",docsRedirect:"/docs",mailRedirect:"/mail",projectsRedirect:"/projects",wikisRedirect:"/wikis",...o().Hj,...o().ur,templateCreator:"/@",lp:"/lp",md:"/md",firstBlock:"/first-block",makeWithNotion:"/mwn",makeWithNotionAgenda:"/mwn-agenda",makeWithNotionLive:"/makewithnotionlive",aiConnectors:"/ai-connectors",explore:"/explore"},s={frontDev:"/__frontDev",sandbox:"/sandbox",storybook:"/storybook",components:"/components",frontAdmin:"/__front-admin"},c={consultingPartners:"/consulting-partners"};a.logout,a.login,a.loginCalendar,a.loginMail,a.calendarAuth,a.signup,a.signupCalendar,a.signupMail,a.desktopEmailConfirm,a.loginWithEmail,a.unsubscribe,a.make,a.onboarding,a.invoice,a.admin,a.loginPasswordReset,a.addAnotherAccountModal,a.addAnotherAccount,a.passwordResetCallback,a.passwordChangeRedirect,a.googlePopupRedirect,a.googleAuthCallback,a.googlePopupCallback,a.passkeyAuthVerify,a.passkeyAuthCallback,a.applePopupRedirect,a.appleAuthCallback,a.applePopupCallback,a.microsoftPopupRedirect,a.microsoftAuthCallback,a.microsoftPopupCallback,a.samlAuth,l([a.samlAuth]),a.samlAuthCallback,a.slackPopupRedirect,a.slackAuthCallback,a.trelloPopupRedirect,a.trelloAuthCallback,a.externalAuthCallback,a.externalAuthNativeCallback,a.asanaPopupRedirect,a.asanaAuthCallback,a.evernotePopupRedirect,a.evernoteAuthCallback,a.googleDrivePopupRedirect,a.googleDrivePickerPopup,a.externalIntegrationAuthCallback,a.externalIntegrationPopupRedirect,a.deprecatedGuideRedirect,a.community,a.consultantsRedirect,a.setupSessions,a.myIntegrations,a.creatorProfileIntegrations,a.templatePreview,a.oauthAuthorization,a.notionCalendarAuthorization,a.nativeOauthCallback,a.nativeMailOauthCallback,a.nativeMailSamlAuthCallback,a.nativeMailAppleAuthCallback,a.nativeMailMicrosoftAuthCallback,a.nativeMailPasskeyAuthCallback,a.nativeCalendarOauthCallback,a.nativeCalendarSamlAuthCallback,a.nativeCalendarAppleAuthCallback,a.nativeCalendarMicrosoftAuthCallback,a.nativeCalendarPasskeyAuthCallback,a.templateSubmission,a.creatorProfile,a.creatorProfileAnalytics,a.creatorProfileTemplates,a.creatorProfileCoupons,a.templateCreatorSubmission,a.localizedTemplates,a.studentGroupSignup,a.marketplace,a.gallery,l([a.marketplace,a.gallery]),a.gallery,Object.values(r),Object.values(i),l(Object.values(i)),Object.values(s),i.templateCreator,Object.values(c),a.plansSettings,a.membersSettings,a.settingsConsoleDefault,a.settingsConsoleOrganization;function l(e){return e.map((e=>`${e}/*`))}const d={notionTwitter:"https://twitter.com/NotionHQ",appStore:"https://itunes.apple.com/app/notion-notes-tasks-wikis/id1232780281",playStore:"https://play.google.com/store/apps/details?id=notion.id",stagingChromeExtension:"https://chrome.google.com/webstore/detail/plicnlhlnddfonieaidfmagnjmkiiojd",devChromeExtension:"https://chrome.google.com/webstore/detail/ndbjfpdjbfcljfnfhoopkljapcaomhha",prodChromeExtension:"https://chrome.google.com/webstore/detail/knheggckgoiihginacbkhaalnibhilkk",devFirefoxExtension:"https://dev.notion.so/notion/Web-Clipper-fb7489a8ebe64aec987b9b136fa0fafc",prodFirefoxExtension:"https://addons.mozilla.org/en-US/firefox/addon/notion-web-clipper/",devSafariExtension:"https://dev.notion.so/65ce293fa99a4fb0a3302da0e8b09d73#2305488a752c497f80ee810d27454aea",prodSafariExtension:"https://apps.apple.com/us/app/notion-web-clipper/id1559269364?mt=12",notionCalendarAppStore:"https://apps.apple.com/app/notion-calendar/id1607562761",notionCalendarPlayStore:"https://play.google.com/store/apps/details?id=com.cron.calendar",notionMailBetaTermsAndConditions:"https://notion.notion.site/Notion-Mail-Beta-Terms-090d634456024038a86f8f0fedf55741",statusPage:"https://status.notion.so",mailHome:"https://mail.notion.so/",startWithATemplate:"/help/start-with-a-template",termsAndPrivacy:"https://www.notion.so/28ffdd083dc3473e9c2da6ec011b58ac",termsAndConditions:"https://www.notion.so/notion/Terms-and-Privacy-28ffdd083dc3473e9c2da6ec011b58ac",privacyPolicy:"https://www.notion.so/3468d120cf614d4c9014c09f6adc9091",californiaPrivacyNotice:"https://www.notion.so/notion/Privacy-Policy-3468d120cf614d4c9014c09f6adc9091?pvs=4#18e15495ac2643659c303cbe96358620",mediaKit:"https://www.notion.so/205535b1d9c4440497a3d7a2ac096286",webClipperGuide:"https://www.notion.so/ba54b19ecaeb466b8070b9e683c5fce1",publicAPISpec:"https://www.notion.so/notion/Notion-API-specification-c29dd39d851543b49a24e1571f63c488",publicApiGetStarted:"https://www.notion.so/notion/Getting-started-98aa2aeeaf0b4836b089cd6fce0b920a",consultants:"https://www.notion.so/consulting-partners",cookiePolicy:"https://www.notion.so/notion/GDPR-c8eac6ea83a64fb1a3ea3bcd5c3d4951",developerTerms:"https://www.notion.so/notion/Developer-Terms-ba4131408d0844e08330da2cbb225c20",developerPortal:"https://developers.notion.com",developerOAuthDocs:"https://developers.notion.com/docs/authorization",youTube:"https://www.youtube.com/channel/UCoSvlWS5XcwaSzIcbuJ-Ysg",facebook:"https://www.facebook.com/NotionHQ/",twitter:"https://twitter.com/NotionHQ",linkedIn:"https://www.linkedin.com/company/notionhq/",instagram:"https://www.instagram.com/notionhq/",notionPerks:"https://startupshub.notion.site/Perks-Deal-Book-0671c595db8848eab159618fe9229c04",championsCommunity:"https://www.notion.so/Notion-Champions-20f977eb5fdd40d4a7a396f1742c3ea5",communityConduct:"https://www.notion.so/notion/Code-of-Conduct-9399b74373b94181bb1026d8afb11800",careersInternships:"https://app.ripplematch.com/t/1edfe69a",desktopWhatsNewProd:"https://notion.notion.site/What-s-New-Mac-Windows-5936dabc8dd6497895786c91b9d6f12a",desktopWhatsNewDev:"https://dev.notion.so/notion/What-s-new-in-Notion-for-Mac-Windows-Dev-81bb72067550431ea65cd6dab12a9ff1",notionForStartupsTerms:"https://www.notion.so/notion/Notion-for-Startup-Terms-936b74c0323745a186b1497747074020",notionLennyTerms:"https://www.notion.so/notion/Notion-s-Lenny-Greatest-Ever-Bundle-Program-18aefdeead05803782a7e59f633b60fd?pvs=4",notionCreatorProgramTerms:"https://notion.notion.site/Creator-Program-T-C-s-1bcefdeead0580059194dcc1701c12f3?pvs=4",startupInABox:"https://www.notion.so/templates/startup-in-a-box",notionForSMBsTerms:"https://notion.notion.site/Notion-for-SMB-Terms-6e5f7f5ee9a94c63a7872877db9a6bb8",companyInABox:"https://www.notion.so/templates/company-in-a-box",sellerOnboardingLearnMore:"https://www.notion.so/help/selling-on-marketplace",marketplaceWebhookLearnMore:"https://www.notion.com/help/selling-on-marketplace#webhooks",customerOpenAi:"https://www.notion.com/customers/openai"},u={...d,...c,...s,...i,...a}},430476:(e,t,n)=>{n.d(t,{Fm:()=>b,G2:()=>p,Gd:()=>h,OJ:()=>f,Zj:()=>c,fb:()=>g,fi:()=>m,kx:()=>d,qU:()=>l});n(16280),n(898992),n(354520),n(581454),n(737550);var o=()=>n(532313),a=()=>n(534177),r=()=>n(720665),i=()=>n(651170),s=()=>n(732524);const c=999;async function l(e){let{connection:t,sql:n,args:o,queryName:a}=e;const r={sql:n,args:o,getData:!0},[i]=await p({connection:t,statements:[r],queryName:a});return i.data}async function d(e){let{connection:t,sql:n,args:o,queryName:a}=e;const r={sql:n,args:o};await p({connection:t,statements:[r],queryName:a})}let u;async function p(e){let{connection:t,statements:n,webLockRequest:c,queryName:l}=e;const d=async()=>{const e={sql:"BEGIN",getData:!1},o={sql:"COMMIT",getData:!1},d={sql:"ROLLBACK",getData:!1},p=n.map((e=>({...e,sql:(0,r().hr)(e.sql)}))),m=[e,...p,o],f=(0,s().Xb)({queryName:l,body:m,onError:d}),g=f,b=c?await _(c,(()=>t.execSqliteBatch(g))):await t.execSqliteBatch(g),h=b.body.slice(1,-1),v=function(e){const{batch:t,result:n,lastSuccessfulSqlBatch:o}=e,r=n.body.findIndex(s().Ll);if(r<0)return;const c=n.body[r],l={batch:t,result:n,errorSql:t.body[r].sql,errorArgs:t.body[r].args,errorIndex:r,sqliteCode:"sqliteCode"in c?c.sqliteCode:void 0};switch(c.type){case"Error":return c.message.includes("database disk image is malformed")?o?new(i().lh)({message:c.message,debugInfo:{...l,lastSuccessfulSqlBatch:o}}):new(i().PW)({message:c.message,debugInfo:l}):new(i().yY)({result:c,debugInfo:l});case"ErrorBefore":return new(i().pu)({message:"ErrorBefore before first Error",debugInfo:l});case"PreconditionFailed":return new(i().JV)({message:"The precondition SQL query did not pass, the batch execution was not attempted.",debugInfo:l});case"OutOfSpace":return new(i().w8)({message:"Sqlite has run out of space",debugInfo:l});case"SharedWorkerFailedToDelegate":return new(i().F)({message:"SharedWorker failed to delegate to a Worker",debugInfo:l});default:(0,a().HB)(c)}}({batch:f,result:b,lastSuccessfulSqlBatch:u});if(v||h.some(s().Ll))throw v;return u=p.map((e=>e.sql)),h};return await o().L5({initialInput:void 0,fn:()=>d(),handleError:(e,t)=>"SqlitePreconditionFail"!==e.name||t?{status:"throw",error:e}:{status:"retry"},retryAttemptsMS:[10,100,1e3],retryAttemptRandomOffsetMS:50})}async function m(e){let{connection:t}=e;const n=(0,s().Xb)({queryName:"vacuum",body:[{sql:"VACUUM"}],onError:void 0});await async function(e){return _({isWrite:!0,tables:"offlineStorage"},(()=>_({isWrite:!0,tables:"sqliteRecordCache"},e)))}((()=>t.execSqliteBatch(n)))}function f(e){return e&&e.data?e.data:[]}function g(e){const t=e.data[0];if(0===e.data.length||!t)throw new Error("Expected >1 result rows, instead had none.");return t}function b(e){return JSON.stringify(e).replace(/\u2028/g,"").replace(/\u2029/g,"")}function h(e){return e?1:0}async function _(e,t){var n;return"undefined"==typeof navigator||void 0===(null===(n=navigator.locks)||void 0===n?void 0:n.request)?t():await navigator.locks.request(e.tables,{mode:e.isWrite?"exclusive":"shared"},(()=>t()))}},436604:(e,t,n)=>{n.d(t,{B3:()=>r,eG:()=>a,tx:()=>o});const o="x-notion-cell",a="x-notion-space-short-id",r="x-notion-space-id"},445155:(e,t,n)=>{async function o(){const[e,{default:t}]=await Promise.all([Promise.all([n.e(5821),n.e(13326)]).then(n.bind(n,705821)),Promise.all([n.e(5821),n.e(13326)]).then(n.bind(n,747347))]);return{Sentry:e,sentryInitializeFull:t}}n.d(t,{q:()=>o})},449412:(e,t,n)=>{n.d(t,{Cr:()=>d,Fg:()=>i,O8:()=>l,Om:()=>s,lE:()=>p,nb:()=>c});n(16280);var o=()=>n(502800),a=()=>n(857639),r=()=>n(56222);function i(){return r().A.sdk.captureException(...arguments)}function s(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];r().A.sdk.withScope((e=>{e.setTag("disable_sampling",!0),r().A.sdk.captureException(...t)}))}function c(){if("startTransaction"in r().A.sdk)return r().A.sdk.startTransaction(...arguments)}function l(e,t){const{from:n,type:r,data:s}=t;let c,l="error is not an Error, bypassing Sentry";if(e instanceof Error){const t=u;u=void 0,c=l=i(e,{tags:{from:n,type:r,productArea:t}})||void 0}return a().log({level:"error",from:n,type:r,error:(0,o().convertErrorToLog)(e),data:s,sentryEventId:l}),c}function d(e){r().A.sdk.configureScope((t=>t.setTransactionName(e)))}let u;function p(e){u=e}},459225:(e,t,n)=>{n.d(t,{getPerformanceEventListeners:()=>D,startFlushing:()=>R});n(944114),n(898992),n(672577),n(814603),n(147566),n(198721);var o=()=>n(402673),a=()=>n(780054);class r{constructor(e){let{waitMs:t,maxWaitMs:o,maxSize:a,batchSize:r,prepareBatchFn:i}=e;this.queue=[],this.dequeueDebounced=void 0,this.maxSize=void 0,this.batchSize=void 0,this.measureFn=void 0,this.prepareBatchFn=void 0,this.dequeueDebounced=n(496603).sg(this.dequeue.bind(this),t,{maxWait:o}),this.maxSize=a,this.batchSize=r,this.measureFn=void 0,this.prepareBatchFn=i}enqueue(e){var t;!Boolean(null===(t=performance)||void 0===t?void 0:t.now)||this.queue.length>=this.maxSize||(this.queue.push(e),(0,o().$)(this.dequeueDebounced))}startFlushing(e){this.measureFn=e,this.dequeue()}dequeue(){var e;if(0===this.queue.length)return;if(!this.measureFn)return;const t=this.queue.splice(0,this.batchSize);this.queue.length>0&&(0,o().$)(this.dequeueDebounced);const n=(null===(e=this.prepareBatchFn)||void 0===e?void 0:e.call(this,t))??t;for(const{metric:o,data:a}of n)this.measureFn(o,a)}}const i=new r({waitMs:2e3,maxWaitMs:1e4,maxSize:1e4,batchSize:50});function s(e){var t,n;if(!Boolean(null===(t=performance)||void 0===t?void 0:t.now)||!Boolean(null===(n=performance)||void 0===n?void 0:n.getEntriesByType))return;const{startTime:o,metricData:a,endTime:r,entry:s,parseResponseEndTime:c,parseResponseStartTime:l}=e,d={js_duration:r-o,js_duration_with_long_tasks:p(o,r),js_request_start:o,js_after_fetch:l,js_after_response_body:c};l&&(d.js_network_duration=l-o),l&&c&&(d.js_parse_response_duration=c-l),s&&(a.response_transfer_size=s.transferSize,d.perf_duration=s.duration,d.perf_before_request_start_duration=s.requestStart-s.startTime,d.perf_request_start_to_response_start_duration=s.responseStart-s.requestStart,d.perf_receive_response_duration=s.responseEnd-s.responseStart,d.perf_redirect_duration=s.redirectEnd-s.redirectStart,d.perf_connect_duration=s.connectEnd-s.connectStart,d.perf_dns_duration=s.domainLookupEnd-s.domainLookupStart,d.perf_start=s.startTime,d.perf_redirect_start=s.redirectStart,d.perf_redirect_end=s.redirectEnd,d.perf_fetch_start=s.fetchStart,d.perf_request_start=s.requestStart,d.perf_response_start=s.responseStart,d.perf_response_end=s.responseEnd,d.http_protocol=s.nextHopProtocol,d.encoded_body_size=s.encodedBodySize,d.decoded_body_size=s.decodedBodySize);const u={metric:{metricName:"request.client_latency",startTime:o,endTime:r},data:{...a,...d}};i.enqueue(u)}function c(e){if(!e)return;const t=e.headers.get("X-Notion-Request-Id");return t?[t]:void 0}const l=6e5;let d;const u=[];function p(e,t){let n=0;for(const o of u){const a=Math.max(o.startTime,e),r=Math.min(o.startTime+o.duration,t);a<r&&(n+=r-a)}return n}const m=6e4,f=new(n(99895).G)((()=>[]));function g(e){e.parseResponseStartTime&&e.parseResponseEndTime&&s({startTime:e.startTime,metricData:e.metricData,endTime:e.endTime??performance.now(),entry:e.entry,parseResponseStartTime:e.parseResponseStartTime,parseResponseEndTime:e.parseResponseEndTime})}const b="r",h=`${b};dur=`;function _(e,t){var n,o;const a=null===(n=e.headers.get("Server-Timing"))||void 0===n||null===(n=n.split(","))||void 0===n||null===(n=n.find((e=>e.trimStart().startsWith(h))))||void 0===n?void 0:n.substring(h.length),r=null===(o=t.serverTiming)||void 0===o?void 0:o.find((e=>e.name===b));return void 0!==a&&void 0!==r&&a===r.duration.toString()}function v(e){0===f.get(e).length&&f.delete(e)}function y(e){if(!e.name.startsWith(k))return;const t=f.get(e.name);let n=!1;for(const[o,a]of t.entries())if("fetched"===a.type&&_(a.response,e)){a.entry=e,t.splice(o,1),v(e.name),g(a),n=!0;break}n||t.push({type:"observed",entry:e})}function w(e){const t=f.get(e.response.url);if(!e.entry)for(const[n,o]of t.entries())if("observed"===o.type&&_(e.response,o.entry)){e.entry=o.entry,t.splice(n,1),v(e.response.url);break}if(e.entry){const n=t.indexOf(e);-1!==n&&(t.splice(n,1),v(e.response.url)),g(e)}}const k=`${a().A.domainBaseUrl}${a().A.api.http}`,S=20,A=new r({waitMs:5e3,maxWaitMs:1e4,maxSize:1e4,batchSize:50,prepareBatchFn:e=>{const t=[];for(const{metric:n,data:o}of e)"request.client_latency.asset"===n.metricName&&t.push({metric:n,data:o});return[{metric:{metricName:"request.client_latency.assets",startTime:performance.now(),endTime:performance.now()},data:{assets:t.flatMap((e=>e.data))}}]}});function C(e){if("fetch"===e.initiatorType)return;let t;try{t=new URL(e.name).pathname}catch(o){t=e.name}const n={shortened_url:t.slice(-S),start_time:e.startTime,time:e.duration,response_transfer_size:e.transferSize,perf_duration:e.duration,http_protocol:e.nextHopProtocol,encoded_body_size:e.encodedBodySize,decoded_body_size:e.decodedBodySize};e.transferSize>0&&(n.perf_before_request_start_duration=e.requestStart-e.startTime,n.perf_request_start_to_response_start_duration=e.responseStart-e.requestStart,n.perf_receive_response_duration=e.responseEnd-e.responseStart,n.perf_redirect_duration=e.redirectEnd-e.redirectStart,n.perf_connect_duration=e.connectEnd-e.connectStart,n.perf_dns_duration=e.domainLookupEnd-e.domainLookupStart),A.enqueue({metric:{metricName:"request.client_latency.asset",startTime:e.startTime,endTime:e.startTime+e.duration},data:n})}let I;const P=1e3;let T=!1;function E(){var e,t,n;T||(T=!0,Boolean(null===(e=performance)||void 0===e?void 0:e.setResourceTimingBufferSize)&&Boolean(null===(t=performance)||void 0===t?void 0:t.addEventListener)&&Boolean(null===(n=performance)||void 0===n?void 0:n.clearResourceTimings)&&(performance.setResourceTimingBufferSize(P),performance.addEventListener("resourcetimingbufferfull",(()=>{performance.clearResourceTimings()})),I??=new PerformanceObserver((e=>{for(const t of e.getEntries())"resource"===t.entryType&&(t.name.startsWith(k)?y(t):C(t))})),I.observe({type:"resource",buffered:!0}),PerformanceObserver.supportedEntryTypes.includes("longtask")&&(d??=new PerformanceObserver((e=>{!function(e){const t=u.findIndex((e=>e.startTime>=performance.now()-l));u.splice(0,t);for(const n of e)u.push(n)}(e.getEntries())})),d.observe({type:"longtask",buffered:!0}))))}function R(e){i.startFlushing(e),A.startFlushing(e)}function D(e){const{eventName:t,isPrefetchRequest:n}=e;let o;E();return{onRequestStart(e){o={type:"initiated",startTime:performance.now(),metricData:{event_name:t,is_prefetch_request:n,request_body_size:e}}},onRequestFetched(e){var t;if("initiated"!==(null===(t=o)||void 0===t?void 0:t.type))return;const n={...o,type:"fetched",response:e,parseResponseStartTime:void 0,parseResponseEndTime:void 0,entry:void 0,endTime:void 0,metricData:{...o.metricData,status:"success",status_code:e.status,request_ids:c(e)}};!function(e,t){const n=f.get(t.url),o=n.findIndex((e=>("fetched"===e.type?e.startTime:e.entry.startTime)>=performance.now()-m));n.splice(0,o),n.push(e)}(n,e),o=n},onRequestFailed(e){o&&s({startTime:o.startTime,metricData:{...o.metricData,status:"failed",status_code:null==e?void 0:e.status,request_ids:c(e)},endTime:performance.now(),entry:void 0,parseResponseStartTime:void 0,parseResponseEndTime:void 0})},onParseResponseStart(){var e;"fetched"===(null===(e=o)||void 0===e?void 0:e.type)&&(o.parseResponseStartTime=performance.now())},onParseResponseDone(e){var t;"fetched"===(null===(t=o)||void 0===t?void 0:t.type)&&(o.parseResponseEndTime=performance.now(),o.endTime=performance.now(),w(o))},onParseResponseFailed(e){var t;"fetched"===(null===(t=o)||void 0===t?void 0:t.type)&&(o.parseResponseEndTime=performance.now(),o.endTime=performance.now(),o.metricData.status="failed",o.metricData.status_code=e.status,w(o))}}}},468928:(e,t,n)=>{n.r(t)},469425:(e,t,n)=>{n.d(t,{CY:()=>y,XI:()=>w,p6:()=>g});n(16280),n(944114),n(898992),n(354520),n(581454);var o=()=>n(319625),a=()=>n(502800),r=()=>n(763824),i=()=>n(534177),s=()=>n(720665),c=()=>n(671593),l=()=>n(638681),d=()=>n(430476),u=()=>n(68336);const p={db_true:(0,d().Gd)(!0),arg_true:(0,d().Gd)(!0),db_false:(0,d().Gd)(!1),arg_false:(0,d().Gd)(!1),db_float:10.5,arg_float:10.5,db_string:"hello",arg_string:"hello",db_null:null,arg_null:null},m=l().object({required:{db_true:l().literal(p.db_true),arg_true:l().literal(p.arg_true),db_false:l().literal(p.db_false),arg_false:l().literal(p.arg_false),db_float:l().literal(p.db_float),arg_float:l().literal(p.arg_float),db_string:l().literal(p.db_string),arg_string:l().literal(p.arg_string),db_null:l().isNull(),arg_null:l().isNull()},optional:{},exact:!0});async function f(e,t,n){const a=[...t.statements,{sql:`PRAGMA user_version = ${t.id}`,getData:!1}];if("execSqliteBatchV2"in e){const t=e,c=(0,s().hr)(`\n\t\t  SELECT\n\t\t    CASE user_version\n\t\t    WHEN ${n} THEN 1\n\t\t    ELSE 0 END AS precondition_result\n\t\t    FROM pragma_user_version() LIMIT 1\n\t\t`),l=new C(c,t);try{await(0,d().G2)({connection:l,statements:a})}catch(i){if("SqlitePreconditionFail"!==(0,o().A)(i).name)throw i;await(0,r().wR)(50)}}else{const[o]=await(0,d().G2)({connection:e,statements:[{sql:"SELECT * from pragma_user_version() LIMIT 1",getData:!0}]}),{user_version:r}=(0,d().fb)(o);if(r!==n)throw new Error(`Cannot apply migration ${t.id}: DB user_version=${r}, expected ${n}`);await(0,d().G2)({connection:e,statements:a})}}async function g(e){const{connection:t,target:n,log:o}=e,r=e.dumpSchemaFn??y,{migrations:i,endSchema:s}=n,l=s.pragmas.user_version,d=await r(t),u=d.pragmas.user_version,p=i.filter((e=>e.id>u&&e.id<=l));if(0===p.length)return void A(d,s);for(let a=u+1;a<=l;a++){const e=p[a-u-1];if(!e)throw new Error(`Migrating ${u} -> ${l}: missing migration from ${a-1} to ${a}`);if(e.id!==a)throw new Error(`Migrating ${u} -> ${l}: migration order mismatch: expected id ${a}, had id ${e.id}`)}if(n.fastForward&&0===u&&l===n.fastForward.id&&function(e){const{actual:t,expected:n}=e;return!(0,c().tf)(k(n),t)}({actual:d,expected:{pragmas:{user_version:0},tables:{},indexes:{}}})){o({level:"info",from:"sqliteSchemaHelpers",type:"attemptFastForwardMigration",data:{message:`Attempting fast-forward migration to version ${l}`}});try{await f(t,n.fastForward,0)}catch(m){throw o({level:"error",from:"sqliteSchemaHelpers",type:"fastForwardMigrationError",error:(0,a().convertErrorToLog)(m)}),m}o({level:"info",from:"sqliteSchemaHelpers",type:"successfulFastForwardMigration",data:{message:`Successfully fast-forward migrated to version ${l}`}})}else{o({level:"info",from:"sqliteSchemaHelpers",type:"attemptMigration",data:{message:`Attempting migration from ${u} to ${l}`}});for(const e of p)try{await f(t,e,e.id-1)}catch(m){throw o({level:"error",from:"sqliteSchemaHelpers",type:"migrationError",error:(0,a().convertErrorToLog)(m)}),m}o({level:"info",from:"sqliteSchemaHelpers",type:"successfulMigration",data:{message:`Successfully migrated to ${l}`}})}A(await r(t),s)}function b(){return u().F4`SELECT * FROM pragma_user_version()`}function h(e){const t=e.whereTable?u().F4`schema_type = 'table' AND (${e.whereTable})`:u().F4`schema_type = 'table'`,n=e.whereIndex?u().F4`schema_type = 'index' AND (${e.whereIndex})`:u().F4`schema_type = 'index'`,o=e.whereView?u().F4`schema_type = 'view' AND (${e.whereView})`:u().F4`schema_type = 'view'`,a=e.whereTrigger?u().F4`schema_type = 'trigger' AND (${e.whereTrigger})`:u().F4`schema_type = 'trigger'`,r=[t,n,e.views?o:void 0,e.triggers?a:void 0].filter(i().O9),s=u().F4.or(r,1);return u().F4`
WITH schema_rows AS (
	SELECT
		s.name as                   schema_name,
		s.type as                   schema_type,
		s.tbl_name as               schema_tbl_name,
		s.sql as                    schema_sql,

		COALESCE(c.cid, i.cid) as   col_cid,
		COALESCE(c.name, i.name) as col_name,
		c.type as                   col_type,
		c."notnull" as              col_notnull,
		c.dflt_value as             col_dflt_value,
		c.pk as                     col_pk,
		i.seqno as                  col_seqno
	FROM sqlite_master AS s
	LEFT JOIN pragma_table_info(s.name) AS c ON s.type = 'table' OR s.type = 'view'
	LEFT JOIN pragma_index_info(s.name) AS i ON s.type = 'index'
)
SELECT * FROM schema_rows
WHERE ${s}
ORDER BY schema_type, schema_name, col_seqno, col_cid ASC
`}function _(e,t){const n={pragmas:{user_version:e.user_version},tables:{},indexes:{}};for(const a of t){if("table"===a.schema_type){let e=n.tables[a.schema_name];e||(e={info:{name:a.schema_name,sql:a.schema_sql,tbl_name:a.schema_tbl_name,type:a.schema_type},columns:[]},n.tables[a.schema_name]=e),e.columns.push({cid:a.col_cid??-100,name:a.col_name??"",type:a.col_type,notnull:a.col_notnull??0,dflt_value:a.col_dflt_value,pk:a.col_pk??0})}if("index"===a.schema_type){let e=n.indexes[a.schema_name];e||(e={info:{name:a.schema_name,sql:a.schema_sql,tbl_name:a.schema_tbl_name,type:a.schema_type},columns:[]},n.indexes[a.schema_name]=e),e.columns.push({seqno:a.col_seqno??-100,cid:a.col_cid??-100,name:a.col_name})}if("view"===a.schema_type){var o;let e=null===(o=n.views)||void 0===o?void 0:o[a.schema_name];e||(e={info:{name:a.schema_name,sql:a.schema_sql,tbl_name:a.schema_tbl_name,type:a.schema_type},columns:[]},n.views??={},n.views[a.schema_name]=e),e.columns.push({cid:a.col_cid??-100,name:a.col_name??"",type:a.col_type,notnull:a.col_notnull??0,dflt_value:a.col_dflt_value,pk:a.col_pk??0})}"trigger"===a.schema_type&&(n.triggers??={},n.triggers[a.schema_name]={info:{name:a.schema_name,sql:a.schema_sql,tbl_name:a.schema_tbl_name,type:a.schema_type}})}return n}class v extends Error{constructor(e){super(e.message),this.name="SqliteDriverCheckError",this.actual=void 0,this.expected=void 0,this.actual=e.actual,this.expected=e.expected}}async function y(e,t){const n=[h({triggers:null==t?void 0:t.triggers,views:null==t?void 0:t.views,whereTable:null==t?void 0:t.whereTable,whereIndex:null==t?void 0:t.whereIndex,whereTrigger:null==t?void 0:t.whereTrigger,whereView:null==t?void 0:t.whereView}).asRead(),b().asRead()];null!=t&&t.skipDriverCheck||n.push(u().F4`SELECT
				1 as db_true,
				${p.arg_true} as arg_true,

				0 as db_false,
				${p.arg_false} as arg_false,

				10.5 as db_float,
				${p.arg_float} as arg_float,

				'hello' as db_string,
				${p.arg_string} as arg_string,

				NULL as db_null,
				${p.arg_null} as arg_null`.asRead());const[o,a,r]=await(0,d().G2)({connection:e,statements:n});if((null==t||!t.skipDriverCheck)&&r){const e=(0,d().fb)(r),t=(0,c().tf)(m,e,{rootVariableName:"driverCheckRow"});if(t)throw new v({message:t.message,actual:e,expected:p})}return _((0,d().fb)(a),(0,d().OJ)(o))}function w(e){return function(e,t){const n=[];if(t.resetTables)for(const o of Object.values(e.tables))n.push(u().F4`DROP TABLE IF EXISTS ${u().F4.ident(o.info.name)}`);if(t.resetIndexes)for(const o of Object.values(e.indexes))n.push(u().F4`DROP INDEX IF EXISTS ${u().F4.ident(o.info.name)}`);if(t.resetViews&&e.views)for(const o of Object.values(e.views))n.push(u().F4`DROP VIEW IF EXISTS ${u().F4.ident(o.info.name)}`);if(t.resetTriggers&&e.triggers)for(const o of Object.values(e.triggers))n.push(u().F4`DROP TRIGGER IF EXISTS ${u().F4.ident(o.info.name)}`);return t.resetUserVersion&&n.push(u().F4`PRAGMA user_version = 0`),n}(e,{resetUserVersion:!0,resetTables:!0,resetIndexes:!0,resetViews:!0,resetTriggers:!0}).map((e=>e.asWrite()))}function k(e){const t=l().object({required:{user_version:l().literal(e.pragmas.user_version)},optional:{},exact:!0}),n=e=>l().object({required:{name:l().literal(e.name),type:l().literal(e.type),tbl_name:l().literal(e.tbl_name),sql:l().union([l().string(),l().isNull()])},optional:{}}),o=e=>l().object({required:{info:n(e.info),columns:l().tuple(e.columns.map((e=>l().object({required:{cid:l().literal(e.cid),name:l().literal(e.name),type:null===e.type?l().isNull():l().caseInsensitiveLiteral(e.type),notnull:l().literal(e.notnull),dflt_value:null===e.dflt_value?l().isNull():l().literal(e.dflt_value),pk:l().literal(e.pk)},optional:{}}))))},optional:{}}),a={};for(const[l,m]of Object.entries(e.tables))a[l]=o(m);const r={};for(const[m,f]of Object.entries(e.indexes))r[m]=l().object({required:{info:n(f.info),columns:l().tuple(f.columns.map((e=>l().object({required:{seqno:l().literal(e.seqno),cid:l().literal(e.cid),name:null===e.name?l().isNull():l().literal(e.name)},optional:{}}))))},optional:{}});let i,s;if(e.views){i={};for(const[t,n]of Object.entries(e.views))i[t]=o(n)}if(e.triggers){s={};for(const[t,o]of Object.entries(e.triggers))s[t]=l().object({required:{info:n(o.info)},optional:{}})}const c=l().object({required:a,optional:{},exact:!1}),d=l().object({required:r,optional:{},exact:!1}),u=i&&l().object({required:i,optional:{},exact:!1}),p=s&&l().object({required:s,optional:{},exact:!1});return l().object({required:{pragmas:t,tables:c,indexes:d},optional:{views:u??l().isUndefined(),triggers:p??l().isUndefined()},exact:!0})}class S extends Error{constructor(e){super(e.message),this.name="SqliteSchemaMismatch",this.actual=void 0,this.expected=void 0,this.actual=e.actual,this.expected=e.expected}}function A(e,t){const n=(0,c().tf)(k(t),e,{rootVariableName:"schema"});if(n)throw new S({message:n.message,actual:e,expected:t});if(t.views&&!e.views)throw new S({message:"Expected schema with view info, but no views dumped",actual:e,expected:t});if(t.triggers&&!e.triggers)throw new S({message:"Expected schema with trigger info, but no triggers dumped",actual:e,expected:t})}class C{constructor(e,t){this.precondition=e,this.connection=t}async execSqliteBatch(e){return await this.connection.execSqliteBatchV2({batch:e,precondition:{sql:this.precondition,getData:!0}})}completelyRebuildSqliteDb(){throw new Error("Not implemented.")}}},474618:(e,t,n)=>{(async function(){const e=performance.now(),{loadErrorReporting:t}=await Promise.resolve().then(n.bind(n,365902));await t();const{loadCss:o}=await Promise.resolve().then(n.bind(n,20765));await o();const{getHtmlStreamQueueEntry:a}=await Promise.resolve().then(n.bind(n,105751));await window.LOCALE_SETUP_P,await Promise.race([a("ready"),a("bootReady")]),Promise.all([n.e(75134,"high"),n.e(58795,"high"),n.e(99223,"high"),n.e(9304,"high"),n.e(77848,"high"),n.e(55571,"high"),n.e(90978,"high"),n.e(93282,"high"),n.e(22970,"high"),n.e(90832,"high"),n.e(14310,"high"),n.e(93552,"high"),n.e(30322,"high"),n.e(47934,"high"),n.e(35266,"high"),n.e(28464,"high"),n.e(81374,"high"),n.e(33110,"high"),n.e(67608,"high"),n.e(54759,"high"),n.e(78217,"high")]).then(n.bind(n,276324));const{initializeReactivityVersion:r}=await Promise.resolve().then(n.bind(n,118165)),i=r(),{createMinimalEnvironment:s}=await Promise.resolve().then(n.bind(n,999822)),c=await s(),{loadCurrentUserId:l}=await Promise.resolve().then(n.bind(n,94442)),d=await l(c),{initializeStatsig:u}=await Promise.resolve().then(n.bind(n,904819)),p=await u({environment:c,currentUserId:d});if(d){const{initOPFS:e}=await Promise.resolve().then(n.bind(n,108873));await e({userId:d,environment:c})}const{deliverAndPersistReactivityVersion:m}=await Promise.resolve().then(n.bind(n,715668));await m(c,i);const{prefetchRequests:f}=await Promise.resolve().then(n.bind(n,249229)),g=await f({environment:c,currentUserId:d}),b=performance.now();Promise.all([n.e(58795),n.e(77848),n.e(14310)]).then(n.bind(n,388821));const{loadAppPreboot:h}=await Promise.resolve().then(n.bind(n,543190));await h();const{mainApp:_}=await Promise.all([n.e(75134,"high"),n.e(58795,"high"),n.e(99223,"high"),n.e(9304,"high"),n.e(77848,"high"),n.e(55571,"high"),n.e(90978,"high"),n.e(93282,"high"),n.e(22970,"high"),n.e(90832,"high"),n.e(14310,"high"),n.e(93552,"high"),n.e(30322,"high"),n.e(47934,"high"),n.e(35266,"high"),n.e(28464,"high"),n.e(81374,"high"),n.e(33110,"high"),n.e(67608,"high"),n.e(54759,"high"),n.e(78217,"high")]).then(n.bind(n,276324)),{transactionQueue:v,environment:y}=await _({minimalEnvironment:c,mainStartTime:e,prefetchInitiatedTime:b,prefetchCache:g,initializeStatsigResult:p,currentUserId:d}),[w,{loadConsoleHelpers:k}]=await Promise.all([Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(1797)]).then(n.bind(n,604341)),Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(1797)]).then(n.bind(n,802935))]);w.onConsoleFirstEnabled("loadConsoleHelpers",(async()=>{await k({transactionQueue:v,environment:y})}))})().catch((async e=>{let t=e;try{const{convertErrorToLog:o}=await Promise.resolve().then(n.bind(n,502800));t=o(e)}catch(e){}try{var o;const{electronApi:e}=await Promise.resolve().then(n.bind(n,675742));null==e||null===(o=e.logErrorForOffline)||void 0===o||o.call(e,{level:"error",from:"main",type:"error",error:t})}catch(e){}throw e}))},475133:(e,t,n)=>{n.r(t),n.d(t,{getMobileNativeDeviceInfo:()=>s,getMobileVersion:()=>d,isIPhoneX:()=>c,mobileNativeDeviceInfoKey:()=>i,supportsDarkMode:()=>l});var o=()=>n(711059),a=()=>n(496603),r=()=>n(155959);const i="__reactNativeDeviceInfo";function s(){const e=window[i];return e?{mobileNativeAppVersion:e.appVersion,mobileNativeCellularConnection:e.cellularConnection,mobileNativeDeviceModel:e.deviceModel,mobileNativeDeviceId:e.deviceId,mobileNativeSystemVersion:e.systemVersion,mobileNativeAndroidApiLevel:e.androidApiLevel,mobileNativeDeviceBrand:e.deviceBrand,mobileNativeDeviceManufacturer:e.deviceManufacturer,mobileNativeDeviceCarrier:e.deviceCarrier,mobileNativeDeviceCountry:e.deviceCountry,mobileNativeFreeDiskStorageBytes:e.freeDiskStorageBytes,ramSizeInGB:e.ramSizeInGB,androidKeyboardProviderPackage:e.androidKeyboardProviderPackage,is_mobile_beta:e.isMobileBeta,is_automated_qa_build:(0,r().T)("isAutomatedQABuild")}:{}}function c(){const e=window[i];return!!e&&e.deviceModel.startsWith("iPhone X")}function l(e){const t=window[i];if(!t)return!1;if(e.device.isIOS){const e=t.systemVersion.split(".");if(e.length>0)return parseInt(e[0])>=13}else{const e=t.androidApiLevel;if("number"==typeof e&&!a().yr(e))return e>=29}return!1}function d(e){const t=window[i];if(t)return(0,o().parseMobileAppVersion)(t.appVersion,e.device.isAndroid)}},479954:(e,t,n)=>{n.d(t,{G:()=>p,l:()=>l,loadCurrentUserId:()=>d});var o=()=>n(604341),a=()=>n(857639),r=()=>n(677338),i=()=>n(994310),s=()=>n(502800),c=()=>n(529543);const l="current-user-id";async function d(e){const[t,n,o]=await Promise.all([u(e),p(e),(0,r().j4)()]);return o||t||(Array.isArray(n)?n[0]:n)}async function u(e){let t;const n=i().A.get(l);n&&(t=n);const o=await p(e);if(!o||!t||o.includes(t))return t}async function p(e){const t=await c().getCookie(e,"notion_users");if(t){const e=decodeURIComponent(t);let o;try{o=JSON.parse(e)}catch(n){a().log({level:"error",from:"loginActions",type:"getUserIdsFromCookieJsonError",error:(0,s().convertErrorToLog)(n),data:{message:`decoded cookie was not valid JSON: \`${e}\``,decoded:e,cookieValue:t}})}return Array.isArray(o)?o:void 0}}(0,o().exposeDebugValue)("loadCurrentUserId",d),(0,o().exposeDebugValue)("getUserIdsFromCookie",p)},493720:(e,t,n)=>{n.d(t,{RJ:()=>i,XQ:()=>r});var o=()=>n(558842);function a(e,t){if(e.length!==t.length)return!1;for(let o=0;o<e.length;o++)if(!(0,n(821062).A)(e[o],t[o]))return!1;return!0}class r{constructor(){this.state=void 0}memo(e,t){if(this.state&&a(t,this.state.dependencies))return this.state.memoized;const n=e();return this.state={memoized:n,dependencies:t},n}}function i(){const e=new r;return function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];return e.memo((()=>(0,o().Px)(...n)),n)}}},496603:(e,t,n)=>{n.d(t,{$1:()=>T.a,$n:()=>rt.a,$r:()=>$e.a,$z:()=>L.a,B8:()=>J.a,Bj:()=>fe.a,Bq:()=>D.a,Cr:()=>N.a,D_:()=>x.a,Dw:()=>ze.a,E$:()=>V.a,Et:()=>Y.a,FF:()=>nt.a,Gv:()=>ee.a,HP:()=>se.a,HV:()=>ce.a,Hd:()=>it.a,Hn:()=>Je.a,I6:()=>S.a,Im:()=>$.a,Iy:()=>ot.a,Jt:()=>B.a,KC:()=>Ge.a,Kg:()=>oe.a,Kl:()=>P.a,LG:()=>de.a,LI:()=>Te.a,LW:()=>Oe.a,Lc:()=>h.a,Lm:()=>H.a,MI:()=>Z.a,Mp:()=>Ye.a,My:()=>we.a,NF:()=>et.a,NZ:()=>Be.a,Nt:()=>y.a,O6:()=>Ve.a,Oo:()=>ye.a,Qd:()=>ne.a,R9:()=>b.a,RK:()=>Ke.a,RT:()=>Ue.a,SL:()=>A.a,Si:()=>w.a,Sk:()=>pe.a,T9:()=>ue.a,TF:()=>Pe.a,Tj:()=>C.a,Tn:()=>G.a,Tr:()=>m.a,Uk:()=>I.a,Ul:()=>Ne.a,Up:()=>Se.a,VP:()=>We.a,XM:()=>Ee.a,Z4:()=>te.a,ZH:()=>r.a,__:()=>_.a,aD:()=>R.a,b0:()=>ae.a,bJ:()=>z.a,cJ:()=>_e.a,cb:()=>g.a,cz:()=>Le.a,d4:()=>le.a,dY:()=>p.a,di:()=>Me.a,fN:()=>Ae.a,gD:()=>Q.a,h1:()=>ge.a,hS:()=>Xe.a,hZ:()=>De.a,i2:()=>me.a,ih:()=>v.a,iv:()=>i.a,jB:()=>ke.a,jJ:()=>M.a,k4:()=>qe.a,kP:()=>j.a,kW:()=>re.a,kZ:()=>X.a,lQ:()=>he.a,mK:()=>F.a,mO:()=>o.a,mg:()=>l.a,n4:()=>W.a,nF:()=>je.a,o8:()=>c.a,oE:()=>d.a,pY:()=>ie.a,pb:()=>k.a,qE:()=>s.a,qI:()=>E.a,r4:()=>U.a,rG:()=>q.a,s:()=>Fe.a,s8:()=>He.a,sb:()=>Qe.a,se:()=>Re.a,sg:()=>f.a,sn:()=>tt.a,uk:()=>O.a,vF:()=>st,wq:()=>be.a,x4:()=>Ze.a,xQ:()=>a.a,xW:()=>u.a,xu:()=>xe.a,y1:()=>Ie.a,yT:()=>Ce.a,yU:()=>at.a,yr:()=>K.a,z7:()=>ve.a});n(254664);var o=n.n(n(413139)),a=n.n(n(984058)),r=n.n(n(314792)),i=(n(253551),n.n(n(821013))),s=n.n(n(978659)),c=n.n(n(932629)),l=n.n(n(688055)),d=(n(789647),n.n(n(183673))),u=n.n(n(492078)),p=n.n(n(137334)),m=n.n(n(574154)),f=n.n(n(738221)),g=(n(884684),n.n(n(360895))),b=n.n(n(666245)),h=n.n(n(897648)),_=(n(927537),n.n(n(976135))),v=(n(275288),n.n(n(414425))),y=n.n(n(660680)),w=n.n(n(619747)),k=n.n(n(587612)),S=n.n(n(907309)),A=n.n(n(324713)),C=n.n(n(820826)),I=n.n(n(620681)),P=n.n(n(94469)),T=n.n(n(756170)),E=n.n(n(547307)),R=n.n(n(78325)),D=n.n(n(835970)),q=n.n(n(503176)),M=n.n(n(539754)),O=n.n(n(333215)),N=n.n(n(44377)),B=n.n(n(858156)),L=n.n(n(494394)),x=(n(761448),n(400912),n.n(n(383488))),U=n.n(n(959104)),F=n.n(n(679859)),V=(n(763424),n.n(n(305287))),j=n.n(n(780191)),J=n.n(n(140866)),H=n.n(n(553812)),$=(n(3656),n(183602),n.n(n(962193))),W=n.n(n(302404)),z=n.n(n(623546)),Z=n.n(n(892297)),G=n.n(n(501882)),K=n.n(n(611741)),Q=n.n(n(269843)),X=n.n(n(305187)),Y=n.n(n(198023)),ee=n.n(n(223805)),te=n.n(n(540346)),ne=n.n(n(411331)),oe=(n(338440),n.n(n(185015))),ae=n.n(n(762216)),re=n.n(n(620249)),ie=n.n(n(338970)),se=n.n(n(395950)),ce=n.n(n(468090)),le=n.n(n(179674)),de=n.n(n(473916)),ue=n.n(n(394506)),pe=n.n(n(597551)),me=n.n(n(355083)),fe=n.n(n(150104)),ge=n.n(n(355364)),be=(n(406924),n(631684),n.n(n(136533))),he=n.n(n(263950)),_e=n.n(n(590179)),ve=n.n(n(142194)),ye=n.n(n(858059)),we=n.n(n(142877)),ke=(n(982485),n(852037),n.n(n(306498))),Se=n.n(n(244383)),Ae=n.n(n(971086)),Ce=(n(550583),n.n(n(258253))),Ie=n.n(n(623181)),Pe=n.n(n(14174)),Te=(n(912493),n.n(n(36944))),Ee=n.n(n(745620)),Re=n.n(n(33441)),De=n.n(n(63560)),qe=n.n(n(736049)),Me=(n(47091),n.n(n(737530))),Oe=n.n(n(204124)),Ne=n.n(n(333031)),Be=(n(941298),n(473054),n(859043),n.n(n(190128))),Le=n.n(n(336119)),xe=n.n(n(531126)),Ue=n.n(n(44384)),Fe=n.n(n(834921)),Ve=n.n(n(287779)),je=(n(730872),n.n(n(407350))),Je=n.n(n(6638)),He=n.n(n(231521)),$e=n.n(n(582306)),We=(n(399374),n.n(n(269884))),ze=(n(213222),n.n(n(619488))),Ze=n.n(n(375494)),Ge=n.n(n(280299)),Ke=n.n(n(999786)),Qe=n.n(n(763375)),Xe=n.n(n(950014)),Ye=n.n(n(509063)),et=n.n(n(97200)),tt=(n(273357),n.n(n(618330))),nt=(n(555808),n.n(n(891648))),ot=(n(466645),n(656625),n.n(n(715004))),at=n.n(n(265611)),rt=(n(747248),n.n(n(307082))),it=n.n(n(180962));function st(){return!0}},498212:(e,t,n)=>{n.d(t,{$4:()=>U,$h:()=>x,Ay:()=>j,Ct:()=>l,G2:()=>T,Iq:()=>S,OB:()=>F,S2:()=>E,TC:()=>L,TK:()=>D,Xw:()=>C,ZA:()=>p,_q:()=>y,bC:()=>B,c_:()=>I,dY:()=>R,gB:()=>c,iv:()=>P,lZ:()=>s,np:()=>u,ot:()=>q,rL:()=>N,rN:()=>M,sO:()=>A,uj:()=>h,w7:()=>V,zj:()=>O});n(16280),n(816573),n(878100),n(177936),n(748140),n(821903),n(491134),n(128845),n(237467),n(444732),n(979577),n(898992),n(354520),n(581454),n(964979);var o=()=>n(717344),a=n.n(n(883503)),r=()=>n(871386),i=()=>n(806080);function s(){var e;return null!==(e=globalThis.crypto)&&void 0!==e&&e.randomUUID?globalThis.crypto.randomUUID():(0,r().A)()}function c(e){const t=a()(e),n=(0,i().i)(t.split(""),2).map((e=>parseInt(e.join(""),16)));return(0,r().A)({random:n})}function l(e){const t=(0,o().sc)(e).slice(0,16);return btoa(String.fromCharCode(...t)).replace(/=+$/,"").replace(/\//g,"-").replace(/\+/g,"_")}const d=36;function u(e){return[e.substring(0,8),e.substring(8,12),e.substring(12,16),e.substring(16,20),e.substring(20,32)].join("-")}function p(e){if(e&&I(e))return u(e)}const m=new RegExp(/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i),f=new RegExp(/^[0-9A-F]{12}4[0-9A-F]{3}[89AB][0-9A-F]{15}$/i);const g=new RegExp(/^[0-9A-F]{8}-[0-9A-F]{4}-8[0-3][0-9A-F]{2}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i),b=new RegExp(/^[0-9A-F]{12}8[0-3][0-9A-F]{2}[89AB][0-9A-F]{15}$/i);function h(e){const t=e;return Boolean(t&&t.length===d&&(V.includes(t)||t.match(m)||t.match(g)))}function _(e){return Boolean(e&&e.length===d&&g.test(e))}function v(e){return Boolean(e&&e.length===A&&b.test(e))}function y(e){return _(e)||v(e)}const w=464;function k(e){const t=parseInt(e.substring(0,3),16);return!Number.isNaN(t)&&4095!==t&&t>w}function S(e){return function(e){const t=e;return Boolean(t&&t.length===d&&(V.includes(t)||m.test(t)))}(e)||function(e){return Boolean(e&&e.length===A&&f.test(e))}(e)?"uuid-v4":_(e)?"2"===e[15]||"3"===e[15]?"notion-v1d":k(e)?"notion-v1":"notion-v0":v(e)?"2"===e[13]||"3"===e[13]?"notion-v1d":k(e)?"notion-v1":"notion-v0":"unknown"}const A=32;function C(e){return e.replace(/-/g,"")}function I(e){return Boolean(e&&e.length===A&&/^[a-f0-9]*$/g.test(e))}function P(e){return h(e)||I(e)}function T(e){return h(e)?e:u(e)}function E(e){return h(e)?C(e):e}function R(e){return function(e){return(0,i().s)(e.split(",").map((e=>{const t=e.replace(/\s/g,""),n=p(t);return n||t})))}(e).filter((e=>h(e)))}function D(e){return parseInt(e.slice(-5),16)}Symbol("Without dashes");function q(e){return C(e)}"0".charCodeAt(0),"a".charCodeAt(0),"-".charCodeAt(0);const M="00000000-0000-0000-0000-000000000000",O="00000000-0000-0000-0000-000000000001",N="00000000-0000-0000-0000-000000000003",B="00000000-0000-0000-0000-000000000004",L="00000000-0000-0000-0000-000000000005",x="00000000-0000-0000-0000-000000000006",U="00000000-0000-0000-0000-000000000009",F="00000000-0000-0000-0000-00000000000a",V=[M,O,"00000000-0000-0000-0000-000000000002",N,B,L,x,"00000000-0000-0000-0000-000000000007","00000000-0000-0000-0000-000000000008",U,F,"FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF"],j=s},502492:(e,t,n)=>{n.d(t,{$J:()=>r,PW:()=>s,Q1:()=>c,it:()=>l,kb:()=>i});var o=()=>n(188835);function a(e){return e.src.endsWith("/")?e.dest.endsWith("/")?e.dest:`${e.dest}/`:e.dest.endsWith("/")?e.dest.slice(0,e.dest.length-1):e.dest}function r(e){const t=o().parse(e.httpUrl,!0);t.protocol=`${e.protocol}:`,t.port=e.includePort?t.port:null,t.host=null;const n=o().format(t);return a({src:e.httpUrl,dest:n})}function i(e){const t=o().parse(e.baseUrl),n=o().parse(e.schemeUrl,!0);n.protocol=t.protocol,n.port=t.port,n.host=t.host;const r=o().format(n);return a({src:e.schemeUrl,dest:r})}function s(e){const{url:t,isLocalHost:n}=e;return`${n?"http":"https"}://${t}`}function c(e){if(!e.url.startsWith(`${e.protocol}:`))return e.url;const t=o().parse(e.baseUrl,!0);return o().parse(e.url,!0).hostname===t.hostname?e.url:e.url.replace(`${e.protocol}://*`,`${e.protocol}:/`)}function l(e){return e.url.startsWith(e.protocol)?i({schemeUrl:e.url,baseUrl:e.domainBaseUrl}):e.url}},502800:(e,t,n)=>{n.d(t,{convertErrorToLog:()=>s,WS:()=>i,iX:()=>c});n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(581454);var o=()=>n(496603);const a=["name","message","stack","reason","data","info"];function r(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:15,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:new Set;if(o().Kg(e)||o().Et(e)||o().Lm(e))return e;if(Array.isArray(e))return n.has(e)||t<=0?[{"@":"…"}]:(n.add(e),e.map((e=>r(e,t-1,n))));if(e instanceof Set)return n.has(e)||t<=0?[{"@":"…"}]:(n.add(e),Array.from(e).map((e=>r(e,t-1,n))));if(o().bJ(e)||o().Z4(e)){if(n.has(e)||t<=0)return{"@":"…"};if(n.add(e),function(e){return Boolean("object"==typeof e&&e&&"toJSON"in e&&"function"==typeof e.toJSON)}(e))return r(e.toJSON());const o={};for(const a of Object.keys(e)){if(o[a])continue;if(a.length>0&&"_"===a[0]){o[a]="[omitted]";continue}const i=e[a];o[a]=r(i,t-1,n)}for(const i of a){if(o[i])continue;const a=e[i];a&&(o[i]=r(a,t-1,n))}return o}}function i(e,t){const{stringify:n=JSON.stringify,shouldCleanObjectsForLogging:o=!0}=t??{};try{if("object"==typeof e&&null!==e){return n(o?r(e,10):e)}return String(e)}catch(a){const e=a;return`Unable to safely convert to string: "${e.stack?e.stack:""}"`}}function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:JSON.stringify,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const o={stringify:t,shouldCleanObjectsForLogging:!0};try{if("object"==typeof e&&null!==e){const t=e,{statusCode:a,name:r,message:s,data:c,error:l,stack:d,body:u,reason:p,queryName:m}=t,f={};if(void 0!==a&&(f.statusCode=Number(a)),r&&(f.name=String(r)),s&&(f.message=i(s,o)),p&&!f.message&&n&&(f.message=i(p,o)),m&&(f.queryName=String(m)),c&&(f.miscDataString=i(c,o)),l){f.miscErrorString=i(l,o);const e=l.code;void 0!==e&&(f.code=String(e))}if(d&&(f.stack=String(d)),u)if(f.body={},"object"==typeof u&&null!==u){const e=u,{errorId:t,name:n,message:a,clientData:r}=e;t&&(f.body.errorId=String(t)),n&&(f.body.name=String(n)),a&&(f.body.message=i(a,o)),r&&(f.body.clientDataString=i(r,o))}else f.body.message=i(u,o);return f}return{miscErrorString:i(e,o)}}catch(a){const e=a;return{miscErrorString:`Unable to safely convert error to log: "${e.stack?e.stack:""}"`}}}function c(e,t){const{miscDataToConvertToString:n,...o}=e,a=o;return void 0!==n&&(a.miscDataString=i(n,t)),a}},513416:(e,t,n)=>{n.d(t,{Mh:()=>c,PX:()=>u,f1:()=>l,kX:()=>d,nG:()=>i,xB:()=>r});n(16280),n(898992),n(672577),n(581454);const o={INTERNAL_SERVER_ERROR:500,NOT_IMPLEMENTED:501,BAD_GATEWAY:502,SERVICE_UNAVAILABLE:503,GATEWAY_TIMEOUT:504,HTTP_VERSION_NOT_SUPPORTED:505,VARIANT_ALSO_NEGOTIATES:506,INSUFFICIENT_STORAGE:507,LOOP_DETECTED:508,NOT_EXTENDED:510,NETWORK_AUTHENTICATION_REQUIRED:511,FORBIDDEN:403},a={BAD_REQUEST:400,UNAUTHORIZED:401,PAYMENT_REQUIRED:402,FORBIDDEN:403,NOT_FOUND:404,METHOD_NOT_ALLOWED:405,NOT_ACCEPTABLE:406,PROXY_AUTHENTICATION_REQUIRED:407,REQUEST_TIMEOUT:408,CONFLICT:409,GONE:410,LENGTH_REQUIRED:411,PRECONDITION_FAILED:412,PAYLOAD_TOO_LARGE:413,URI_TOO_LONG:414,UNSUPPORTED_MEDIA_TYPE:415,RANGE_NOT_SATISFIABLE:416,EXPECTATION_FAILED:417,IM_A_TEAPOT:418,MISDIRECTED_REQUEST:421,UNPROCESSABLE_ENTITY:422,LOCKED:423,FAILED_DEPENDENCY:424,TOO_EARLY:425,UPGRADE_REQUIRED:426,PRECONDITION_REQUIRED:428,TOO_MANY_REQUESTS:429,UNAVAILABLE_FOR_LEGAL_REASONS:451,RESOURCE_EXPIRED:419,FETCHING:439},r={...a,...o},i={OK:200,CREATED:201,ACCEPTED:202,NO_CONTENT:204,FOUND:302,TEMPORARY_REDIRECT:307,...a,...o};r.REQUEST_TIMEOUT,r.TOO_EARLY,r.TOO_MANY_REQUESTS,r.INTERNAL_SERVER_ERROR,r.BAD_GATEWAY,r.SERVICE_UNAVAILABLE,r.GATEWAY_TIMEOUT;class s{constructor(e,t,n){this.statusCode=e,this.body=t,this.headers=n}}Object.assign({},...(0,n(534177).WP)(i).map((e=>{let[t,n]=e;return{[t]:(e,t)=>new s(n,e,t)}})));new class{constructor(e){this.processedResponse=e}get statusCode(){return this.processedResponse.statusCode}}(new Error("missing-status-code"));function c(e){return Object.values(r).includes(e)}function l(e){return Object.values(o).includes(e)}function d(e){return"statusCode"in e&&c(e.statusCode)}function u(e){return!d(e)}},520348:(e,t,n)=>{n.d(t,{Jn:()=>d,L8:()=>c,P4:()=>l,Qs:()=>u});n(16280),n(944114);var o=()=>n(851941),a=()=>n(96689),r=()=>n(870723),i=()=>n(165162),s=()=>n(765957);function c(e){let t;try{t=function(e){var t,n;const o=null===(t=s().default.state.currentSpaceStore)||void 0===t?void 0:t.getModel();if(o)return o;const a=null===(n=s().default.state.mainEditorCurrentBlockStore)||void 0===n?void 0:n.pointer.spaceId;if(a){const t=d({defaultRecordCache:e.defaultRecordCache,userId:e.currentUser.id,spaceId:a});if(t)return t}throw new Error("getCurrentSpaceModel called before page was rendered.")}(e).id}catch(n){}return t?{"x-notion-space-id":t}:{}}function l(){var e;const t=null===(e=(0,i().Pr)("mc_config"))||void 0===e?void 0:e.get("cell","disabled");return t&&"disabled"!==t?{"x-notion-cell":t}:{}}function d(e){const{defaultRecordCache:t,userId:n,spaceId:o}=e;return t.inMemoryRecordCache.getRecordModel({pointer:{table:a().NX,id:o},userId:n})}function u(e,t){const n=[],i=[],s={},c={};for(const l of e){const e=t(l);if(!e)throw new Error("Expected a record pointer");const d=(0,o().dR)(e.id);if((0,r().$t)(e.table)||(0,r().kW)(e.table)){let t;if(t=e.table===a().NX?e.id:e.spaceId,t)c[t]??=[],c[t].push(l);else if("main"!==d&&void 0!==d){const e=d.toString();s[e]??=[],s[e].push(l)}else i.push(l)}else n.push(l)}return{spaceCellObjects:c,spaceCellObjectsMissingSpaceId:i,spaceCellObjectsWithShortSpaceId:s,mainDbObjects:n}}},526147:(e,t,n)=>{n.d(t,{Ay:()=>s,F3:()=>c,Fu:()=>i,uB:()=>r});var o=()=>n(498212),a=()=>n(292588);const r={collection:!0},i="record_key",s={table:i,columnTypes:{id:a().A.UUID,version:a().A.Number,last_version:a().A.Number,space_id:a().A.UUID,scope_table:a().A.String,scope_id:a().A.UUID,parent_table:a().A.String,parent_id:a().A.UUID,record_key:a().A.String,alive:a().A.Boolean},requiredColumns:{space_id:!0,scope_table:!0,scope_id:!0,parent_table:!0,parent_id:!0,alive:!0},model:(0,n(152853).P)({RecordStore:!0})};function c(e){const{spaceId:t,parentTable:n,parentId:a}=e,[r,i]=["space",t];return(0,o().gB)(`${t}-${r}-${i}-${n}-${a}`)}},529543:(e,t,n)=>{n.r(t),n.d(t,{canSetCookie:()=>R,canShowCookieUi:()=>D,clearNotionCookiesWithNoPermission:()=>F,clearThirdPartyCookiesAndLocalStorageWithNoPermission:()=>V,getBrowserId:()=>S,getCookie:()=>k,getCookieConsentCookie:()=>w,getCookieWithoutPermissionCheck:()=>I,getExperimentDeviceId:()=>A,getGhostAdminUserId:()=>P,getHasPermissionArgs:()=>U,getPartnerStackCookie:()=>h,getPublicDomainUserId:()=>T,getS2STrackingCookie:()=>v,hasPermissionForCookie:()=>B,hasPermissionForThirdPartyService:()=>x,hasPermissionForTrackingType:()=>L,refreshNotionCookies:()=>M,removeCookie:()=>q,removeCookieConsentCookie:()=>y,setCookie:()=>f,setCookieConsentCookie:()=>g,setPartnerStackCookie:()=>b,setS2STrackingCookie:()=>_,validateHasPermissionArgs:()=>O});n(814603),n(147566),n(198721);var o=()=>n(283699),a=()=>n(857639),r=()=>n(206267),i=()=>n(839816),s=()=>n(427704),c=()=>n(711059),l=()=>n(502800),d=()=>n(780054),u=()=>n(155959),p=()=>n(675742),m=()=>n(836251);async function f(e,t){if(!R(e))return;if(!(await B(t.name,e)))return;const{mobileNative:n,device:a}=e,r={path:s().JZ.root,expires:o().T0(t.name),secure:!1,domain:o().R2(d().A),...t};if(a.isElectron&&p().electronApi&&p().electronApi.setCookie)p().electronApi.setCookie(r);else if(n&&n.setCookie)await n.setCookie(r);else{const{name:e,value:t,...n}=r;N().remove(e),N().set(e,t,{...n,expires:n.expires?new Date(n.expires):void 0})}}async function g(e,t){await f(e,{name:"notion_cookie_consent",value:JSON.stringify(t)})}async function b(e,t){await f(e,{name:"growSumoPartnerKey",value:t})}async function h(e){return k(e,"growSumoPartnerKey")}async function _(e,t){await f(e,{name:"notion_s2s_tracking_params",value:`partnerKey=${t.partnerKey}&clickId=${t.clickId}`})}async function v(e){const t=await k(e,"notion_s2s_tracking_params");if(t){const e=new URLSearchParams(t),n=e.get("partnerKey"),o=e.get("clickId");if(n&&o)return{partnerKey:n,clickId:o}}return{}}function y(){q("notion_cookie_consent")}async function w(e){return await I(e,"notion_cookie_consent")}async function k(e,t){if(await B(t,e))return I(e,t)}async function S(e){if(!(await B("notion_browser_id",e)))return;const t=await k(e,"notion_browser_id");if(t)return t;const n=r().JW();return await f(e,{name:"notion_browser_id",value:n}),n}async function A(e){const t=await S(e);if(t)return t;const n=await k(e,"notion_experiment_device_id");if(n)return n;const o=r().JW();return await f(e,{name:"notion_experiment_device_id",value:o}),o}const C=new(n(493720).XQ);async function I(e,t){if(void 0===t)return void a().log({level:"error",from:"clientCookieHelpers",type:"safeCookieRead--name-undefined",data:{message:"Trying to read cookie with undefined name"}});if(!E(e))return;const{mobileNative:n}=e;if(n&&n.api.getCookie){const e=await n.api.getCookie(t);if(!("error"in e))return e.value;a().log({level:"error",from:"clientCookieHelpers",type:`safeCookieRead--mobileNative--${t}`,error:(0,l().convertErrorToLog)(e.error)})}return C.memo((()=>N().get()),[document.cookie])[t]}async function P(e){return await k(e,"notion_ghost_admin_user_id")}async function T(e){if(e.device.isBrowser&&!(0,m().ok)())return await k(e,"notion_public_domain_user_id")}function E(e){return!(!e.device.isBrowser&&!e.device.isElectron)||!!e.mobileNative&&((0,u().T)("supportsWebViewCookies")||Boolean(e.mobileNative.api.getCookie))}function R(e){const{device:t,mobileNative:n}=e;return!!t.isBrowser||(t.isElectron?Boolean(null===p().electronApi||void 0===p().electronApi?void 0:p().electronApi.setCookie):!!n&&((0,u().T)("supportsWebViewCookies")||Boolean(n.api.setCookie)))}function D(e){const{isMobileNative:t,isIOS:n,isAndroid:o,mobileAppVersion:a}=e.device;if(!t)return!0;if(!a)return!0;const r=c().parseMobileAppVersion(a,o);let i;if(o)i=c().parseVersion("0.6.302");else{if(!n)return!0;i=c().parseVersion("0.4.403")}return!r||!i||c().isLessThanVersion(r,i)}function q(e,t){const n=t||o().R2(d().A);N().remove(e,{domain:n})}async function M(e){const{isMobileNative:t,isAndroid:n}=e.device;if(!t||!n)for(const a of o().og()){if(i().Pb.includes(a))continue;const t=await k(e,a);t&&await f(e,{name:a,value:t})}}async function O(e){const t=await U(e),n=o().v(t);n.hasError&&a().log({level:"error",type:n.errorType,from:"clientCookieHelpers",data:{miscDataToConvertToString:t}})}function N(){return n(412215)}async function B(e,t){return o().Hn({name:e,cookieService:t.cookieService})}async function L(e,t){return o().Eo({trackingType:e,cookieService:t.cookieService})}async function x(e,t){return o().nQ({service:e,cookieService:t.cookieService})}async function U(e){return o().hl({cookieService:e.cookieService})}async function F(e){return o().q1({cookieService:e.cookieService})}async function V(e){return o().bD({cookieService:e.cookieService})}(0,n(604341).exposeDebugValue)("canReadCookie",E)},530199:(e,t,n)=>{n.d(t,{IG:()=>c,P5:()=>l,ZI:()=>r,ah:()=>d,dG:()=>u,fT:()=>i,h4:()=>s,q8:()=>p,uu:()=>a,xu:()=>o});const o="deepLinkOpenNewTab",a="configureOpenInDesktopApp",r="p",i="pm",s="v",c="d",l="t",d="aq",u="defaultUserMessage",p="targetConfig"},532313:(e,t,n)=>{n.d(t,{L5:()=>a,v8:()=>r});var o=()=>n(763824);async function a(e){const{fn:t,handleError:n,retryAttemptsMS:a,retryAttemptRandomOffsetMS:r}=e;let i,s=e.initialInput;for(let l=0;l<=a.length;l+=1)try{return await t(s,l)}catch(c){const e=l>=a.length,t=n(c,e,l,s);if("return"===t.status)return t.result;if("throw"===t.status){i=t.error;break}if(e){i=c;break}const d=a[l]+Math.random()*r;await o().wR(d),"retry"===t.status&&void 0!==t.input&&(s=t.input)}throw i}function r(e){return a({fn:e,handleError:()=>({status:"retry"}),retryAttemptsMS:arguments.length>1&&void 0!==arguments[1]?arguments[1]:[1e3,2e3,5e3,1e4],retryAttemptRandomOffsetMS:arguments.length>2&&void 0!==arguments[2]?arguments[2]:200,initialInput:void 0})}},532659:(e,t,n)=>{n.d(t,{hS:()=>a,jY:()=>o});n(944114),n(898992),n(354520),n(581454),n(727413);const o={*fromValues(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];yield*t},collect(e){const t=[];for(const n of e)t.push(n);return t},*withIndex(e){let t=0;for(const n of e)yield[t,n],t++},*chunk(e,t){let n=[];for(const o of e)n.push(o),n.length>=t&&(yield n,n=[]);n.length>0&&(yield n)},*map(e,t){for(const n of e)yield t(n)},*flatten(e){for(const t of e)yield*t},*concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];for(const o of t)yield*o},*filter(e,t){for(const n of e){t(n)&&(yield n)}},*take(e,t){let n=0;for(const o of e)if(yield o,n+=1,n>=t)break},*until(e,t){for(const n of e)if(yield n,t(n))break},*cleanup(e,t){try{for(const t of e)yield t}finally{t()}},withSideEffect:(e,t)=>Object.assign({},e,{*[Symbol.iterator](){for(const n of e)t(n),yield n}}),*ensureReturned(e){try{for(const t of e)yield t}finally{var t;if(i(e))null===(t=e.return)||void 0===t||t.call(e)}},*withStats(e,t){let n=0,o=0;const a=e[Symbol.iterator]();let r=Date.now();try{for(;;){const e=a.next(),i=Date.now()-r;if(e.done){null==t||t({type:"done",length:n,totalTimeMs:o,result:e.value});break}const{value:s}=e;0===n&&(null==t||t({type:"initial",initialTimeMs:i})),n+=1,o+=i,yield{value:s,elapsedTimeMs:i},r=Date.now()}}finally{var i;null===(i=a.return)||void 0===i||i.call(a)}}},a={is:e=>r(e),async*fromValues(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];yield*t},async collect(e){const t=[];for await(const n of e)t.push(n);return t},async*withIndex(e){let t=0;for await(const n of e)yield[t,n],t++},async*chunk(e,t){let n=[];for await(const o of e)n.push(o),n.length>=t&&(yield n,n=[]);n.length>0&&(yield n)},async*map(e,t){for await(const n of e)yield t(n)},async*flatten(e){for await(const t of e)yield*t},async*concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];for(const o of t)yield*o},async*filter(e,t){for await(const n of e){t(n)&&(yield n)}},async*take(e,t){let n=0;for await(const o of e)if(yield o,n+=1,n>=t)break},async*until(e,t){for await(const n of e)if(yield n,t(n))break},async*cleanup(e,t){try{for await(const t of e)yield t}finally{await t()}},withCleanup:(e,t)=>Object.assign({},e,{async*[Symbol.asyncIterator](){try{for await(const t of e)yield t}finally{await t()}}}),withSideEffect:(e,t)=>Object.assign({},e,{async*[Symbol.asyncIterator](){for await(const n of e)await t(n),yield n}}),async*ensureReturned(e){try{for await(const t of e)yield t}finally{var t;if(i(e))await(null===(t=e.return)||void 0===t?void 0:t.call(e))}},async*withStats(e,t){let n=0,o=0;const a=e[Symbol.asyncIterator]();let r=Date.now();try{for(;;){const e=await a.next(),i=Date.now()-r;if(e.done){null==t||t({type:"done",length:n,totalTimeMs:o,result:e.value});break}const{value:s}=e;0===n&&(null==t||t({type:"initial",initialTimeMs:i})),n+=1,o+=i,yield{value:s,elapsedTimeMs:i},r=Date.now()}}finally{var i;await(null===(i=a.return)||void 0===i?void 0:i.call(a))}}};function r(e){return Symbol.asyncIterator in e}function i(e){return"next"in e}},534177:(e,t,n)=>{n.d(t,{$r:()=>_,EI:()=>o,GV:()=>v,Gv:()=>h,HB:()=>f,O:()=>i,O9:()=>d,Pe:()=>l,Vq:()=>u,WP:()=>r,Xk:()=>s,s8:()=>w,u0:()=>b,ub:()=>g,uv:()=>a,uy:()=>p,y$:()=>c,zR:()=>y});n(16280),n(898992),n(737550);function o(e){return e.length>0}const a=Object.keys,r=Object.entries;function i(e,t){return t in e}Object.assign;function s(e,t){return e.includes(t)}function c(e,t){return e.has(t)}function l(e){return null!==e}function d(e){return void 0!==e}function u(e){return null!=e}function p(e){return!u(e)}class m extends Error{}function f(e,t){if(t)throw new m(t());let n="(unknown)";try{try{n=JSON.stringify(e)??"undefined"}catch(o){n=String(e);const t=o instanceof Error?o.message:void 0;t&&(n+=` (Not serializable: ${t})`)}}catch{}throw new m(`Expected value to never occur: ${n}`)}function g(e){}function b(e){return t=>"true"in e(t)}function h(e){return"object"==typeof e&&null!==e}function _(e){return e.toString()}function v(e,t){return e}Symbol("deprecated api name"),Symbol("abstracted api name"),Symbol("info message"),Symbol("warning message");function y(e,t){return e.startsWith(t)}function w(e,t){const n={};for(const[o,a]of r(e))n[o]=t(a,o);return n}},543190:(e,t,n)=>{n.d(t,{loadAppPreboot:()=>o});async function o(){await async function(){const{default:e}=await Promise.resolve().then(n.t.bind(n,440961,19)),t=e.findDOMNode;e.findDOMNode=function(e){try{return t(e)}catch(n){return null}}}()}},547568:(e,t,n)=>{n.d(t,{$:()=>i});n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(898992),n(354520),n(581454);var o=()=>n(427704);const a={bot:!0,api:!0,login:!0,logincallback:!0,[o().JZ.appleAuthCallback.substring(1)]:!0,[o().JZ.googleAuthCallback.substring(1)]:!0,[o().JZ.microsoftAuthCallback.substring(1)]:!0,[o().JZ.samlAuthCallback.substring(1)]:!0,[o().JZ.passkeyAuthVerify.substring(1)]:!0,[o().JZ.passkeyAuthCallback.substring(1)]:!0,[o().JZ.passkeyRegistrationCallback.substring(1)]:!0,[o().JZ.passkeyRegistrationVerification.substring(1)]:!0,[o().JZ.trelloAuthCallback.substring(1)]:!0,[o().JZ.externalAuthCallback.substring(1)]:!0,[o().JZ.asanaAuthCallback.substring(1)]:!0,[o().JZ.slackAuthCallback.substring(1)]:!0,[o().JZ.externalIntegrationAuthCallback.substring(1)]:!0,logoutcallback:!0,desktop:!0,mobile:!0,android:!0,signup:!0,product:!0,logout:!0,"join-us":!0,pricing:!0,about:!0,hiring:!0,why:!0,investors:!0,server:!0,invoice:!0,invite:!0,native:!0,make:!0,onboarding:!0,unsubscribe:!0,space:!0,"tools-and-craft":!0,getStatus:!0,status:!0,jobs:!0,security:!0,join:!0,students:!0,educators:!0,work:!0,startups:!0,wiki:!0,wikis:!0,projects:!0,remote:!0,notes:!0,press:!0,blog:!0,"about-us":!0,guide:!0,help:!0,faq:!0,faqs:!0,media:!0,"media-kit":!0,install:!0,download:!0,template:!0,templates:!0,mac:!0,windows:!0,linux:!0,ios:!0,sso:!0,saml:!0,auth:!0,docs:!0,developers:!0,signed:!0,image:!0,images:!0,front:!0,tutorial:!0,customers:!0,setlocale:!0,new:!0,nativetab:!0,"notion-assets":!0},r=Object.values(o().JZ).map((e=>{if(!e.startsWith("/"))return;const t=e.split("/")[1];return t&&0!==t.length?t:void 0})).filter(n(534177).O9),i=new Set([...Object.keys(a),...r])},547726:(e,t,n)=>{n.d(t,{A:()=>a});class o extends(()=>n(757695))().Store{getInitialState(){return{pageId:void 0,lastViewTime:void 0,lastExitTime:void 0}}}const a=o},554493:(e,t,n)=>{n.d(t,{H:()=>a});const o="TempFeatureGateValueCache";function a(e){return`${o}-${e}`}},558842:(e,t,n)=>{n.d(t,{Du:()=>i,Gn:()=>m,Px:()=>s,lz:()=>p,xx:()=>l});n(898992),n(354520),n(803949);var o=n(296540),a=()=>n(496603),r=()=>n(534177);function i(e){return e||null}function s(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const o=t.filter(r().O9);return 1===o.length?o[0]:e=>{o.forEach((t=>{!function(e,t){"function"==typeof e?e(t):e&&(e.current=t)}(t,e)}))}}function c(){return o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED}function l(){var e;return Boolean(null===(e=c())||void 0===e||null===(e=e.ReactCurrentOwner)||void 0===e?void 0:e.current)}function d(e){if(e)return"string"==typeof e?e:e.displayName||e.name}function u(e){if(e.name||e.fileName)return e.fileName?`<${e.name??"unknown component"}> (at ${e.fileName}:${e.lineNumber})`:`<${e.name??"unknown component"}>`}function p(){const e=function(){var e,t,n,o,a;const r=null===(e=c())||void 0===e||null===(e=e.ReactCurrentOwner)||void 0===e?void 0:e.current;if(!r)return;const i=r._debugOwner;return{name:d(r.type),fileName:null===(t=r._debugSource)||void 0===t?void 0:t.fileName,lineNumber:null===(n=r._debugSource)||void 0===n?void 0:n.lineNumber,owner:i&&{name:d(i.type),fileName:null===(o=i._debugSource)||void 0===o?void 0:o.fileName,lineNumber:null===(a=i._debugSource)||void 0===a?void 0:a.lineNumber}}}();if(!e)return;const t=u(e),n=e.owner&&u(e.owner);return n?`${t||"<unknown component>"} in ${n}`:t||void 0}function m(e){return(0,a().aD)([e],(e=>e||[]))}},567417:(e,t,n)=>{n.r(t),n.d(t,{default:()=>a});class o extends(()=>n(757695))().Store{getInitialState(){}}const a=new o},571963:(e,t,n)=>{n.r(t),n.d(t,{getDesktopLoadOrigin:()=>h,getFilteredSubMetrics:()=>d,getSubMetricsAndMetricDataForTracking:()=>l,getTimeOriginToAppStartSubMetrics:()=>m,isPrewarmedTabInitialRender:()=>f,setDesktopLoadContext:()=>b,updateLoadCachedPageChunkCalledAt:()=>u,waitForInitialPageRenderToComplete:()=>p});var o=()=>n(410690);const a="loadOrigin",r="tabCount";var i=()=>n(534177),s=()=>n(939768),c=()=>n(961581);function l(e){const t={},{subMetricsStore:n,metricDataStore:o,OPFSMetricDataStore:a}=c().A.state;for(const[r,s]of(0,i().WP)(n.state)){if(void 0===s)continue;const{startTime:n,endTime:o}=s;if(e.isPrewarmedTab){if(o<=e.prewarmedTabNavigationStart){t[r]=0;continue}if(n<e.prewarmedTabNavigationStart&&o>e.prewarmedTabNavigationStart){t[r]=o-e.prewarmedTabNavigationStart;continue}}t[r]=o-n}return{sub_metrics:t,opfs_metrics:a.state,...o.state}}function d(){const{page_origin_to_app_start:e,app_start_to_main_start:t,main_start_to_prefetch_initiated:n,prefetch_initiated_to_statsig_bootup_blocking_start:o,statsig_bootup_blocking:a,render_start_to_render_end:r,render_end_to_await_page_chunk_start:i,await_page_chunk_start_to_await_page_chunk_end:s,await_page_chunk_end_to_browser_repaint_start:l}=c().A.state.subMetricsStore.state;return{page_origin_to_app_start:e,app_start_to_main_start:t,main_start_to_prefetch_initiated:n,prefetch_initiated_to_statsig_bootup_blocking_start:o,statsig_bootup_blocking:a,render_start_to_render_end:r,render_end_to_await_page_chunk_start:i,await_page_chunk_start_to_await_page_chunk_end:s,await_page_chunk_end_to_browser_repaint_start:l}}function u(){const{initialLoadCachedPageChunkCalledAt:e}=c().A.state;void 0===e&&c().A.update((e=>({...e,initialLoadCachedPageChunkCalledAt:performance.now()})))}async function p(){await c().A.waitUntil((()=>c().A.state.initialRenderCompleted))}function m(){const e=o().getAppStartTime();if(!e)return;let t;try{const n=performance.getEntriesByType("navigation")[0];t={time_origin_to_fetch_start:n.fetchStart,fetch_start_to_domain_lookup_start:g({from:n.fetchStart,to:n.domainLookupStart}),domain_lookup_start_to_domain_lookup_end:g({from:n.domainLookupStart,to:n.domainLookupEnd}),connect_start_to_secure_connection_start:g({from:n.connectStart,to:n.secureConnectionStart}),secure_connection_start_to_connect_end:g({from:n.secureConnectionStart,to:n.connectEnd}),connect_end_to_request_start:g({from:n.connectEnd,to:n.requestStart}),request_start_to_response_start:g({from:n.requestStart,to:n.responseStart}),response_start_to_response_end:g({from:n.responseStart,to:n.responseEnd}),response_end_to_app_start:g({from:n.responseEnd,to:e})}}catch(n){}return t}function f(e){const{historyState:t,initialRoute:n}=e.RouterStore.state;return"blank"===n.name&&1===(null==t?void 0:t.index)}function g(e){let{from:t,to:n}=e;return 0===t||0===n?0:n-t}function b(){const e=s().qn(window.location.href,a),t=s().qn(window.location.href,r);if(!e)return;const n=s().qm(window.location.href,a),o=s().qm(n,r);window.history.replaceState(window.history.state,"",o),c().A.setDesktopLoadContext(e,t)}function h(e){return e?"new_prewarmed_tab":c().A.state.desktopLoadOrigin}},584262:(e,t,n)=>{n.d(t,{B5:()=>h,CW:()=>d,GD:()=>y,b_:()=>k,ec:()=>p,hJ:()=>f,k3:()=>l,p3:()=>b,qK:()=>_,rh:()=>u,y8:()=>m,z2:()=>g});n(944114);var o=()=>n(496603),a=()=>n(763824),r=()=>n(498212),i=()=>n(939768),s=()=>n(206267),c=()=>n(249386);const l="pvs",d="qid",u="nid",p="openPageUpdatesTab";let m=function(e){return e[e.Email=0]="Email",e[e.Search=1]="Search",e[e.Sidebar=2]="Sidebar",e[e.LastVisitedPage=3]="LastVisitedPage",e[e.CopyLink=4]="CopyLink",e[e.Notification=5]="Notification",e[e.Slack=6]="Slack",e[e.ButtonAutomation=7]="ButtonAutomation",e[e.GithubLinkback=8]="GithubLinkback",e[e.SidebarBookmark=9]="SidebarBookmark",e[e.SidebarWorkspace=10]="SidebarWorkspace",e[e.SidebarShared=11]="SidebarShared",e[e.SidebarPrivate=12]="SidebarPrivate",e[e.SidebarTeam=13]="SidebarTeam",e[e.SidebarTeamBrowser=14]="SidebarTeamBrowser",e[e.SidebarQuickAdd=15]="SidebarQuickAdd",e[e.SidebarHome=16]="SidebarHome",e[e.SidebarTrash=17]="SidebarTrash",e[e.Breadcrumb=18]="Breadcrumb",e[e.PushNotification=19]="PushNotification",e[e.GoogleEvent=20]="GoogleEvent",e[e.Export=21]="Export",e[e.Import=22]="Import",e[e.Expand=23]="Expand",e[e.MentionInPage=24]="MentionInPage",e[e.LinkInPage=25]="LinkInPage",e[e.SearchQuery=26]="SearchQuery",e[e.SearchRecents=27]="SearchRecents",e[e.SidebarRecents=28]="SidebarRecents",e[e.Widget=29]="Widget",e[e.BackForward=30]="BackForward",e[e.PeekOpen=31]="PeekOpen",e[e.PeekClose=32]="PeekClose",e[e.PeekScroll=33]="PeekScroll",e[e.Direct=34]="Direct",e[e.LeaveOrRemove=35]="LeaveOrRemove",e[e.Duplicate=36]="Duplicate",e[e.Onboarding=37]="Onboarding",e[e.AppRedirect=38]="AppRedirect",e[e.NativeShareLink=39]="NativeShareLink",e[e.EditPublicPage=40]="EditPublicPage",e[e.Activity=41]="Activity",e[e.AIChat=42]="AIChat",e[e.Create=43]="Create",e[e.ChangeCollectionView=44]="ChangeCollectionView",e[e.Move=45]="Move",e[e.SwitchSpace=46]="SwitchSpace",e[e.Login=47]="Login",e[e.Restore=48]="Restore",e[e.JoinTeam=49]="JoinTeam",e[e.PersonalHomeViewAll=50]="PersonalHomeViewAll",e[e.AISlackQna=51]="AISlackQna",e[e.AISlackAssistant=52]="AISlackAssistant",e[e.AIQna=53]="AIQna",e[e.PersonalHomePage=54]="PersonalHomePage",e[e.PersonalHomeTileRecents=55]="PersonalHomeTileRecents",e[e.PersonalHomeTileTrending=56]="PersonalHomeTileTrending",e[e.PersonalHomeTileShared=57]="PersonalHomeTileShared",e[e.PersonalHomeTileTasks=58]="PersonalHomeTileTasks",e[e.PersonalHomeTileTips=59]="PersonalHomeTileTips",e[e.PersonalHomePageTasks=60]="PersonalHomePageTasks",e[e.PersonalHomeUnknown=61]="PersonalHomeUnknown",e[e.PersonalHomeNotes=62]="PersonalHomeNotes",e[e.PersonalHomeTileSimilarUsers=63]="PersonalHomeTileSimilarUsers",e[e.PageError=64]="PageError",e[e.SidebarPublicPageTemplateIncludes=65]="SidebarPublicPageTemplateIncludes",e[e.DefaultHome=66]="DefaultHome",e[e.PersonalHomeTasksCreate=67]="PersonalHomeTasksCreate",e[e.LocationProperty=68]="LocationProperty",e[e.TypedDBConversionToast=69]="TypedDBConversionToast",e[e.PersonalHomeTileTasksCreate=70]="PersonalHomeTileTasksCreate",e[e.DuplicateTemplateSwitchSpace=71]="DuplicateTemplateSwitchSpace",e[e.PersonalHomeTileTemplates=72]="PersonalHomeTileTemplates",e[e.SiteSettings=73]="SiteSettings",e[e.SiteBanner=74]="SiteBanner",e[e.CommentPublicPage=75]="CommentPublicPage",e[e.MobileInbox=76]="MobileInbox",e[e.TurnOnSprints=77]="TurnOnSprints",e[e.PersonalHomeLink=78]="PersonalHomeLink",e[e.PersonalHomeEmailLink=79]="PersonalHomeEmailLink",e[e.PersonalHomeErrorRedirect=80]="PersonalHomeErrorRedirect",e[e.PersonalHomeTileMostVisited=81]="PersonalHomeTileMostVisited",e[e.PersonalHomeTileLastEdited=82]="PersonalHomeTileLastEdited",e[e.PersonalHomeTileFavorites=83]="PersonalHomeTileFavorites",e[e.PageLayoutEditor=84]="PageLayoutEditor",e[e.AIEphemeralView=85]="AIEphemeralView",e[e.PersonalHomeCalendarAttachment=86]="PersonalHomeCalendarAttachment",e[e.PersonalHomeTileRecentsQuickAdd=87]="PersonalHomeTileRecentsQuickAdd",e[e.AiWriterDeeplink=88]="AiWriterDeeplink",e[e.PersonalHomeTileCustomDb=89]="PersonalHomeTileCustomDb",e[e.PersonalHomePageCustomDb=90]="PersonalHomePageCustomDb",e[e.PersonalHomeCustomDbCreate=91]="PersonalHomeCustomDbCreate",e[e.PersonalHomeTileCustomDbCreate=92]="PersonalHomeTileCustomDbCreate",e[e.SearchTabs=93]="SearchTabs",e[e.SidebarPrivatePane=94]="SidebarPrivatePane",e[e.SidebarSharedPane=95]="SidebarSharedPane",e[e.SiteCustomHeader=96]="SiteCustomHeader",e[e.PageTableOfContents=97]="PageTableOfContents",e[e.SearchCustomAction=98]="SearchCustomAction",e[e.DeletedFromTrashPageActions=99]="DeletedFromTrashPageActions",e[e.ChartsLaunchModal=100]="ChartsLaunchModal",e[e.SitesTooltipsTourDeeplink=101]="SitesTooltipsTourDeeplink",e[e.SitesLaunchModal=102]="SitesLaunchModal",e[e.HomeDigestEmail=103]="HomeDigestEmail",e[e.FormViewResponses=104]="FormViewResponses",e[e.FormPublicPage=105]="FormPublicPage",e[e.FormInternalPage=106]="FormInternalPage",e[e.AIGoogleDriveQna=107]="AIGoogleDriveQna",e[e.FormsLaunchModal=108]="FormsLaunchModal",e[e.WorkflowTemplateOnboarding=109]="WorkflowTemplateOnboarding",e[e.ShareMenu=110]="ShareMenu",e[e.NewWorkspaceWorkflowTemplateOnboarding=111]="NewWorkspaceWorkflowTemplateOnboarding",e[e.FormEmbed=112]="FormEmbed",e[e.TeamHomePage=113]="TeamHomePage",e[e.DemoTour=114]="DemoTour",e[e.FormResponseSnapshot=115]="FormResponseSnapshot",e[e.Workflows=116]="Workflows",e[e.CreateFormDeepLink=117]="CreateFormDeepLink",e[e.AuditLog=118]="AuditLog",e[e.MiniQuickFind=119]="MiniQuickFind",e[e.ActivityDigestEmail=120]="ActivityDigestEmail",e[e.SavePageTranslationCopy=121]="SavePageTranslationCopy",e[e.VerifiedPagesSettings=122]="VerifiedPagesSettings",e[e.EmbedPublicPageViewOriginal=123]="EmbedPublicPageViewOriginal",e[e.BrowsePage=124]="BrowsePage",e[e.WorkflowTemplatesDeeplink=125]="WorkflowTemplatesDeeplink",e[e.PublicShareLink=126]="PublicShareLink",e[e.UnifiedFeed=127]="UnifiedFeed",e[e.TeamFeed=128]="TeamFeed",e[e.UnifiedFeedScroll=129]="UnifiedFeedScroll",e[e.TeamFeedScroll=130]="TeamFeedScroll",e[e.ChartsDrilldown=131]="ChartsDrilldown",e[e.UnifiedDigestEmail=132]="UnifiedDigestEmail",e[e.MarketingMagicBox=133]="MarketingMagicBox",e[e.SharingEmptySidebarSection=134]="SharingEmptySidebarSection",e[e.AIMicrosoftTeamsQna=135]="AIMicrosoftTeamsQna",e[e.Backlinks=136]="Backlinks",e[e.TranscriptionBlockPopup=137]="TranscriptionBlockPopup",e[e.WorkspaceSettingsPeopleSectionHeader=138]="WorkspaceSettingsPeopleSectionHeader",e[e.CollectionViewBlockWorkflowControl=139]="CollectionViewBlockWorkflowControl",e[e.LibraryPage=140]="LibraryPage",e[e.PublicSiteShareViaSocialButton=141]="PublicSiteShareViaSocialButton",e[e.PublicPageSettings=142]="PublicPageSettings",e[e.PublicSiteViewerShareViaSocialButton=143]="PublicSiteViewerShareViaSocialButton",e[e.ManageDataSourcesMenu=144]="ManageDataSourcesMenu",e[e.GenericFeed=145]="GenericFeed",e[e.BrowserNavigation=146]="BrowserNavigation",e[e.Initialization=147]="Initialization",e[e.TranscriptionRecordingStoppedNotification=148]="TranscriptionRecordingStoppedNotification",e[e.PublicSiteMobileShareViaSocialButton=149]="PublicSiteMobileShareViaSocialButton",e[e.WorkspaceDiscovery=150]="WorkspaceDiscovery",e}({});function f(e){return{[m.Activity]:"activity",[m.AIChat]:"ai_chat",[m.AIMicrosoftTeamsQna]:"ai_microsoft_teams_qna",[m.AISlackAssistant]:"ai_slack_assistant",[m.AISlackQna]:"ai_slack_qna",[m.AIQna]:"ai_qna",[m.AIEphemeralView]:"ai_chat_ephemeral_view",[m.AuditLog]:"audit_log",[m.AppRedirect]:"app_redirect",[m.BackForward]:"back_forward",[m.Breadcrumb]:"breadcrumb",[m.BrowserNavigation]:"browser_navigation",[m.ButtonAutomation]:"button_automation",[m.ChangeCollectionView]:"change_collection_view",[m.CopyLink]:"copy_link",[m.Create]:"create",[m.DefaultHome]:"default_home",[m.Direct]:"direct",[m.Duplicate]:"duplicate",[m.EditPublicPage]:"edit_public_page",[m.Email]:"email",[m.Expand]:"expand",[m.Export]:"export",[m.GithubLinkback]:"github_linkback",[m.GoogleEvent]:"google_event",[m.PersonalHomeViewAll]:"personal_home_view_all",[m.Import]:"import",[m.Initialization]:"initialization",[m.JoinTeam]:"join_team",[m.LastVisitedPage]:"last_visited_page",[m.LeaveOrRemove]:"leave_or_remove",[m.LinkInPage]:"link_in_page",[m.Login]:"login",[m.MentionInPage]:"mention_in_page",[m.Move]:"move",[m.NativeShareLink]:"native_share_link",[m.Notification]:"notification",[m.Onboarding]:"onboarding",[m.PeekClose]:"peek_close",[m.PeekOpen]:"peek_open",[m.PeekScroll]:"peek_scroll",[m.PushNotification]:"push_notification",[m.Restore]:"restore",[m.Search]:"search",[m.SearchQuery]:"search_query",[m.SearchRecents]:"search_recents",[m.SearchCustomAction]:"search_custom_action",[m.Sidebar]:"sidebar",[m.SidebarBookmark]:"sidebar_bookmark",[m.SidebarHome]:"sidebar_home",[m.SidebarPrivate]:"sidebar_private",[m.SidebarQuickAdd]:"sidebar_quick_add",[m.SidebarRecents]:"sidebar_recents",[m.SidebarShared]:"sidebar_shared",[m.SidebarTeam]:"sidebar_team",[m.SidebarTeamBrowser]:"sidebar_team_browser",[m.SidebarTrash]:"sidebar_trash",[m.SidebarWorkspace]:"sidebar_workspace",[m.Slack]:"slack",[m.SidebarPublicPageTemplateIncludes]:"sidebar_public_page_template_includes",[m.SwitchSpace]:"switch_space",[m.Widget]:"widget",[m.PersonalHomeTileRecents]:"home_tile_recents",[m.PersonalHomeTileTrending]:"home_tile_trending",[m.PersonalHomeTileShared]:"home_tile_shared",[m.PersonalHomeTileTasks]:"home_tile_tasks",[m.PersonalHomeTileTips]:"home_tile_tips",[m.PersonalHomePage]:"home",[m.PersonalHomePageTasks]:"home_tasks",[m.PersonalHomeUnknown]:"home_unknown",[m.PersonalHomeTileSimilarUsers]:"home_tile_similar_users",[m.PersonalHomeNotes]:"home_tile_notes",[m.PersonalHomeTasksCreate]:"home_tasks_create",[m.PersonalHomeTileTasksCreate]:"home_tile_tasks_create",[m.PersonalHomeCustomDbCreate]:"home_custom_db_create",[m.PersonalHomeTileCustomDbCreate]:"home_tile_custom_db_create",[m.PageError]:"page_error",[m.LocationProperty]:"location_property",[m.TypedDBConversionToast]:"typed_db_conversion_toast",[m.DuplicateTemplateSwitchSpace]:"duplicate_template_switch_space",[m.PersonalHomeTileTemplates]:"home_templates",[m.SiteSettings]:"published_site_settings",[m.SiteBanner]:"published_site_banner",[m.CommentPublicPage]:"comment_public_page",[m.MobileInbox]:"mobile_inbox",[m.TurnOnSprints]:"turn_on_sprints",[m.PersonalHomeLink]:"home_link",[m.PersonalHomeEmailLink]:"home_email_link",[m.PersonalHomeErrorRedirect]:"home_error_redirect",[m.PersonalHomeTileMostVisited]:"home_tile_most_visited",[m.PersonalHomeTileLastEdited]:"home_tile_last_edited",[m.PersonalHomeTileFavorites]:"home_tile_favorites",[m.PageLayoutEditor]:"page_layout_editor",[m.PersonalHomeCalendarAttachment]:"home_calendar_attachment",[m.PersonalHomeTileRecentsQuickAdd]:"home_tile_recents_quick_add",[m.PersonalHomeTileCustomDb]:"home_tile_custom_db",[m.PersonalHomePageCustomDb]:"home_custom_db",[m.AiWriterDeeplink]:"ai_writer_deeplink",[m.SearchTabs]:"search_tabs",[m.SidebarPrivatePane]:"sidebar_private_pane",[m.SidebarSharedPane]:"sidebar_shared_pane",[m.SiteCustomHeader]:"site_custom_header",[m.PageTableOfContents]:"page_table_of_contents",[m.DeletedFromTrashPageActions]:"deleted_from_trash_page_actions",[m.ChartsLaunchModal]:"charts_launch_modal",[m.FormsLaunchModal]:"forms_launch_modal",[m.SitesTooltipsTourDeeplink]:"sites_tooltips_tour_deeplink",[m.SitesLaunchModal]:"sites_launch_modal",[m.HomeDigestEmail]:"home_digest_email",[m.FormViewResponses]:"form_view_responses",[m.FormPublicPage]:"form_public_page",[m.FormInternalPage]:"form_internal_page",[m.AIGoogleDriveQna]:"ai_qna_google_drive",[m.WorkflowTemplateOnboarding]:"workflow_template_onboarding",[m.ShareMenu]:"share_menu",[m.NewWorkspaceWorkflowTemplateOnboarding]:"new_workspace_workflow_template_onboarding",[m.FormEmbed]:"form_embed",[m.DemoTour]:"demo_tour",[m.TeamHomePage]:"team_home_page",[m.FormResponseSnapshot]:"form_response_snapshot",[m.Workflows]:"workflows",[m.CreateFormDeepLink]:"create_form_deep_link",[m.MiniQuickFind]:"mini_quick_find",[m.ActivityDigestEmail]:"activity_digest_email",[m.SavePageTranslationCopy]:"save_page_translation_copy",[m.VerifiedPagesSettings]:"verified_pages_settings",[m.EmbedPublicPageViewOriginal]:"embed_public_page_view_original",[m.BrowsePage]:"browse_page",[m.WorkflowTemplatesDeeplink]:"workflow_templates_deeplink",[m.MarketingMagicBox]:"marketing_magic_box",[m.PublicShareLink]:"public_share_link",[m.UnifiedFeed]:"unified_feed",[m.TeamFeed]:"team_feed",[m.UnifiedFeedScroll]:"unified_feed_scroll",[m.TeamFeedScroll]:"team_feed_scroll",[m.ChartsDrilldown]:"charts_drilldown",[m.UnifiedDigestEmail]:"unified_digest_email",[m.SharingEmptySidebarSection]:"sharing_empty_sidebar_section",[m.Backlinks]:"backlinks",[m.TranscriptionBlockPopup]:"transcription_block_popup",[m.WorkspaceSettingsPeopleSectionHeader]:"workspace_settings_people_section_header",[m.PublicSiteShareViaSocialButton]:"public_site_share_via_social_button",[m.PublicSiteViewerShareViaSocialButton]:"public_site_viewed_share_via_social_button",[m.CollectionViewBlockWorkflowControl]:"collection_view_block_workflow_control",[m.LibraryPage]:"library_page",[m.PublicPageSettings]:"public_page_settings",[m.ManageDataSourcesMenu]:"manage_data_sources_menu",[m.GenericFeed]:"generic_feed",[m.TranscriptionRecordingStoppedNotification]:"transcription_recording_stopped_notification",[m.PublicSiteMobileShareViaSocialButton]:"public_site_mobile_share_via_social_button",[m.WorkspaceDiscovery]:"workspace_discovery"}[e]}function g(e){return{[m.Activity]:!1,[m.AIChat]:!1,[m.AIMicrosoftTeamsQna]:!1,[m.AISlackAssistant]:!0,[m.AISlackQna]:!0,[m.AIQna]:!1,[m.AIEphemeralView]:!1,[m.AppRedirect]:!1,[m.BackForward]:!1,[m.Breadcrumb]:!1,[m.BrowserNavigation]:!1,[m.ButtonAutomation]:!1,[m.ChangeCollectionView]:!1,[m.CopyLink]:!0,[m.Create]:!1,[m.DefaultHome]:!1,[m.Direct]:!0,[m.Duplicate]:!1,[m.EditPublicPage]:!1,[m.Email]:!0,[m.Expand]:!1,[m.Export]:!0,[m.GithubLinkback]:!0,[m.GoogleEvent]:!0,[m.Import]:!0,[m.Initialization]:!1,[m.JoinTeam]:!1,[m.LastVisitedPage]:!1,[m.LeaveOrRemove]:!1,[m.LinkInPage]:!1,[m.Login]:!1,[m.MentionInPage]:!1,[m.Move]:!1,[m.NativeShareLink]:!0,[m.Notification]:!1,[m.Onboarding]:!1,[m.PeekClose]:!1,[m.PeekOpen]:!1,[m.PeekScroll]:!1,[m.PersonalHomeViewAll]:!1,[m.PushNotification]:!0,[m.Restore]:!1,[m.Search]:!1,[m.SearchCustomAction]:!1,[m.SearchQuery]:!1,[m.SearchRecents]:!1,[m.Sidebar]:!1,[m.SidebarBookmark]:!1,[m.SidebarHome]:!1,[m.SidebarPrivate]:!1,[m.SidebarQuickAdd]:!1,[m.SidebarRecents]:!1,[m.SidebarShared]:!1,[m.SidebarTeam]:!1,[m.SidebarTeamBrowser]:!1,[m.SidebarTrash]:!1,[m.SidebarWorkspace]:!1,[m.SidebarPublicPageTemplateIncludes]:!1,[m.Slack]:!0,[m.SwitchSpace]:!1,[m.Widget]:!0,[m.PersonalHomePage]:!1,[m.PersonalHomeTasksCreate]:!1,[m.PersonalHomeTileTasksCreate]:!1,[m.PersonalHomeCustomDbCreate]:!1,[m.PersonalHomeTileCustomDbCreate]:!1,[m.PersonalHomeTileRecents]:!1,[m.PersonalHomeTileTrending]:!1,[m.PersonalHomeTileShared]:!1,[m.PersonalHomeTileTasks]:!1,[m.PersonalHomeTileTips]:!1,[m.PersonalHomePageTasks]:!1,[m.PersonalHomeUnknown]:!1,[m.PersonalHomeTileSimilarUsers]:!1,[m.PersonalHomeTileRecentsQuickAdd]:!1,[m.PersonalHomeNotes]:!1,[m.PageError]:!1,[m.LocationProperty]:!1,[m.TypedDBConversionToast]:!1,[m.DuplicateTemplateSwitchSpace]:!1,[m.PersonalHomeTileTemplates]:!1,[m.SiteSettings]:!1,[m.SiteBanner]:!1,[m.CommentPublicPage]:!1,[m.MobileInbox]:!1,[m.TurnOnSprints]:!1,[m.PersonalHomeLink]:!1,[m.PersonalHomeEmailLink]:!1,[m.PersonalHomeErrorRedirect]:!1,[m.PersonalHomeTileMostVisited]:!1,[m.PersonalHomeTileLastEdited]:!1,[m.PersonalHomeTileFavorites]:!1,[m.PersonalHomeTileCustomDb]:!1,[m.PersonalHomePageCustomDb]:!1,[m.PageLayoutEditor]:!1,[m.PersonalHomeCalendarAttachment]:!1,[m.AiWriterDeeplink]:!1,[m.SearchTabs]:!1,[m.SidebarPrivatePane]:!1,[m.SidebarSharedPane]:!1,[m.SiteCustomHeader]:!1,[m.PageTableOfContents]:!1,[m.DeletedFromTrashPageActions]:!1,[m.ChartsLaunchModal]:!1,[m.SitesTooltipsTourDeeplink]:!1,[m.SitesLaunchModal]:!1,[m.HomeDigestEmail]:!0,[m.FormViewResponses]:!1,[m.FormPublicPage]:!1,[m.FormInternalPage]:!1,[m.AIGoogleDriveQna]:!1,[m.FormsLaunchModal]:!1,[m.WorkflowTemplateOnboarding]:!1,[m.ShareMenu]:!1,[m.NewWorkspaceWorkflowTemplateOnboarding]:!1,[m.FormEmbed]:!1,[m.DemoTour]:!1,[m.TeamHomePage]:!1,[m.FormResponseSnapshot]:!1,[m.Workflows]:!1,[m.CreateFormDeepLink]:!1,[m.AuditLog]:!1,[m.MiniQuickFind]:!1,[m.ActivityDigestEmail]:!0,[m.SavePageTranslationCopy]:!1,[m.VerifiedPagesSettings]:!1,[m.EmbedPublicPageViewOriginal]:!0,[m.BrowsePage]:!1,[m.WorkflowTemplatesDeeplink]:!0,[m.MarketingMagicBox]:!0,[m.PublicShareLink]:!1,[m.UnifiedFeed]:!1,[m.TeamFeed]:!1,[m.UnifiedFeedScroll]:!1,[m.TeamFeedScroll]:!1,[m.ChartsDrilldown]:!1,[m.UnifiedDigestEmail]:!0,[m.SharingEmptySidebarSection]:!1,[m.Backlinks]:!1,[m.TranscriptionBlockPopup]:!1,[m.WorkspaceSettingsPeopleSectionHeader]:!1,[m.PublicSiteShareViaSocialButton]:!0,[m.PublicSiteViewerShareViaSocialButton]:!0,[m.CollectionViewBlockWorkflowControl]:!1,[m.LibraryPage]:!1,[m.PublicPageSettings]:!1,[m.ManageDataSourcesMenu]:!1,[m.GenericFeed]:!1,[m.TranscriptionRecordingStoppedNotification]:!1,[m.PublicSiteMobileShareViaSocialButton]:!0,[m.WorkspaceDiscovery]:!1}[e]}function b(e,t,n,a){return i().O$(e,{...a??{},[c().BM]:o().sb(t),[l]:n})}function h(e){const t=[],n=[["initial_","setOnce"],["","set"]];for(const[o,a]of n)for(const n of["notion_",""])for(const[r,i]of Object.entries(e))t.push({name:`${o}${n}${r}`,value:i,operation:a});return t}class _{constructor(e){this.invoked=void 0,this.completed=(0,a().yX)(),this.loader=e}async load(e){const{config:t,isEnabled:n,endpoint:o,deviceId:r,disableCookies:i}=e;if(this.invoked)return this.invoked.promise;this.invoked=(0,a().yX)();const s=t&&n?await this.loader({config:t,endpoint:o,deviceId:r,disableCookies:i}):void 0;return this.invoked.resolve(s),this.completed.resolve(s),s}async getInstance(){return this.completed.promise}}const v={anonymousDeviceId:w()};function y(){v.anonymousDeviceId=w()}function w(){return`a_${(0,r().ot)(s().JW())}`}function k(e){return e?(0,r().S2)(e):v.anonymousDeviceId}},590265:(e,t,n)=>{n.d(t,{LY:()=>f,Ln:()=>i,T6:()=>c,YD:()=>s,Zc:()=>u,Zi:()=>m,gQ:()=>g,gg:()=>b,xv:()=>l});n(814603),n(147566),n(198721);var o=()=>n(534177),a=()=>n(427704);const r=["creators","categories","templates","search","collections"],i=["life","school","work","personal"];function s(e,t){const n=p(e,t);return`${n.pathname}${n.search}`}function c(e){for(const t of r)if(e===t)return!0;return!1}function l(e){const{routePageType:t,slug:n}=e;switch(t){case"categories":if(n)return i.includes(n)?"templateTopicPage":"templateCategoryPage";break;case"collections":return n?"templateCollectionPage":"templateCollectionsPage";case"creators":return n?"templateCreatorPage":"templateCreatorsPage";case"templates":if(n)return"templateDetailPage";break;case"search":return"templateSearchPage";default:return(0,o().HB)(t)}}const d=["templates"];function u(e){return d.includes(e)}function p(e,t){let n=`${e.domainBaseUrl}/${t.name}`;t.pageType&&(n+=`/${t.pageType}`),t.slug&&(n+=`/${t.slug}`);const o=new URL(n);return t.query&&o.searchParams.append("query",encodeURIComponent(t.query)),t.preview&&o.searchParams.append("preview",encodeURIComponent("true")),t.tags&&o.searchParams.append("tags",t.tags),t.groupedTags&&o.searchParams.append("groupedTags",t.groupedTags),t.showMarketplaceCheckoutModal&&o.searchParams.append("checkout","true"),t.showBuyerProfileModal&&o.searchParams.append("bp","true"),t.orderBy&&o.searchParams.append("orderBy",encodeURIComponent(t.orderBy)),t.paid&&o.searchParams.append("paid",encodeURIComponent(t.paid)),t.qualification&&o.searchParams.append("qualification",encodeURIComponent(t.qualification)),t.locales&&o.searchParams.append("locales",encodeURIComponent(t.locales)),t.crumbs&&o.searchParams.append("cr",encodeURIComponent(t.crumbs)),o}function m(e,t){return p(e,t).toString()}function f(e){const{docMatch:t,routeName:n,parsed:o}=e,{pageType:r,slug:i}=t,s=o.query.preview?decodeURIComponent(o.query.preview):void 0,c=o.query.bp?decodeURIComponent(o.query.bp):void 0;return[{name:n,pageType:r,slug:i,tags:o.query.tags?decodeURIComponent(o.query.tags):void 0,groupedTags:o.query.groupedTags?decodeURIComponent(o.query.groupedTags):void 0,orderBy:o.query.orderBy?decodeURIComponent(o.query.orderBy):void 0,madeBy:o.query.madeBy?decodeURIComponent(o.query.madeBy):void 0,paid:o.query.paid?decodeURIComponent(o.query.paid):void 0,crumbs:o.query.cr?decodeURIComponent(o.query.cr):void 0,preview:"true"===s,showBuyerProfileModal:"true"===c,locales:o.query.locales?decodeURIComponent(o.query.locales):void 0,showMarketplaceCheckoutModal:"true"===(o.query.checkout?decodeURIComponent(o.query.checkout):void 0),coupon:o.query.coupon?decodeURIComponent(o.query.coupon):void 0,from:o.query.from?decodeURIComponent(o.query.from):void 0,showReviewModal:"true"===(o.query.review?decodeURIComponent(o.query.review):void 0),templateGalleryItem:o.query.tg?decodeURIComponent(o.query.tg):void 0},"gallery"===n?a().JZ.gallery:a().JZ.marketplace]}function g(e){const{docMatch:t,routeName:n,parsed:o}=e,r=o.query.bp?decodeURIComponent(o.query.bp):void 0,{pageType:i}=t;return[{name:n,pageType:i,query:o.query.query,orderBy:o.query.orderBy,qualification:o.query.qualification,showBuyerProfileModal:"true"===r,showMarketplaceCheckoutModal:"true"===(o.query.checkout?decodeURIComponent(o.query.checkout):void 0),showReviewModal:"true"===(o.query.review?decodeURIComponent(o.query.review):void 0),templateGalleryItem:o.query.tg?decodeURIComponent(o.query.tg):void 0},"gallery"===n?a().JZ.gallery:a().JZ.marketplace]}function b(e){const{routeName:t,parsed:n}=e;return[{name:t,showBuyerProfileModal:"true"===(n.query.bp?decodeURIComponent(n.query.bp):void 0),showMarketplaceCheckoutModal:"true"===(n.query.checkout?decodeURIComponent(n.query.checkout):void 0),showReviewModal:"true"===(n.query.review?decodeURIComponent(n.query.review):void 0),templateGalleryItem:n.query.tg?decodeURIComponent(n.query.tg):void 0},"gallery"===t?a().JZ.gallery:a().JZ.marketplace]}},591779:(e,t,n)=>{n.d(t,{$x:()=>i,MR:()=>a,ZC:()=>r,_$:()=>s,b_:()=>d,lW:()=>l,wb:()=>u});var o=n(296540);function a(e,t){if(!e||!t)return!1;if(e.length!==t.length)return!1;if(e===t)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function r(e,t){const n=(0,o.useRef)(t);return(0,o.useEffect)((()=>{n.current=e}),[e]),n.current}function i(e,t){const n=!t(r(e),e);return(0,o.useDebugValue)(n),n}function s(e,t){const n=!t(r(e,e),e);return(0,o.useDebugValue)(n),n}function c(e,t){const n=(0,o.useRef)(0);return i(e,t)&&n.current++,(0,o.useDebugValue)(n.current),n.current}function l(e,t,n){return(0,o.useDebugValue)(void 0===t?"disabled":`${t}ms`),d(e,e,t,n)}function d(e,t,n,a){const[r,i]=(0,o.useState)(t),s=c(e,a);return(0,o.useDebugValue)(void 0===n?"disabled":`${n}ms`),(0,o.useEffect)((()=>{if(void 0===n)return;const t=window.setTimeout((()=>{i(e)}),n);return()=>{window.clearTimeout(t)}}),[n,s]),void 0===n?e:r}function u(e,t,n){const[a,r]=(0,o.useState)(e),i=(0,o.useRef)(),s=(0,o.useRef)(null),l=(0,o.useRef)(0),d=c(e,n);return(0,o.useDebugValue)(void 0===t?"disabled":`${t}ms`),(0,o.useEffect)((()=>{if(i.current||void 0===t)s.current=e,l.current=!0;else{r(e);const n=()=>{l.current?(l.current=!1,r(s.current),i.current=window.setTimeout(n,t)):i.current=void 0};i.current=window.setTimeout(n,t)}}),[t,d]),(0,o.useEffect)((()=>()=>{i.current&&clearTimeout(i.current)}),[]),void 0===t?e:a}},592328:(e,t,n)=>{n.d(t,{A:()=>o});n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698);const o=class{constructor(){this.listeners=new Set}addListener(e){this.listeners.add(e)}addOnceListener(e){var t=this;const n=function(){e(...arguments),t.removeListener(n)};this.addListener(n)}removeListener(e){this.listeners.delete(e)}emit(){for(const e of this.listeners)e(...arguments)}listenerCount(){return this.listeners.size}}},603250:(e,t,n)=>{n.d(t,{e:()=>a});n(898992),n(737550);const o=["4r5e","5h1t","5hit","a55","ar5e","arrse","arse","ass-fucker","assfucker","assfukka","asshole","assholes","asswhole","a_s_s","b!tch","b00bs","b17ch","b1tch","ballbag","ballsack","beastial","beastiality","bellend","bestial","bestiality","bi+ch","biatch","bitch","bitcher","bitchers","bitches","bitchin","bitching","blow job","blowjob","blowjobs","boiolas","bollock","bollok","boner","boob","boobs","booobs","boooobs","booooobs","booooooobs","buceta","bugger","bunny fucker","butthole","buttmunch","buttplug","c0ck","c0cksucker","carpet muncher","cawk","chink","cipa","cl1t","clit","clitoris","clits","cnut","cock","cock-sucker","cockface","cockhead","cockmunch","cockmuncher","cocks","cocksuck ","cocksucked ","cocksucker","cocksucking","cocksucks ","cocksuka","cocksukka","cokmuncher","coksucka","coon","cummer","cumming","cums","cumshot","cunilingus","cunillingus","cunnilingus","cunt","cuntlick ","cuntlicker ","cuntlicking ","cunts","cyalis","cyberfuc","cyberfuck ","cyberfucked ","cyberfucker","cyberfuckers","cyberfucking ","d1ck","dickhead","dildo","dildos","dirsa","dog-fucker","donkeyribber","doosh","duche","dyke","ejaculate","ejaculated","ejaculates ","ejaculating ","ejaculatings","ejaculation","ejakulate","f u c k","f u c k e r","f4nny","fag","fagging","faggitt","faggot","faggs","fagot","fagots","fags","fannyflaps","fannyfucker","fanyy","fatass","fcuk","fcuker","fcuking","feck","fecker","felching","fellate","fellatio","fingerfuck ","fingerfucked ","fingerfucker ","fingerfuckers","fingerfucking ","fingerfucks ","fistfuck","fistfucked ","fistfucker ","fistfuckers ","fistfucking ","fistfuckings ","fistfucks ","fook","fooker","fuck","fucka","fucked","fucker","fuckers","fuckhead","fuckheads","fuckin","fucking","fuckings","fuckingshitmotherfucker","fuckme ","fucks","fuckwhit","fuckwit","fudge packer","fudgepacker","fuk","fuker","fukker","fukkin","fuks","fukwhit","fukwit","fux","fux0r","f_u_c_k","gangbang","gangbanged ","gangbangs ","gaylord","gaysex","goatse","god-dam","god-damned","goddamn","goddamned","hardcoresex ","heshe","hoar","hoare","hoer","homo","horniest","horny","hotsex","jack-off ","jackoff","jerk-off ","jism","jiz ","jizm ","jizz","knobead","knobed","knobend","knobhead","knobjocky","knobjokey","kock","kummer","kumming","kunilingus","l3i+ch","l3itch","labia","lmfao","m0f0","m0fo","m45terbate","ma5terb8","ma5terbate","masochist","master-bate","masterb8","masterbat*","masterbat3","masterbate","masterbation","masterbations","masturbate","mo-fo","mof0","mofo","mothafuck","mothafucka","mothafuckas","mothafuckaz","mothafucked ","mothafucker","mothafuckers","mothafuckin","mothafucking ","mothafuckings","mothafucks","mother fucker","motherfuck","motherfucked","motherfucker","motherfuckers","motherfuckin","motherfucking","motherfuckings","motherfuckka","motherfucks","muff","mutha","muthafecker","muthafuckker","muther","mutherfucker","n1gga","n1gger","nazi","nigg3r","nigg4h","nigga","niggah","niggas","niggaz","nigger","niggers ","nob jokey","nobhead","nobjocky","nobjokey","numbnuts","nutsack","orgasim ","orgasims ","orgasm","orgasms ","p0rn","penisfucker","phonesex","phuck","phuked","phuking","phukked","phukking","phuks","phuq","pigfucker","pimpis","pissed","pisser","pissers","pisses ","pissflaps","pissin ","pissing","pissoff ","porn","porno","pornography","pornos","pron","pube","pusse","pussi","pussies","pussy","pussys ","retard","rimjaw","rimming","s hit","s.o.b.","sadist","schlong","scroat","scrote","sh!+","sh!t","sh1t","shagger","shaggin","shagging","shemale","shi+","shit","shitdick","shite","shited","shitey","shitfuck","shitfull","shithead","shiting","shitings","shits","shitted","shitter","shitters ","shitting","shittings","shitty ","skank","slut","sluts","smegma","smut","son-of-a-bitch","s_h_i_t","t1tt1e5","t1tties","testical","titfuck","tittie5","tittiefucker","titties","tittyfuck","tittywank","titwank","tosser","tw4t","twat","twathead","twatty","twunt","twunter","v14gra","v1gra","viagra","w00se","wank","wanker","wanky","whoar","whore","willies","xrated","xxx"].map((e=>e.toLowerCase()));function a(e){const t=e.toLowerCase();return o.some((e=>t.includes(e)))}},604341:(e,t,n)=>{n.r(t),n.d(t,{exposeDebugGetter:()=>i,exposeDebugInstance:()=>s,exposeDebugValue:()=>r,isConsoleEnabled:()=>u,onConsoleFirstEnabled:()=>l,unregisterConsoleFirstEnabledCallback:()=>d});const o={};async function a(){localStorage.setItem("__console","true"),o.isEnabled=!0;for(const[e,t]of c)await t();c.clear()}function r(e,t){return o.isEnabled?(o[e]=t,t):t}function i(e,t){o.isEnabled&&Object.defineProperty(o,e,{get:t})}function s(e,t){0}"undefined"!=typeof window?(o.isEnabled=function(){var e;if(navigator.webdriver)return!0;if(null!==localStorage.getItem("__console"))return"true"===localStorage.getItem("__console");0;if("CONFIG"in window&&"object"==typeof window.CONFIG&&null!==(e=window.CONFIG)&&void 0!==e&&e.isAdminMode)return!0;return!1}(),window.__console=o,o.enable=async()=>{console.log("Loading __console..."),await a(),console.log("__console enabled. You may need to refresh the page to access some __console functionality.")},o.enableAndReload=()=>{a(),window.location.reload(),console.log("Reloading...")},o.disable=()=>{localStorage.setItem("__console","false"),o.isEnabled=!1,console.log("__console disabled, please refresh the page for the setting to take effect.")}):void 0!==n.g&&(n.g.__console=o,o.isEnabled=!0);const c=new Map;function l(e,t){u()?t():c.set(e,t)}function d(e){c.delete(e)}function u(){return Boolean(o.isEnabled)}r("debugCaptureError",(async function(e){try{return{value:await e()}}catch(t){return{error:t}}}))},615234:(e,t,n)=>{n.d(t,{Gq:()=>c,TD:()=>o,WY:()=>f,Xb:()=>a,i5:()=>p,lG:()=>s,m1:()=>d,nD:()=>i,pT:()=>r,uu:()=>u,wK:()=>l,xq:()=>m});const o=1e3,a=60*o,r=60*a,i=24*r,s=7*i,c=365*i,l=Number(1),d=60*l,u=60*d,p=24*u,m=365*p;function f(e){return Math.round(Math.floor(e/a)*a)}},617871:(e,t,n)=>{n.d(t,{$1:()=>O,AC:()=>P,CX:()=>q,E8:()=>w,EY:()=>E,F6:()=>u,Fo:()=>h,Fv:()=>k,Hd:()=>c,Iy:()=>v,KB:()=>B,MN:()=>g,Nu:()=>D,XF:()=>S,XO:()=>R,ZY:()=>C,a1:()=>T,bh:()=>a,d0:()=>p,e1:()=>l,eL:()=>N,eg:()=>y,io:()=>o,k0:()=>s,me:()=>d,mi:()=>L,mj:()=>M,mn:()=>_,ob:()=>r,tU:()=>I,wK:()=>b,zG:()=>i});const o="c0d82879-3eea-4c21-b0d1-42a775022c4b",a="cdc46cd9-f0e9-48fd-b3aa-18481098e29e",r="b759b994-5c32-4268-bbb0-41f435abb8d9",i="7f5d87f7-be5f-45ee-83d3-b9af153f0ee0",s="e0dbc237-dcea-4ed7-8de0-bfc1ea6ac768",c="2e19d8ee-fc61-48c1-be14-07a3aff43542",l="3a6a5bc3-6b3a-467e-9fc5-de4b6024a0e1",d="15d02cbd-b82a-4ccd-928e-f2ff0806f9ba",u="ccb795df-ffbb-414b-9a98-15a5cfb3297c",p="249a0797-abfd-4ee9-b8c3-30c32489eb2b",m="042f18e5-d630-4b45-964d-583e1d62b602",f="59487270-7a29-4dff-b0a1-3fc4b0f08fa8",g="7df961b7-66d9-4fb1-a963-ebcd171d6148",b="45c081d8-e28b-43d0-87be-c373ca160336",h="e719abb7-effd-42f1-aa3f-0f449710fdc0",_="8e608b04-c0f8-4a71-8a38-b33ea0464d4d",v="2ee61629-4bb5-451e-8602-ff69872ed50e",y="9a9a6abb-ac0a-4cc8-9041-7626f934465c",w="e4c2ee0b-cd35-4e55-9e27-87690ae043f8",k="e8db4f1e-32ef-4588-8b7f-dcbf2e05e21f",S="adc9a52f-3aab-444b-83b7-e73200ee0279",A="00000000-0000-4000-8000-000000000005",C="00000000-0000-4000-8000-000000000003",I=[c,l,s,_],P=[y];function T(e){return[A,m].includes(e)}function E(e){return T(e)||e===_}function R(e){return[c,l].includes(e)}const D={[a]:"github",[r]:"github",[d]:"jira",[i]:"slack","70570080-b12c-4484-a5ef-7c917ea6af1e":"zoom",[g]:"asana",[b]:"trello",[l]:"figma","4d0f0ca4-c5f2-42c8-b6a6-795a590a3e57":"dropbox","ef9a1f68-a912-4bc0-9523-1688a2a52645":"onedrive",[m]:"google_calendar","09c1d111-fdc8-4ab8-8351-8b4b4edf2976":"hubspot",[h]:"gitlab",[p]:"box","************************************":"adobe_xd","a5cac57e-e610-4b62-b515-9e2757c0a945":"clickup",[c]:"google_drive","48fba5a0-411d-43eb-aad4-1daff8c3c64d":"pagerduty",[s]:"zendesk",[o]:"cron",[_]:"salesforce",[v]:"salesforce",[f]:"quip",[y]:"gmail",[w]:"google_contacts",[k]:"jira_data_center",[u]:"jira",[S]:"notion_automation_public_api"};function q(e){const{integrationId:t}=e;return t in D||t===C||t===A}function M(e){return e===r||e===_}function O(e){return function(e){return[C,a].includes(e)}(e)?"github":T(e)?"google_calendar":D[e]}function N(e){return e===_?v:void 0}function B(e){return e===_}function L(e){return e===_?"Salesforce":"Integration"}},632055:e=>{e.exports={}},638681:(e,t,n)=>{n.r(t),n.d(t,{any:()=>ee,array:()=>D,bigInt:()=>I,binary:()=>h,boolean:()=>k,buffer:()=>Y,caseInsensitiveLiteral:()=>y,contains:()=>K,dateString:()=>b,gt:()=>V,gte:()=>F,instanceOf:()=>ie,integer:()=>U,intersection:()=>B,isNull:()=>T,isUndefined:()=>R,keyValidator:()=>Q,lazy:()=>ne,literal:()=>v,literals:()=>oe,lt:()=>J,lte:()=>j,matchesRegExp:()=>_,maxLength:()=>H,minLength:()=>$,nonEmpty:()=>L,nullable:()=>re,number:()=>A,object:()=>O,percentage:()=>Z,record:()=>M,shortID:()=>z,string:()=>l,tuple:()=>q,undefinable:()=>ae,union:()=>N,unknown:()=>te,uuid:()=>u,uuidWithoutDashes:()=>m,without:()=>G});n(944114),n(581454);var o=()=>n(244641),a=()=>n(498212),r=()=>n(534177),i=()=>n(671593);function s(e){return()=>e}const c=(0,i().rv)((function(e){return i().cS.not`defined`(void 0===e)||i().cS.not`a string`("string"!=typeof e)}),s("string"));function l(){return c}const d=(0,i().xs)(c,(function(e){return i().cS.not`a valid uuid`(!(0,a().uj)(e))}),s("uuid"));function u(){return d}const p=(0,i().xs)(c,(function(e){return i().cS.not`a valid uuid`(!(0,a().c_)(e))}),s("UUIDWithoutDashes"));function m(){return p}function f(e){const t=function(e){if(e.match(/^\d{1,2}:\d{2}/))return;const t=o().c9.fromISO(e);if(t.isValid)return t;const n=e.replace(/ /,"T");if(n!==e){const e=o().c9.fromISO(n);if(e.isValid)return e}}(e);return Boolean(t)}const g=(0,i().xs)(c,(function(e){return i().cS.not`a valid ISO 8601 date string`(!f(e))}),s("ISO8601DateString"));function b(){return g}function h(){return c}function _(e){return(0,i().xs)(c,(function(t){return i().cS.not`matches regexp ${String(e)}`(!t.match(e))}))}function v(e){return(0,i().rv)((function(t){return i().cS.not`defined`(void 0===t)||i().cS.not`${e}`(t!==e)}),JSON.stringify(e))}function y(e){return(0,i().xs)(c,(function(t){return i().cS.not`defined`(void 0===t)||i().cS.not`${e}`(t.toUpperCase()!==e.toUpperCase())}),(()=>JSON.stringify(e)))}const w=(0,i().rv)((function(e){return i().cS.not`defined`(void 0===e)||i().cS.not`a boolean`("boolean"!=typeof e)}),"boolean");function k(){return w}const S=(0,i().rv)((function(e){return i().cS.not`defined`(void 0===e)||i().cS.not`a number`("number"!=typeof e)}),"number");function A(){return S}const C=(0,i().rv)((function(e){return i().cS.not`defined`(void 0===e)||i().cS.not`a bigint`("bigint"!=typeof e)}),"bigint");function I(){return C}const P=(0,i().rv)((function(e){return i().cS.not`defined`(void 0===e)||i().cS.not`${null}`(null!==e)}),"null");function T(){return P}const E=(0,i().rv)((function(e){return i().cS.not`${void 0}`(void 0!==e)}),"undefined");function R(){return E}function D(e){return(0,i().rv)((function(t){return i().cS.not`defined`(void 0===t)||i().cS.not`an array`(!Array.isArray(t))||i().cS.anyFails(Array.from(t.keys()),(n=>i().cS.keyIsNot(e,n,t)))}),(()=>`Array<${e}>`))}function q(e){return(0,i().rv)((function(t){return i().cS.not`defined`(void 0===t)||i().cS.not`an array`(!Array.isArray(t))||i().cS.anyFails(e,((e,n)=>i().cS.keyIsNot(e,n,t)))||i().cS.keyIsNot(v(e.length),"length",t)}),(()=>`[${e.map(String).join(", ")}]`))}function M(e,t){return(0,i().rv)((function(e){return i().cS.not`defined`(void 0===e)||i().cS.not`an object`("object"!=typeof e||null===e||Array.isArray(e))||i().cS.anyFails((0,r().uv)(e),(n=>i().cS.keyIsNot(t,n,e)))}),(()=>`Record<${e}, ${t}>`))}function O(e){let{required:t,optional:n,exact:o}=e;return(0,i().rv)((function(e){const a=i().cS.not`defined`(void 0===e)||i().cS.not`an object`("object"!=typeof e||null===e||Array.isArray(e))||i().cS.anyFails((0,r().uv)(t),(n=>i().cS.keyIsNot(t[n],n,e)))||i().cS.anyFails((0,r().uv)(n),(t=>i().cS.keyIsNot(N([n[t],E]),t,e)));if(!o||a)return a;const s=[];for(const o of(0,r().uv)(e)){o in t||o in n||s.push({claim:"not present",path:[o]})}return s.length>0?s:null}),(()=>`{ ${[...(0,r().WP)(t).map((e=>{let[t,n]=e;return`${"string"==typeof t?t:String(t)}: ${n}`})),...(0,r().WP)(n).map((e=>{let[t,n]=e;return`${"string"==typeof t?t:String(t)}?: ${n}`}))].join("; ")} }`))}function N(e){return(0,i().rv)((function(t){return i().cS.noneOf(e,t)}),(()=>e.map(String).join(" | ")))}function B(e){return(0,i().rv)((function(t){return i().cS.anyFails(e,(e=>i().cS.noneOf([e],t)))}),(()=>e.map(String).join("&")))}function L(e){return(0,i().xs)(e,(function(e){return i().cS.not`populated`(0===Object.keys(e).length)}))}const x=(0,i().xs)(S,(function(e){return i().cS.not`an integer`(!Number.isInteger(e))}));function U(){return x}function F(e,t){return(0,i().xs)(e,(function(e){return i().cS.not`≥ ${t}`(e<t)}))}function V(e,t){return(0,i().xs)(e,(function(e){return i().cS.not`> ${t}`(e<=t)}))}function j(e,t){return(0,i().xs)(e,(function(e){return i().cS.not`≤ ${t}`(e>t)}))}function J(e,t){return(0,i().xs)(e,(function(e){return i().cS.not`< ${t}`(e>=t)}))}function H(e,t){return(0,i().xs)(e,(function(e){return i().cS.keyIsNot(j(A(),t),"length",e)}))}function $(e,t){return(0,i().xs)(e,(function(e){return i().cS.keyIsNot(F(A(),t),"length",e)}))}const W=(0,i().xs)(L(c),(function(e){return i().cS.anyFails([...[...e].keys()],(t=>i().cS.keyIsNot(oe(...n(183558).Mb),t,e)))}),s("shortID"));function z(){return W}function Z(){return j(F(A(),0),100)}function G(e,t){return(0,i().xs)(e,(function(e){return i().cS.not`without ${t}`(e.includes(t))}))}function K(e,t){return(0,i().xs)(e,(function(e){return i().cS.not`contains ${t}`(!e.includes(t))}))}const Q=G(G(L(l()),"{"),"}"),X=(0,i().rv)((function(e){return i().cS.not`a buffer`(!Buffer.isBuffer(e))}),s("Buffer"));function Y(){return X}function ee(){return(0,i().rv)((function(e){return null}),"any")}function te(){return(0,i().rv)((function(e){return null}),"unknown")}function ne(e){let t;return(0,i().rv)((function(n){return t||(t=e()),i().cS.noneOf([t],n)}),(()=>(t||(t=e()),t.toString())))}function oe(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return N(t.map(v))}function ae(e){return N([e,R()])}function re(e){return N([e,T()])}function ie(e){const t=e.name||String(e);return(0,i().rv)((function(n){return i().cS.not`instanceof ${t}`(!(n instanceof e))}),t)}O({required:{},optional:{foo:l()}}).value;O({required:{foo:l()},optional:{}}).value;O({required:{foo:N([l(),R()])},optional:{}}).value;O({required:{foo:ee()},optional:{}}).value;O({required:{},optional:{foo:l()}}).value;O({required:{foo:l()},optional:{}}).value;O({required:{foo:l()},optional:{bar:l()}}).value},640254:(e,t,n)=>{n.r(t)},644425:(e,t,n)=>{n.r(t),n.d(t,{initializeEarlyLogging:()=>c,updateNativeErrorHandler:()=>s});var o=()=>n(857639),a=()=>n(502800),r=()=>n(780054);let i=e=>{};function s(e){i=e}function c(){o().initialize(r().A),function(){const e=e=>{const t=e.reason?e.reason.message:e.message,n=e.reason?e.reason.status:e.status;if("HttpRequestError"===e.name)o().log({level:"info",from:"main",type:e.name,error:(0,a().convertErrorToLog)(e)});else if("QueueApiError"===e.name)o().log({level:"info",from:"main",type:e.name,error:(0,a().convertErrorToLog)(e)});else if("XHR Error: 0"===t||"Token was invalid or expired."===t||"No local database found."===t||"Script error."===t);else if(400===n)o().log({level:"warning",from:"main",type:"badRequest",error:(0,a().convertErrorToLog)(e)});else if("Request Timeout after 30000ms"===t)o().log({level:"warning",from:"main",type:t,error:(0,a().convertErrorToLog)(e)});else{const t={level:"error",from:"main",type:"ClientError",error:(0,a().convertErrorToLog)(e,JSON.stringify,!1)};o().log(t)}};window.addEventListener("error",e),window.addEventListener("unhandledrejection",e)}(),document.addEventListener("securitypolicyviolation",(e=>{const{blockedURI:t,violatedDirective:n,sourceFile:a}=e,r=e.disposition;o().log({level:"report"===r?"warning":"error",from:"main",type:"securitypolicyviolation",data:{miscDataToConvertToString:{blockedURI:t,violatedDirective:n,sourceFile:a}}})}))}},645873:(e,t,n)=>{n.d(t,{O2:()=>m,Qz:()=>y,_h:()=>h,fJ:()=>g,jQ:()=>_,u2:()=>f});n(944114),n(898992),n(803949);var o=n(296540),a=()=>n(56366),r=()=>n(187174),i=()=>n(558842),s=()=>n(319625),c=()=>n(763824),l=()=>n(11048),d=()=>n(765957),u=()=>n(227440),p=n(474848);class m{constructor(e,t,o){this.name=void 0,this.loader=void 0,this.promise=void 0,this.retryMs=void 0,this.attempts=void 0,this.loadAttempt=async e=>{e&&await Promise.all([c().wR(this.retryMs),u().A.waitUntil((()=>u().A.state.online))]);const t=this.attempts++;try{return await this.loader()}catch(o){throw n(449412).Fg(o,{extra:{waitMs:this.retryMs,attempts:t},tags:{dependencyName:this.name}}),n(857639).log({level:"warning",from:"useDependency",type:"loadError",error:(0,n(502800).convertErrorToLog)(o),data:{miscDataToConvertToString:{waitMs:this.retryMs},name:this.name}}),this.retryMs=Math.min(2*this.retryMs,3e4),o}},this.options=o,this.name=e,this.loader=t,this.promise=new(c().Il)(this.loadAttempt),this.retryMs=500,this.attempts=0}getLoadingState(){return this.promise.state}getLoadingMetrics(){const{state:e}=this.promise;if("resolved"===e.status){const{startedAt:t,resolvedAt:n}=e;return{startedAt:t,resolvedAt:n}}}reset(){this.promise=new(c().Il)(this.loadAttempt),this.retryMs=500}async load(){var e;null!==(e=this.options)&&void 0!==e&&e.waitForInitialPageRender&&"rendered"!==d().default.state.renderPhase&&await d().default.waitUntil((()=>"rendered"===d().default.state.renderPhase));const t=await this.promise.runWithRetry(),o=this.getLoadingMetrics();return o&&o.startedAt&&o.resolvedAt&&n(905343).A.addTrace({type:"lazy_load",name:this.name,start:o.startedAt,end:o.resolvedAt}),t}}function f(e){const{dependency:t,renderLoading:n,children:o,forceRenderLoading:s}=e,c=g(t),d=!s&&"resolved"===c.status,u=(0,r().BC)({state:c,spinAfterMs:300,render(e){if(n)return n(e)},forceRenderLoading:s}),m=(0,a().K8)((()=>d?o(c.value):null),[d,o,c.value],{debugName:`DependencyConsumer(${t.name}).useComputedStore`});return(0,p.jsx)(l().A,{name:`Lazy_${t.name}`,children:d?(0,i().Du)(m):u})}function g(e,t){const[n,a]=(0,o.useState)({asyncState:e.getLoadingState(),dependency:e});return n.dependency!==e&&a({asyncState:e.getLoadingState(),dependency:e}),(0,o.useEffect)((()=>{null!=t&&t.disabled||"idle"!==n.asyncState.status&&"rejected"!==n.asyncState.status&&"pending"!==n.asyncState.status||async function(){try{const e=await n.dependency.load();a({asyncState:{status:"resolved",value:e},dependency:n.dependency})}catch(e){a({asyncState:{status:"rejected",error:(0,s().A)(e)},dependency:n.dependency})}}()}),[null==t?void 0:t.disabled,n.dependency,n.asyncState]),n.asyncState}function b(e){const{renderLoading:t,forceRenderLoading:n,...o}=e;return o}function h(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return v(e,t,{...n,shouldForwardRef:!1,forceRenderLoading:n.forceRenderLoading??!1})}function _(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return v(e,t,{...n,shouldForwardRef:!0,forceRenderLoading:n.forceRenderLoading??!1})}function v(e,t,n){const{shouldForwardRef:a}=n,r=function(o,r){const i=o.renderLoading||n.renderLoading,s=o.forceRenderLoading||n.forceRenderLoading,c=b(o);return(0,p.jsx)(f,{renderLoading:i?e=>i(e,c):void 0,dependency:e,forceRenderLoading:s,children:e=>{const n=t(e);return(0,p.jsx)(n,{...a?{ref:r}:{},...c})}})};return r.displayName=`DependencyComponent(${e.name})`,a?(0,o.memo)((0,o.forwardRef)(r)):(0,o.memo)(r)}function y(e,t){const n=function(n,o){const a=n.renderLoading,r=b(n);return(0,p.jsx)(f,{renderLoading:a?e=>a(e,r):void 0,dependency:e,children:e=>t(e,{...r,ref:o})})};return n.displayName=`withDependency(${e.name})`,(0,o.forwardRef)(n)}},650031:(e,t,n)=>{n.d(t,{A:()=>a});class o extends(()=>n(757695))().Store{getInitialState(){return{isActive:!1,userId:void 0}}isReady(){return Boolean(this.state.remotePresenceData)}}const a=o},651170:(e,t,n)=>{n.d(t,{CS:()=>m,F:()=>p,JV:()=>d,PW:()=>c,lh:()=>s,pu:()=>l,w8:()=>u,yY:()=>i});n(16280);var o=()=>n(714681);class a extends Error{constructor(e){const{message:t,debugInfo:n}=e;super(t),this.debugInfo=void 0,this.debugInfo=n}}function r(e){return e instanceof a}class i extends a{constructor(e){const{result:t,debugInfo:n}=e;super({message:t.message,debugInfo:n}),this.name=t.name}}class s extends a{constructor(){super(...arguments),this.name="SqliteDatabaseBecameCorruptDuringSession"}}class c extends a{constructor(){super(...arguments),this.name="SqliteDatabaseWasCorruptWhenSessionBegan"}}class l extends a{constructor(){super(...arguments),this.name="SqliteInvalidResult"}}class d extends a{constructor(){super(...arguments),this.name="SqlitePreconditionFail"}}class u extends a{constructor(){super(...arguments),this.name="SqliteOutOfSpace"}}class p extends a{constructor(){super(...arguments),this.name="SqliteSharedWorkerFailedToDelegate"}}function m(e,t){let{isBrowser:n}=t;return{errorSql:r(e)?e.debugInfo.errorSql:void 0,lastSuccessfulSqlBatch:e instanceof s?e.debugInfo.lastSuccessfulSqlBatch:void 0,sqliteCode:r(e)?e.debugInfo.sqliteCode:void 0,wasmSqliteDbVersion:n?o().c:void 0}}},669484:(e,t,n)=>{n.d(t,{performPrefetchRequests:()=>s,v:()=>c});n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(898992),n(354520),n(581454);var o=()=>n(861017),a=()=>n(534177),r=()=>n(780054),i=()=>n(740456);function s(e){let{environment:t,currentUserId:n,options:s={}}=e;const l="prefetchCache"in t?t.prefetchCache:new(i().iO),d=s.nextRoute??(0,o().parseRoute)({url:window.location.href,isMobile:t.device.isMobile,baseUrl:r().A.domainBaseUrl,publicDomainName:r().A.publicDomainName,protocol:r().A.protocol,currentUrl:window.location.href});(0,i().VJ)({isMobile:t.device.isMobile}),(0,i().h6)();const{blockId:u,collectionViewId:p,teamId:m,userId:f}=c({route:d,userId:n}),g=u&&!p&&(0,i().nD)(u)||[];if("root"===d.name&&f)(0,i().Nl)({prefetchCache:l,environment:t,activeUserId:f,data:{}});else if("root"===d.name)return l;const{isTeamHome:b,teamHomeCollectionViewIds:h}=(0,i().ro)(m),{isHome:_,unifiedFeedEnabled:v,aiChatEnabled:y,homeCollectionViewIds:w}=(0,i().i6)(u),k=[...new Set([...w,...h,p,...g].filter(a().O9))],S=!s.skipPrefetchPageChunk&&void 0!==u,A=k.length>0||"blank"===d.name;return Promise.all([u&&S&&(0,i().s1)({prefetchCache:l,environment:t,activeUserId:f,blockId:u}),_&&(0,i().QM)({unifiedFeedEnabled:v,aiChatEnabled:y}),b&&(0,i().fn)(),A&&(0,i().fX)(),...k.map((e=>(0,i().qk)({prefetchCache:l,environment:t,activeUserId:f,collectionViewId:e})))]),l}function c(e){const{route:t,userId:n}=e;switch(t.name){case"root":return(0,i().getPredictedRootRedirectPageForPrefetch)();case"page":return{blockId:t.blockId,collectionViewId:t.collectionViewId,teamId:void 0,userId:n};case"team":return{blockId:void 0,collectionViewId:void 0,teamId:t.teamId,userId:n};default:return{blockId:void 0,collectionViewId:void 0,teamId:void 0,userId:n}}}},669528:(e,t,n)=>{n.r(t)},671593:(e,t,n)=>{n.d(t,{Qq:()=>m,Xj:()=>p,cS:()=>l,jA:()=>f,rv:()=>r,tf:()=>u,xs:()=>i});n(16280),n(944114),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(898992),n(354520),n(803949),n(581454),n(908872);var o=()=>n(302777),a=()=>n(973647);function r(e,t){return{validator:e,toString:"string"==typeof t?()=>t:t}}function i(e,t,n){const o=e.validator;return r((e=>o(e)||t(e)),n??e.toString)}class s{constructor(){this.failures=new Set,this.subtree=new Map}}function c(e){const t=JSON.stringify(e)??"undefined",n=t.slice(0,55),o=n.length<t.length?"...":"";return`\`${n.replace(/`/g,"\\`")}${o}\``}const l={not:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{if(!1===e)return null;{const e=function(e){const t=[];for(var n=arguments.length,o=new Array(n>1?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];for(const r in e)if(t.push(e[r]),r in o){const e=o[r];t.push(c(e))}return t.join("")}(...t);return[{claim:e,path:[]}]}}},noneOf:function(e,t){if(0===e.length)throw new Error("No types provided.");const n=[];for(const o of e){const e=o.validator(t);if(!e)return null;n.push(...e)}return n},keyIsNot:function(e,t,n){let o=e.validator(n[t]);return o&&(o=o.map((e=>{let{path:n,...o}=e;return{...o,path:[t,...n]}}))),o},anyFails:function(e,t){for(const n of e.keys()){const o=t(e[n],n,e);if(o)return o}return null}};function d(e,t,n){const a=function(e){const t=new s;return e.forEach((e=>{let{claim:n,path:o}=e;o.reduce(((e,t)=>{let n=e.subtree.get(t);return void 0===n&&(n=new s,e.subtree.set(t,n)),n}),t).failures.add(n)})),t}(t),r=function(e,t){return function e(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return t.failures.size>0&&(o=o.concat({path:n,isNots:[...t.failures]})),t.subtree.forEach(((t,a)=>{o=e(t,n.concat(a),o)})),o}(e,[t])}(a,n),i=r.map((e=>e.path.length)),l=Math.max(...i),d=r.filter((e=>e.path.length===l));return[d.length>1?`${n} failed validation. Fix one:`:`${n} failed validation:`,...d.map((t=>{let{path:n,isNots:a}=t;const r=function(e){let t="";for(const n of e){const e=String(n);let o;o="string"!=typeof n?`[${e}]`:""===t?e:`.${e}`,t+=o}return t}(n),i=(l="or",(s=a).slice(0,-2).concat("").join(", ")+s.slice(-2).join(`${s.length>2?",":""} ${l} `));var s,l;const d=function(e,t){const n=t.slice(1);return c(n.length>0?(0,o().OU)(e,n):e)}(e,n);return`${r} should be ${i}, instead was ${d}.`}))].join(d.length>1?"\n":" ")}function u(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const o=e.validator(t);if(o){const e=d(t,o,n.rootVariableName||"payload");return new Error(e)}}function p(e,t){return void 0===u(e,t)}function m(e,t){return a().Q.unwrapOr(f(e,t),void 0)}function f(e,t){const n=u(e,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:{});return void 0!==n?{error:n}:{value:t}}},671887:(e,t,n)=>{n.d(t,{D:()=>o});const o=new(n(178624).R)({key:"spaceIdToShortId",namespace:n(419494).Bq,important:!0,trackingType:"necessary"})},671973:(e,t,n)=>{n.d(t,{N:()=>a});function o(e){return"page"===e||"new_page"===e||"collection"===e}const a=new class{constructor(){this.state={id:void 0,parameters:void 0,initialPageRendered:!1,transitionReady:!1,type:void 0,navigationRendered:!1}}markNativeStart(e){this.state={...this.state,id:e.id,parameters:e,transitionReady:!1,type:void 0,navigationRendered:!1}}markInitialPageRendered(){this.state={...this.state,initialPageRendered:!0}}markTransitionReady(e,t,n){if(this.state={...this.state,type:t},o(t)){const t=this.state;t.initialPageRendered||t.navigationRendered?this.trackMetricEnd(e):n?this.state={...this.state,transitionReady:!0}:this.trackMetricEnd(e,!0)}}markNavigationRender(e){const t=this.state;t.transitionReady?o(t.type)&&this.trackMetricEnd(e):this.state={...this.state,navigationRendered:!0}}trackMetricEnd(e,t){var n,o,a;const r=this.state,i=r.id;if(!i)return;const s={duration_ms:Date.now()-r.parameters.start_time_ms,is_initial_transition:r.initialPageRendered,transition_type:r.type,before_app_start:(null===(n=r.parameters)||void 0===n?void 0:n.before_app_start)??!1,start_time_ms_since_app_start:null===(o=r.parameters)||void 0===o?void 0:o.start_time_ms_since_app_start,transition_to_same_page:t??!1};null===(a=e.mobileNative)||void 0===a||a.nativeToWebRenderEnd(i,s),this.state={id:void 0,parameters:void 0,initialPageRendered:!1,transitionReady:!1,type:void 0,navigationRendered:!1}}}},675742:(e,t,n)=>{n.d(t,{electronApi:()=>o});const o=window.__electronApi},677338:(e,t,n)=>{n.d(t,{$A:()=>G,$P:()=>Q,$m:()=>z,ED:()=>B,Fo:()=>q,HP:()=>J,Ht:()=>Z,IT:()=>ne,IV:()=>se,JU:()=>M,KB:()=>j,NI:()=>F,SQ:()=>oe,TR:()=>U,Td:()=>re,Ti:()=>ae,Ug:()=>R,dC:()=>te,dY:()=>K,dw:()=>ee,f3:()=>Y,hl:()=>W,hn:()=>O,ip:()=>$,j4:()=>N,l0:()=>ce,oJ:()=>L,pG:()=>V,r9:()=>ie,sY:()=>D,wA:()=>H,x0:()=>x,zt:()=>X});var o=()=>n(720665),a=()=>n(206267),r=()=>n(820103),i=()=>n(861017),s=()=>n(427704);const c="sessionTags",l="urlAnalytics",d="sessionTabId",u="activeUserId",p="sessionAppRefreshed",m="oauthAuthorizationPage",f="postLoginRedirectURL",g="postLoginRedirectURLTS",b="postOnboardingRedirectURL",h="postOnboardingRedirectURLTS",_="oauthInitiatedTimestamp",v="postLoginFallbackRedirectUrl",y="postLoginFallbackRedirectUrlTimestamp",w="onboardingFormResponseId",k="onboardingFormSecretKey",S="onboardingFormSpaceId",A="onboardingFormSpaceIntent",C="onboardingFormResponseIdTimestamp",I="newUserSignupSourceKey",P="loginSuccessKey",T="magicBoxPrompt",E="magicBoxPromptTimestamp";function R(e){sessionStorage.setItem(c,JSON.stringify(e))}function D(){const e=sessionStorage.getItem(c);if(e)return(0,o().$l)(e)}function q(e){const{pathname:t,query:n,previous_path:o,...a}=e;sessionStorage.setItem(l,JSON.stringify(a))}function M(){const e=sessionStorage.getItem(l);if(e)return(0,o().$l)(e)}function O(e){sessionStorage.setItem(u,e)}function N(){return sessionStorage.getItem(u)||void 0}function B(e){sessionStorage.setItem(p,JSON.stringify(e))}function L(){const e=sessionStorage.getItem(p);return!!e&&(0,o().$l)(e)}function x(){let e=sessionStorage.getItem(d);return e||(e=a().JW(),sessionStorage.setItem(d,e)),e}function U(){sessionStorage.setItem(_,`${Date.now()}`)}function F(e){void 0===e?sessionStorage.removeItem(m):sessionStorage.setItem(m,JSON.stringify(e))}function V(){const e=sessionStorage.getItem(m);if(e)return(0,o().$l)(e)}function j(e){const t=sessionStorage.getItem(f),n=sessionStorage.getItem(g),o=sessionStorage.getItem(_),{config:a,fileHostProtocol:r,fileHostName:c}=e;return a&&o&&t&&i().m6(t,a.domainBaseUrl)?parseInt(o)>Date.now()-12e4?{type:"oauth",result:t}:void sessionStorage.removeItem(_):t&&(t.startsWith(`${s().JZ.globalOauthAuthorization}`)||t.startsWith(`${s().JZ.globalOauthPostLogin}`))?{type:"global_oauth",result:t}:t&&n?parseInt(n)>Date.now()-9e5?t.startsWith(`${r}://${c}/`)?{type:"file",result:t}:{type:"other",result:t}:void J():void 0}function J(){sessionStorage.removeItem(f),sessionStorage.removeItem(g)}function H(e){sessionStorage.setItem(f,e),sessionStorage.setItem(g,`${Date.now()}`)}function $(){sessionStorage.removeItem(w),sessionStorage.removeItem(C),sessionStorage.removeItem(k),sessionStorage.removeItem(S),sessionStorage.removeItem(A)}function W(e,t,n,o){sessionStorage.setItem(w,e),sessionStorage.setItem(k,t),sessionStorage.setItem(S,n),o&&sessionStorage.setItem(A,o),sessionStorage.setItem(C,`${Date.now()}`)}function z(){const e=sessionStorage.getItem(w),t=sessionStorage.getItem(k),n=sessionStorage.getItem(S),a=sessionStorage.getItem(A),r=sessionStorage.getItem(C);return r&&e&&parseInt(r)<Date.now()-10*o().Xb?null:e&&t&&n?{formResponseId:e,formSecretKey:t,formSpaceId:n,formSpaceIntent:a??void 0}:null}function Z(e){sessionStorage.setItem(I,e)}function G(){sessionStorage.removeItem(I)}function K(){const e=sessionStorage.getItem(I);if("string"==typeof(t=e)&&r().Rw.includes(t))return e;var t}function Q(e){sessionStorage.setItem(v,e),sessionStorage.setItem(y,`${Date.now()}`)}function X(){sessionStorage.removeItem(v),sessionStorage.removeItem(y)}function Y(){const e=sessionStorage.getItem(v),t=sessionStorage.getItem(y);return t&&e&&parseInt(t)<Date.now()-10*o().Xb?null:e}function ee(){const e=sessionStorage.getItem(b),t=sessionStorage.getItem(h);return t&&e&&parseInt(t)<Date.now()-15*o().Xb?null:e}function te(){sessionStorage.removeItem(b),sessionStorage.removeItem(h)}function ne(e){sessionStorage.setItem(b,e),sessionStorage.setItem(h,`${Date.now()}`)}function oe(){sessionStorage.setItem(P,`${Date.now()}`)}function ae(){sessionStorage.removeItem(P)}function re(){const e=sessionStorage.getItem(P);return!!e&&parseInt(e)>Date.now()-Number(o().Xb)}function ie(){const e=sessionStorage.getItem(T),t=sessionStorage.getItem(E);return t&&e&&parseInt(t)<Date.now()-15*o().Xb?null:e}function se(){sessionStorage.removeItem(T),sessionStorage.removeItem(E)}function ce(e){sessionStorage.setItem(T,e),sessionStorage.setItem(E,`${Date.now()}`)}},680091:(e,t,n)=>{n.d(t,{X:()=>o});const o=new class{constructor(){this.processingTime=void 0}setProcessingTime(e){this.processingTime=e}getProcessingTime(){return this.processingTime}}},692186:(e,t,n)=>{n.r(t)},697938:(e,t,n)=>{n.d(t,{E:()=>r});n(16280),n(944114),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(581454);var o=()=>n(496603),a=()=>n(534177);function r(e,t){const n=new Set,r=['  graph [rankdir = "LR"];'],i=new Map,s=new Map;for(const o of e.nodes){const e=[];let c=o.label;if("store"===o.type){if(t&&o.recordStoreDetails){const{id:e}=o.recordStoreDetails;let t=i.get(e);if(t||(t={id:e,table:o.recordStoreDetails.table,nodes:[]},i.set(e,t)),o.recordStoreDetails.table!==t.table)throw new Error("Huh, didn't expect that to happen");if(o.blockStoreDetails){const e=o.blockStoreDetails.type;if(t.blockType){if(t.blockType!==e)throw new Error("Hmm this data is weird")}else t.blockType=e;n.add(o.id)}s.set(o.id,`"${e}":"${o.id}"`),t.nodes.push(o);continue}if(o.recordStoreDetails){const e=o.recordStoreDetails;o.blockStoreDetails?(c=`${c}: ${e.table} ${e.id} (${o.blockStoreDetails.type} block)`,n.add(o.id)):e.path.length>0&&(c=`${c} path: ${e.path.join(".")}`)}e.push("shape=box")}else"computedstore"===o.type?e.push("shape=diamond"):"component"===o.type?e.push("shape=ellipse"):"unknown"===o.type?e.push("shape=tripleoctagon"):(0,a().HB)(o.type);e.push(`label="${c}"`),r.push(`  "${o.id}" [${e.join(",")}];`)}for(const[a,c]of i.entries()){const e=["shape=record"];for(const o of c.nodes){let e=o.label;o.recordStoreDetails&&o.recordStoreDetails.path.length&&(e=`${e}: ${o.recordStoreDetails.path.join(".")}`),o.label=e}const t=o().My(c.nodes,["label"]).map((e=>`<${e.id}> ${e.label}`)).join("|");let n=`${c.table} ${c.id}`;c.blockType&&(n+=` (${c.blockType} block)`);const i=`<__title> ${n}|${t}`;e.push(`label="${i}"`),r.push(`  "${a}" [${e.join(",")}];`)}if(!t)for(const o of e.nodes)o.parentUIStoreId&&r.push(`  "${o.parentUIStoreId}" -> "${o.id}" [style=dotted];`);for(const{from:o,to:a}of e.edges){const e=s.get(o)??`"${o}"`,t=[];n.has(o)&&t.push("color=red"),r.push(`  ${e} -> "${a}" [${t.join(",")}];`)}return`digraph G {\n${r.join("\n")}\n}`}},705059:(e,t,n)=>{function o(e){return{isHomeKey:`isHome:${e}`,homeCollectionViewIdsKey:`homeCollectionViewIds:${e}`,unifiedFeedEnabledKey:`unifiedFeedEnabled:${e}`,aiChatEnabledKey:`aiChatEnabled:${e}`}}n.d(t,{h:()=>o})},706762:(e,t,n)=>{function o(){return function(){if(!(window.navigator&&window.navigator.storage&&window.FileSystemFileHandle&&window.FileSystemFileHandle.prototype.createWritable))return!1;return!0}()&&function(){if(!window.SharedWorker)return!1;return!0}()}n.d(t,{s:()=>o})},711059:(e,t,n)=>{n.r(t),n.d(t,{AllVersionHeaders:()=>A,AndroidVersionHeader:()=>k,ClientVersionHeader:()=>_,IOSVersionHeader:()=>w,MacVersionHeader:()=>v,PublicApiVersionHeader:()=>S,UpdateType:()=>a,WindowsVersionHeader:()=>y,formatVersion:()=>c,getUpdateType:()=>l,isEqualVersion:()=>d,isGreaterThanOrEqualToVersion:()=>p,isGreaterThanVersion:()=>u,isLessThanOrEqualToVersion:()=>f,isLessThanVersion:()=>m,maxVersion:()=>g,parseMobileAppVersion:()=>s,parseVersion:()=>i,parseVersionStringFromTag:()=>r,versionStringGreaterThanOrEqualToVersion:()=>h});n(16280),n(898992),n(908872);var o=()=>n(857639);let a=function(e){return e[e.Major=0]="Major",e[e.Minor=1]="Minor",e[e.Patch=2]="Patch",e[e.Silent=3]="Silent",e}({});function r(e){const t=e.split("-v");return t.length>1?t[1].split(",")[0]:e}function i(e){const t=e.match(/(\d+)\.(\d+)\.(\d+)\.(\d+)/);if(t&&e===t[0])return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3]),parseInt(t[4])];const n=e.match(/(\d+)\.(\d+)\.(\d+)/);return n&&e===n[0]?[parseInt(n[1]),parseInt(n[2]),parseInt(n[3])]:void 0}function s(e,t){if(!e)return;let n;if(t){let t;const o=e.split(".");t="beta"===o[o.length-1]?o.slice(0,o.length-1).join("."):o.join("."),n=i(t)}else n=i(e);return n}function c(e){if(e)return e.join(".")}function l(e){const{currentVersion:t,nextVersion:n,app:r}=e;if(!t||!n)return a.Major;if("client"===r&&4===t.length&&function(e){return 3===e.length}(n))throw new Error(`Failed attempting to update client from ${t} to ${n}`);return n[0]>t[0]?a.Major:n[1]>t[1]?a.Minor:n[2]>t[2]?a.Patch:"client"===r?a.Silent:(o().log({level:"error",from:"versionHelpers",type:"getUpdateType",error:{message:"Failed to get update type"},data:{miscDataToConvertToString:{currentVersion:t,nextVersion:n,currentVersionType:typeof t,nextVersionType:typeof n}}}),a.Patch)}function d(e,t){return!(!e||!t)&&0===b(e,t)}function u(e,t){return-1===b(e,t)}function p(e,t){const n=b(e,t);return-1===n||0===n}function m(e,t){return 1===b(e,t)}function f(e,t){const n=b(e,t);return 1===n||0===n}function g(e){return e.reduce(((e,t)=>-1===b(t,e)?t:e),e[0])}function b(e,t){for(let n=0,o=0;e.length,o<t.length;n++,o++){if(e[n]>t[o])return-1;if(e[n]<t[o])return 1}return e.length>t.length?-1:t.length>e.length?1:0}function h(e,t){const n=i(e);if(void 0===n)return!1;const o=b(n,t);return 0===o||-1===o}const _="notion-client-version",v="notion-mac-version",y="notion-windows-version",w="notion-ios-version",k="notion-android-version",S="notion-version",A=[_,v,y,w,k,S]},713996:(e,t,n)=>{n.d(t,{AM:()=>p,D8:()=>s,DE:()=>u,Tr:()=>c,Vo:()=>a,Xe:()=>i,_P:()=>o,al:()=>d,hx:()=>g,kr:()=>r,o3:()=>m,oh:()=>l,yp:()=>f});const o={name:"onboarding",pattern:"/onboarding"},a={name:"general",pattern:"/general{/:subtab}"},r={name:"people",pattern:"/people{/:subtab}"},i={name:"security",pattern:"/security{/:subtab}"},s={name:"data_and_compliance",pattern:"/data-and-compliance{/:subtab}"},c={name:"analytics",pattern:"/analytics{/:subtab}"},l=[o,a,r,i,s,c];let d=function(e){return e.Workspaces="workspaces",e.EmailDomains="email-domains",e.Scim="scim",e}({}),u=function(e){return e.Members="members",e.Guests="guests",e.Groups="groups",e.ManagedUsers="managed-users",e}({});const p=["general","workspaces","groups","audit_log"],m=["general","members","teamspaces","pages"],f=["general","members","guests","groups","teamspaces"],g=["general","members","browse_content","exports"]},714681:(e,t,n)=>{n.d(t,{S:()=>a,c:()=>o});const o="v5";function a(e){return`notion-tab-${e}`}},715668:(e,t,n)=>{n.d(t,{deliverAndPersistReactivityVersion:()=>o});async function o(e,t){}},720665:(e,t,n)=>{n.d(t,{$l:()=>C,$z:()=>S,A2:()=>i().A,AH:()=>h,Bu:()=>i().B,D_:()=>E,Et:()=>w,Fs:()=>T,Gq:()=>r().Gq,HP:()=>d,Hg:()=>g,LG:()=>p,MU:()=>m,Om:()=>P,TD:()=>r().TD,Up:()=>f,WY:()=>r().WY,Xb:()=>r().Xb,Z$:()=>A,Z4:()=>_,Zg:()=>I,g_:()=>l,h_:()=>b,hr:()=>v,i5:()=>r().i5,lG:()=>r().lG,m1:()=>r().m1,nD:()=>r().nD,pN:()=>c,pT:()=>r().pT,qE:()=>k,qt:()=>s,sb:()=>a().s,uu:()=>r().uu,w$:()=>y,wK:()=>r().wK,xq:()=>r().xq,zu:()=>u});n(16280),n(944114),n(269479),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(898992),n(354520),n(430670),n(581454),n(908872);var o=()=>n(496603),a=()=>n(806080),r=()=>n(615234),i=()=>n(140283);function s(e){return o().Mp(e,o().n4)}function c(e){return null==e}function l(e){const t=e;for(const n in t)void 0===t[n]&&(t[n]=null);return t}function d(e){return Object.keys(e)}function u(e){return d(e).map((t=>e[t]))}function p(e,t){return o().LG(e,t)}const m=Object.fromEntries;function f(e,t){return o().Up(e,t)}function g(e){let t=!1;return function(){if(t)return e(...arguments);t=!0}}function b(e,t){return e.filter((e=>!t(e)))}function h(e){return Object.fromEntries(Object.entries(e).map((e=>{let[t,n]=e;return[n,t]})))}function _(e,t,n,a){const r=o().pY(n,a);if(Object.keys(r).length!==n.length)throw new Error("zipBy indexes must be unique for each value");const i=o().pY(e,t);if(Object.keys(i).length!==e.length)throw new Error("zipBy indexes must be unique for each value");const s=e.map((e=>[e,r[t(e)]])),c=n.filter((e=>!i[a(e)])).map((e=>[void 0,e]));return s.concat(c)}function v(e){return e.split("\n").map((e=>e.trim())).join("\n")}function y(e,t){let n=e;if(n.sticky||!n.global){const t=new Set(n.flags.split(""));t.delete("y"),t.add("g"),n=new RegExp(e.source,Array.from(t).join(""))}const o=n.lastIndex,a=[];let r=null;for(;null!==(r=n.exec(t));)a.push(r);return n.lastIndex=o,a}function w(e){return o().MI(e)}function k(e,t,n){return Math.max(t,Math.min(e,n))}function S(e,t,n){const o=new Map;for(let a=0;a<e.length;a++){const r=e[a],i=t(r,a);let s=o.get(i);s||(s=[],o.set(i,s)),s.push(n?n(r,a):r)}return o}function A(e,t){const n=o().sb(e);if(n.length>1)throw new Error(t);return n[0]}function C(e){try{return JSON.parse(e)}catch(t){return}}function I(e,t,n){return e[t]=n,e}function P(e){return Object.keys(e).reduce(((t,n)=>({...t,[o().LW(n)]:"object"==typeof e[n]?P(e[n]):e[n]})),{})}function T(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){let n=e;for(const o of t)n=o(n);return n}}function E(e){return e}},732430:(e,t,n)=>{n.d(t,{Lb:()=>i,QF:()=>a,XQ:()=>r,mx:()=>c,s9:()=>l});var o=()=>n(645873);const a=new(o().O2)("posts",(()=>Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(9304),n.e(96346),n.e(16471),n.e(90825),n.e(77848),n.e(55571),n.e(92845),n.e(71718),n.e(48632),n.e(34359),n.e(90625),n.e(15566),n.e(71104),n.e(93282),n.e(22970),n.e(14310),n.e(93552),n.e(27506),n.e(30322),n.e(47934),n.e(35266),n.e(28464),n.e(81374),n.e(33110),n.e(67608),n.e(88177),n.e(54759),n.e(41720),n.e(72462),n.e(43396)]).then(n.bind(n,155721)))),r=(0,o()._h)(a,(e=>e.PostChannelCollectionView)),i=(0,o()._h)(a,(e=>e.MobileUnifiedFeed)),s={PostsLaunchModal:new(o().O2)("PostsLaunchModal",(async()=>await Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(23904)]).then(n.bind(n,357397))))},c=(0,o()._h)(s.PostsLaunchModal,(e=>e.PostsLaunchModal)),l=(0,o()._h)(a,(e=>e.PostPresence))},732524:(e,t,n)=>{function o(e){return"INTEGER"===e||"TEXT"===e||"BLOB"===e}n.d(t,{Ll:()=>a,Xb:()=>r,tx:()=>o});function a(e){return"Error"===e.type||"ErrorBefore"===e.type||"PreconditionFailed"===e.type||"OutOfSpace"===e.type||"SharedWorkerFailedToDelegate"===e.type}function r(e){return e}},734459:(e,t,n)=>{n.d(t,{Zf:()=>i,ho:()=>s,re:()=>c});var o=()=>n(775084),a=()=>n(939768),r=()=>n(427704);const i="new";function s(e){const t=`${r().JZ.settingsConsoleOrganization}{/:organizationId}`;return e?`${t}${e.pattern}`:t}function c(e){const{queryParams:t,tabRoute:n,organizationId:r}=e,i=e.properties??{};if(n){const e=s(n),c=(0,o().wE)(e)({...i,organizationId:r});return t?(0,a().Gm)({url:c,query:t}):c}const c=s();return(0,o().wE)(c)({organizationId:r})}},735270:(e,t,n)=>{n.d(t,{i:()=>i});var o=()=>n(430476),a=()=>n(68336),r=()=>n(469425);class i{constructor(e){this.preconditionSchemaCheck=void 0,this.migrationsCompleted=void 0,this.args=e,this.preconditionSchemaCheck=(0,n(720665).hr)(`\n\t\t\tSELECT\n\t\t\t\tCASE user_version\n\t\t\t\tWHEN ${this.args.migrations.endSchema.pragmas.user_version} THEN 1\n\t\t\t\tELSE 0 END AS precondition_result\n\t\t\t\tFROM pragma_user_version() LIMIT 1\n\t\t`),this.migrationsCompleted=void 0}async ensureMigrated(){if(!this.migrationsCompleted){const e=performance.now();this.migrationsCompleted=(0,r().p6)({connection:this.args.connection,target:this.args.migrations,log:n(857639).log}).then((()=>{n(567417).default.setState({startTime:e,endTime:performance.now()})}))}await this.migrationsCompleted}async optimize(){const e=[a().F4`PRAGMA analysis_limit=40000`.asRead(),a().F4`PRAGMA optimize`.asRead()];await(0,o().G2)({connection:this.args.connection,statements:e,queryName:"optimize"})}async execSqliteBatch(e){switch(this.args.type){case"v1":return await this.ensureMigrated(),await this.args.connection.execSqliteBatch(e);case"v2":return await this.ensureMigrated(),await this.args.connection.execSqliteBatchV2({batch:e,precondition:{sql:this.preconditionSchemaCheck,getData:!0}})}}async completelyRebuildSqliteDb(){await this.args.connection.completelyRebuildSqliteDb(),this.migrationsCompleted=void 0,await this.ensureMigrated()}debugSchema(){return(0,r().CY)(this.args.connection)}async debugQuery(e,t){const n="debug query round-trip from JS";console.time(n);const a=await(0,o().qU)({connection:this.args.connection,sql:e,args:t});return console.timeEnd(n),a}async debugTransaction(e){const t="debug transaction round-trip from JS";console.time(t);const n=await(0,o().G2)({connection:this.args.connection,statements:e});return console.timeEnd(t),n}async debugDeleteAllDataAndResetDatabase(){console.log("Resetting database");const e=(0,r().XI)(this.args.migrations.endSchema);await(0,o().G2)({connection:this.args.connection,statements:e})}}},740456:(e,t,n)=>{n.d(t,{Nl:()=>R,QM:()=>L,VJ:()=>O,fX:()=>B,fn:()=>x,getPredictedRootRedirectPageForPrefetch:()=>V,h6:()=>N,hK:()=>P,i6:()=>U,iO:()=>T,nD:()=>q,qk:()=>D,ro:()=>F,s1:()=>E});n(944114);var o=n.n(n(625473)),a=()=>n(17022),r=()=>n(105751),i=()=>n(857639),s=()=>n(851941),c=()=>n(763884),l=()=>n(436604),d=()=>n(386164),u=()=>n(496603),p=()=>n(763824),m=()=>n(498212),f=()=>n(534177),g=()=>n(680091),b=()=>n(865085),h=()=>n(459225),_=()=>n(705059),v=()=>n(671887),y=()=>n(906551),w=()=>n(754704),k=()=>n(571963),S=()=>n(100875),A=()=>n(347320),C=()=>n(554493);function I(e){return{url:e.url,method:e.method,format:e.format,headers:(0,u().cJ)(e.headers,[l().B3]),data:e.data?(0,u().cJ)(e.data,["omitExistingRecordVersions","page.spaceId","returnPropertyResultCache"]):void 0}}function P(e){const t=I(e);return(0,m().gB)(o()(t))}class T{constructor(){this.cache=new Map,this.cacheAnalytics=new Map}getPrefetchAnalytic(e){return this.cacheAnalytics.get(e)}async prefetchMultiCellHttpRequest(e,t,n){const o=(0,w().createApiHttpJsonRequestOptions)({...n}),a=await this.prefetchHttpRequest(e,o);if("failed"===a.type||!a.data.fanoutData)return;const r=async o=>{const a=await p().lX(o,10,(async o=>{const a=(0,w().createApiHttpJsonRequestOptions)({environment:n.environment,eventName:t,data:o.request,activeUserId:n.activeUserId,tracking:n.tracking,abortSignal:n.abortSignal,encoding:n.encoding,eventListeners:n.eventListeners,headers:o.headers}),r=P(a),i=`${e}-${r}`;return this.prefetchHttpRequest(i,a)})),i=[];for(const e of a)e&&"success"===e.type&&e.data.fanoutData&&e.data.fanoutData.length&&i.push(...e.data.fanoutData);i.length&&await r(i)};await r(a.data.fanoutData)}prefetchHttpRequest(e,t){const o=new AbortController;t.abortSignal=o.signal;const a=performance.now(),r=(0,n(974233).default)(t),i={request:t,responsePromise:r,abortController:o,responseStatus:"loading",requestStartTime:a,responseEndTime:void 0};return this.cache.set(e,i),this.cacheAnalytics.set(e,{type:"attempted",started_at:a}),r.then((t=>{this.cache.get(e)&&this.cache.set(e,{...i,responseStatus:t.type,responseEndTime:performance.now()})})),r}logPageUnloadBeforeRequestCompletion(e,t){function n(){i().log({level:"warning",from:"prefetchHelpers",type:"pageUnloadedBeforePrefetchCompleted",data:{message:`Prefetch request for "${e}" was still in-flight when page was unloaded.`,prefetchKey:e},keepalive:!0})}window.addEventListener("unload",n),t.finally((()=>{window.removeEventListener("unload",n)}))}logIfKeyIsNeverConsulted(e){const t=Date.now();setTimeout((()=>{if(this.cache.has(e)){const n=Date.now()-t;i().log({level:"warning",from:"prefetchHelpers",type:"prefetchKeyNeverConsulted",data:{message:`Prefetch request for "${e}" was not consulted after ${n} ms.`,prefetchKey:e},keepalive:!0})}}),Number(n(720665).Xb))}getPrefetchedHttpRequest(e){const{key:t,request:n,abortSignal:o,isMobileNative:a}=e,r=this.cache.get(t);if(r){this.cache.delete(t);const e=function(e){const t=I(e.request),n=I(e.cachedRequest);if(t.url!==n.url)return"url";if(t.method!==n.method)return"method";if(t.format!==n.format)return"format";if(!(0,u().n4)(t.headers,n.headers))return"headers";if(!(0,u().n4)(t.data,n.data))return"data"}({request:n,cachedRequest:r.request});if(!e){const e={type:"hit-match",started_at:r.requestStartTime,finished_at:r.responseEndTime};return this.cacheAnalytics.set(t,e),o&&function(e,t){if(e.aborted)return void t.abort();e.addEventListener("abort",(()=>{t.abort()}))}(o,r.abortController),r.responsePromise}{var s,c;i().log({level:"info",from:"prefetchHelpers",type:"prefetchHitMismatch",data:{message:`Got cache hit but it's mismatched for prefetch key "${t}". Mismatch type: ${e}`,prefetchKey:t,mismatchType:e,incomingRequest:n[e],cachedRequest:r.request[e]}});const o=a?{type:"hit-mismatch",mismatch_type:e,cached_page_id:null===(s=r.request[e])||void 0===s||null===(s=s.page)||void 0===s?void 0:s.id,incoming_page_id:null===(c=n[e])||void 0===c||null===(c=c.page)||void 0===c?void 0:c.id,started_at:r.requestStartTime,finished_at:r.responseEndTime}:{type:"hit-mismatch",mismatch_type:e,started_at:r.requestStartTime,finished_at:r.responseEndTime};this.cacheAnalytics.set(t,o)}}else if(!this.cacheAnalytics.has(t)){const e={type:"miss"};this.cacheAnalytics.set(t,e)}}getPrefetchStatus(e){var t;return null===(t=this.cache.get(e))||void 0===t?void 0:t.responseStatus}}async function E(e){const{prefetchCache:t,environment:n,activeUserId:i,blockId:c,omitExistingRecordVersions:u}=e;if(await(0,r().getHtmlStreamQueueEntry)("serverSidePrefetchDataPending"))return;if(i&&b().OPFSBootupRegistry.isEnabled&&b().OPFSBootupRegistry.isPageInCache)return;(0,k().updateLoadCachedPageChunkCalledAt)();const f=(0,s().dR)(e.blockId),_="loadCachedPageChunkV2",y={environment:n,eventName:_,data:{page:{id:c,spaceId:void 0},cursor:{stack:[]},verticalColumns:n.device.isMobile,...u?{omitExistingRecordVersions:u}:{}},activeUserId:i,tracking:void 0,headers:f?{[l().eG]:`${f}`}:void 0,eventListeners:(0,h().getPerformanceEventListeners)({eventName:_,isPrefetchRequest:!0})};let S=3;const A=t.prefetchHttpRequest(`loadCachedPageChunk(${y.data.page.id})`,(0,w().createApiHttpJsonRequestOptions)(y)),C=await A,I=[];"success"===C.type&&(I.push(...C.data.cursors),function(e){const t=performance.now(),{pageChunk:n}=e,o=n.recordMap;if(!Object.keys(o.collection_view??{}).length)return;B();const a=performance.now();g().X.setProcessingTime(a-t)}({environment:n,pageId:c,pageChunk:C.data}));let P=C;for(;S>0&&I.length>0;){const e=[...I];I.length=0,await p().lX(e,5,(async e=>{if(e.stack.length>0){const n=(0,m().gB)(o()(e)),r=(0,d().WS)({cellId:e.cellId}),i={...y,data:{...y.data,cursor:e},headers:r};P=await t.prefetchHttpRequest(`loadCachedPageChunk(${y.data.page.id})-${n}`,(0,w().createApiHttpJsonRequestOptions)(i)),a().e.withListenerIgnored((()=>{"success"===P.type&&P.data.spaceId&&P.data.spaceShortId&&v().D.setState({...v().D.state,[P.data.spaceId]:P.data.spaceShortId})})),"success"===P.type&&I.push(...P.data.cursors)}})),S--}return P}function R(e){const{prefetchCache:t,environment:n,activeUserId:o,data:a}=e;c().K.get({userId:e.activeUserId,key:(0,C().H)("prefetchGetSpaces")})?t.prefetchMultiCellHttpRequest("getSpaces","getSpacesFanout",{environment:n,eventName:"getSpacesInitial",data:a,activeUserId:o,tracking:void 0,eventListeners:(0,h().getPerformanceEventListeners)({eventName:"getSpaces",isPrefetchRequest:!0})}):t.prefetchHttpRequest("getSpaces",(0,w().createApiHttpJsonRequestOptions)({environment:n,eventName:"getSpaces",data:a,activeUserId:o,tracking:void 0,eventListeners:(0,h().getPerformanceEventListeners)({eventName:"getSpaces",isPrefetchRequest:!0})}))}function D(e){const{prefetchCache:t,environment:n,activeUserId:o,collectionViewId:a}=e,r=(0,A().A)(a),i=j(r);i&&t.prefetchHttpRequest(r,(0,w().createApiHttpJsonRequestOptions)({environment:n,eventName:"queryCollection",data:i,activeUserId:o,tracking:{src:"initial_load"},abortSignal:void 0,eventListeners:(0,h().getPerformanceEventListeners)({eventName:"queryCollection",isPrefetchRequest:!0}),headers:(0,d().WS)({spaceId:i.source.spaceId})}))}function q(e){const t=Object(j(A().O));if(!t)return[];const n=t[e];return n?(delete t[e],o=A().O,a=t,localStorage.setItem(`LRU:KeyValueStore2:${o}`,JSON.stringify({timestamp:Date.now(),value:a,important:!0})),Array.isArray(n.v)?n.v??[]:[]):[];var o,a}const M={SidebarComponent:n(362749).lA.SidebarComponent,CollectionViewBlock:n(938090).H6.CollectionViewBlock,personalHome:n(117296).KM.personalHome,posts:n(732430).QF,teamHome:n(3243).tq.TeamHome,aiChatView:n(277942).s7,BlockPropertyRouter:n(239391).J};function O(e){e.isMobile||M.SidebarComponent.load()}function N(){M.BlockPropertyRouter.load()}function B(){M.CollectionViewBlock.load()}function L(e){M.personalHome.load(),e.unifiedFeedEnabled&&M.posts.load(),e.aiChatEnabled&&M.aiChatView.load()}function x(){M.teamHome.load(),M.posts.load()}function U(e){if(!e)return{isHome:!1,homeCollectionViewIds:[],unifiedFeedEnabled:!1,aiChatEnabled:!1};const{isHomeKey:t,homeCollectionViewIdsKey:n,unifiedFeedEnabledKey:o,aiChatEnabledKey:a}=(0,_().h)(e);return{isHome:Boolean(j(t)),homeCollectionViewIds:j(n)??[],unifiedFeedEnabled:Boolean(j(o)),aiChatEnabled:Boolean(j(a))}}function F(e){if(!e)return{isTeamHome:!1,teamHomeCollectionViewIds:[]};const{teamHomeCollectionViewIdsKey:t}=(0,y().k)(e);return{isTeamHome:!0,teamHomeCollectionViewIds:j(t)??[]}}function V(){const{route:e,userId:t,spaceId:n}={route:j("lastVisitedRoute"),userId:j("lastVisitedRouteUserId"),spaceId:j("lastVisitedRouteSpaceId")},o=(0,S().S9)({userId:t,spaceId:n}),a=o?j(o):"last_visited_page";switch(a){case"personal_home":return{blockId:(0,m().gB)(`${t}-${n}-main`),collectionViewId:void 0,teamId:void 0,userId:t};case"chat":return{blockId:void 0,collectionViewId:void 0,teamId:void 0,userId:t};case"first_page":const o=(0,S().qm)({userId:t,spaceId:n});return{blockId:o?j(o):void 0,collectionViewId:void 0,teamId:void 0,userId:t};case"last_visited_page":case void 0:return"team"===(null==e?void 0:e.name)?{blockId:void 0,collectionViewId:void 0,teamId:e.teamId,userId:t}:"page"===(null==e?void 0:e.name)?{blockId:e.blockId,collectionViewId:e.collectionViewId,teamId:void 0,userId:t}:{blockId:void 0,collectionViewId:void 0,teamId:void 0,userId:t};default:return(0,f().ub)(a),{blockId:void 0,collectionViewId:void 0,teamId:void 0,userId:t}}}function j(e){const t=localStorage.getItem(`LRU:KeyValueStore2:${e}`);if(!t)return;const n=JSON.parse(t);if(!n)return;return n.value}},745875:(e,t,n)=>{n.d(t,{Io:()=>a,JZ:()=>d,Yo:()=>c,fp:()=>l,ox:()=>i,rQ:()=>r});var o=n.n(n(883503));function a(e,t){return Math.sqrt(Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2))}function r(e){return!(e<=0)&&(e>=100||100*Math.random()<e)}function i(e){return!(e<=0)&&(e>=1||(arguments.length>1&&void 0!==arguments[1]?arguments[1]:s)()<e)}function s(){return Math.random()}function c(e,t){if(t<=0)return!1;if(t>=100)return!0;const n=function(e,t){return parseInt(o()(e).slice(0,8),16)/4294967295*t}(e,100);return n<t}function l(e,t){return c(e,100*t)}function d(e){if(0===e.length)return NaN;const t=Math.floor(e.length/2),n=[...e].sort(((e,t)=>e-t));return e.length%2!=0?n[t]:(n[t-1]+n[t])/2}},754704:(e,t,n)=>{n.r(t),n.d(t,{buildQueryParametersForTracking:()=>c,createApiHttpJsonRequestOptions:()=>u,createApiHttpRequestOptions:()=>p,getTrackingHeaders:()=>l});n(814603),n(147566),n(198721);const o="x-notion-active-user-header",a="notion-audit-log-platform";var r=()=>n(711059),i=()=>n(780054),s=()=>n(520348);function c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();const n=new URLSearchParams;null!=e&&e.queuedTimestamp&&n.set("throttledMs",(t-e.queuedTimestamp).toString()),null!=e&&e.src&&n.set("src",e.src),null!=e&&e.trigger&&n.set("trigger",e.trigger);const o=n.toString();return o?`?${o}`:""}function l(e){const t={};return null!=e&&e.userFlow&&(t["X-Notion-User-Flow"]=null==e?void 0:e.userFlow),t}function d(e){const{device:t}=e,n={};return t.version&&(n[r().ClientVersionHeader]=t.version),t.desktopAppVersion&&(t.isMac?n[r().MacVersionHeader]=t.desktopAppVersion:t.isWindows&&(n[r().WindowsVersionHeader]=t.desktopAppVersion)),t.mobileAppVersion&&(t.isIOS?n[r().IOSVersionHeader]=t.mobileAppVersion:t.isAndroid&&(n[r().AndroidVersionHeader]=t.mobileAppVersion)),t.auditLogPlatform&&(n[a]=t.auditLogPlatform),n}function u(e){return p({...e,format:"json"})}function p(e){const{environment:t,eventName:n,data:a,activeUserId:r,tracking:u,abortSignal:p,format:m,encoding:f,eventListeners:g,headers:b}=e,h={environment:t,url:`${i().A.api.http}/${n}${c(u)}`,method:"POST",data:a,format:m,headers:{...d(t),...l(u),[o]:r||"",..."defaultRecordCache"in t&&"currentUser"in t?s().L8(t):{},...s().P4(),...b},abortSignal:p,encoding:f,eventListeners:g};return null!=b&&b["x-notion-space-short-id"]&&delete b["x-notion-space-id"],h}},763824:(e,t,n)=>{n.d(t,{Il:()=>_,Mi:()=>w,O4:()=>u,Vq:()=>g,aN:()=>v,bT:()=>r,dS:()=>y,e2:()=>h,lG:()=>p,lX:()=>c,mO:()=>d,nQ:()=>f,vA:()=>s,wR:()=>l,xz:()=>b,yL:()=>i,yX:()=>m});n(16280),n(944114),n(898992),n(354520),n(581454),n(759848);var o=()=>n(532659),a=()=>n(882362);function r(){return new Promise((e=>setImmediate(e)))}function i(e){return Boolean(e&&("object"==typeof e||"function"==typeof e)&&"then"in e&&"function"==typeof e.then)}function s(e,t,n){return new Promise(((o,a)=>{let r=0;const i=[],s=()=>{const c=e.slice(r,r+t);r+=t,c.length>0?n(c).then((e=>{i.push(e),setImmediate(s)})).catch(a):o(i)};s()}))}async function c(e,t,n){if(t<=0)throw new Error(`Invalid concurrency limit: ${t}`);let a;if(Array.isArray(e)){if(e.length<=t){const t=(e,t)=>n(e,t);return Promise.all(e.map(t))}a=new Array(e.length)}else a=[];const r=o().jY.withIndex(e)[Symbol.iterator]();let i=!1;const s=async()=>{try{for(;!i;){const e=r.next();if(e.done)return;const[t,o]=e.value,i=await n(o,t);a[t]=i}}catch(e){throw i=!0,e}},c=[];for(let o=0;o<t;o++)c.push(s());return await Promise.all(c),a}function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a().u;return new Promise((n=>{t.setTimeout((()=>{n()}),e)}))}function d(e,t){return new Promise((n=>{setTimeout((()=>{n(t)}),e)}))}async function u(e){const t=m(),n=Promise.all(e.map((async(e,n)=>{await e,t.resolve(n)})));return{winner:await t.promise,rest:n}}function p(e){return Promise.race(e).then((e=>({status:"fulfilled",value:e})),(e=>({status:"rejected",reason:e})))}function m(){let e,t;const n=new Promise(((n,o)=>{e=n,t=o}));return{resolve:e,reject:t,promise:n}}async function f(e,t){let n;const o=new Promise((t=>{n=setTimeout((()=>{n=void 0,t({result:void 0,timeout:!0})}),e)})),a=i(t)?t:Promise.race(t);return await Promise.race([o,a.then((e=>({result:e,timeout:!1}))).finally((()=>{n&&clearTimeout(n)}))])}function g(e,t){return f(t,e)}async function b(e,t,n){const o=await g(n(),t);return e<=1||!o.timeout?o:b(e-1,t,n)}class h{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a().u;this.deferredPromise=m(),this.isCompleted=!1,this.timeSource=e}async wait(e,t){e>0&&await l(e,this.timeSource);const n=t-e;n>0&&await Promise.race([this.deferredPromise.promise,l(n,this.timeSource)]),this.isCompleted||(this.isCompleted=!0,this.deferredPromise.resolve(void 0))}trigger(){this.isCompleted||this.deferredPromise.resolve(void 0),this.isCompleted=!0}}class _{constructor(e){this._state=void 0,this.runTask=e,this._state={status:"idle"}}get status(){return this._state.status}get state(){return this._state}get elapsedMs(){return"pending"===this._state.status?performance.now()-this._state.startedAt:"resolved"===this._state.status?this._state.resolvedAt-this._state.startedAt:"rejected"===this._state.status?this._state.rejectedAt-this._state.startedAt:void 0}get settledAt(){return"resolved"===this._state.status?this._state.resolvedAt:"rejected"===this._state.status?this._state.rejectedAt:void 0}get result(){return"resolved"===this._state.status?{value:this._state.value}:"rejected"===this._state.status?{error:this._state.error}:void 0}async runImpl(e){const t=performance.now();try{const n=this.runTask(e);this._state={status:"pending",startedAt:t,promise:n};const o=await n;return this._state={status:"resolved",value:o,startedAt:t,resolvedAt:performance.now()},o}catch(o){throw this._state={status:"rejected",error:(0,n(319625).A)(o),startedAt:t,rejectedAt:performance.now()},o}}async runWithRetry(){const e=this._state;return"rejected"===e.status?await this.runImpl(e.error):await this.run()}async run(){const e=this._state;switch(e.status){case"idle":return await this.runImpl();case"pending":return await e.promise;case"resolved":return e.value;case"rejected":throw e.error;default:(0,n(534177).HB)(e)}}async getPendingOrResolved(){const e=this._state;switch(e.status){case"pending":return await e.promise;case"resolved":return e.value;default:return}}}async function v(e){const t=await Promise.allSettled(e),n=[];for(const o of t){if("rejected"===o.status)throw o.reason;n.push(o.value)}return n}async function y(e,t,n){return c(e,t,n).then((t=>e.filter(((e,n)=>t[n]))))}function w(e){return async function(t){return await e(t),t}}},763884:(e,t,n)=>{n.d(t,{K:()=>a});var o=()=>n(419494);const a=new class{constructor(e,t){this.localStorageStore=void 0,this.localStorageStore=new(o().Ay)({namespace:e,important:t,trackingType:"necessary"})}get(e){let{userId:t,key:n}=e;return this.localStorageStore.get(this.makeUserKey(t,n))}set(e){let{userId:t,key:n,value:o}=e;return this.localStorageStore.set(this.makeUserKey(t,n),o)}remove(e){let{userId:t,key:n}=e;return this.localStorageStore.remove(this.makeUserKey(t,n))}clearStorageForUser(e){this.localStorageStore.scan((t=>{t.startsWith(e)&&this.remove({userId:e,key:t})}))}scan(e){this.localStorageStore.scan(e)}makeUserKey(e,t){return`${e||"guest"}:${t}`}}(o().cd,!1)},765957:(e,t,n)=>{n.r(t),n.d(t,{AppStoreCurrentSpaceStore:()=>l,AppStoreCurrentSpaceViewStore:()=>d,AppStoreCurrentUserSettingsStore:()=>p,AppStoreMainEditorCurrentBlockStore:()=>u,default:()=>c,isTransitioningSpaces:()=>r});var o=()=>n(496506),a=()=>n(534177);function r(e){switch(e){case"notTransitioning":return!1;case"joiningOrCreatingSpace":case"switchingToOrLoadingSpace":return!0;default:(0,a().HB)(e)}}class i extends(()=>n(757695))().Store{getInitialState(){return{initialized:!1,spaceTransitionStatus:"notTransitioning",renderPhase:"booting",pageVisitStore:new(n(547726).A),presenceStore:new(n(650031).A),inAppCalloutStore:new(n(841946).A)}}waitUntilRendered(){return this.waitUntil((()=>"rendered"===this.state.renderPhase))}}i.sidebarIsExpandedPreferenceKey="sidebarIsExpanded";const s=new i,c=s,l=new(o().ComputedStore)((()=>s.getState().currentSpaceStore),{debugName:"AppStore.CurrentSpaceStore"}),d=new(o().ComputedStore)((()=>s.getState().currentSpaceViewStore),{debugName:"AppStore.CurrentSpaceViewStore"}),u=new(o().ComputedStore)((()=>s.getState().mainEditorCurrentBlockStore),{debugName:"AppStore.MainEditorCurrentBlockStore"}),p=new(o().ComputedStore)((()=>s.getState().currentUserSettingsStore),{debugName:"AppStore.CurrentUserSettingsStore"})},780054:(e,t,n)=>{n.d(t,{A:()=>r});n(16280);const o=window.CONFIG_OVERRIDE??{env:"production",isAdminMode:!1,isDevelopingInAirplaneMode:!1,isLocalhost:!1,offline:!0,version:"23.13.0.3575",buildTarget:"client",domainBaseUrl:"https://www.notion.so",adminUrl:"https://admin.notion.so",publicDomainName:"notion.site",protocol:"notion",staticS3:{url:"https://prod-notion-assets.s3-us-west-2.amazonaws.com",bucket:"prod-notion-assets"},lastUpdatedTime:1748003713872,api:{http:"/api/v3"},googleOAuth:{clientId:"905154081809-858sm3f0qnalqd9d44d9gecjtrdji9tf.apps.googleusercontent.com"},messageStore:{url:"https://msgstore.www.notion.so",api:"/api/v1"},audioProcessor:{url:"https://audioprocessor.www.notion.so",api:"/api/v1"},stripe:{key:"pk_live_vuNO27XGTCbXjVwneiECILjT"},calendar:{domainBaseUrl:"https://calendar.notion.so",notionAuthUrl:"https://calendar.notion.so/notion-auth",openNotionDatabaseUrl:"https://calendar.notion.so/open-notion-database",createEventUrl:"https://calendar.notion.so/event/create",calendarSettingsUrl:"https://calendar.notion.so/settings/calendars",calendarMeetWithUrl:"https://calendar.notion.so/meet-with",desktopProtocol:"cron",downloadUrl:"https://www.notion.so/calendar/download"},zoom:{desktopProtocol:"zoommtg"},cron:{domainBaseUrl:"https://calendar.cron.com"},mail:{apiBaseUrl:"https://api.mail.notion.so",domainBaseUrl:"https://mail.notion.so",protocol:"notionmail"},identity:{domainBaseUrl:"https://identity.notion.so"},revenueCat:{apiResponseMaxAge:6048e5,entitlementIDs:{personal:"notion.id.personal_pro"},productIDs:{personal:{monthly:"notion.id.personal_pro_monthly",yearly:"notion.id.personal_pro_yearly"}}},mutiny:{personalKey:"1149e901f65fc47c"},partnerStack:{apiKey:"pk_6nwYfqCKEoPt2lTuU8Veswm2zArJ3Apq"},pricing:{invoiceDaysUntilDue:30,free:{spaceBlockLimit:1e3,fileUploadMaxBytes:5e6},team_free:{spaceBlockLimit:1e3,fileUploadMaxBytes:5e6},personal_free:{fileUploadMaxBytes:5e6},student:{productId:"prod_FhChFoDp7gS1Ba"},personal:{productId:"prod_CpavZFCbxF2YGx",monthlyPrice:500,yearlyPrice:4800},plus:{productId:"prod_CpawK4ih14xs4t",monthlyPricePerMember:1e3,yearlyPricePerMember:9600},business:{productId:"prod_LEnFERYcTgENz8",monthlyPricePerMember:1800,yearlyPricePerMember:18e3},enterprise:{productId:"prod_Cpb8M1AFEFhdy1",monthlyPricePerMember:2500,yearlyPricePerMember:24e3},ai:{productId:"prod_N6tyEr9FFSTXJo",monthlyPricePerMember:1e3,yearlyPricePerMember:9600},sites_custom_hostnames:{productId:"prod_Q71OevO5uJ7LaT"}},promotions:{ai:{writer:{baseGrant:{singlePlayerAmount:10,multiplayerAmount:10,unit:"responses"},userGrant:{singlePlayerAmount:10,multiplayerAmount:10,unit:"responses"},grant032023:{singlePlayerAmount:20,multiplayerAmount:20,unit:"responses",waitMs:6048e5},studentGrant:{singlePlayerAmount:50,multiplayerAmount:50,unit:"responses"},studentGitHubGrant:{singlePlayerAmount:50,multiplayerAmount:50,unit:"responses"},maxAllowance:{free:500,paid:500}},qna:{baseGrant:{singlePlayerAmount:10,multiplayerAmount:10,unit:"responses"},userGrant:{singlePlayerAmount:10,multiplayerAmount:10,unit:"responses"},grant032023:{singlePlayerAmount:20,multiplayerAmount:10,unit:"responses",waitMs:6048e5},studentGrant:{singlePlayerAmount:50,multiplayerAmount:50,unit:"responses"},studentGitHubGrant:{singlePlayerAmount:50,multiplayerAmount:50,unit:"responses"},maxAllowance:{free:100,paid:500}}}},trials:{default:{duration:14},enterprise:{duration:30},mm_ent:{duration:30},upwork:{duration:30},perfmark:{duration:30},reverse:{duration:14},reverse_mm_ent:{duration:30},business_reverse:{duration:30},business_cc:{duration:30},stacked_business_trial:{duration:30},admin_3m:{duration:90},admin_6m:{duration:180},admin_12m:{duration:365},admin_3m_smb:{duration:90},admin_6m_smb:{duration:180},admin_12m_lenny:{duration:365},admin_12m_lenny_business:{duration:365},admin_1m_startups_business:{duration:30},admin_3m_startups_business:{duration:90},admin_6m_startups_business:{duration:180},admin_12m_startups_business:{duration:365},creator_6m:{duration:180},creator_12m:{duration:365},creator_12m_no_ai:{duration:365},admin_6m_business:{duration:180},plus_bundle_1m:{duration:30},business_1m:{duration:30}},desktopS3:{url:"https://s3-us-west-2.amazonaws.com/desktop-release.notion-static.com"},publicFileS3:{url:"https://s3-us-west-2.amazonaws.com/public.notion-static.com",bucket:"public.notion-static.com"},secureFileConfig:{rootPath:"/f",protocol:"https",hostname:"file.notion.so"},loggly:{token:"9b01b08e-c969-4e27-837c-805d1fc6ec7b"},splunk:{token:"EA76605A-F565-4B17-A496-34435622A1EB"},embedly:{key:"421626497c5d4fc2ae6b075189d602a2"},iframely:{key:"222a85036317ca50d3ba5f321bfda6f0"},iframely_prod:{key:"656ac74fac4fff346b811dca7919d483"},aif:{url:"https://aif.notion.so/aif-production.html"},contentful:{spaceId:"spoqsaf9291f"},iOSAppId:1232780281,facebook:{pixelId:"499229960464487"},statsig:{apiKey:"client-Tgza5wNFa8dVt9BdeUfG6Vkm29bHxX10MhoztTMzLBB"},googleReCaptcha:{siteKey:"6LcvqigfAAAAAPaPL3j2YLldFcZVGwKvG9TmjDgK"},turnstile:{sitekey:"0x4AAAAAAADLq8YYJOHc6qqw"},google:{clientId:"905154081809-858sm3f0qnalqd9d44d9gecjtrdji9tf.apps.googleusercontent.com",mapsApiKey:"AIzaSyB543mcD0Ehv18H5e0iD8L-J2lyN7AvKCo"},sprig:{environmentId:"2HKBN1wgCwHr"},front:{domainBaseUrl:"https://www.notion.com"},imageProxy:{baseUrl:"https://img.notionusercontent.com/"}};if(!o)throw new Error("CONFIG not found");window.CONFIG=o;const a=(0,n(140583).E)(window,window.navigator.userAgent);o.isMobile=a.isMobile();const r=o},787212:(e,t,n)=>{n.r(t)},796237:(e,t,n)=>{n.d(t,{D:()=>i,l:()=>r});var o=n.n(n(285914)),a=n.n(n(190031));function r(e){const t=a().encode(e);return o().encode(t)}function i(e){const t=o().decode(e);return a().decode(t)}},798880:(e,t,n)=>{n.d(t,{Ay:()=>i,BF:()=>c,EP:()=>f,MF:()=>p,O5:()=>d,ai:()=>m,hR:()=>g,mc:()=>l,oo:()=>r,zz:()=>u});var o=()=>n(498212),a=()=>n(292588);const r="notion_user",i={table:r,columnTypes:{id:a().A.UUID,version:a().A.Number,last_version:a().A.Number,email:a().A.String,given_name:a().A.String,family_name:a().A.String,profile_photo:a().A.String,onboarding_completed:a().A.Boolean,mobile_onboarding_completed:a().A.Boolean,clipper_onboarding_completed:a().A.Boolean,reverify:a().A.Boolean,name:a().A.String,is_banned:a().A.Boolean,suspended_time:a().A.Number},model:(0,n(152853).P)({RecordStore:!0,interfaces:{ActorModelInterface:"@notionhq/shared/models/ActorModelInterface"},properties:{admin_user:{getMethod:!1,getKeyStoreMethod:!1},global_oauth_client:{getMethod:!1,getKeyStoreMethod:!1},given_name:{getMethod:!1},family_name:{getMethod:!1},name:{getMethod:!1}}})},s="https://s3.us-west-2.amazonaws.com/public.notion-static.com/5478707d-f8ea-46d8-902a-d6d932ac20da.png",c={id:o().rN,version:0,email:"<EMAIL>",admin:!0,name:"Notion App",profile_photo:s},l={id:o().zj,version:0,email:"<EMAIL>",admin:!0,name:"Notion Support",profile_photo:s};o().bC;function d(e){return e===c.id}const u={id:o().TC,version:0,email:"<EMAIL>",admin:!0,name:"Notion AI",profile_photo:"https://s3-us-west-2.amazonaws.com/public.notion-static.com/7b0ea76e-4936-406d-8896-ae8bce45615d/image_1-2.png"};function p(e){return e===u.id}const m={id:o().$h,version:0,email:"<EMAIL>",admin:!0,name:"Notion App",profile_photo:s},f={id:o().$4,version:0,email:"<EMAIL>",admin:!1,name:"Notion",profile_photo:s},g={id:o().OB,version:0,email:"<EMAIL>",admin:!0,name:"Anonymous",profile_photo:s}},806080:(e,t,n)=>{n.d(t,{i:()=>o,s:()=>a});n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698);function o(e,t){if(0===e.length||t<=0)return[];const n=new Array(Math.ceil(e.length/t));for(let o=0,a=0;o<e.length;o+=t,a++)n[a]=e.slice(o,o+t);return n}function a(e){return Array.from(new Set(e))}},820103:(e,t,n)=>{n.d(t,{He:()=>i,JT:()=>o,Rw:()=>r,uL:()=>a});const o="default",a=["ai_onboarding_phase_2","form_response_onboarding","education_flow","mail_onboarding","calendar_onboarding","performance_marketing_term_onboarding","mobile_promote_app_download","mobile_tutorial_onboarding","ai_onboarding_phase_2_5",o,"undefined"],r=["mail","calendar"],i=["ai_onboarding_phase_2","ai_onboarding_phase_2_5"]},821062:(e,t,n)=>{n.d(t,{A:()=>s});n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698);const o=Object.prototype.toString,a=Object.prototype.hasOwnProperty,{getPrototypeOf:r}=Object,i=Object.prototype;function s(e,t){if(e===t)return!0;if(Array.isArray(e)){if(!Array.isArray(t))return!1;if(e.length!==t.length)return!1;if(0===e.length)return!0;for(let n=0;n<e.length;n++)if(!d(e[n],t[n]))return!1;return!0}if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(c(e)){if(!c(t))return!1;const n=Object.keys(e),o=Object.keys(t);if(n.length!==o.length)return!1;for(const r in e)if(a.call(e,r)){if(!a.call(t,r))return!1;const n=r.slice(-5);if("props"===n||"style"===n||"Props"===n||"Style"===n){if(!s(e[r],t[r]))return!1}else if(!d(e[r],t[r]))return!1}return!0}if(e instanceof Set){if(!(t instanceof Set))return!1;if(e.size!==t.size)return!1;if(0===e.size)return!0;const n=e.values();let o;for(;o=n.next(),!o.done;)if(!t.has(o.value))return!1;return!0}if(e instanceof Map){if(!(t instanceof Map))return!1;if(e.size!==t.size)return!1;if(0===e.size)return!0;const n=e.entries();let o;for(;o=n.next(),!o.done;)if(!t.has(o.value[0])||!d(t.get(o.value[0]),o.value[1]))return!1;return!0}return!1}function c(e){if("[object Object]"!==o.call(e))return!1;const t=r(e);return null===t||t===i}function l(e){for(const t in e)if(a.call(e,t))return!1;return!0}function d(e,t){return e===t||(Array.isArray(e)?!(0!==e.length||!Array.isArray(t)||0!==t.length):"object"==typeof e&&null!==e&&!!(c(e)&&l(e)&&"object"==typeof t&&null!==t&&c(t)&&l(t)))}},824862:(e,t,n)=>{n.d(t,{Ay:()=>s,FF:()=>r,GP:()=>i,eR:()=>c});var o=()=>n(498212),a=()=>n(292588);const r={notion_user:!0,space:!0},i="bot",s={table:i,columnTypes:{id:a().A.UUID,version:a().A.Number,last_version:a().A.Number,name:a().A.String,parent_table:a().A.String,parent_id:a().A.UUID,created_at:a().A.Number,created_by_id:a().A.UUID,created_by_table:a().A.String,last_edited_at:a().A.Number,last_edited_by_id:a().A.UUID,last_edited_by_table:a().A.String,alive:a().A.Boolean,type:a().A.String,integration_id:a().A.UUID,icon:a().A.String,space_id:a().A.UUID,capabilities:a().A.JSON},model:(0,n(152853).P)({RecordStore:!0,interfaces:{ActorModelInterface:"@notionhq/shared/models/ActorModelInterface"}})},c={id:o().rL,version:0,name:"Automation",type:"automation",created_at:0,created_by_id:o().rN,created_by_table:"notion_user",space_id:o().rN,parent_id:o().rN,parent_table:"notion_user",alive:!0,icon:"https://s3.us-west-2.amazonaws.com/public.notion-static.com/5478707d-f8ea-46d8-902a-d6d932ac20da.png",integration_id:n(617871).XF,capabilities:{read_content:!0}}},835243:(e,t,n)=>{n.d(t,{$e:()=>p,DZ:()=>r,aZ:()=>a,n4:()=>u});n(944114),n(581454);var o=()=>n(526147);function a(e){const{spaceId:t}=e;return{table:"record_key",id:(0,o().F3)(e),spaceId:t}}const r=(0,n(959013).YK)({prefixInvalidError:{id:"collectionUniqueIdHelpers.prefixInvalidError",defaultMessage:"The prefix must start with a letter, followed by one or more (up to 9) alphanumeric characters or hyphens."}}),i=10,s=`[A-Z][A-Z0-9-]{1,${i-1}}`,c=new RegExp(`^${s}$`),l=`${s}-[0-9]+`,d=new RegExp(`^${l}$`);new RegExp(`(?<uniqueId>${l})`,"g");function u(e){return c.test(e)}function p(e){return d.test(e)}},836251:(e,t,n)=>{n.d(t,{UL:()=>h,MN:()=>b,MX:()=>d,wU:()=>v,j5:()=>y,xS:()=>u,hK:()=>w,VY:()=>A,J0:()=>C,Gn:()=>S,J9:()=>k,Al:()=>_,ok:()=>p,w6:()=>m,$o:()=>g,nR:()=>f});n(944114),n(814603),n(147566),n(198721);var o=()=>n(662303);const a=JSON.parse('{"guides.guides":"https://www.notion.com/help","guides.comments":"https://www.notion.com/help/comments-mentions-and-reminders","guides.backlinks":"https://www.notion.com/help/create-links-and-backlinks","guides.notionForTeams":"https://www.notion.com/teams","guides.keyboardShortcuts":"https://www.notion.com/help/keyboard-shortcuts","guides.reinstallMac":"https://www.notion.com/help/notion-for-desktop#mac-desktop-app","guides.reinstallWindows":"https://www.notion.com/help/notion-for-desktop#windows-desktop-app","guides.whyDesktop":"https://www.notion.com/help/notion-for-desktop#why-use-the-desktop-app","guides.upgradeAndroid":"https://www.notion.com/help/notion-for-mobile","guides.upgradeiOS":"https://www.notion.com/help/notion-for-mobile","guides.dataAndIntegrations":"https://www.notion.com/help/category/import-export-and-integrate","guides.import":"https://www.notion.com/help/import-data-into-notion","guides.importEvernote":"https://www.notion.com/help/import-data-into-notion#evernote","guides.importGoogleDocs":"https://www.notion.com/help/import-data-into-notion#google-docs","guides.importQuip":"https://www.notion.com/help/import-data-into-notion#quip","guides.importDropboxPaper":"https://www.notion.com/help/import-data-into-notion#dropbox-paper","guides.importWorkflowy":"https://www.notion.com/help/import-data-into-notion#workflowy","guides.importConfluence":"https://www.notion.com/help/import-data-into-notion#confluence","guides.importAsana":"https://www.notion.com/help/asana","guides.sharing":"https://www.notion.com/help/sharing-and-permissions","guides.invitePerson":"https://www.notion.com/help/add-members-admins-guests-and-groups","guides.formulas":"https://www.notion.com/help/formulas","guides.formulasWhatsChanged":"https://www.notion.com/help/new-formulas-whats-changed","guides.allUpdates":"https://www.notion.com/help/updates-and-notifications","guides.notifications":"https://www.notion.com/help/updates-and-notifications","guides.quickFind":"https://www.notion.com/help/searching-with-quick-find","guides.relations":"https://www.notion.com/help/relations-and-rollups","guides.githubRelation":"https://www.notion.com/help/github","guides.reminders":"https://www.notion.com/help/reminders","guides.trash":"https://www.notion.com/help/duplicate-delete-and-restore-content","guides.pageHistory":"https://www.notion.com/help/duplicate-delete-and-restore-content#version-history","guides.pageHistoryRestore":"https://www.notion.com/help/duplicate-delete-and-restore-content#restore-past-versions-of-a-page","guides.database":"https://www.notion.com/help/intro-to-databases","guides.databaseView":"https://www.notion.com/help/views-filters-and-sorts#view","guides.databaseType":"https://www.taylorswift.com/tour-us/","guides.databaseFilter":"https://www.notion.com/help/views-filters-and-sorts#filter","guides.databaseSort":"https://www.notion.com/help/views-filters-and-sorts#sort","guides.databaseGroup":"https://www.notion.com/help/boards#reorder-columns-&-cards","guides.databaseProperties":"https://www.notion.com/help/database-properties","guides.databaseCalendar":"https://www.notion.com/help/calendars","guides.sidebarHome":"https://www.notion.com/help/home-and-my-tasks","guides.taskDatabases":"https://www.notion.com/help/guides/give-your-to-dos-a-home-with-task-databases","guides.syncedDatabases":"https://www.notion.com/help/guides/synced-databases-bridge-different-tools","guides.syncedContent":"https://www.notion.com/help/link-previews-and-synced-databases","guides.tableOfContents":"https://www.notion.com/help/columns-headings-and-dividers#table-of-contents","guides.billingAndPayment":"https://www.notion.com/help/billing-and-payment-info","guides.billingAndPaymentRevised":"https://www.notion.com/help/category/plans-billing-and-payment","guides.equations":"https://www.notion.com/help/math-equations","guides.profileSettings":"https://www.notion.com/help/account-settings#profile-settings","guides.notificationSettings":"https://www.notion.com/help/notification-settings","guides.referralsAndCredit":"https://www.notion.com/help/billing-and-payment-info","guides.exportWorkspace":"https://www.notion.com/help/workspace-settings","guides.exportMembers":"https://www.notion.com/help/workspace-settings","guides.deleteWorkspace":"https://www.notion.com/help/create-delete-and-switch-workspaces#delete-workspace","guides.members":"https://www.notion.com/help/add-members-admins-guests-and-groups","guides.groups":"https://www.notion.com/help/add-members-admins-guests-and-groups","guides.changeBillingInterval":"https://www.notion.com/help/billing-and-payment-info","guides.databaseTemplates":"https://www.notion.com/help/database-templates","guides.samlSettings":"https://www.notion.com/help/saml-sso-configuration","guides.securitySettings":"https://www.notion.com/help/workspace-settings","guides.scim":"https://www.notion.com/help/provision-users-and-groups-with-scim","guides.imagesFaqs":"https://www.notion.com/help/images-files-and-media","guides.videosFaqs":"https://www.notion.com/help/images-files-and-media","guides.audioFaqs":"https://www.notion.com/help/images-files-and-media","guides.embedsFaqs":"https://www.notion.com/help/embed-and-connect-other-apps","guides.remoteTips":"https://www.notion.com/help/guides/using-notion-for-remote-work","guides.transclusions":"https://www.notion.com/help/synced-blocks","guides.unfurling":"https://www.notion.com/help/embed-and-connect-other-apps#link-previews","guides.joinOrCreateWorkspace":"https://www.notion.com/help/create-delete-and-switch-workspaces#join-an-existing-workspace","guides.autogeneratedDomains":"https://www.notion.com/help/public-pages-and-web-publishing","guides.billingGuide":"https://www.notion.com/help/billing-and-payment-info","guides.connectedApps":"https://www.notion.com/help/embed-and-connect-other-apps","guides.publicAPI":"https://www.notion.com/help/add-and-manage-connections-with-the-api","guides.enterpriseIntegrationSettings":"https://www.notion.com/help/add-and-manage-connections-with-the-api#enterprise-connection-settings","guides.tasksAndProjectsGettingStarted":"https://www.notion.com/help/guides/getting-started-with-projects-and-tasks","templateGallery":"https://www.notion.com/templates","guides.howToSubmitATemplate":"https://www.notion.com/help/guides/the-ultimate-guide-to-notion-templates","community":"https://www.notion.so/notion/04f306fbf59a413fae15f42e2a1ab029","terms":"https://www.notion.so/notion/Terms-and-Privacy-28ffdd083dc3473e9c2da6ec011b58ac","terms.discounts.ai.summer.2023":"https://www.notion.so/notion/Notion-AI-Add-On-Summer-2023-Discount-Terms-and-Conditions-41a44ee5ddef45dd9594fdb599e9f4cf","terms.currency.fxRates":"https://notion.notion.site/Local-Currency-FAQs-bb75546f474a440dbb3447382b9db19b","contentPolicy":"https://www.notion.so/notion/1b9a773d5583486cb5c1d39a8d777a55","guides.enterpriseAdmins":"https://notion.com/help/enterprise-admins","guides.auditLog":"https://www.notion.com/help/audit-log","guides.auditLog.pageEvents":"https://www.notion.com/help/audit-log#audit-log-events","guides.teamspacesLearnMore":"https://www.notion.com/help/guides/teamspaces-give-teams-home-for-important-work","guides.teamspacesWorkspaceOwner":"https://www.notion.com/help/guides/grant-access-teamspaces","guides.teamspacesSettingsAndPermissions":"https://www.notion.com/help/intro-to-teamspaces#modify-teamspace-settings","guides.customizePageView":"https://www.notion.com/help/customize-and-style-your-content#customize-backlinks-&-comments","guides.domainManagement":"https://www.notion.com/help/domain-management","guides.samlJIT":"https://www.notion.com/help/saml-sso-configuration#just-in-time-(jit)-provisioning","guides.workspaceAnalytics":"https://notion.com/help/workspace-analytics","guides.pageAnalytics":"https://notion.com/help/page-analytics","guides.students":"https://www.notion.com/product/notion-for-education","guides.transferContent":"https://www.notion.com/help/transfer-content-deprovisioned-user","guides.duplicateToSpace":"https://www.notion.com/help/transfer-content-to-another-account","guides.mfa":"https://notion.com/help/two-step-verification","adminContentSearch":"https://www.notion.com/help/admin-content-search","ai.fairUsePolicies":"https://www.notion.com/help/ai-pricing-and-usage","ai.terms":"https://notion.notion.site/fa9034c8b5a04818a6baf3eac2adddbb","guides.wikis":"https://notion.com/help/wikis-and-verified-pages","ai.dataSharingTerms":"https://www.notion.so/notion/Notion-Data-Sharing-Terms-3a6400543d2a4aba8b4d1053c6770810","guides.workspaceConsolidation":"https://notion.notion.site/Notion-Workspace-Consolidation-Guide-0eca1f05f2614ff6818c86c3b3fb0357","blog.pmLaunch":"https://www.notion.com/blog/new-ai-powered-notion-projects","blog.enterpriseLaunch":"https://www.notion.com/blog/the-next-chapter-in-notion-for-enterprise","blog.notionCalendarLaunch":"https://www.notion.com/blog/introducing-notion-calendar","guides.sprintPlanning":"https://www.notion.com/help/guides/product-engineering-notion-sprint-planning","guides.aiAutofill":"https://www.notion.com/help/autofill","guides.managedUsers":"https://www.notion.com/help/managed-users-dashboard","guides.guestRequests":"https://www.notion.com/help/add-members-admins-guests-and-groups#guests","embed.guides":"https://www.notion.com/embed/help","guides.altText":"https://www.notion.com/help/images-files-and-media#alt-text","guides.externalImportAndSync":"https://www.notion.com/help/jira#synced-databases","database.loadTimesAndPerformance":"https://www.notion.com/help/optimize-database-load-times-and-performance","guides.customDataRetentionRules":"https://notion.com/help/custom-data-retention-settings","guides.homeCalendar":"https://www.notion.com/help/home-and-my-tasks#upcoming-events","charts.launchModalPage":"https://notion.notion.site/27827b36230e497ca178d8a763f86d18","guides.charts":"https://www.notion.com/help/charts","guides.chartsVisualizeData":"https://notion.com/help/guides/charts-visualize-data-track-progress-in-notion","layouts.launchModalPage":"https://www.notion.so/notion/Layout-Builder-Beta-Overview-Doc-49149951bff0410fa194b73c952b5982?pvs=4","guides.hipaaCompliancy":"https://www.notion.com/help/hipaa","guides.hipaaBAA":"https://notion.notion.site/Business-Associate-Agreement-909d9f4ccca041b1a23d0fe6e56fa111","guides.forms":"https://www.notion.com/help/forms","product.forms":"https://www.notion.com/product/forms","guides.projects":"https://www.notion.com/product/projects","guides.docs":"https://www.notion.com/product/docs","guides.marketplaceSelling":"https://www.notion.com/help/selling-on-marketplace","resurrectionLearnMore":"https://notion.notion.site/Limited-Time-Plus-Plan-Discount-10cefdeead05800fb455f8c4512aff44","guides.addingTemplateFromMarketplace":"https://www.notion.com/help/finding-templates-on-marketplace#add-a-template","guides.aiConnectors":"https://www.notion.com/help/notion-ai-connectors","guides.aiConnectorGithub":"https://www.notion.com/help/notion-ai-connector-for-github","guides.aiConnectorSlack":"https://www.notion.com/help/notion-ai-connectors-for-slack","guides.aiConnectorJira":"https://www.notion.com/help/notion-ai-connector-for-jira","guides.webhookAction":"https://www.notion.com/help/webhook-actions","guides.passkeys":"https://www.notion.com/help/passkeys","guides.aiConnectorLinear":"https://www.notion.com/help/notion-ai-connector-for-linear","guides.notionAiForWork":"https://notion.com/blog/notion-ai-for-work","guides.meetingNotes":"https://www.notion.com/help/ai-meeting-notes","guides.meetingNotes.consent":"https://www.notion.com/help/ai-meeting-notes#consent-message","guides.aiConnectorMicrosoftTeams":"https://www.notion.com/help/notion-ai-connector-for-microsoft-teams","guides.aiConnectorSharepoint":"https://www.notion.com/help/notion-ai-connector-for-microsoft-sharepoint-and-onedrive"}');var r=()=>n(861017),i=()=>n(939768),s=()=>n(780054),c=()=>n(852100),l=()=>n(155959);function d(e){return!!function(e){return"navigation"in e}(window)&&("back"===e?window.navigation.canGoBack:window.navigation.canGoForward)}function u(e){return o().J&&o().J[e]?o().J[e]:a[e]}function p(){try{return window.location.host===new URL(s().A.domainBaseUrl).host}catch{}return!1}function m(e){const{RouterStore:t}=e;return f(t.state.route)}function f(e){return Boolean(("page"===e.name||"space"===e.name)&&e.requestedOnPublicDomain)}function g(e){return Boolean(("page"===e.name||"space"===e.name)&&e.requestedOnExternalDomain)}function b(e){const{TabbedRouterStore:t,RouterStore:n}=e;return _(e)?t.canGoForward():e.device.isElectron?d("forward"):n.canGoForward()}function h(e){const{TabbedRouterStore:t,RouterStore:n}=e;return _(e)?t.canGoBack():e.device.isElectron?d("back"):n.canGoBack()}function _(e){const{device:t}=e;return t.isMobile&&t.isPhone&&(0,l().T)("supportsWebManagedTabNavigation")}function v(e){const t=(0,r().parseRoute)({url:e,baseUrl:s().A.domainBaseUrl,publicDomainName:s().A.publicDomainName,isMobile:!1,protocol:s().A.protocol,currentUrl:void 0});return"page"===t.name?t.blockId:"team"===t.name?t.peekViewBlockId:void 0}function y(e){return`${s().A.domainBaseUrl}/${e||o().J.login}`}function w(e){const{route:t}=e.RouterStore.state;if(t&&"marketplace"===t.name&&"templates"===t.pageType&&t.slug)return(0,c().sO)(t.slug,t)}function k(e){const{route:t}=e.RouterStore.state;return"team"===t.name}function S(e){const{teamStore:t,environment:n}=e;if(!t)return!1;const{route:o}=n.RouterStore.state;return"team"===o.name&&(null==t?void 0:t.id)===o.teamId}function A(e){const{routeId:t,spaceId:n,formId:o}=e;return(0,i().Gm)({url:u(t),query:{from:"forms",spaceId:n,formId:o}})}function C(e){try{if((0,i().cW)(e))return!0;const t=new URL(e),n=["https://notion.so","https://notion.com","https://notion.site"];"localhost"===window.location.hostname&&n.push("http://localhost:3000");return n.some((e=>(0,i().mJ)(t.href,e)))}catch(t){return!1}}},838364:(e,t,n)=>{n.d(t,{Hc:()=>r,lS:()=>a,nG:()=>o});const o="reactNativeWebViewSchemeHandlerBody",a=44,r=52},839816:(e,t,n)=>{n.d(t,{II:()=>c,Pb:()=>r,Rv:()=>a,X6:()=>i,dc:()=>o,fr:()=>s});const o=["necessary","preference","performance","targeting"],a=["notion_user_id","notion_users","notion_public_domain_user_id","notion_browser_id","notion_ghost_admin_user_id","notion_ghost_preferred_role","notion_cookie_consent","notion_check_cookie_consent","notion_locale","notion_experiment_device_id","csrf","notion_calendar_csrf","NEXT_LOCALE","file_token","growSumoPartnerKey","notion_s2s_tracking_params","device_id","sync_session","l_sync_session","s_sync_session","d_sync_session","p_sync_session","notion_cookie_sync_completed","notion_test_cookie_sync_completed","notion_sync_user_id"],r=["l_sync_session","d_sync_session","s_sync_session","p_sync_session"],i={token_v2:"necessary",file_token:"necessary",sync_session:"necessary",l_sync_session:"necessary",d_sync_session:"necessary",s_sync_session:"necessary",p_sync_session:"necessary",notion_user_id:"necessary",notion_users:"necessary",notion_public_domain_user_id:"performance",notion_browser_id:"performance",notion_ghost_admin_user_id:"necessary",notion_ghost_preferred_role:"necessary",notion_cookie_consent:"necessary",notion_check_cookie_consent:"necessary",notion_locale:"necessary",notion_experiment_device_id:"necessary",NEXT_LOCALE:"necessary",eap_csrf:"necessary",csrf:"necessary",notion_calendar_csrf:"necessary","mutiny.user.token":"performance",io:"necessary",_fbp:"targeting",growSumoPartnerKey:"performance",notion_s2s_tracking_params:"necessary",notion_cookie_sync_completed:"necessary",notion_test_cookie_sync_completed:"necessary",notion_sync_user_id:"necessary",device_id:"necessary",momentic_test_main_cell_outage:"testOnly",momentic_test_canary_cell:"testOnly"},s={amplitude:"performance",intercom:"preference",zendesk:"preference",statsig:"necessary",clearbit:"targeting",loggly:"necessary",mutiny:"performance",google_tag_manager:"targeting",marketo:"targeting",customer_io:"targeting",partner_stack:"targeting",metadata_io:"targeting",reddit:"targeting"},c={amplitude:["amp_","amplitude_"],intercom:["intercom"],zendesk:["zendesk"],statsig:["STATSIG_"],clearbit:["cb_"],loggly:["loggly"],mutiny:["mutiny","_mutiny"],google_tag_manager:["_gcl_"],marketo:["_mkto_"],customer_io:["_cio"],partner_stack:["fs_uid","_gid","_dw","entry_time","_dwrf","_ga","__ssid","__zlcmid"],metadata_io:["Metadata_"],reddit:["_rdt_"]}},841126:(e,t,n)=>{n.r(t)},841946:(e,t,n)=>{n.d(t,{A:()=>c,z:()=>o});n(16280),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(581454);const o={dummy_modal:0,dummy_modal_2:0,omnimodal:0,mwn_feature_modal:0,wiki_promo:0,ai_assistant_announcement:0,ai_github_ai_connector_announcement:0,ai_jira_universal_qna_announcement:0,ai_microsoft_teams_universal_qna_announcement:0,notion_calendar_launch_promo:0,organization_onboarding_modal:0,posts_onboarding_modal:0,notion_mail_launch_modal:0,notion_mail_launch_modal_2:0,notion_mail_launch_2_callout:0,passkey_nudge_modal:0,ai_for_work_announcement_modal:0,meeting_notes_prelaunch_modal:0,jira_sync_info_popup:0,improve_jira_sync_popup:0,sidebar_tour:1,better_template_initialization:2,asana_post_import_tour:2,ai_slack_qna_embed_tooltip:2,set_default_landing_page_tooltip:2,integration_webhooks:3,import_data_prompt:3,customer_io:3,add_on_discount_popup:3,ai_onboarding_tooltip:3,ai_sales_assisted_notification_tooltip:3,ai_slack_qna_notification_tooltip:3,ai_google_drive_qna_notification_tooltip:3,ai_assistant_origin_element_tooltip:3,ai_enhanced_qna_notification_tooltip:3,student_ai_offer_popup:3,clear_templates_opt_out:3,open_in_calendar_tooltip:3,desktop_preference_popup:3,cookie_preference_popup:3,dictation_new_page_origin_element_tooltip:3,startup_sidebar_footer:3,sidebar_upgrade_nudge:3,ai_limit_nudge:3,block_limit_nudge:3,student_org_prompt:3,language_switch_prompt:3,desktop_download_sidebar:3,forms_share_form_tooltip:3,forms_add_conditional_logic_tooltip:3,demo_tour:3,post_reverse_trial_sidebar:3,onboarding_reverse_trial_sidebar:3,resurrection_offer_sidebar:3,customize_slug_tooltip:3,collection_add_item_button_tooltip:3,ai_page_translation:3,workflow_template_installation:3,jira_ai_sidebar_reminder:3,collaboration_callout:3,public_share_link_tooltip:3,self_serve_business_trial_nudge_d0:3,self_serve_business_trial_nudge_d1:3,self_serve_business_trial_nudge_d3:3,self_serve_business_trial_nudge_d5:3,self_serve_business_trial_nudge_d8:3,self_serve_business_trial_nudge_d11:3,self_serve_business_trial_nudge_d14:3,self_serve_business_trial_nudge_d17:3,self_serve_business_trial_nudge_d20:3,self_serve_business_trial_ending_soon:3,search_unified_find_result_tooltip:3,public_share_link_topbar_tooltip:3,research_mode_chat_history_tooltip:3,anyone_with_the_link_sidebar_callout:3},a=["cookie_preference_popup","desktop_preference_popup","student_ai_offer_popup","demo_tour","ai_page_translation","integration_webhooks"],r=["ai_assistant_announcement","notion_calendar_launch_promo","dummy_modal","mwn_feature_modal","organization_onboarding_modal","posts_onboarding_modal","notion_mail_launch_modal","notion_mail_launch_modal_2"];class i extends(()=>n(757695))().Store{getInitialState(){return{activeCallouts:new Set,didShowModal:{value:!1,modalShown:null}}}updateCalloutStatus(e){let{calloutId:t,visible:n,validateCanShow:o=!1}=e;const{activeCallouts:a}=this.state;if(n){if(o&&!this.getCalloutVisibility(t).canShow)return!1;const e=new Set(a).add(t);for(const n of a){const o=s(n);s(t)<o&&e.delete(n)}this.setState({activeCallouts:e,didShowModal:{value:this.state.didShowModal.value||r.includes(t),modalShown:r.includes(t)?t:this.state.didShowModal.modalShown}})}else{const e=new Set(a);e.delete(t);this.setState({...this.state,activeCallouts:e})}return!0}getCalloutVisibility(e){const{activeCallouts:t}=this.state;if(t.has(e))return{canShow:!0};if(a.includes(e)&&this.state.didShowModal.value)return{canShow:!1,conflictingKey:e};if(r.includes(e)&&this.state.didShowModal.value&&this.state.didShowModal.modalShown!==e)return{canShow:!1,conflictingKey:e};for(const n of t){if(s(n)<=s(e))return{canShow:!1,conflictingKey:n}}return{canShow:!0}}}function s(e){if(e in o){return o[e]}return 100}const c=i},851941:(e,t,n)=>{n.d(t,{X2:()=>r,Yf:()=>i,dR:()=>s});var o=()=>n(498212),a=()=>n(534177);function r(e){const t=e.length===o().sO?e.slice(3,12):e.slice(3,8)+e.slice(9,13);return parseInt(t,16)}const i={production:58102339916,staging:4561079407,development:22037813777,local:12471060754};function s(e){if(void 0===e)return;const t=(0,o().Iq)(e);switch(t){case"uuid-v4":case"unknown":return;case"notion-v1":case"notion-v1d":case"notion-v0":break;default:return void(0,a().ub)(t)}const n=r(e);return function(e){return e===i.production||e===i.staging||e===i.development||e===i.local}(n)?"main":n}},852100:(e,t,n)=>{n.d(t,{$L:()=>u,bq:()=>d,li:()=>l,sO:()=>c});n(814603),n(147566),n(198721);var o=()=>n(427704),a=()=>n(720665),r=()=>n(780054),i=()=>n(11113);const s=["creators","categories","search","collections"];function c(e,t,n){const a=new URL(`${r().A.domainBaseUrl}${o().JZ.marketplace}/templates/${encodeURIComponent(e)}`);if(t&&t.pageType&&s.includes(t.pageType)){const e=(0,i().oY)(t.pageType,"search"===t.pageType?t.query:t.slug);e&&a.searchParams.append("cr",encodeURIComponent(e))}return t&&t.crumbs&&(a.searchParams.has("cr")||a.searchParams.append("cr",encodeURIComponent(t.crumbs))),n&&n.username!==t.slug&&(a.searchParams.delete("cr"),a.searchParams.append("cr",`cre:${encodeURIComponent(n.username)}`)),a.toString()}function l(e,t){const n=new URL(c(e,t));return n.searchParams.append("preview",encodeURIComponent("true")),n.toString()}function d(e){return Math.floor((Date.now()-e.created_at)/a().lG)<=2}function u(e){switch(e){case"public_page":case"notion_template_gallery":return e;default:return}}},854150:(e,t,n)=>{n.d(t,{Ay:()=>u,fL:()=>i,nJ:()=>l,y:()=>r,yK:()=>d});var o=()=>n(638681),a=()=>n(292588);const r=["none","team_level_guest","member","owner"],i={space:!0},s={disable_public_access:o().boolean(),disable_team_page_edits:o().boolean(),disable_guests:o().boolean(),disable_export:o().boolean()},c={disable_invite_link:o().boolean()},l=(o().union([o().object({required:{visibility:o().literals("team_members","space_members"),space_member_join_access:o().literal("invite_only"),invite_access:o().literals("team_members","team_owners")},optional:{...s,...c}}),o().object({required:{visibility:o().literal("space_members"),space_member_join_access:o().literal("self_join"),invite_access:o().literal("team_members")},optional:{...s,...c}})]),["open","closed","private"]),d="team",u={table:d,columnTypes:{id:a().A.UUID,version:a().A.Number,last_version:a().A.Number,space_id:a().A.UUID,name:a().A.String,description:a().A.String,icon:a().A.String,created_time:a().A.Number,created_by_table:a().A.String,created_by_id:a().A.UUID,last_edited_time:a().A.Number,last_edited_by_table:a().A.String,last_edited_by_id:a().A.UUID,archived_by:a().A.UUID,archived_at:a().A.Number,team_pages:a().A.UUIDArray,pinned_pages:a().A.UUIDArray,parent_id:a().A.UUID,parent_table:a().A.String,settings:a().A.JSON,is_default:a().A.Boolean,membership:a().A.JSON,permissions:a().A.JSON},model:(0,n(152853).P)({RecordStore:!0,properties:{pinned_pages:{getMethod:!1},settings:{getKeyStoreMethod:!0},icon:{getMethod:"getRawIcon"},membership:{getMethod:"getRawMembership"}}})}},855337:(e,t,n)=>{e.exports=n.p+"4297f07bc0552d8b.ts"},857639:(e,t,n)=>{n.r(t),n.d(t,{BatchedLogger:()=>k,clientLogglyEnvironmentData:()=>p,initialize:()=>u,log:()=>h,logWithSampleRate:()=>_,pushWithMaxLength:()=>w,rateLimitedLog:()=>y,setConsoleLogLevel:()=>m});n(944114);var o=()=>n(502800),a=()=>n(745875);n(18107),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(967357),n(898992),n(803949);function r(e){return e}function i(){const e=new Array;return function(t,n){if("object"!=typeof n||null===n)return n;for(;e.length>0&&e.at(-1)!==this;)e.pop();return e.includes(n)?"[Circular]":(e.push(n),n)}}class s{constructor(e){let{token:t,transform:n}=e;this.token=void 0,this.transform=void 0,this.handles=void 0,this.token=t,this.transform=n??r,this.handles=new Set}static initialize(e){let{splunk:t}=e;const n=Array.isArray(globalThis._DualLogger)?globalThis._DualLogger:new Array;globalThis._DualLogger=new s(t),n.forEach((e=>{var t;return null===(t=globalThis._DualLogger)||void 0===t?void 0:t.push(e)}))}async pushAsync(e){const t=await this.transform(e);return(o=s.SPLUNK_URL,a={method:"POST",headers:{"Content-Type":"text/plain; charset=utf-8",Authorization:`Splunk ${this.token}`},mode:"cors",keepalive:e.keepalive,body:JSON.stringify(t,i()),priority:"low"},n.g.fetch(o,a)).then((0,n(763824).Mi)((t=>{!t.ok&&globalThis&&globalThis.console&&console.error(`Failed to log to splunk with error code (${t.status})`,e)}))).catch((e=>{globalThis&&globalThis.console&&console.error("Failed to connect to splunk server",e)}));var o,a}push(e){const t=this.pushAsync(e);this.handles.add(t),t.finally((()=>this.handles.delete(t)))}async flush(){const e=Array.from(this.handles);e.forEach((e=>this.handles.delete(e))),await Promise.all(e)}}s.SPLUNK_URL="https://http-inputs-notion.splunkcloud.com/services/collector/raw";const c=new Array;globalThis._DualLogger??=c;let l="debug",d="local";function u(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};l=e.isLocalhost?"warning":"debug",d=e.env,s.initialize({splunk:{...t.splunk,token:e.splunk.token}})}const p={};function m(){l=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"debug"}(0,n(604341).exposeDebugValue)("setLogLevel",m);const f=["error","warning","info","debug"];function g(e,t){return f.indexOf(e)<=f.indexOf(t)}function b(e){const{level:t,from:n,type:o,...a}=e,r=`${(new Date).toISOString()} [${t}] ${n}: ${o}`;console.info(r,a)}function h(e,t){_({logMessage:e,samplePercentage:v(e),forceSuppressConsoleLog:null==t?void 0:t.forceSuppressConsoleLog})}function _(e){var t;let{logMessage:n,samplePercentage:r,forceSuppressConsoleLog:i}=e;if(g(n.level,l)&&!i&&b(n),!(0,a().rQ)(r)||!function(e){if("ios"===p.os&&"error"===e.level&&e.error&&"{}"===(0,o().WS)(e.error))return!1;return!0}(n))return;if(n.data&&n.data.miscDataToConvertToString){const{miscDataToConvertToString:e,...t}=n.data;n.data=Object.assign(t,e)}const s={environment:d,...n,instantClientData:{href:"undefined"!=typeof window?window.location.href:void 0,clientTimestamp:Date.now()},clientEnvironmentData:p};void 0!==n.data&&(s.data=(0,o().iX)(n.data)),null===(t=globalThis._DualLogger)||void 0===t||t.push(s)}function v(e){return e.error&&"VersionError"===e.error.name?1:e.error&&"ClientError"===e.error.name?10:100}const y=n(496603).nF(h,2e3);function w(e,t,n){e.push(t),e.length>n&&e.splice(0,e.length-n)}class k{constructor(e){this.from=void 0,this.type=void 0,this.level=void 0,this.logToConsole=void 0,this.maxLength=void 0,this.messages=[],this.flush=()=>{if(0===this.messages.length)return;h({level:this.level,from:this.from,type:this.type,data:{miscDataToConvertToString:{messages:this.messages}}}),this.messages=[]};const{from:t,type:n,level:o,maxLength:a,logToConsole:r}=e;this.maxLength=a,this.from=t,this.type=n,this.level=o,this.logToConsole=r??!0}log(e){this.logToConsole&&g(e.level,this.level)&&b(e),w(this.messages,{...e,ts:Date.now()},this.maxLength)}}},861017:(e,t,n)=>{n.d(t,{I2:()=>W,oU:()=>te,Bi:()=>de,ak:()=>oe,tt:()=>ue,Rq:()=>be,TD:()=>he,S_:()=>ve,_:()=>_e,g$:()=>me,LC:()=>pe,Hs:()=>fe,nX:()=>ke,p0:()=>we,bQ:()=>ge,YO:()=>ye,b1:()=>ae,wX:()=>G,Fy:()=>Z,bn:()=>ce,hZ:()=>Ce,Jq:()=>ie,v1:()=>K,bh:()=>Pe,Jx:()=>Q,WZ:()=>ee,Dk:()=>Ie,bD:()=>Ee,L7:()=>ne,parseRoute:()=>re,BH:()=>Se,m6:()=>Ae});n(581454),n(964979),n(814603),n(147566),n(198721);var o=()=>n(775084),a=()=>n(496603),r=()=>n(498212),i=()=>n(502492),s=()=>n(534177),c=()=>n(939768),l=()=>n(720665),d=()=>n(885443),u=()=>n(584262);const p="allTemplates",m={type:"private"},f=!0;function g(e){if(e&&(0,r().iv)(e))return(0,r().G2)(e)}var b=()=>n(796237),h=()=>n(835243),_=()=>n(547568),v=()=>n(236005),y=()=>n(590265),w=()=>n(671593),k=()=>n(638681);const S=k().object({required:{name:k().literal("nativeTab"),tab:k().literal("search")},optional:{spaceId:k().undefinable(k().string()),searchQuery:k().undefinable(k().string()),searchRequest:k().undefinable(k().string())}}),A=k().object({required:{name:k().literal("nativeTab"),tab:k().literal("updates")},optional:{spaceId:k().undefinable(k().string())}}),C=k().object({required:{name:k().literal("nativeTab"),tab:k().literal("assistant")},optional:{spaceId:k().undefinable(k().uuid())}}),I=k().object({required:{name:k().literal("nativeTab"),tab:k().literal("trash")},optional:{spaceId:k().undefinable(k().uuid())}}),P=k().object({required:{name:k().literal("nativeTab"),tab:k().literal("settings")},optional:{spaceId:k().undefinable(k().uuid())}}),T=k().object({required:{name:k().literal("nativeTab"),tab:k().literal("members")},optional:{spaceId:k().undefinable(k().uuid())}}),E=k().object({required:{name:k().literal("nativeTab"),tab:k().literal("help")},optional:{spaceId:k().undefinable(k().uuid())}}),R=k().object({required:{name:k().literal("nativeTab"),tab:k().literal("templates")},optional:{spaceId:k().undefinable(k().uuid())}}),D=k().object({required:{name:k().literal("nativeTab"),tab:k().literal("offline")},optional:{spaceId:k().undefinable(k().uuid()),from:k().undefinable(k().union([k().literal("offline_indicator"),k().literal("settings")]))}}),q=k().object({required:{name:k().literal("nativeTab"),tab:k().literal("empty")},optional:{spaceId:k().undefinable(k().uuid())}}),M=k().union([S,A,C,I,P,T,E,R,D,q]);function O(e,t){const n={name:"nativeTab",tab:e,...t},o=(0,w().Qq)(M,n);return o||{name:"unknown"}}var N=()=>n(427704),B=()=>n(824862),L=()=>n(332393);const x="is_from_push_notification";var U=()=>n(734459),F=()=>n(713996),V=()=>n(61844),j=()=>n(530199);const J={intercom:!0,import:!0,billing:!0,plans:!0,members:!0,audit_log:!0,connected_apps:!0,identity_provisioning:!0,analytics:!0,admin_content_search:!0,contact_us:!0,profile:!0,notifications_and_settings:!0,notifications:!0,user_settings:!0,language_and_region:!0,settings:!0,quickfind:!0,security:!0,qna:!0,ai_writer:!0,create_form:!0,tryAI:!0,upgrade_requests:!0,ai:!0},H=["front_personal"],$=["sidebar_private_section","sidebar_workspace_section","sidebar_team_section","sidebar_outliner_item","mobile_bottom_bar","mobile_widget","new_page_and_dictate_shortcut","new_page_and_dictate_action"],W={popupRedirect:"externalIntegrationPopupRedirect",authCallback:"externalIntegrationAuthCallback",datadogAuthCallback:"datadogAuthCallback"},z=["workflowViewType","workflowViewKey","workflowPrompt","chatThreadId"];function Z(e){if("page"!==e.name&&"chat"!==e.name)return;const t={};for(const n of z)e[n]&&(t[n]=e[n]);return Object.keys(t).length>0?t:void 0}function G(e){const t={};for(const n of z){const o=e[n];o&&"string"==typeof o&&(t[n]=o)}return t}function K(e){const t="page"===e.name||"space"===e.name||"chat"===e.name||"marketplace"===e.name||"gallery"===e.name||"team"===e.name||"posts"===e.name||"workspaceDiscovery"===e.name?e:void 0;return Boolean(t)}function Q(e){return"page"===e.name||"team"===e.name}const X={chat:!0,page:!0,team:!0},Y={gallery:!1,marketplace:!1,space:!1,posts:!1,workspaceDiscovery:!1};function ee(e){if(!K(e))return!1;switch(e.name){case"chat":case"page":case"team":return X[e.name];default:return Y[e.name]}}const te=[n(179034).ev,n(96689).NX,n(798880).oo,n(854150).yK],ne={front:!0,invoiceRedirect:!0,unsubscribe:!0,upcomingInvoice:!0,invoiceById:!0,templatesRedirect:!0,communityRedirect:!0,guideRedirect:!0},oe={appleAuthCallback:!0,googleAuthCallback:!0,loginWithEmailCallback:!0,loginSuccessCallback:!0,microsoftAuthCallback:!0,samlAuthCallback:!0,passkeyAuthCallback:!0,passkeyRegistrationCallback:!0,trelloAuthCallback:!0,asanaAuthCallback:!0,slackAuthCallback:!0,passwordResetCallback:!0,externalAuthCallback:!0,externalAuthNativeCallback:!0},ae={page:!0,space:!0};function re(e){return ie(e)[0]}function ie(e){var t,n,o,a;if(e.url&&(e.url.startsWith("/api/v3/")||e.url.startsWith("/signed/")||e.url.startsWith("/image/")||"/getStatus"===e.url))return[{name:"unknown"},"/?"];let l=e.url;try{l=new URL(e.url,e.currentUrl||e.baseUrl).toString()}catch{}const d=c().qg(l);let w=Boolean(e.publicDomainName&&(null===(t=d.host)||void 0===t?void 0:t.endsWith(e.publicDomainName)));const k="dev.notion.so"===d.host||"stg.notion.so"===d.host,S="localhost"===d.hostname&&"3101"===d.port||"http://localhost:3101"===e.baseUrl&&k,A="localhost"===d.hostname&&"3003"===d.port,C="worker.dev.notion.so"===d.hostname,I="https:"===d.protocol&&((null===(n=d.hostname)||void 0===n?void 0:n.endsWith(".ngrok-free.app"))||(null===(o=d.hostname)||void 0===o?void 0:o.endsWith(".ngrok.app"))),P=null===(a=globalThis.Meticulous)||void 0===a?void 0:a.isRunningAsTest,T="file-local.notion.so"===d.hostname||"local.notion.so"===d.hostname||A||S||C||I||P;let E;if(d.protocol&&d.host&&d.hostname){const Fe=Boolean(e.baseUrl===`${d.protocol}//${d.host}`||w||T);let Ve=!1;if(e.protocol){i().$J({httpUrl:e.baseUrl,protocol:e.protocol})===`${d.protocol}//${d.hostname}`&&(Ve=!0)}const je=Boolean(e.currentUrl&&e.currentUrl.startsWith(`${d.protocol}//${d.host}`));if(Fe||Ve)E=!1;else{if(!je)return[{name:"external"},"/external"];E=!0,w=!0}}const R=d.query.origin;if(w){if(!d.pathname)return[{name:"unknown"},"/?"];const Je=function(e){const{hostname:t,publicDomainName:n,requestedOnPublicDomain:o,requestedOnExternalDomain:a}=e;if(a)return t||void 0;if(o)return c().t4({publicDomainName:n},t||"")}({hostname:d.hostname,publicDomainName:e.publicDomainName,requestedOnPublicDomain:!0,requestedOnExternalDomain:E}),He=d.pathname.startsWith(`${N().GJ.embedPublicPages}/`),$e=He?d.pathname.substring(N().GJ.embedPublicPages.length+1):(d.pathname||"/").substring(1),[We,ze]=$e.split("/"),Ze=ze||We,Ge=ce(Ze);return Ge?[{name:"page",blockId:Ge,spaceId:void 0,spaceDomain:Je,peekViewBlockId:void 0,peekMode:void 0,requestedOnPublicDomain:!0,requestedOnExternalDomain:E,embedded:He,origin:R,...le(d,e.isMobile)},"/:spaceDomain?/:blockId"]:[{name:"page",blockId:void 0,slug:Ze??"",spaceId:void 0,spaceDomain:Je,peekViewBlockId:void 0,peekMode:void 0,requestedOnPublicDomain:!0,requestedOnExternalDomain:E,embedded:He,...le(d,e.isMobile),origin:R},"/:spaceDomain?/:slug"]}if(d.pathname&&d.pathname.startsWith("/www.notion.so")&&(d.pathname=d.pathname.slice(14)),d.pathname||(d.pathname="/"),"/"!==d.pathname&&d.pathname.endsWith("/")&&(d.pathname=d.pathname.slice(0,-1)),"/"===d.pathname){return[{name:"root",target:J[d.query.target]?d.query.target:void 0,origin:R,templateGalleryItem:d.query.tg},"/"]}const D=function(e){if(!e.pathname)return;const t=(0,U().ho)(),n=se({pattern:t,pathname:e.pathname});if(n){const{organizationId:o,...a}=n;return o&&(r().iv(o)||o===U().Zf)?[{name:"settingsConsoleOrganization",tabRoute:void 0,organizationId:o,properties:a,params:e.query},t]:[{name:"settingsConsoleOrganization",tabRoute:void 0,organizationId:void 0,properties:a,params:e.query},t]}for(const i of F().oh){const t=(0,U().ho)(i),n=se({pattern:t,pathname:e.pathname});if(n){const{organizationId:o,...a}=n;return o&&(r().iv(o)||o===U().Zf)?[{name:"settingsConsoleOrganization",tabRoute:i,organizationId:o,properties:a,params:e.query},t]:[{name:"settingsConsoleOrganization",tabRoute:void 0,organizationId:void 0,properties:a,params:e.query},t]}}const o=`${N().JZ.settingsConsoleDefault}{/*extra}`,a=se({pattern:o,pathname:e.pathname});if(a)return[{name:"settingsConsoleDefault"},o];return}(d);if(D)return D;if(d.pathname===N().JZ.modal){return[{name:"modal",templateGalleryItem:d.query.tg,projectManagementLaunch:d.query.pjm},N().JZ.modal]}if(d.pathname===N().JZ.appleAuthCallback){const{code:Ke,error:Qe,encryptedUserObject:Xe,di:Ye}=d.query;let et,tt,nt;try{const ot=JSON.parse(b().D(d.query.state));et=ot.callbackType,tt=ot.encryptedToken,nt=ot.encryptedNonce}catch{}return[{name:"appleAuthCallback",code:Ke,encryptedToken:tt,encryptedNonce:nt,callbackType:et,encryptedUserObject:Xe,error:Qe,webBrowserDeviceId:Ye},N().JZ.appleAuthCallback]}if(d.pathname===N().JZ.googleAuthCallback){const{code:at,error:rt,di:it}=d.query;let st,ct,lt;try{const dt=JSON.parse(b().D(d.query.state));st=dt.callbackType,ct=dt.encryptedToken,lt=dt.drive}catch(Ue){}return lt?[{name:"googleDriveAuthCallback",code:at,error:rt},N().JZ.googleAuthCallback]:[{name:"googleAuthCallback",code:at,callbackType:st,encryptedToken:ct,error:rt,webBrowserDeviceId:it},N().JZ.googleAuthCallback]}if(d.pathname===N().JZ.microsoftAuthCallback){const{code:ut,error:pt,di:mt,state:ft}=d.query;let gt,bt,ht;try{const _t=JSON.parse(b().D(ft));gt=_t.callbackType,bt=_t.encryptedToken,ht=_t.encryptedNonce}catch{}return[{name:"microsoftAuthCallback",code:ut,encryptedToken:bt,encryptedNonce:ht,callbackType:gt,state:ft,error:pt,webBrowserDeviceId:mt},N().JZ.microsoftAuthCallback]}if(d.pathname===N().JZ.passkeyAuthVerify)return[{name:"passkeyAuthVerify",callbackType:d.query.callbackType},N().JZ.passkeyAuthVerify];if(d.pathname===N().JZ.passkeyAuthCallback)return[{name:"passkeyAuthCallback",attestation:d.query.attestation,attemptId:d.query.attemptId},N().JZ.passkeyAuthCallback];if(d.pathname===N().JZ.passkeyRegistrationVerification)return[{name:"passkeyRegistrationVerification",callbackType:d.query.callbackType,csrfToken:d.query.csrfToken,attemptId:d.query.attemptId,options:JSON.parse(d.query.options)},N().JZ.passkeyRegistrationVerification];if(d.pathname===N().JZ.passkeyRegistrationCallback)return[{name:"passkeyRegistrationCallback",csrfToken:d.query.csrfToken,attemptId:d.query.attemptId,response:d.query.response},N().JZ.passkeyRegistrationCallback];if(d.pathname===N().JZ.externalAuthNativeCallback)return[{name:"externalAuthNativeCallback",notionState:d.query.notion_state},N().JZ.externalAuthNativeCallback];if(d.pathname===N().JZ.slackAuthCallback){const{type:vt,state:yt}=d.query;return"new"===vt?[{name:"slackAuthCallback",type:"new",state:yt,error:d.query.error},d.pathname]:[{name:"slackAuthCallback",type:"legacy",code:d.query.code,state:yt},N().JZ.slackAuthCallback]}if(d.pathname===N().JZ.logout)return[{name:"logout"},N().JZ.logout];if(d.pathname===N().JZ.addAnotherAccount)return[{name:"addAnotherAccount",redirectURL:d.query.redirectURL},N().JZ.addAnotherAccount];if(d.pathname.startsWith("/native/"))return[{name:"nativeRedirect",redirect:c().ZO(e.url).slice(7)},"/native/?"];if(d.pathname.startsWith("/nativemail/"))return[{name:"nativeMailRedirect",redirect:c().ZO(e.url).slice(11)},"/nativemail/?"];if(d.pathname.startsWith("/nativecalendar/"))return[{name:"nativeCalendarRedirect",redirect:c().ZO(e.url).slice(15)},"/nativecalendar/?"];if(d.pathname===N().JZ.login)return[{name:"login",email:d.query.email,redirectURL:d.query.redirectURL},N().JZ.login];if(d.pathname===N().JZ.loginCalendar)return[{name:"login/calendar",email:d.query.email,redirectURL:d.query.redirectURL,addAnotherAccount:Boolean(d.query.addAnotherAccount)},N().JZ.loginCalendar];if(d.pathname===N().JZ.loginMail)return[{name:"login/mail",email:d.query.email,redirectURL:d.query.redirectURL,addAnotherAccount:Boolean(d.query.addAnotherAccount)},N().JZ.loginMail];if(d.pathname===N().JZ.notFound)return[{name:"notFound"},N().JZ.notFound];const q="/signup/:trialUpsell",M=se({pattern:q,pathname:d.pathname});if(M){const{trialUpsell:wt}=M;if(wt&&(0,V().Wx)(wt))return[{name:"signupWithTrial",trialUpsell:wt},q]}if(d.pathname===N().JZ.signup){const kt=d.query.referrer;return[{name:"signup",email:d.query.email,prompt:d.query.prompt,referrer:(0,s().Xk)(H,kt)?kt:void 0},N().JZ.signup]}if(d.pathname===N().JZ.signupCalendar){const St=d.query.referrer;return[{name:"signup/calendar",email:d.query.email,referrer:(0,s().Xk)(H,St)?St:void 0,redirectURL:d.query.redirectURL,addAnotherAccount:Boolean(d.query.addAnotherAccount)},N().JZ.signupCalendar]}if(d.pathname===N().JZ.signupMail){const At=d.query.referrer;return[{name:"signup/mail",email:d.query.email,referrer:(0,s().Xk)(H,At)?At:void 0,redirectURL:d.query.redirectURL,addAnotherAccount:Boolean(d.query.addAnotherAccount)},N().JZ.signupMail]}if(d.pathname===N().JZ.loginWithEmail){const Ct=d.query,{state:It,password:Pt,redirectURL:Tt,di:Et,isMicrosoft:Rt}=Ct;return[{name:"loginWithEmailCallback",state:It,password:Pt,isSignup:"true"===d.query.isSignup,redirectURL:Tt,webBrowserDeviceId:Et,isMicrosoft:Rt},N().JZ.loginWithEmail]}if(d.pathname===N().JZ.loginSuccess){const Dt=d.query,{di:qt}=Dt;return[{name:"loginSuccessCallback",webBrowserDeviceId:qt},N().JZ.loginSuccess]}if(d.pathname===N().JZ.loginPasswordReset){const Mt=d.query,{state:Ot,password:Nt}=Mt;return[{name:"passwordResetCallback",state:Ot,password:Nt},N().JZ.loginPasswordReset]}if(d.pathname===N().JZ.passwordChangeRedirect)return[{name:"passwordChangeRedirect"},N().JZ.passwordChangeRedirect];if(d.pathname===N().JZ.samlAuthCallback){const{userId:Bt,error:Lt,isNewSignup:xt,token:Ut,previousUserId:Ft,csrfState:Vt}=d.query;return[{name:"samlAuthCallback",userId:Bt,previousUserId:Ft,error:Lt,isNewSignup:void 0!==xt&&"true"===xt.toLowerCase(),token:Ut,csrfState:Vt},N().JZ.samlAuthCallback]}if(d.pathname.startsWith("/profiles/")){const jt=d.pathname.split("/");if(jt.length<4)return[{name:"notFound"},d.pathname];let Jt,Ht;try{Jt=g(jt[2]),Ht=g(jt[3])}catch(Ue){return[{name:"notFound"},d.pathname]}return Ht&&Jt?[{name:"personProfileRedirect",userId:Ht,spaceId:Jt},d.pathname]:[{name:"notFound"},d.pathname]}const x=(0,v().Mq)(d.pathname,!1);if(x){const[$t,Wt]=x;return[{name:"front",type:$t,alreadyRedirected:Boolean(d.query[v().Kr])},`/front${Wt}`]}if(d.pathname===N().JZ.templates)return[{name:"templatesRedirect"},N().JZ.templates];if(d.pathname===N().JZ.help)return[{name:"guideRedirect"},N().JZ.help];if(d.pathname===N().JZ.community)return[{name:"communityRedirect"},N().JZ.community];if(d.pathname===N().JZ.deprecatedGuideRedirect)return[{name:"guideRedirect"},N().JZ.deprecatedGuideRedirect];if(d.pathname===N().JZ.unsubscribe)return[{name:"unsubscribe",payload:d.query.payload},N().JZ.unsubscribe];if(d.pathname===N().JZ.make)return[{name:"make",prompt:d.query.prompt},N().JZ.make];if(d.pathname===N().JZ.onboarding)return[{name:"onboarding"},N().JZ.onboarding];if(d.pathname===N().JZ.googleOneTapRedirect)return[{name:"googleOneTapRedirect",code:d.query.code,frontPathName:d.query.frontPathName,trialName:(0,V().Wx)(d.query.trialName)?d.query.trialName:void 0},N().JZ.googleOneTapRedirect];if(d.pathname===N().JZ.invoice)return[{name:"invoiceRedirect"},N().JZ.invoice];if(d.pathname===N().JZ.terms)return[{name:"termsRedirect"},N().JZ.terms];if(d.pathname===N().JZ.contentPolicy)return[{name:"contentPolicyRedirect"},N().JZ.contentPolicy];if(d.pathname===N().JZ.workflowTemplates){const zt=g(d.query.s),Zt=g(d.query.u),Gt="allTemplates"!==(z=d.query.t)&&"aiBuilder"!==z?p:z,Kt=function(e){const t=null!=e&&e.startsWith("team:")?g(e.slice(5)):void 0;return void 0!==t?{type:"team",teamId:t}:"private"===e?{type:"private"}:m}(d.query.loc),Qt=function(e){return"false"!==e&&("true"===e||f)}(d.query.ac),Xt=function(e){if(e)return decodeURIComponent(e)}(d.query.prompt),Yt=function(e){if("marketing_magic_box"===e||"workflow_templates_deeplink"===e)return e}(d.query.origin);return[{name:"workflowTemplates",spaceId:zt,userId:Zt,modalType:Gt,autoCreateDatabase:Qt,location:Kt,prompt:Xt,origin:Yt},N().JZ.workflowTemplates]}var z;if(d.pathname===N().JZ.desktopEmailConfirm)return[{name:"desktopEmailConfirm"},N().JZ.desktopEmailConfirm];if(d.pathname===N().JZ.home){return[{name:"home",peekViewBlockId:d.query[j().ZI],peekMode:d.query[j().fT],targetConfig:Re(d.query[j().q8]),origin:R},N().JZ.home]}if(d.pathname===N().JZ.posts)return[{name:"posts"},N().JZ.posts];if(d.pathname===N().JZ.workspaceDiscovery)return[{name:"workspaceDiscovery"},N().JZ.workspaceDiscovery];const Z=`${N().JZ.nativeTab}/:tab`,G=se({pattern:Z,pathname:d.pathname});if(G){const{tab:en}=G;return[O(en,d.query),Z]}const K="/team/:teamId",Q=se({pattern:K,pathname:d.pathname});if(Q){const{teamId:tn}=Q,nn=r().ZA(d.query[j().ZI]),on=d.query[j().fT];if(tn){return e.isMobile&&nn?[{name:"page",spaceDomain:"notion",spaceId:void 0,blockId:nn,peekViewBlockId:void 0,peekMode:void 0,requestedOnPublicDomain:w,...le(d,e.isMobile)},K]:[{name:"team",teamId:tn,spaceId:d.query.spaceId,configureOpenInDesktopApp:"true"===d.query[j().uu],peekViewBlockId:nn,peekMode:on},K]}}const X="{/:spaceDomain}/invite/:inviteCode",Y=se({pattern:X,pathname:d.pathname});if(Y){const{inviteCode:an,spaceDomain:rn}=Y;if(an)return[{name:"teamInvite",inviteCode:an,spaceDomain:rn},X]}const ee="/team/:teamId/join",ne=se({pattern:ee,pathname:d.pathname});if(ne){const{teamId:sn}=ne;if(sn)return[{name:"teamsInvite",teamId:sn},ee]}const oe="/ai-gtm/:blockId",ae=se({pattern:oe,pathname:d.pathname});if(ae){const cn=r().ZA(ae.blockId);if(cn)return[{name:"aiGtmFavoriteImport",blockId:cn},oe]}const re="/invoice/:id",ie=se({pattern:re,pathname:d.pathname});if(ie){const{id:ln}=ie;if(ln)return"upcoming"===ln?[{name:"upcomingInvoice",spaceId:d.query.spaceId},re]:[{name:"invoiceById",invoiceId:ln},re]}const de="/design{/:page}",ue=se({pattern:de,pathname:d.pathname});if(ue){const{page:dn}=ue;return[{name:"uiDoc",page:dn},de]}if(d.pathname===N().JZ.creatorProfile)return[{name:"creatorProfile"},N().JZ.creatorProfile];if(d.pathname.startsWith(N().JZ.creatorProfileTemplates)){return[{name:"creatorProfileTemplates",initialValues:{url:d.query.url?decodeURIComponent(d.query.url):void 0,name:d.query.name?decodeURIComponent(d.query.name):void 0}},N().JZ.creatorProfileTemplates]}if(d.pathname===N().JZ.creatorProfileAnalytics)return[{name:"creatorProfileAnalytics"},N().JZ.creatorProfileAnalytics];if(d.pathname.startsWith(N().JZ.creatorProfileCoupons))return[{name:"creatorProfileCoupons"},N().JZ.creatorProfileCoupons];const pe=`${N().JZ.creatorProfileIntegrations}/:type/:id{/:subpageType}`,me=se({pattern:pe,pathname:d.pathname});if(me){const{type:un,id:pn,subpageType:mn}=me,fn=r().ZA(pn)||pn;return"public"===un&&fn?[{name:"creatorProfileIntegrations",pointer:{id:fn,table:L().Li},publish:"publish"===mn,lab:"lab"===mn},pe]:"internal"===un&&fn?[{name:"creatorProfileIntegrations",pointer:{id:fn,table:B().GP}},pe]:[{name:"creatorProfileIntegrations",pointer:void 0},pe]}const fe=`${N().JZ.creatorProfileIntegrations}/:id`;if(se({pattern:fe,pathname:d.pathname}))return[{name:"creatorProfileIntegrations",pointer:void 0},fe];if(d.pathname===N().JZ.creatorProfileIntegrations)return[{name:"creatorProfileIntegrations"},N().JZ.creatorProfileIntegrations];if(d.pathname===N().JZ.localizedTemplates)return[{name:"localizedTemplates"},N().JZ.localizedTemplates];const ge=se({pattern:"/marketplace/:pageType",pathname:d.pathname}),be=se({pattern:"/marketplace/:pageType/:slug",pathname:d.pathname});if(be)return(0,y().LY)({routeName:"marketplace",parsed:d,docMatch:be});if(ge)return(0,y().gQ)({routeName:"marketplace",parsed:d,docMatch:ge});if(d.pathname===N().JZ.marketplace)return(0,y().gg)({routeName:"marketplace",parsed:d});const he=se({pattern:"/gallery/:pageType",pathname:d.pathname}),_e=se({pattern:"/gallery/:pageType/:slug",pathname:d.pathname});if(_e)return(0,y().LY)({routeName:"gallery",parsed:d,docMatch:_e});if(he)return(0,y().gQ)({routeName:"gallery",parsed:d,docMatch:he});if(d.pathname===N().JZ.gallery)return(0,y().gg)({routeName:"gallery",parsed:d});if(d.pathname===N().JZ.templateSubmission)return[{name:"inAppTemplateSubmission"},N().JZ.templateSubmission];if(d.pathname===N().JZ.templateCreatorSubmission)return[{name:"inAppTemplateCreatorSubmission"},N().JZ.templateCreatorSubmission];if(d.pathname===N().JZ.studentGroupSignup)return[{name:"studentGroupSignup"},N().JZ.studentGroupSignup];if(d.pathname===N().JZ.startupsApplication)return[{name:"startupsApplication"},N().JZ.startupsApplication];if(d.pathname===N().JZ.smbsApplication)return[{name:"smbsApplication"},N().JZ.smbsApplication];if(d.pathname===N().JZ.lennyApplication)return[{name:"lennyApplication"},N().JZ.lennyApplication];if(d.pathname===N().JZ.creatorProgramApplication)return[{name:"creatorProgramApplication"},N().JZ.creatorProgramApplication];const ve="{/:spaceDomain}/:maybeUniqueId",ye=se({pattern:ve,pathname:d.pathname});if(ye){const{spaceDomain:gn,maybeUniqueId:bn}=ye;if(bn){const hn=bn.toUpperCase();if((0,h().$e)(hn))return[{name:"uniqueId",uniqueId:hn,spaceDomain:gn},ve]}}const we="/__export/:maybeBlockId{/:blockExportType}",ke=se({pattern:we,pathname:d.pathname});if(ke){const{maybeBlockId:_n,blockExportType:vn}=ke;if(_n){const yn=ce(_n);if(yn&&("markdown"===vn||"pdf"===vn||"html"===vn||void 0===vn))return[{name:"exportPreview",blockId:yn,blockExportType:vn||"html"},we]}}if(d.pathname===N().JZ.admin)return[{name:"admin"},N().JZ.admin];if(d.pathname.startsWith(N().JZ.adminListData))return[{name:"adminListData",category:d.pathname.slice(N().JZ.adminListData.length+1)},"/admin/data/:category"];if(d.pathname===N().JZ.privacyCenterRedirect)return[{name:"privacyCenterRedirect"},N().JZ.privacyCenterRedirect];const Se=`${N().JZ.admin}/:table/:id`,Ae=se({pattern:Se,pathname:d.pathname});if(Ae){const{table:wn}=Ae;let{id:kn}=Ae;if(kn){function Sn(){return"user"===wn?"notion_user":"page"===wn?"block":wn}const An=Sn();if("block"===An&&(kn=ce(kn)||"unknown"),te.includes(An))return[{name:"adminSingleRecord",table:An,id:kn},Se]}}if(d.pathname===N().JZ.newPage){const Cn=d.query.spaceId,In=r().ZA(Cn),Pn=$.find((e=>e===d.query.from))?d.query.from:void 0;return[{name:"new",spaceId:In,type:d.query.type,id:d.query.id,from:Pn},N().JZ.newPage]}if(d.pathname===N().JZ.oauthAuthorization)return[{name:"oauthAuthorization",responseType:d.query.response_type,redirectUri:d.query.redirect_uri,integrationId:d.query.client_id,state:d.query.state,owner:d.query.owner,userId:d.query.user_id,spaceId:d.query.space_id},N().JZ.oauthAuthorization];if(d.pathname===N().JZ.notionCalendarAuthorization)return[{name:"notionCalendarAuthorization",csrf:d.query.csrf,calendarCsrf:d.query.calendar_csrf,redirectUri:d.query.redirect_uri,spaceId:d.query.space_id,state:d.query.state},N().JZ.notionCalendarAuthorization];if(d.pathname===N().JZ.globalOauthPostLogin)return[{name:"globalOauthPostLogin",state:d.query.state,clientId:d.query.client_id,redirectUri:d.query.redirect_uri,scope:d.query.scope,responseType:d.query.response_type},N().JZ.globalOauthPostLogin];if(d.pathname===N().JZ.externalAuthCallback){let Tn;const En=d.query.error_description;return En&&(Tn=decodeURIComponent(En)),[{name:"externalAuthCallback",notionState:d.query.notion_state,error:d.query.error,errorDescription:Tn},N().JZ.externalAuthCallback]}if(se({pattern:N().JZ.externalIntegrationPopupRedirect,pathname:d.pathname}))return[{name:W.popupRedirect,userId:d.query.userId,spaceId:d.query.spaceId,integrationId:d.query.integrationId,externalObjectInstanceBlockId:d.query.externalObjectInstanceBlockId,notionAuthorizationCode:d.query.notionAuthorizationCode,callbackType:d.query.callbackType,redirectToAuth:"true"===d.query.redirectToAuth},N().JZ.externalIntegrationPopupRedirect];if(d.pathname===N().JZ.initiateExternalAuthentication)return[{name:"initiateExternalAuthentication",notion_user_id:d.query.notion_user_id,notion_workspace_id:d.query.notion_workspace_id,notion_last_visited_url:d.query.notion_last_visited_url,notion_authorization_code:d.query.notion_authorization_code,external_object_instance_block_id:d.query.external_object_instance_block_id,callback_type:d.query.callback_type,integration_id:d.query.integration_id,...d.query.integration_id&&{integration_id:d.query.integration_id}},N().JZ.initiateExternalAuthentication];if(d.pathname===N().JZ.initiateExternalAuthenticationFromDesktop)return[{name:"initiateExternalAuthenticationFromDesktop",redirectUri:d.query.redirectUri},N().JZ.initiateExternalAuthenticationFromDesktop];if(d.pathname.toLowerCase()===N().JZ.externalIntegrationAuthCallback.toLowerCase()){let Rn;const Dn=d.query.error_description;return Dn&&(Rn=decodeURIComponent(Dn)),[{name:W.authCallback,state:d.query.state,code:d.query.code,error:d.query.error,errorDescription:Rn,errorUri:d.query.error_uri},N().JZ.externalIntegrationAuthCallback]}if(d.pathname===N().JZ.datadogAuthCallback){let qn;const Mn=d.query.error_description;return Mn&&(qn=decodeURIComponent(Mn)),[{name:W.datadogAuthCallback,organizationId:d.query.dd_oid,organizationName:d.query.dd_org_name,site:d.query.site,domain:d.query.domain,state:d.query.state,code:d.query.code,error:d.query.error,errorDescription:qn,errorUri:d.query.error_uri},N().JZ.datadogAuthCallback]}if(d.pathname===N().JZ.githubStudentPackAuthCallback){return[{name:"githubStudentPackAuthCallback",code:d.query.code,state:d.query.state},N().JZ.githubStudentPackAuthCallback]}if(d.pathname===N().JZ.githubStudentPackHome)return[{name:"githubStudentPackHome"},N().JZ.githubStudentPackHome];if(d.pathname===N().JZ.formResponse)return[{name:"formResponse",formResponseId:d.query.id,secretKey:d.query.secretKey,formSpaceId:d.query.spaceId,formSpaceIntent:d.query.i},N().JZ.formResponse];const Ce=`${N().JZ.myIntegrations}/:integrationType/:id/lab`,Ie=se({pattern:Ce,pathname:d.pathname});if(Ie){const{integrationType:On,id:Nn}=Ie,Bn=r().ZA(Nn);return"public"===On&&Bn?[{name:"creatorProfileIntegrations",pointer:{id:Bn,table:L().Li},lab:!0,publish:!1},Ce]:[{name:"creatorProfileIntegrations",pointer:void 0},Ce]}const Pe=`${N().JZ.myIntegrations}/:integrationType/:id`,Te=se({pattern:Pe,pathname:d.pathname});if(Te){const{integrationType:Ln,id:xn}=Te,Un=r().ZA(xn);return"public"===Ln&&Un?[{name:"myIntegrations",pointer:{id:Un,table:L().Li}},Pe]:"internal"===Ln&&Un?[{name:"myIntegrations",pointer:{id:Un,table:B().GP}},Pe]:[{name:"myIntegrations",pointer:void 0},Pe]}const Ee=`${N().JZ.myIntegrations}/:id`;if(se({pattern:Ee,pathname:d.pathname}))return[{name:"myIntegrations",pointer:void 0},Ee];if(d.pathname===N().JZ.myIntegrations)return[{name:"myIntegrations"},N().JZ.myIntegrations];if(d.pathname===N().JZ.quickSearch)return[{name:"quickSearch"},N().JZ.quickSearch];if(d.pathname===N().JZ.blank)return[{name:"blank"},N().JZ.blank];const De=se({pattern:`${N().JZ.chat}/:id`,pathname:d.pathname});if(d.pathname===N().JZ.chat||De){const Fn=d.query[j().ZI]?ce(d.query[j().ZI]):void 0,Vn=d.query[j().fT],jn=d.query[u().k3],Jn=d.hash?d.hash.substring(1):"",Hn=r().ZA(Jn),$n=d.query[j().P5],Wn=$n?r().ZA($n):void 0,zn=Re(d.query[j().q8]);return[{name:"chat",scrollToBlockId:Hn,peekViewBlockId:Fn,pageVisitSource:jn,peekMode:Vn,configureOpenInDesktopApp:"true"===d.query[j().uu],assistantQueryPrefill:d.query[j().ah],spaceId:d.query.spaceId,deepFind:d.query.deepFind,threadId:Wn,defaultUserMessage:d.query[j().ah]||d.query[j().dG],targetConfig:zn},N().JZ.chat]}const qe=d.query.q,Me=d.query.searchRequest,Oe=d.query.state;if(d.pathname.startsWith("/space/")){const Zn=d.pathname.slice(7),Gn=r().ZA(Zn);return Gn?[{name:"space",spaceId:Gn,spaceDomain:void 0,requestedOnPublicDomain:w,searchQuery:qe,searchRequest:Me,state:Oe},"/space/:spaceId"]:[{name:"unknown"},"/space/:spaceId"]}const Ne=(d.pathname||"/").substring(1),[Be,Le]=Ne.split("/"),xe="/:spaceDomain?/:blockId";if(Le){const Kn=ce(Le);if(!Kn)return[{name:"unknown"},"/?"];let Qn;return Qn=_().$.has(Be)?void 0:Be,[{name:"page",blockId:Kn,spaceId:void 0,spaceDomain:Qn,peekViewBlockId:void 0,peekMode:void 0,requestedOnPublicDomain:!1,...le(d,e.isMobile),origin:R},xe]}{const Xn=ce(Be);let Yn;return Xn?[{name:"page",blockId:Xn,spaceId:void 0,spaceDomain:Yn,peekViewBlockId:void 0,peekMode:void 0,requestedOnPublicDomain:!1,...le(d,e.isMobile),origin:R},xe]:Be.length>0&&_().$.has(Be)?[{name:"unknown"},"/?"]:[{name:"space",spaceId:void 0,spaceDomain:Yn||Be,requestedOnPublicDomain:!1,searchQuery:qe,searchRequest:Me,state:void 0},"/:spaceDomain"]}}function se(e){const t=(0,o().YW)(e.pattern)(e.pathname);if(t)return(0,l().MU)((0,s().WP)(t.params).map((e=>{let[t,n]=e;return[t,"string"==typeof n?n:void 0]})))}function ce(e){const t=e.substring(e.length-r().sO);return r().ZA(t)||(r().uj(e)?r().Xw(e):void 0)}function le(e,t){const n=e.hash?e.hash.substring(1):"",o=r().ZA(n),a=r().ZA(e.query[j().h4]),i=r().ZA(e.query[j().ZI]),s=e.query[j().fT],c="true"===e.query.duplicate,l=e.query.workspaceId,d=Number.parseInt(e.query.updateSidebarTab),p=Number.isFinite(d)?d:void 0,m=r().ZA(e.query[j().IG]),f=Boolean(e.query.showMoveTo),g=Boolean(e.query.saveParent),b=e.query.q,h=e.query.searchRequest,_=e.query[u().CW],v=e.query.tid,y=e.query.from,w="true"===e.query[j().uu],k=e.query.tg,S="true"===e.query.pjm,A=e.query[u().k3],C={isPush:"true"===e.query[x]};const I=e.query.state,P="true"===e.query.demoWorkspaceMode,T=e.query.demoTemplateSlug,E=e.query.wfv,R=e.query.wfk,D=e.query.wfp,q=e.query[j().P5]??e.query.ct,M=Re(e.query[j().q8]);return t&&i?{blockId:i,scrollToBlockId:o,collectionViewId:a,discussionId:m,showMoveTo:f,saveParent:g,shouldDuplicate:c,workspaceId:l,searchQuery:b,searchRequest:h,tid:v,from:y,templateGalleryItem:k,projectManagementLaunch:S,configureOpenInDesktopApp:w,pageVisitSource:A,mobileData:C,queryId:_,state:I,demoWorkspaceMode:P,demoTemplateSlug:T,workflowViewType:E,workflowViewKey:R,workflowPrompt:D,chatThreadId:q}:{scrollToBlockId:o,updateSidebarTab:p,collectionViewId:a,peekViewBlockId:i,peekMode:s,discussionId:m,showMoveTo:f,saveParent:g,shouldDuplicate:c,workspaceId:l,searchQuery:b,searchRequest:h,tid:v,from:y,templateGalleryItem:k,projectManagementLaunch:S,configureOpenInDesktopApp:w,pageVisitSource:A,mobileData:C,queryId:_,state:I,demoWorkspaceMode:P,demoTemplateSlug:T,workflowViewType:E,workflowViewKey:R,workflowPrompt:D,chatThreadId:q,targetConfig:M}}const de=["popup","redirect","nativeredirect","native","nativemailredirect","nativecalendarredirect"];function ue(e,t){const n={callbackType:t.authType.callbackType};t.authType.redirectToAuth&&(n.redirectToAuth="true");const o=(0,d().iK)();return o&&(n.tid=o),c().Gm({url:`${e}${N().JZ.applePopupRedirect}`,query:n})}function pe(e,t){const n={callbackType:t.authType.callbackType};t.authType.redirectToAuth&&(n.redirectToAuth="true"),t.source&&(n.source=t.source);const o=(0,d().iK)();return o&&(n.tid=o),c().Gm({url:`${e}${N().JZ.microsoftPopupRedirect}`,query:n})}function me(e,t,n){const o={callbackType:t.authType.callbackType};return t.email&&(o.email=t.email),t.contacts&&(o.contacts="true"),t.authType.redirectToAuth&&(o.redirectToAuth="true"),t.source&&(o.source=t.source),n&&(o.requestId=n),c().Gm({url:`${e}${N().JZ.googlePopupRedirect}`,query:o})}function fe(e,t){const n={callbackType:t.authType.callbackType,blockId:t.blockId,userId:t.userId};return t.authType.redirectToAuth&&(n.redirectToAuth="true"),c().Gm({url:`${e}${N().JZ.slackPopupRedirect}`,query:n})}function ge(e,t){const n={callbackType:t.authType.callbackType,userId:t.userId,isElectronDevice:t.isElectronDevice?"true":"false"};return t.authType.redirectToAuth&&(n.redirectToAuth="true"),c().Gm({url:`${e}${N().JZ.trelloPopupRedirect}`,query:n})}function be(e,t){const n={callbackType:t.authType.callbackType,userId:t.userId};return t.authType.redirectToAuth&&(n.redirectToAuth="true"),c().Gm({url:`${e}${N().JZ.asanaPopupRedirect}`,query:n})}function he(e,t){const n={callbackType:t.authType.callbackType,userId:t.userId,isElectronDevice:t.isElectronDevice?"true":"false"};return t.authType.redirectToAuth&&(n.redirectToAuth="true"),c().Gm({url:`${e}${N().JZ.evernotePopupRedirect}`,query:n})}function _e(e,t){const n={callbackType:t.authType.callbackType};return t.authType.redirectToAuth&&(n.redirectToAuth="true"),c().Gm({url:`${e}${N().JZ.googleDrivePopupRedirect}`,query:n})}function ve(e,t){return c().Gm({url:`${e}${N().JZ.googleDrivePickerPopup}`,query:t})}function ye(e,t){const n=window.btoa(JSON.stringify(t));return c().Gm({url:`${e}/upgraded-account`,query:{state:n}})}function we(e){const{route:t}=e;if("space"===t.name)return t.spaceId?ke(t.spaceId):`/${t.spaceDomain}`;(0,s().HB)(t.name)}function ke(e){return`/space/${r().Xw(e)}`}function Se(e){return"nativeTab"!==e.name&&"quickSearch"!==e.name&&"chat"!==e.name&&"marketplace"!==e.name&&"gallery"!==e.name&&"team"!==e.name&&"posts"!==e.name&&"workspaceDiscovery"!==e.name&&"blank"!==e.name}function Ae(e,t){if(c().bk(e)){const n=c().qg(e),o=c().qg(t);return n.host===o.host&&n.pathname===N().JZ.initiateExternalAuthenticationFromDesktop}return!1}function Ce(e){return"page"===e.name?a().oE([e.blockId,e.peekViewBlockId]):Ie(e)?a().oE([e.peekViewBlockId]):[]}function Ie(e){return"chat"===e.name||"team"===e.name}function Pe(e){return Boolean("page"===e.name&&e.embedded)}const Te=["search","researcher","setup-generator","markdown-chat"];function Ee(e){return!!(0,s().Xk)(Te,e)}function Re(e){if(!e)return;const t=decodeURIComponent(e);return function(e){try{const t=JSON.parse(decodeURI(e)),n=t.type;return n&&Ee(n)?t:void 0}catch(t){return}}(t)??(Ee(n=t)?{type:n}:void 0);var n}},862114:(e,t,n)=>{n.r(t)},865085:(e,t,n)=>{n.d(t,{OPFSBootupRegistry:()=>o});const o=new class{constructor(){this.isEnabled=!1,this.isServerEnabled=void 0,this.isElectronEnabled=void 0,this.pageCache=void 0,this.isPageInCache=!1,this.pageRecordMapBufferPromise=void 0,this.IPRMetricData=void 0;const e={isServerEnabled:void 0};if(Boolean(globalThis.__isElectron))this.isElectronEnabled=(0,n(165162).localUserOnlyGateCheck)({gateName:"opfs_recordmap_cache"})??!1,this.isEnabled=Boolean(this.isElectronEnabled);else{const t=(0,n(105751).f)("opfsBootupRegistry");Object.assign(this,{...e,...t}),this.isEnabled=Boolean(this.isServerEnabled)}(0,n(706762).s)()||(this.isEnabled=!1),this.isEnabled&&(this.pageCache=(0,n(882368).getOPFSPageCache)())}updateInitialMetricData(e){this.IPRMetricData={...this.IPRMetricData,...e}}}},866540:(e,t,n)=>{n.d(t,{G:()=>r});var o=()=>n(469425);const a=68;async function r(e){const t=await o().CY(e);return t.pragmas.user_version===a?{endSchema:t,migrations:[],fastForward:void 0}:(await n.e(41201).then(n.bind(n,941201))).AllMigrations}},870723:(e,t,n)=>{n.d(t,{$t:()=>a,RP:()=>i,kW:()=>r,vJ:()=>o});n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698);function o(e){return!!(a(e)||function(e){return c.has(e)}(e)||r(e))}function a(e){return s.has(e)}function r(e){return l.includes(e)}function i(e){return a(e.table)}const s=new Set(["agent","automation_action","automation","block","collection_view","collection","comment","custom_emoji","discussion","external_authentication_token","external_object","follow","form_question","invite","page_exit","page_visit","reaction","record_counter","record_key","record_mention","scheduled_digest_message","server_integrations__jira_webhook","skill","snapshot","space_bot","space_permission_group","space_permission_group_member","space_user","space_view","space","subscription_banner","team","webhook_subscription","workflow_template_instance","space_user_recovery","assistant_chat_step","assistant_chat_session","layout","ai_embedding_config","site","trusted_domain","assistant_session_starter","free_chart","channel","related_content","form_response_snapshot","workflow","workflow_automation_run","workflow_transcript","workflow_artifact","workflow_external_scoped_connection","workflow_module","inference_transcript","thread","thread_message","schedule_for_event","text_slice_block_mapping","upgrade_request","workspace_encryption_key","legal_hold_page","legal_hold_page_actor","user_seen_record","bot_extended_metadata","file_upload","space_entitlement_usage","space_user_entitlement_usage"]),c=new Set(["activity"]),l=["notification"]},882362:(e,t,n)=>{n.d(t,{u:()=>o});const o=new class{setTimeout(e,t){for(var n=arguments.length,o=new Array(n>2?n-2:0),a=2;a<n;a++)o[a-2]=arguments[a];setTimeout(e,t,...o)}}},882368:(e,t,n)=>{n.d(t,{getOPFSPageCache:()=>l});n(16280),n(944114),n(816573),n(878100),n(177936),n(748140),n(821903),n(491134),n(128845),n(237467),n(444732),n(979577),n(581454),n(814603),n(147566),n(198721);var o=()=>n(681335),a=()=>n(706762);function r(e){performance.mark(`OPFS:PageCache:${e}`)}function i(e,t,n){return performance.measure(`OPFS:PageCache:${e}`,`OPFS:PageCache:${t}`,`OPFS:PageCache:${n}`)}class s{constructor(){this.remote=void 0;const e=new SharedWorker(new URL(n.p+n.u(39047),n.b),{type:void 0});e instanceof SharedWorker?(e.port.start(),this.remote=o().LV(e.port),e.port.postMessage({name:"debug",enabledValue:this.getDebugValue()})):(this.remote=o().LV(e),e.postMessage({name:"debug",enabledValue:this.getDebugValue()}))}async readBuffer(e,t,n){const o=this.getKey(e,t);if(r("readBuffer.start"),null!=n&&n.aborted)throw new Error(n.reason);let a;n&&(a=this.registerAbortListener(n));try{var s;const{buffer:e,metrics:t}=await this.remote.readBuffer(o,null===(s=a)||void 0===s?void 0:s.abortId);r("readBuffer.got-buffer");const n=i("readBuffer","readBuffer.start","readBuffer.got-buffer"),c=new Uint8Array(e),{metadata:l,offset:d}=this.getMetadata(c);return{metadata:l,buffer:e,iterator:this.getChunkIterator(c.subarray(d)),metrics:{...t,total:n.duration}}}finally{var c;null===(c=a)||void 0===c||c.unregister()}}async readJSON(e,t,n){r("readJSON.start");const{iterator:o,metrics:a,metadata:s}=await this.readBuffer(e,t,n);r("readJSON.got-text");const c=[];for(const r of o)c.push(r);r("readJSON.parsed-json");const l=i("readJSON.got-text","readJSON.start","readJSON.got-text"),d=i("readJSON.parse-json","readJSON.got-text","readJSON.parsed-json"),u=i("readJSON.total","readJSON.start","readJSON.parsed-json");return{metadata:s,chunks:c,metrics:{...a,getText:l.duration,parseJSON:d.duration,total:u.duration}}}async write(e,t,n){const o=this.getKey(e,t);return await this.remote.write(o,n.map((e=>JSON.stringify(e.toJson()))))}async checkIfExists(e,t){const n=this.getKey(e,t);return await this.remote.checkIfExists(n)}async delete(e,t){const n=this.getKey(e,t);return await this.remote.delete(n)}async deleteAll(){return await this.remote.deleteAll()}getDebugValue(){return localStorage.getItem("debug")}getKey(e,t){return`${e}__${t}`}registerAbortListener(e){const t=(0,n(498212).Ay)(),o=()=>this.remote.abort(t,e.reason);return e.addEventListener("abort",o,{once:!0}),{abortId:t,unregister:function(){e.removeEventListener("abort",o)}}}getMetadata(e){const t=e.indexOf(10),n=-1===t?e.length:t,o=(new TextDecoder).decode(e.subarray(0,n));return{metadata:JSON.parse(o),offset:n+1}}*getChunkIterator(e){const t=new TextDecoder;let n=0;for(;n<e.length;){const o=e.indexOf(10,n),a=-1===o?e.length:o+1,r=t.decode(e.subarray(n,a));if(r.length>0){const e=JSON.parse(r);yield e}n=a}}}let c;function l(){if((0,a().s)())return c||(new URL(n(855337),n.b).origin===window.location.origin?(c=new s,c):void 0)}},885443:(e,t,n)=>{n.d(t,{B6:()=>c,O8:()=>l,iK:()=>s,sg:()=>a});n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698);var o=()=>n(939768);const a="tid",r=new Set(["visit","page_visit","landing_page_visit","login_success","onboarding_show","desktop_app_signup_browser_visit"]);let i;function s(){return i}function c(e){if(r.has(e))return s()}function l(e){if(!function(e){return!i&&!!(0,o().qn)(e,a)}(e))return;const t=(0,o().qn)(e,a);i=t;const n=(0,o().qm)(e,a);window.history.replaceState(window.history.state,"",n)}},902006:(e,t,n)=>{n.d(t,{$B:()=>v,Fl:()=>f,GK:()=>c,Gb:()=>s,KI:()=>h,My:()=>S,PI:()=>u,Py:()=>A,RW:()=>T,S:()=>g,SC:()=>r,Tq:()=>k,V9:()=>C,Ve:()=>E,_u:()=>p,aK:()=>d,e5:()=>m,fK:()=>_,fl:()=>a,h5:()=>I,lF:()=>b,n1:()=>y,w3:()=>P});n(898992),n(672577);var o=()=>n(534177);const a=["slack","google-drive","github","jira","microsoft-teams","sharepoint","gmail","linear","outlook"];function r(e){return!!(0,o().Xk)(a,e)}const i=["gmail","outlook"];function s(e){return!!(0,o().Xk)(i,e)}const c={"google-drive":"google_drive_qna_ingestion",jira:"jira_qna_ingestion",github:"github_qna",gmail:"gmail_ai_connector","microsoft-teams":"microsoft_teams_qna",sharepoint:"sharepoint_qna",linear:"linear_ai_connector",outlook:"outlook_ai_connector"},l={"google-drive":"google_drive","microsoft-teams":"microsoft_teams"};function d(e){return(0,o().O)(l,e)?l[e]:e}const u=[...a,"web","githubCode"],p="7c57d10c-00cc-48bc-a552-fb3911dd7745",m="1031cb6c-4935-4cc1-9c08-c9a4ecaae323",f="65683ef9-c923-4a14-8c5b-bfcf591cf5a1",g="c1f230b1-6635-4ab0-a3f5-f7eb105a87c3",b="c91548d4-25d0-44a2-ab2e-19f6f0b11d96",h="1724f2a1-f543-43a7-82af-8a0f85bda112",_="07351274-83bb-4118-9f5b-9d75f0453999",v="6694bee1-9451-4484-a972-f56741db481e",y="5f7ef930-12b9-452b-8eb9-a385e84b7aa6",w=[{regex:/https:\/\/(?<workspace>[\w-]+)\.slack\.com\/archives\/(?<channel>[^\/]+)\/p(?<timestamp>\d+)(\?thread_ts=(?<thread_ts>\d+\.\d+)&cid=(?<cid>[^\/]+))?/,type:"slack"},{regex:/https:\/\/docs\.google\.com\/(?<fileType>document|spreadsheets|presentation)\/d\/(?<fileId>[a-zA-Z0-9_-]+)(?:\/\S*)?/,type:"google-drive"},{regex:/https:\/\/github\.com\/(?<orgName>[^\/]+)\/(?<repoName>[^\/]+)\/(?:blob|tree|blame)\/(?<branchName>[^\/]+)\/(?<codePath>[^#]+)(#(?<lineNumber>L\d+(?:C\d+)?(?:-L\d+)?))?/,type:"githubCode"},{regex:/https:\/\/github\.com\/(?<orgName>[^\/]+)\/(?<repoName>[^\/]+)\/pull\/(?<prNumber>\d+)(\/(files|commits|checks)?)?/,type:"github"},{regex:/https:\/\/app\.graphite\.dev\/github\/pr\/(?<orgName>[^\/]+)\/(?<repoName>[^\/]+)\/(?<prNumber>\d+)(?:\/\S*)?/,type:"github"},{regex:/https:\/\/(?:[^\.]+)\.sourcegraphcloud\.com\/github\.com\/(?<orgName>[^\/]+)\/(?<repoName>[^\/@]+)(?:@(?<branchName>[a-f0-9]+))?\/-\/blob\/(?<codePath>[^?]+)\?(?<lineNumbers>L\d+|l\d+)?/,type:"githubCode"},{regex:/https:\/\/(?<site>[^/]+\.atlassian\.net).*?(?:browse\/|[?&]selectedIssue=)(?<issueKey>[A-Z0-9]+-\d+).*?/,type:"jira"},{regex:/https:\/\/teams\.microsoft\.com\/l\/message\/(?<channelId>[^\/?&]+)\/(?<timestamp>\d+)\?[^#]*?(tenantId=(?<tenantId>[0-9a-fA-F-]{36}))?(?:\S*)?/,type:"microsoft-teams"},{regex:/https:\/\/(?<site>[^.]+)\.sharepoint\.com\/:(?<fileType>[pwx]):\/r\/(?:sites|personal)\/(?<driveName>[^\/]+)\/.*[?&]sourcedoc=(?:%7B|\{)(?<sourceDocId>[0-9A-Fa-f-]+)(?:%7D|\})/,type:"sharepoint"}];function k(e){const t=w.find((t=>t.regex.test(e)));return(null==t?void 0:t.type)??"web"}const S=[...["slack_unfurl_ai_action",...a.map((e=>`${d(e)}_connect_button`))],"ai_full_page_welcome_connector_action_card","ai_corner_chat_welcome_connector_action_card","ai_corner_chat_connectors_button","link_preview_unfurl_menu_ai_connector_action","ai_workspace_settings_connector_cards","ai_chat_followup_upsell_suggestion","ai_chat_search_results_tab","ai_chat_scoped_search_menu","assistant_overflow_menu_add_connectors","ai_connectors_sunset_banner","ai_connector_ai_upsell_intro_modal","ai_connector_ai_plan_upsell_intro_modal_opened"],A="#";function C(e){return e}function I(e){return e}function P(e){return e}function T(e){return e}function E(e){return e}},904819:(e,t,n)=>{n.d(t,{initializeStatsig:()=>a});n(16280);var o=()=>n(780054);async function a(e){let{environment:t,currentUserId:a}=e;const{statsigClientLoader:r,getDeviceAttributesForStatsigUser:i,StatsigInitializer:s,getOrCreateStableID:c}=await Promise.resolve().then(n.bind(n,165162)),l=await Promise.resolve().then(n.bind(n,529543)),{getPerformanceEventListeners:d}=await Promise.resolve().then(n.bind(n,459225)),{log:u}=await Promise.resolve().then(n.bind(n,857639)),{convertErrorToLog:p}=await Promise.resolve().then(n.bind(n,502800)),m=c();let f,g;try{const e=await Promise.all([l.getBrowserId(t),l.getExperimentDeviceId(t)]);f=e[0],g=e[1]}catch(b){return u({level:"error",from:"statsig",type:"fetchCookies",error:p(b),data:{userId:a}}),{initializedOnServer:!1}}s.environment=t;try{r.loadStageOne({currentUserId:a,device:t.device,deviceId:g,browserId:f,overrideStableID:m})}catch(b){u({level:"error",from:"statsig",type:"loadStageOne",error:p(b),data:{userId:a}})}return s.initializePromise=async function(){const{parseRoute:e}=await Promise.resolve().then(n.bind(n,861017)),s=e({url:window.location.href,isMobile:t.device.isMobile,baseUrl:o().A.domainBaseUrl,publicDomainName:o().A.publicDomainName,protocol:o().A.protocol,currentUrl:window.location.href});let c,l=a;if("page"===s.name||"root"===s.name){const{getPredictedRootRedirectPageForPrefetch:e}=await Promise.resolve().then(n.bind(n,740456)),t="root"===s.name?e():{blockId:s.blockId,userId:a};c=t.blockId,l=t.userId}const u=await Promise.resolve().then(n.bind(n,754704)),{locale:p}=await Promise.resolve().then(n.bind(n,662303)),b={browserId:f,deviceId:g,device:i(t.device),stableID:m,...c?{page:{id:c,spaceId:void 0}}:{},locale:p,shouldStringifyInitialValues:!0,clientSdkApiKey:o().A.statsig.apiKey},h=u.createApiHttpJsonRequestOptions({environment:{device:t.device},eventName:"getStatsigResults",data:b,activeUserId:l,tracking:void 0,eventListeners:d({eventName:"getStatsigResults",isPrefetchRequest:!0})}),{default:_}=await Promise.resolve().then(n.bind(n,974233)),v=_(h),y=await v;"success"===y.type&&r.loadStageTwo(y.data)}().then((()=>{s.isComplete=!0}),(e=>{e instanceof Error?s.error=e:s.error=new Error("Unknown error when initializing Statsig")})),{initializedOnServer:!0,finishInitializePromise:s.initializePromise}}},906551:(e,t,n)=>{function o(e){return{teamHomeCollectionViewIdsKey:`teamHomeCollectionViewIds:${e}`}}n.d(t,{k:()=>o})},928217:(e,t,n)=>{n.d(t,{Pv:()=>u,Rs:()=>c,Yg:()=>m,jM:()=>a,ji:()=>f,nD:()=>l,oR:()=>p});n(898992),n(737550);var o=()=>n(638681);const a="ai.grant032023",r="ai.qnaBetaRefresh",i="ai.limitedQnaRefresh",s="ai.assistantLaunchGrant",c="ai.studentGitHubGrant",l=[a,r,i,s,c],d=o().object({required:{singlePlayerAmount:o().number(),multiplayerAmount:o().number(),unit:n(7615).Yq},optional:{}}),u=o().object({required:{baseGrant:d,userGrant:d,grant032023:o().intersection([d,o().object({required:{waitMs:o().number()},optional:{}})]),studentGrant:d,studentGitHubGrant:d,assistantLaunchGrant:o().intersection([d,o().object({required:{limit:o().number()},optional:{}})]),maxAllowance:o().object({required:{free:o().number(),paid:o().number()},optional:{}})},optional:{}});function p(e,t){return m({space:t,grantId:r})?e.qna:e.writer}function m(e){var t;let{space:n,grantId:o}=e;return Boolean(null==n||null===(t=n.getSettings())||void 0===t||null===(t=t.grant_awards)||void 0===t?void 0:t.some((e=>e.id===o)))}function f(e,t,n){if(n>1){const e=t.multiplayerAmount*n;return t.limit&&e>t.limit?t.limit:e}return t.singlePlayerAmount}},938090:(e,t,n)=>{n.d(t,{H6:()=>i,P2:()=>p,XA:()=>l,YL:()=>s,dN:()=>m,fV:()=>c,h3:()=>d});var o=n(296540),a=()=>n(645873),r=n(474848);const i={UnlistedCollectionViewDismissButton:new(a().O2)("UnlistedCollectionViewDismissButton",(async()=>await Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(52274)]).then(n.bind(n,925409)))),UnlistedCollectionViewMoreButton:new(a().O2)("UnlistedCollectionViewMoreButton",(async()=>await Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(96346),n.e(77848),n.e(55571),n.e(92845),n.e(71718),n.e(5605),n.e(48486),n.e(14310),n.e(93552),n.e(95541),n.e(38441),n.e(44745)]).then(n.bind(n,796722)))),CollectionViewSettingsButton:new(a().O2)("CollectionViewSettingsButton",(async()=>await Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(26483),n.e(14310),n.e(93552),n.e(21871)]).then(n.bind(n,216680)))),CollectionViewBlock:new(a().O2)("CollectionViewBlock",(async()=>await Promise.all([n.e(75134,"high"),n.e(58795,"high"),n.e(99223,"high"),n.e(9304,"high"),n.e(96346,"high"),n.e(90825,"high"),n.e(77848,"high"),n.e(55571,"high"),n.e(48486,"high"),n.e(56952,"high"),n.e(74562,"high"),n.e(27711,"high"),n.e(59794,"high"),n.e(34359,"high"),n.e(15566,"high"),n.e(65487,"high"),n.e(78708,"high"),n.e(26483,"high"),n.e(93282,"high"),n.e(22970,"high"),n.e(14310,"high"),n.e(93552,"high"),n.e(30322,"high"),n.e(47934,"high"),n.e(35266,"high"),n.e(84341,"high"),n.e(28464,"high"),n.e(22542,"high"),n.e(67608,"high"),n.e(95803,"high"),n.e(91145,"high"),n.e(19356,"high"),n.e(36264,"high"),n.e(65682,"high"),n.e(943,"high")]).then(n.bind(n,539736)))),RestrictedCollectionView:new(a().O2)("RestrictedCollectionView",(async()=>await Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(22114),n.e(24515)]).then(n.bind(n,217172)))),CollectionViewBlockWorkflowControl:new(a().O2)("CollectionViewBlockWorkflowControl",(async()=>await Promise.all([n.e(75134),n.e(58795),n.e(99223),n.e(77848),n.e(55571),n.e(14310),n.e(93552),n.e(72535)]).then(n.bind(n,402046))))},s=(0,a()._h)(i.UnlistedCollectionViewDismissButton,(e=>e.default)),c=(0,a()._h)(i.UnlistedCollectionViewMoreButton,(e=>e.default)),l=(0,a()._h)(i.CollectionViewSettingsButton,(e=>e.CollectionViewSettingsButton)),d=(0,a()._h)(i.CollectionViewBlockWorkflowControl,(e=>e.CollectionViewBlockWorkflowControl)),u=(0,a().jQ)(i.CollectionViewBlock,(e=>e.CollectionViewBlockWithErrorBoundary)),p=(0,a()._h)(i.RestrictedCollectionView,(e=>e.RestrictedCollectionView)),m=o.forwardRef((function(e,t){const a=(0,o.useRef)(!1),s=(0,o.useRef)(i.CollectionViewBlock.getLoadingState().status),c=(0,o.useRef)(performance.now());return(0,n(852507).l)((()=>{a.current=!n(961581).A.state.initialRenderCompleted})),(0,r.jsx)(u,{...e,mountedBeforeInitialPageRender:a.current,cvbLoadingStateOnInitialRender:s.current,lazyRenderStartedAtRef:c,ref:t})}))},939768:(e,t,n)=>{n.d(t,{$_:()=>p,AY:()=>x,FB:()=>L,GP:()=>d,Gm:()=>u,Jf:()=>S,Jh:()=>b,O$:()=>v,P0:()=>B,Z$:()=>s,ZL:()=>C,ZO:()=>m,al:()=>N,bk:()=>P,cW:()=>f,d9:()=>M,hd:()=>h,he:()=>O,iW:()=>q,mJ:()=>U,pf:()=>g,q7:()=>A,qg:()=>l,qm:()=>_,qn:()=>y,t4:()=>I,t7:()=>F});n(16280),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698),n(898992),n(803949),n(581454),n(814603),n(147566),n(198721);var o=()=>n(188835),a=()=>n(534177),r=()=>n(720665);const i="/v1/oauth/authorize";function s(e){try{e=decodeURI(e)}catch(t){if(!(t instanceof URIError))throw t}return e.substring(e.lastIndexOf("/")+1)}let c=null;function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{return o().parse(e,!0,t.slashesDenoteHost)}catch(n){try{return{...o().parse(e),query:{}}}catch(n){return c||(c=o().parse("",!0)),c}}}function d(e){return o().format(e)}function u(e){const t=l(e.url);return t.search=null,t.query=e.query||{},t.hash=e.hash||null,d(t)}function p(e){const t=l(e);return d({protocol:t.protocol,auth:t.auth,host:t.host})}function m(e){const t=l(e);return t.protocol=null,t.host=null,t.hostname=null,t.slashes=!1,d(t)}function f(e){const t=l(e);return Boolean(!t.host&&!t.hostname)}function g(e){const t=l(e.relativeUrl),n=l(e.baseUrl);return t.protocol=n.protocol,t.host=n.host,t.hostname=n.hostname,d(t)}function b(e){const t=l(e.url);return t.path=null,t.pathname=e.pathname,d(t)}function h(e,t){return b({url:e,pathname:t})}function _(e,t){const n=l(e);return n.search=null,delete n.query[t],d(n)}function v(e,t){const n=l(e);return n.search=null,n.query={...n.query,...t},d(n)}function y(e,t){return l(e).query[t]}const w={"thumpmagical.top":!0,"geoloc8.com":!0,"kutabminaj.top":!0,"cutisbuhano.xyz":!0,"bhapurimillat.xyz":!0,"kingoffightermens.top":!0,"boxgeneral.xyz":!0,"ahnd.ga":!0,"steptossmessage.top":!0,"earthdiscover.xyz":!0,"sopecasniteroi.com.br":!0,"clangchapshop.xyz":!0},k=["http:","https:","mailto:","itms-apps:","tel:","cron:","cronlocal:","x-apple.systempreferences:","zoommtg:","notionmaillocal:","notionmaildev:","notionmailstg:","notionmail:"];function S(e){const{str:t,allowNoProtocol:n}=e;if(t&&"string"==typeof t)try{const e=l(t);if(e.host&&w[e.host])return;if(e.protocol&&e.host)return A(t);if(!e.protocol){try{const{host:e}=new URL(`stub:${t}`);if(w[e])return}catch{}try{const{host:e}=new URL(`stub://${t}`);if(w[e])return}catch{}}if(e.protocol&&k.includes(e.protocol)||n&&!e.protocol)return t}catch(o){return}}function A(e){if(e)try{const t=new URL(e);if(w[t.host])return;if(k.includes(t.protocol))return t.href}catch{}}function C(e){return(e||"").replace(/(?:https?|ftp):\/\/[\n\S]+/g,"")}function I(e,t){let{publicDomainName:n}=e;if(!n||!t)return;const o=Array.from(new Set([n,n.split(":")[0]]).values());for(const a of o)if(t.endsWith(`.${a}`))return t.substring(0,t.length-a.length-1)}function P(e){try{return new URL(e)}catch{return}}const T="none",E={utm_source:T,utm_medium:T,utm_campaign:T,utm_term:T,utm_content:T},R=["utm_source","utm_medium","utm_campaign","utm_term","utm_content","fbclid","gclid","device","targetid","criterionid","previous_path","ps_partner_key","ps_xid","trial_source"];function D(e){const t={};return R.forEach((n=>{t[n]=e.get(n)??void 0})),t}function q(e,t){const n=P(e);if(!n)return;const{searchParams:o}=n,i={...D(o),pathname:n.pathname,query:n.search},s={...E,...t},c={...i};return Object.keys(c).forEach((e=>{const t=s[e];(0,a().O9)(c[e])||(0,r().pN)(t)||(c[e]=t)})),c}function M(e){e=e.trim().toLowerCase();const t="àáäâèéëêìíïîòóöôùúüûñç·/_,:;";for(let n=0,o=28;n<o;n++)e=e.replace(new RegExp(t.charAt(n),"g"),"aaaaeeeeiiiioooouuuunc------".charAt(n));return e=e.replace(/[<>:"/\\|?*\x00-\x1F]| +$/g,"").replace(/\s+/g,"-").replace(/-+/g,"-")}function O(e){var t;const n=l(e);if(null!==(t=n.pathname)&&void 0!==t&&t.startsWith("/native"))throw new Error("Already on native redirect URL");return n.pathname=`/native${n.pathname}`,d(n)}function N(e){const t=P(e);if(t)return t.searchParams}function B(e){const{baseUrl:t,clientId:n,redirectUri:o,state:a}=e,r={client_id:n,response_type:"code",owner:"user",redirect_uri:o};return a&&(r.state=a),u({url:g({baseUrl:t,relativeUrl:i}),query:r})}function L(e){return new URL(e).host.replace("www.","")}function x(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";try{const t=new URL(e);return null!==t&&("http:"===t.protocol||"https:"===t.protocol)}catch(t){return!1}}Symbol("UrlString");function U(e,t){if(!e||!t)return!1;const[n,o]=[e,t].map(L),a=n.split("."),r=o.split("."),[i,s]=a.length<r.length?[a,r]:[r,a];return s.slice(-i.length).join(".")===i.join(".")}function F(e,t){if(!e||!t)return!1;const[n,o]=[e,t].map((e=>l(e).pathname||""));if(n===o)return!0;try{const e=decodeURIComponent(decodeURIComponent(n));return e===decodeURIComponent(decodeURIComponent(o))}catch(a){return!1}}},945522:(e,t,n)=>{n.r(t),n.d(t,{MobileNativeService:()=>g,createMobileNativeService:()=>b});n(16280),n(517642),n(658004),n(733853),n(845876),n(432475),n(515024),n(731698);var o=n.n(n(952224)),a=()=>n(857639),r=()=>n(502800),i=()=>n(763824),s=()=>n(780054),c=(n(944114),()=>n(155959));function l(e,t){return void 0===t?null:t}class d{constructor(e){var t=this;this.api=void 0,this.environment=void 0,this.receiveHandlers=void 0,this.sendChannel=void 0,this.sendChannelJson=void 0,this.sendChannelJsonWithReply=void 0,this.sendViaMessagePort=void 0,this.responseMap={},this.rejectMap={},this.preEnvironmentBridgeMetrics=[],this.notionPerformance=void 0,this.experimentStore=void 0,this.handleReceiveStringFromChannel=async e=>{if("MessagePort"===e)return;let t;try{t=JSON.parse(e)}catch(n){return void a().log({level:"error",from:"eventBasedApi",type:"JsonParseError",error:(0,r().convertErrorToLog)(n),data:{message:e}})}return this.handleReceiveChannel(t)},this.handleReceiveChannel=async e=>{if("request"===e.type){if(this.receiveHandlers[e.name]){const t=await this.receiveHandlers[e.name](...e.args),n={id:e.id,type:"response",name:e.name,result:t,error:void 0};if((0,c().T)("supportsJsonBridgeV3")){if((0,c().T)("supportsJsonBridgeWithReplyV3"))return n;this.sendChannelJson(n)}else this.sendViaMessagePort(JSON.stringify(n,l))}}else if("response"===e.type){if(this.responseMap[e.id]){const t=this.responseMap[e.id],n=this.rejectMap[e.id];delete this.responseMap[e.id],delete this.rejectMap[e.id],e.error?n(new Error(e.error)):t(e.result)}}else a().log({level:"error",from:"eventBasedApi",type:"JsonParseError",error:new Error("native->web data has no recognizable type"),data:{message:e}});return null},this.sendChannel=e.sendChannel,this.sendChannelJson=e.sendChannelJson,this.sendChannelJsonWithReply=e.sendChannelJsonWithReply,this.sendViaMessagePort=e.sendViaMessagePort,this.receiveHandlers=e.receiveHandlers;const n={};for(const o of e.sendCapabilities)n[o]=function(){const e=(Math.random()*Math.pow(10,16)).toString(),n=performance.now();for(var a=arguments.length,r=new Array(a),i=0;i<a;i++)r[i]=arguments[i];const s={id:e,type:"request",name:o,args:r};let d;if((0,c().T)("supportsJsonBridgeV3"))if((0,c().T)("supportsJsonBridgeWithReplyV3"))d=t.sendChannelJsonWithReply(s).then((e=>e.error?t.sendChannelJsonWithReply(JSON.stringify(s,l)).then((e=>e.result)):e.result));else{const n=new Promise(((n,o)=>{t.responseMap[e]=n,t.rejectMap[e]=o}));t.sendChannelJson(s),d=n}else{const n=JSON.stringify(s,l),o=new Promise(((n,o)=>{t.responseMap[e]=n,t.rejectMap[e]=o}));(0,c().T)("supportsWebMessagePort")&&t.sendViaMessagePort(n)||t.sendChannel(n),d=o}return d.then((e=>{const a=o.toString(),r=performance.now(),i={metric:{metricName:"mobilenative.bridge_request.web",startTime:n,endTime:r},data:{method_name:a}};return t.environment?t.sendBridgeMetric(i):t.preEnvironmentBridgeMetrics.push(i),e}))};this.api=n}enableMetricCollection(e,t,n){this.environment=e,this.notionPerformance=t,this.experimentStore=n;for(const o of this.preEnvironmentBridgeMetrics)this.sendBridgeMetric(o);this.preEnvironmentBridgeMetrics.splice(0)}sendBridgeMetric(e){const t=this.environment,n=this.experimentStore,o=this.notionPerformance;t&&n&&o&&n.checkGate({gateName:"mobile_bridge_performance_logging"})&&o.DO_NOT_USE_measureLegacy(e.metric,{environment:t,data:e.data})}}const u="__reactNativeCapabilities";class p{constructor(){this.emitter=new(n(137007).EventEmitter)}addListener(e,t,n,o){const a=`${e}:${n}:${t}`;return this.emitter.on(a,o),()=>this.emitter.off(a,o)}dispatch(e,t,n,o){const a=`${e}:${n}:${t}`;this.emitter.emit(a,o)}}let m=null;window.onmessage=e=>{if("MessagePort"===e.data&&e.ports&&e.ports.length){const o=e.ports[0];var t,n;if(m&&m.onmessage)o.onmessage=null===(t=m)||void 0===t?void 0:t.onmessage,null===(n=m)||void 0===n||n.close(),m.onmessage=null;m=o}};const f=window.DEVICE_READY_P;class g{constructor(e){this.sqliteConnection=void 0,this.listeners=new Set,this.receiveHandlers=void 0,this.eventBasedApi=void 0,this.initialNotification=void 0,this.device=void 0,this.startupMetric=void 0,this.horizontalSizeClass=void 0,this.componentEvents=new p,(0,n(604341).exposeDebugValue)("mobileNative",this),this.device=e.device,this.horizontalSizeClass=e.horizontalSizeClass,this.receiveHandlers={nativeToWebRenderStart:e=>{},connectivityTypeChanged:e=>{},toggleAvailableOffline:e=>{},openLink:e=>{this.initialNotification={url:e,clearHistory:!1}},openLinkV2:e=>{this.initialNotification=e},pushNotificationTokenRefresh:()=>{},backButtonPress:()=>{},keyboardWillShow:e=>{},keyboardDidShow:e=>{},keyboardWillHide:()=>{},keyboardDidHide:()=>{},keyboardConfigChanged:e=>{},safeAreaConfigChanged:e=>{},keyboardShortcut:()=>{},nativeBottomBarDidChange:e=>{},pause:()=>{},resume:async()=>{},appUpdateError:()=>{},appUpdateChecking:()=>{},appUpdateAvailable:()=>{},appUpdateNotAvailable:()=>{},appUpdateProgress:()=>{},appUpdateReady:()=>{},appUpdateFinished:()=>{},statusBarTap:()=>{},themeChanged:e=>{},track:()=>{},refreshSubscriptionData:async e=>{},refreshSubscriptionDataV2:async()=>{},processMobileActionBarAction:e=>{},logout:e=>{},logoutV2:e=>{},logoutAll:()=>{},openDestinationV2:e=>{},openDestination:e=>{},updateTransactionState:e=>{},setCurrentUserId:e=>{},setCurrentSpace:e=>{},updateFileUploadProgress:e=>{},updateHorizontalSizeClass:e=>{},updateTabbedRouterState:e=>[{}],getLocalSearchResults:e=>[{error:"Browser API not ready yet"}],customAddCommentMenuItemTapped:()=>{},filterValidMoveToBlocks:e=>[{error:"Browser API not ready yet"}],completeMoveToTransaction:e=>[{error:"Browser API not ready yet"}],undoRevision:()=>{},searchTeams:e=>[{error:"Browser API not ready yet"}],getImageBlockUrls:e=>[{error:"Browser API not ready yet"}]};const t=window.ReactNativeWebView?{send:e=>window.ReactNativeWebView.postMessage(e),sendJson:e=>window.ReactNativeWebView.postJsonMessage(e),sendJsonWithReply:e=>window.ReactNativeWebView.postJsonMessageWithReply(e),listen:e=>window.addEventListener("message",e)}:{send:e=>window.postMessage(e,"*"),sendJson:e=>{},sendJsonWithReply:e=>{},listen:e=>document.addEventListener("message",e)};window.ReactNativeWebView&&(window.ReactNativeWebView.browserApiRequest=async e=>this.eventBasedApi.handleReceiveChannel(e)),m&&(0,c().T)("supportsWebMessagePort")&&(m.onmessage=e=>{this.eventBasedApi.handleReceiveStringFromChannel(e.data)});t.listen((e=>{if(e.data&&!function(e){return e.source===window&&"string"==typeof e.data&&0===e.data.indexOf("setImmediate$")}(e)&&!function(e){var t,n;return"string"==typeof e.data&&e.data.startsWith("webpack")||"string"==typeof(null===(t=e.data)||void 0===t?void 0:t.type)&&(null===(n=e.data)||void 0===n?void 0:n.type.startsWith("webpack"))}(e))for(const t of Array.from(this.listeners))t(e.data)})),this.eventBasedApi=new d({sendChannel:t.send,sendChannelJson:t.sendJson,sendChannelJsonWithReply:t.sendJsonWithReply,sendViaMessagePort:e=>!!m&&(m.postMessage(e),!0),receiveHandlers:this.receiveHandlers,sendCapabilities:e.sendCapabilities}),(0,n(644425).updateNativeErrorHandler)((e=>{const t=this.eventBasedApi.api.handleWebError;t&&t(e)}))}async initialize(e){let{sendCapabilities:t}=e;const o=t.indexOf("execSqliteBatch")>-1;if(!this.api.execSqliteBatch||!o)return;const i={execSqliteBatch:async e=>{if(!this.api.execSqliteBatch)throw new Error("execSqlBach API removed after SqliteConnection was created");const t=await this.api.execSqliteBatch(e).catch((e=>{throw a().log({level:"error",from:"mobileNative.ts",type:"execSqliteBatch",error:(0,r().convertErrorToLog)(e),data:{}}),e}));if(t.error){const e=new Error(t.error.message);throw e.name=t.error.name,e}return t.value},completelyRebuildSqliteDb:()=>Promise.resolve()};this.sqliteConnection=new(n(735270).i)({connection:i,migrations:await(0,n(866540).G)(i),type:"v1"})}markInitializationComplete(e){this.startupMetric={metricName:"mobilenative.service_initialization",startTime:e,endTime:performance.now()}}get api(){return this.eventBasedApi.api}updateReceiveApiHandlers(e){Object.assign(this.receiveHandlers,e)}share(e){this.api.share&&this.api.share(e)}copyText(e,t){this.api.copyToClipboard&&this.api.copyToClipboard({contents:e,message:t})}async moveTo(e){if(this.api.moveTo)return await this.api.moveTo(e)}async openEmojiPicker(){if(this.api.openEmojiPicker)return await this.api.openEmojiPicker()}async openFilePicker(e){if(this.api.openFilePicker)return await this.api.openFilePicker(e)}async uploadFile(e){if(this.api.uploadFile)return await this.api.uploadFile(e)}setTheme(e,t){this.api.setAppTheme&&this.api.setAppTheme(t?"system":e)}openLink(e){"in-app"===n(410555).A.state?this.openInAppBrowser(e):this.openExternalBrowser(e)}openInAppBrowser(e){this.api.openInAppBrowser&&this.api.openInAppBrowser(o()(e))}openExternalBrowser(e){this.api.openExternalBrowser?this.api.openExternalBrowser(e):this.openInAppBrowser(e)}openAuthSessionBrowser(e){this.api.openAuthSessionBrowser?this.api.openAuthSessionBrowser(e):this.openExternalBrowser(e)}closeInAppBrowser(){this.api.closeInAppBrowser&&this.api.closeInAppBrowser()}debugLog(e){var t,n;null===(t=(n=this.api).debugLog)||void 0===t||t.call(n,e)}openUpgradeModal(e){this.api.openUpgradeModalV2?this.api.openUpgradeModalV2(e):this.api.openUpgradeModal&&this.api.openUpgradeModal(e.spaceId,e.from)}exitApp(){this.api.exitApp&&this.api.exitApp()}showSplashscreen(){this.api.showSplashScreen&&this.api.showSplashScreen()}hideSplashscreen(){this.api.hideSplashScreen&&this.api.hideSplashScreen()}handlePerformanceMetricsUpdate(e){this.api.handlePerformanceMetricsUpdate&&this.api.handlePerformanceMetricsUpdate(e)}buzz(){this.api.buzz&&this.api.buzz()}enableBridgeMetricsCollection(e,t,n){this.startupMetric&&(t.DO_NOT_USE_measureLegacy(this.startupMetric,{environment:e,data:{}}),this.startupMetric=void 0),this.eventBasedApi.enableMetricCollection(e,t,n)}showLightBox(e){this.api.showLightBox&&this.api.showLightBox(e)}showLightBoxV2(e){const{url:t,previewUrl:n,type:a,from:r,state:i}=e;if(!t&&!n&&this.device.isIOS&&!(0,c().T)("supportsSecureLightboxImageUrlFetchingState"))return;const s=n?o()(n):void 0,l=t?o()(t):void 0;this.showLightBox({items:[{type:a,previewUrl:s,originalUrl:l,downloadName:e.downloadName,state:i}],startingIndex:0,from:r})}setBadgeNumber(e,t){this.api.setBadgeNumber&&this.api.setBadgeNumber(e,t)}openUpdateSettings(){this.api.openUpdateSettings&&this.api.openUpdateSettings()}toggleBottomBar(e){this.api.toggleBottomBar&&this.api.toggleBottomBar(e)}toggleNativeHome(){this.api.toggleNativeHome&&this.api.toggleNativeHome()}async setCookie(e){this.api.setCookie&&await this.api.setCookie(e)}sidebarVisibility(e,t){if(this.api.sidebarVisibility){const n={isVisible:e,width:t};this.api.sidebarVisibility(n)}}hasNativeAppleLogin(){return Boolean(this.api.requestNativeAppleAuth)}hasNativeGoogleLogin(){return this.device.isAndroid&&Boolean(this.api.requestGoogleJwt)}supportsNativeHomeOnPhone(){return(0,c().T)("supportsNativeHome")&&this.device.isPhone}async requestNativeAppleAuth(){if(this.api.requestNativeAppleAuth)return this.api.requestNativeAppleAuth()}async requestGoogleJwt(){if(this.device.isAndroid&&this.api.requestGoogleJwt)return this.api.requestGoogleJwt({webClientId:s().A.googleOAuth.clientId})}async logoutOfGoogle(){if(this.device.isAndroid&&this.api.logoutOfGoogle)return this.api.logoutOfGoogle({webClientId:s().A.googleOAuth.clientId})}async resetAssetCache(){this.api.resetAppCache&&await this.api.resetAppCache()}async setLogglyData(e){this.api.setLogglyData&&await this.api.setLogglyData(e)}async unregisterPushNotifications(){this.api.unregisterPushNotifications&&await this.api.unregisterPushNotifications()}async cancelUserBackgroundTasks(){this.api.cancelUserBackgroundTasks&&await this.api.cancelUserBackgroundTasks()}renderMobileActionBar(e){this.api.renderMobileActionBar&&this.api.renderMobileActionBar(e)}sendRawMessageStoreMessage(e){this.api.sendRawMessageStoreMessage&&this.api.sendRawMessageStoreMessage(e)}sendRawAudioProcessorMessage(e){this.api.sendRawAudioProcessorMessage&&this.api.sendRawAudioProcessorMessage(e)}get setWebViewAllowsNavigationGestures(){return this.api.setWebViewAllowsNavigationGestures}subscribeToOpenLink(e){if(this.updateReceiveApiHandlers({openLink:t=>e(t,!1,!1),openLinkV2:t=>e(t.url,t.clearHistory,t.showWeb??!0)}),this.initialNotification){const{url:t,clearHistory:n,showWeb:o}=this.initialNotification;this.initialNotification=void 0,setTimeout((()=>e(t,n,o??!0)))}}subscribeToUpdateTransactionState(e){this.updateReceiveApiHandlers({updateTransactionState:e})}showNativeHomeTab(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{};(0,c().T)("supportsNativeHome")&&(this.device.isAndroid?this.exitApp():this.toggleNativeHome(),n(496603).cb(e,350))}markTransitionReady(e){const t=()=>{var t,o;null===(t=(o=this.api).transitionReady)||void 0===t||t.call(o,{type:e.type,id:e.id}),n(671973).N.markTransitionReady(e.environment,e.type,e.isNavigationEvent)};e.sendImmediately?t():window.requestAnimationFrame((()=>{setTimeout((()=>{t()}),0)}))}recordPageVisit(e,t){this.api.recordPageVisit&&this.api.recordPageVisit({userId:e,...t})}nativeToWebRenderEnd(e,t){this.api.nativeToWebRenderEnd&&this.api.nativeToWebRenderEnd({id:e,metrics:t})}updateCustomAddCommentMenuItemEnabled(e){this.api.updateCustomAddCommentMenuItemEnabled&&this.api.updateCustomAddCommentMenuItemEnabled(e)}updateAiAssistantEnabledState(e){this.api.updateAiAssistantEnabledState&&this.api.updateAiAssistantEnabledState(e)}updateAiAssistantVisibilityState(e){this.api.updateAiAssistantVisibilityState&&this.api.updateAiAssistantVisibilityState(e)}requestKeepScreenOn(e){var t,n;null===(t=this.api)||void 0===t||null===(n=t.requestKeepScreenOn)||void 0===n||n.call(t,e)}async getSqliteDiskUsage(){var e,t;const n=await(null===(e=this.api)||void 0===e||null===(t=e.getSqliteDiskUsage)||void 0===t?void 0:t.call(e));return n?Number((n/1024/1024).toFixed(2)):0}submitUserFeedback(e,t){this.api.submitUserFeedback?this.api.submitUserFeedback(e):t()}}async function b(e,t){await f,await i().wR(0);const n=window[u]||[],o=new g({device:e.device,sendCapabilities:n,horizontalSizeClass:t});return await o.initialize({sendCapabilities:n}),o}},959013:(e,t,n)=>{n.d(t,{XU:()=>i.XU,dT:()=>i.dT,sA:()=>s.A,Gr:()=>i.Gr,Dk:()=>c.A,EY:()=>l.E,MT:()=>d.MT,YK:()=>u,j4:()=>p,Vq:()=>r,tz:()=>m});var o=n(587155),a=n(327025);n(898992),n(354520);function r(e){return null!=e}n(581454);var i=n(804106),s=n(408666),c=n(335596),l=n(360665),d=n(531234);Symbol.for("LocalizedString"),Symbol("defined message descriptor");function u(e){return e}function p(e){return(0,o.Ay)(e)}const m=a.A},961581:(e,t,n)=>{n.d(t,{A:()=>r});var o=()=>n(757695);class a extends o().Store{getInitialState(){return{subMetricsStore:o().Store.createValue({},{name:"subMetricsStore"}),OPFSMetricDataStore:o().Store.createValue({},{name:"OPFSMetricDataStore"}),metricDataStore:o().Store.createValue({num_api_calls_initiated:0,num_api_calls_completed:0,wasm_sqlite_initialized:"skipped-unsupported-device"},{name:"metricDataStore"}),initialRenderCompleted:!1,initialRenderAfterLoginCompleted:!1,initialLoadCachedPageChunkCalledAt:void 0,prewarmedTabAppStartTimeOverride:void 0}}incrementNumApiCallsInitiated(){this.state.initialRenderCompleted||this.state.metricDataStore.update((e=>({...e,num_api_calls_initiated:e.num_api_calls_initiated+1})))}incrementNumApiCallsCompleted(){this.state.initialRenderCompleted||this.state.metricDataStore.update((e=>({...e,num_api_calls_completed:e.num_api_calls_completed+1})))}setDesktopLoadContext(e,t){const n=t?parseInt(t):void 0;this.update((t=>({...t,desktopLoadOrigin:e,desktopTabCount:n})))}}const r=new a},973647:(e,t,n)=>{n.d(t,{Q:()=>o,j:()=>a});n(944114);const o={success:e=>({value:e}),fail:e=>({error:e}),isSuccess:e=>!("error"in e),isFail:e=>"error"in e,unwrap(e){if(o.isFail(e))throw e.error;return e.value},unwrapOr:(e,t)=>o.isFail(e)?t:e.value,reduce(e,t){let n={value:e[0]};for(let a=1;a<e.length;a++){const r=e[a];if(o.isFail(n))return n;n=t(n.value,r)}return n},map(e,t){const n=[];for(const a of e){const e=t(a);if(o.isFail(e))return e;n.push(e.value)}return{value:n}},partition(e){const t=[],n=[];for(const a of e)o.isSuccess(a)?t.push(a.value):n.push(a.error);return{successes:t,errors:n}}};function a(e){try{const t=e();return function(e){switch(typeof e){case"undefined":case"string":case"bigint":case"symbol":case"boolean":return!1;case"function":case"object":return Boolean(e&&"then"in e&&"function"==typeof e.then)}return!1}(t)?Promise.resolve(t.then((e=>({value:e})),(e=>({error:e})))):{value:t}}catch(t){return{error:t}}}},974233:(e,t,n)=>{n.d(t,{C:()=>c,V:()=>l,default:()=>f});n(16280),n(944114),n(964979);var o=()=>n(206267),a=()=>n(449412),r=()=>n(534177),i=()=>n(155959);const s=!1;class c extends Error{constructor(e,t){super(e),this.name=void 0,this.message=void 0,this.data=void 0,this.message=e,this.name="HttpRequestError",this.data=t}}function l(e){var t;return e.offline?"Offline":null!==(t=e.body)&&void 0!==t&&t.debugMessage?e.body.debugMessage:`HTTP ${e.status}`}function d(e){return{"X-Notion-User-Id":e.headers.get("X-Notion-User-Id")||void 0,"X-Notion-Request-Id":e.headers.get("X-Notion-Request-Id")||void 0,"X-Notion-Client-Log-Call-Stack":e.headers.get("X-Notion-Client-Log-Call-Stack")||void 0,"content-type":e.headers.get("content-type")||void 0,"cf-ray":e.headers.get("cf-ray")||void 0}}async function*u(e){if(!e.body)return;const t=e.body.getReader(),n=new TextDecoder;let o="";try{for(;;){const e=await t.read();if(e.done){const e=o.trim();e.length>0&&(yield JSON.parse(e));break}{o+=n.decode(e.value,{stream:!0});const t=o.split("\n");o=t[t.length-1];for(let e=0;e<t.length-1;e++){const n=t[e].trim();n.length>0&&(yield JSON.parse(n))}}}}catch(a){await t.cancel(a)}}async function p(e){try{return await async function(e){const t={type:"application/json"},n=new Blob([e],t).stream().pipeThrough(new CompressionStream("gzip")).getReader(),o=[];for(;;){const{done:e,value:t}=await n.read();if(e)break;o.push(t)}const a=new Blob(o,t);return s&&console.log(`Gzipped payload: ${e.length} bytes uncompressed, ${a.size} bytes compressed`),a}(e)}catch(t){(0,a().O8)(t,{from:"httpRequest",type:"gzipFailed"})}}function m(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];null==e||e(...n)}catch{}}const f=async function(e){var t;const a={...e.headers};let s;"GET"===e.method||"json"!==e.format&&"jsonStream"!==e.format||(a["Content-Type"]="application/json"),"jsonStream"===e.format&&(a.Accept="application/x-ndjson");let l=0;if(e.data){const t=JSON.stringify(e.data);if(s=t,l=t.length,"gzip"===e.encoding&&"json"===e.format&&(f=e.environment.device,window.CompressionStream&&(!f.isMobileNative||(0,i().T)("supportsBodyInHttpRequests")))){const e=await p(s);if(e){const t=await e.arrayBuffer();s=t,l=t.byteLength,a["Content-Encoding"]="gzip"}}}var f;const g={method:e.method,credentials:"same-origin",headers:a,body:s,signal:e.abortSignal};let b,h=e.url;e.environment.device.isMobileNative&&!(0,i().T)("supportsBodyInHttpRequests")&&void 0!==s&&(h=n(939768).O$(h,{[n(838364).nG]:s}),delete g.body),m(null===(t=e.eventListeners)||void 0===t?void 0:t.onRequestStart,l);try{b=await fetch(h,g)}catch(A){var _;if(m(null===(_=e.eventListeners)||void 0===_?void 0:_.onRequestFailed,void 0),A instanceof ReferenceError)throw A;if(A instanceof DOMException&&"AbortError"===A.name)return function(e){const{url:t,requestBody:n,headers:a}=e;return{type:"failed",offline:!1,status:0,error:new c("Request aborted.",{url:t,requestBody:n,responseBody:void 0,offline:!1,status:0}),body:{errorId:o().JW(),name:"AbortedError",clientData:{type:"aborted"}},headers:a}}({url:h,requestBody:e.data,headers:a});if(A instanceof TypeError)return function(e){const{url:t,requestBody:n,cause:a}=e;return{type:"failed",offline:!0,status:0,error:new c("Offline.",{url:t,requestBody:n,responseBody:void 0,offline:!0,status:0,cause:a}),body:{errorId:o().JW(),name:"HttpRequestError",clientData:{type:"offline"}}}}({url:h,requestBody:e.data,cause:A});throw A}var v,y;if(200!==b.status)return m(null===(v=e.eventListeners)||void 0===v?void 0:v.onRequestFailed,b),await async function(e,t){let n;try{n=await t.json()}catch(A){}const a=`Received HTTP ${t.status}`;if(0===t.status)return{type:"failed",offline:!0,status:t.status,error:new c(a,{url:e.url,requestBody:e.data,responseBody:n,offline:!0,status:t.status}),body:{errorId:o().JW(),name:"HttpRequestError",clientData:{type:"offline"}}};{const o=d(t);return{type:"failed",offline:!1,status:t.status,headers:o,error:new c(a,{url:e.url,requestBody:e.data,responseBody:n,offline:!1,status:t.status}),body:n}}}(e,b);m(null===(y=e.eventListeners)||void 0===y?void 0:y.onRequestFetched,b);try{var w,k;m(null===(w=e.eventListeners)||void 0===w?void 0:w.onParseResponseStart);const t=await async function(e,t){const n=d(t);let o=e.format;return"jsonStream"!==e.format||function(e){const t=e.headers.get("Content-Type");return!!t&&"application/x-ndjson"===t}(t)||(o="json"),"json"===o?{type:"success",status:t.status,data:await t.json(),headers:n}:"text"===o?{type:"success",status:t.status,data:await t.text(),headers:n}:"jsonStream"===o?{type:"success",status:t.status,data:u(t),headers:n}:void(0,r().HB)(o)}(e,b);return m(null===(k=e.eventListeners)||void 0===k?void 0:k.onParseResponseDone,b),t}catch(A){var S;m(null===(S=e.eventListeners)||void 0===S?void 0:S.onParseResponseFailed,b);const t=(0,n(319625).A)(A);return{type:"failed",offline:!1,status:b.status,headers:d(b),error:new c(`Unable to parse HTTP response: ${t.message}`,{url:e.url,requestBody:e.data,responseBody:b.body,offline:!1,status:b.status,cause:t}),body:e.data}}}},994310:(e,t,n)=>{n.d(t,{A:()=>a});var o=()=>n(419494);const a=new(o().Ay)({namespace:o().Bq,important:!0,trackingType:"necessary"})},999822:(e,t,n)=>{async function o(){const{createDevice:e}=await Promise.resolve().then(n.bind(n,141625)),{Store:t}=await Promise.resolve().then(n.bind(n,757695)),{default:o}=await Promise.resolve().then(n.bind(n,52523)),a=t.createValue("unknown",{name:"horizontalSizeClassStore"}),r=e(window,{horizontalSizeClassStore:a});if(r.isElectron){const[e,t]=await Promise.all([Promise.resolve().then(n.bind(n,711059)),Promise.resolve().then(n.bind(n,219187))]);r.desktopAppVersion=e.formatVersion(await t.getAndCacheDesktopVersionAsync())}const{ComputedStore:i}=await Promise.resolve().then(n.bind(n,496506)),s=new i((()=>({...r})),{debugName:"SharedDeviceStore"});if(r.isMobileNative){const e=performance.now(),[{createMobileNativeService:t},i]=await Promise.all([Promise.resolve().then(n.bind(n,945522)),Promise.resolve().then(n.bind(n,475133))]),c=await t({device:r},a),l=i.getMobileNativeDeviceInfo();r.isIPhoneX=i.isIPhoneX(),r.mobileAppVersion=l.mobileNativeAppVersion,r.isMobileBeta=l.is_mobile_beta??!1,l.ramSizeInGB&&(r.ramSizeInGB=parseFloat(l.ramSizeInGB)),c.markInitializationComplete(e);const d={device:r,deviceStore:s,mobileNative:c};return{...d,cookieService:new o(d)}}const c={device:r,deviceStore:s,mobileNative:void 0};return{...c,cookieService:new o(c)}}n.d(t,{createMinimalEnvironment:()=>o})}},i={};function s(e){var t=i[e];if(void 0!==t)return t.exports;var n=i[e]={id:e,loaded:!1,exports:{}};return r[e].call(n.exports,n,n.exports,s),n.loaded=!0,n.exports}s.m=r,s.amdO={},e=[],s.O=(t,n,o,a)=>{if(!n){var r=1/0;for(d=0;d<e.length;d++){for(var[n,o,a]=e[d],i=!0,c=0;c<n.length;c++)(!1&a||r>=a)&&Object.keys(s.O).every((e=>s.O[e](n[c])))?n.splice(c--,1):(i=!1,a<r&&(r=a));if(i){e.splice(d--,1);var l=o();void 0!==l&&(t=l)}}return t}a=a||0;for(var d=e.length;d>0&&e[d-1][2]>a;d--)e[d]=e[d-1];e[d]=[n,o,a]},s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},n=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,s.t=function(e,o){if(1&o&&(e=this(e)),8&o)return e;if("object"==typeof e&&e){if(4&o&&e.__esModule)return e;if(16&o&&"function"==typeof e.then)return e}var a=Object.create(null);s.r(a);var r={};t=t||[null,n({}),n([]),n(n)];for(var i=2&o&&e;"object"==typeof i&&!~t.indexOf(i);i=n(i))Object.getOwnPropertyNames(i).forEach((t=>r[t]=()=>e[t]));return r.default=()=>e,s.d(a,r),a},s.d=(e,t)=>{for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},s.f={},s.e=(e,t)=>Promise.all(Object.keys(s.f).reduce(((n,o)=>(s.f[o](e,n,t),n)),[])),s.u=e=>(({22:"CollectionChartView",48:"OrganizationOnboardingModal",209:"TeamJoinLeaveButton",287:"tourTutorial",890:"icon-mapPin",943:"CollectionViewBlock",1042:"WikiPromoPopup",1423:"PagePropertiesRowNameMenu",1660:"icon-creditCard",1797:"consoleHelpers",1978:"third-party-scripts",2040:"emoji-norwegian",2144:"LoginWithTemplate",2147:"icon-deepnote",2268:"emoji-korean",2411:"localDuplicate",2501:"MarketplaceReviewNudge",2569:"AutomationModal",2597:"TopbarLayoutInfoButton",2686:"ImproveJiraSyncPopup",2703:"chatSidebar",2865:"RestrictedTransclusionBlockContent",2922:"icon-present",3272:"emoji-french",3476:"breadcrumb",3551:"offlineSettings",3773:"TranslateBanner",3791:"icon-claude",3967:"Omnimodal",4249:"icon-arrowExpandDiagonalBottomLeftToTopRight",4586:"linkMenu",4750:"TeamspacesPageEmptyState",4876:"moveToTeam",4902:"transcriptionErrorActions",5053:"ai",5140:"icon-page",5287:"CollectionLinkExistingModal",5815:"icon-apple",5834:"transformActions",5857:"icon-paperPlane",5938:"FilePropertyModule",5999:"MarketplaceReviewModal",6122:"icon-miro",6178:"DisconnectContactModal",6893:"notifications",6993:"isTemplate",7091:"opfs-console",7097:"desktop",7391:"icon-lucidspark",7457:"icon-infoCircle",7520:"selfServeBusinessTrialsActions",7717:"ExperimentSettings",7989:"PinnedInfoPanelToggleButton",8243:"AgentModelPicker",8340:"translation",8881:"icon-plus",8953:"SidebarTrash",9150:"workflowAdvanced",9304:"PageViewBlock",9423:"emoji-danish",9555:"icon-trello",9647:"oauthAuthorization",9832:"icon-zoom",10143:"icon-microsoftTeams",10399:"workflowDeeplinkActions",10408:"customEmoji",10815:"DowngradeModal",10831:"collectionFormsShared",10871:"ConfirmOverwriteModal",11097:"workflowActions",11126:"contextualizedOnboardingFlowHelpers",11235:"icon-zendesk",11922:"TimelineItemDateRange",12039:"mobileBottomBar",12062:"CollectionItemNameConfigMenuItemNotInProd",12071:"addCollectionViewSourceToNewCollection",13017:"ConnectOAuthIntegrationModal",13319:"activityUpdate",13326:"sentry",14068:"errorBoundaryDebugger",14079:"icon-make",14201:"SnapshotAdminDebugButton",14227:"SharingEnabledPropertyNameTooltip",14310:"RecordMap",14344:"icon-xMark",14358:"UniversalQnaModals",14491:"selfServeBusinessTrialExplorePlanModal",15135:"icon-workflowy",15399:"WorkflowSettingsLinkedCollections",15498:"lowPriKeyboardShortcuts",15529:"WorkflowTemplatesOnboardingModal",15538:"icon-arrowInCircleUp",15780:"ConfigureIntegrationModal",15891:"icon-slack",15961:"assistantAnimatedFace",16006:"VerifiedPagesSettings",16130:"conditionalLogicTooltip",16345:"PublishSite",16453:"icon-plug",16697:"WorkflowEditorCollectionViewPage",16999:"icon-confluence",17546:"icon-googleCalendar",17586:"loadWorkflowTemplateInstances",18071:"initiateExternalAuthentication",18331:"mobileCommentsModalRenderer",18710:"icon-arrowUpRightSquare",18766:"RelationMenuRow",19227:"ErrorInfoPopup",19314:"icon-pagerDuty",19596:"organizationSettingsConsole",19884:"admin",20108:"claimAndUpgrade",20488:"AddAnotherAccountLoginModal",20512:"icon-hex",20589:"PageHeaderInfoPanelToggleButton",21088:"AuthSyncListener",21377:"UpdateSidebarTabInfo",21446:"JiraSyncSourceTooltip",21697:"icon-grid",21871:"CollectionViewSettingsButton",21914:"icon-hubSpot",22542:"formulas",22844:"workflow-console",22903:"SidebarSwitcherExternalPagesButton",22931:"CreatorProfileModal",23133:"CollectionFormEditorView",23754:"designPreviews",23873:"icon-excalidraw",23904:"PostsLaunchModal",24515:"RestrictedCollectionView",24598:"WorkflowTemplatesTooltip",24708:"WorkflowSettingsConfirmAddTemplate",25171:"InviteDropdownMenuItemLabel",25556:"use-page-subscription-manager",25583:"drawing",26207:"pageVerificationMenuRenderer",26403:"collectionDebugSettings",26845:"publicTopbarShareButton",27229:"SitesDomainsSection",27315:"emoji-portuguese",27761:"forkPageModal",27942:"ScrollableBlockLimitBanner",28307:"SidebarInboxButton",28387:"publicAIBuilder",28763:"JiraDCSyncModals",28958:"inPageFind",29021:"recurrence",29348:"icon-people",29366:"katex",29945:"RelationPropertyMenu",30015:"PublicShareLinkLoginModal",30214:"LoggedOutAppBanner",30260:"icon-sparkles",30512:"icon-lucidchart",30603:"TranscriptionBlockDeferredUI",30737:"icon-dropbox",30848:"icon-notionCalendar",31061:"sidebar",31388:"selfServeBusinessTrialEndingModal",31517:"AppTemplates",31743:"selfServeBusinessTrialStartModal",32179:"icon-codepen",32413:"ModalOverlayCollectionView",32568:"DesktopEmailConfirmPage",32644:"imageHyperlink",33168:"icon-googleDrive",33429:"wasm-sqlite-shared-worker",33590:"PublicPageSidebarContent",34208:"pageTemplateModalActions",34251:"icon-dashlane",34420:"contextualizedOnboardingActions",34616:"icon-gear",35115:"sidebarMobile",35364:"icon-inVision",35390:"emoji-german",35537:"formulaPermissions",35756:"CollectionViewSelect",35818:"radioModalRenderer",35905:"CollapsiblePanel",36432:"UncollectibleExperienceOverlay",36448:"icon-amplitude",36733:"primusV8",37043:"framer-motion",37045:"createAndDuplicatePageInSpace",37062:"PostImportIntroPopup",37125:"comments",37353:"markdown-linkify-it",37398:"GoogleImportContactsButton",37565:"icon-medicine",38949:"icon-polymer",39047:"opfs-page-cache-worker",39301:"PublishedSiteSettings",39689:"icon-pitch",39778:"CollectionSettingsCustomizeMenuPrototype",39953:"icon-jira",40198:"pageCovers",40200:"CreatorProfile",40418:"icon-code",40432:"TrialInfoModal",40471:"icon-abstract",40502:"MwnFeatureModal",40589:"tableSelectionOverlayPropertyActions",40642:"NewDomainModal",40902:"SidebarLibraryButton",41091:"slashMenuIcons",41135:"personalHomeTipsHelpers",41412:"pageVerificationBadge",41835:"icon-arrowSquarePathUpDown",41837:"icon-mixpanel",42050:"createHasSchemaChangedForQueryStore",42481:"HelpButtonContent",42704:"TranscriptionBlockTabMenu",43151:"peekRenderer",43396:"posts",43862:"icon-whimsical",43935:"TableHeaderPropertyCreationMenu",44032:"icon-checkmarkCircle",44158:"automationTypecheck",44225:"icon-asana",44425:"offlineSync",44539:"icon-figma",44632:"PersonProfileContainer",44745:"UnlistedCollectionViewMoreButton",44802:"personalHome",44852:"icon-microsoftOutlook",45016:"Onboarding",45248:"PulsingWrapper",45624:"PerformanceToolbar",45666:"ChatCollectionView",45758:"integrations",45915:"LanguageSwitchPromptPopup",46204:"SiteSettingsLayoutEditor",46283:"AgentChatView",46726:"TranscriptionBlockSettingsMenu",46933:"exportJsxRenderer",46990:"BlockPropertyRouter",47057:"StartupsApplication",47108:"AIChatView",47161:"PostUpgradeAnnouncementModal",47307:"SharingEnabledIcon",47381:"Toaster",47536:"CollectionTasksEmptyStateModal",47779:"TableHeaderCellMenu",48486:"loadLocalSubtree",49044:"icon-alarm",49222:"mobileActionBar",49229:"icon-replit",49291:"icon-chat",49373:"icon-googleDocs",49908:"TooltipOnSlackEmbed",50437:"WorkflowInPlaceEditor",50462:"CalendarAgendaView",50568:"HIPAASuccessModal",50708:"icon-browserAdd",50710:"icon-personCropCircleBadgeExclamationPoint",51106:"emoji-dutch",51197:"notionscript",51226:"GoogleImportsInviteWithModal",51363:"TeamHomeMoreMenu",51466:"RemoveAddOnModal",51609:"outlinerViewAll",51646:"SelectableHoverMenuOverlay",51680:"CollectionMapView",51859:"FormShareMenu",51872:"emoji-english",52009:"SlideBlock",52084:"icon-clickUp",52274:"UnlistedCollectionViewDismissButton",52614:"floatingTableOfContents",52840:"AIForWorkModal",52903:"ConfirmPrivilegedActionModal",53095:"coediting",53147:"FeatureIntroPopup",53179:"topbar",53631:"emoji-swedish",53638:"mobileCalendarDayMenu",53928:"MarketplaceThirdPartyLinkModal",53974:"icon-twitter",53977:"OutlinerToggleOpenSetupModalButton",54087:"restrictedContentDialog",54134:"icon-personCircle",54215:"MaybeMarketplaceReviewNudge",54220:"simpleFormulas",54398:"MailLaunchModal",54518:"dictation",54564:"githubStudentPack",54928:"topbarActionButtons",54980:"icon-commentInfo",55072:"WorkspaceDiscovery",55222:"PublicShareSidebarContent",55632:"icon-priceTag",55724:"icon-youTube",55776:"formulaEditor",55901:"SlackImportContactsButton",56188:"JiraSyncTeamSpaceModal",56301:"selfServeBusinessTrialLossAversionModal",56353:"oauthPostLogin",56388:"MeetingNotesPrelaunchModal",56409:"activityNotification",56859:"DomainsSection",56989:"UpgradeRequestFormModal",57314:"restrictedAccess",57478:"ContactSalesModal",57613:"personPropertyInviteActions",58204:"TipsInAppModal",58251:"CreatorProgram",58319:"icon-linkedin",58587:"chrono-node",58596:"FullPageAIChatLoader",58657:"initiateExternalAuthenticationFromDesktop",58777:"notionCalendarAuthorization",58790:"PerfmarkTrialIneligibleToast",58795:"RecordModel",59111:"formPropertyRenderer",59287:"icon-office365",59337:"emoji-finnish",59388:"serverBackedLocalDuplicate",59430:"icon-coda",59698:"icon-microsoftSharePoint",59970:"payments",60245:"icon-bitwarden",60453:"PasskeyNudgeModal",60858:"icon-notion",60896:"invoice",61227:"BeamRenderer",61242:"icon-addSquareRounded",61362:"switchFromTabbedToSimpleLayout",61487:"icon-typeform",61488:"crdt-debugging-overlay",61492:"icon-quip",61560:"MailLaunchModal2",62033:"topbarSidebarButton",62058:"MobileNativePerformanceListener",62342:"contextualizedOnboardingHelpers",62396:"icon-globe",62516:"RecentsCachingListener",62620:"CollectionNewViewsPopupComponent",62989:"SimpleMonacoWrapper",63137:"createPageInTeamSync",63538:"BuilderAddModuleButtonEducationTooltip",63539:"mobileRedesigned",63990:"switchFromSimpleToTabbedLayout",64087:"icon-exclamationMarkCircle",64165:"CustomCollectionView",64199:"message-store-debug-panel",64457:"icon-clock",64557:"icon-sketch",64632:"icon-eye",65015:"emojiData",65129:"react-pdf",65213:"subscriptionActions",65688:"FormBlock",66042:"mobileNativeFullPageComponents",66073:"MarketplaceEmailCaptureModal",66169:"LazyAiLandingPageExperimentSwitcher",66249:"SidebarBottomActions",66592:"CollectionSettingsCustomizeMenu",66805:"verifyPasskeyRedirectPage",66960:"PageError",67045:"AddPasskeyRedirectPage",67199:"icon-microsoftWord",67601:"icon-basecamp",67920:"TransferWorkspaceModal",68070:"minisearch",68117:"SlackImportsInviteWithModal",68126:"LoginWithAIPrompt",68224:"publicSharingTopbar",68540:"TranscriptionBlockSecondaryUI",68548:"react-day-picker",68642:"icon-loom",68744:"JiraSyncInfoPopup",69095:"formEditorActionButtons",69184:"clipboardActions",69224:"CustomDBPanelEmptyState",69307:"PrivatePageEmptyState",69734:"codeBlockValue",69746:"SidebarWorkspaceDiscoveryButton",69945:"emoji-chinese",70074:"icon-zapier",70216:"@simplewebauthn/browser",70522:"updateSyncedCollection",70593:"LargeSurfaceRenderError",70605:"views-main-builder-modules",70916:"icon-gitLab",70959:"icon-arrowLeftRight",70998:"about-developers-modal",71204:"workflowTemplatesHelpers",71677:"TrialModalsDeps",71768:"icon-onePassword",71830:"icon-templates",72535:"CollectionViewBlockWorkflowControl",72982:"SidebarComponent",73006:"external-object-instance-block-menu",73090:"CollectionSettingsConnectedRelationUpsell",73502:"StudentAiOfferPopup",73801:"serverDuplicate",73865:"icon-airtable",74085:"mobile",74688:"callouts",75152:"icon-arrowUTurnUpLeft",75155:"SalesforceAuthModal",75436:"MidtermCheckoutModal",75528:"wasm-sqlite-worker",75681:"postRender",76378:"icon-bubbleEllipses",76793:"formulaAnalytics",76983:"moveTo",77282:"RequestMembersModal",77355:"icon-instagram",77375:"icon-openAi",77651:"GeneralAccessPermissionSection",77757:"emoji-japanese",77761:"ConfirmHIPAAModal",77957:"icon-oneDrive",77970:"trialActions",78171:"SitesSettingsTab",78217:"mainApp",78389:"icon-gmail",78472:"icon-google",78724:"icon-maps",78984:"assistantWriter",79239:"LocalizedTemplates",79254:"icon-adobeXd",79665:"automations",79883:"CollectionItemCover",80071:"buyerProfile",80931:"icon-tiktok",80983:"MapTile",81074:"imageEdit",81330:"PresentationMode",81638:"icon-emojiFace",81764:"icon-salesforce",81929:"onboardingActions",82028:"PingSalesModal",82094:"SavePageToOPFS",82106:"icon-questionMarkCircle",82120:"PropertyModulePersonProperty",82430:"errorBoundaryDebuggingStore",82926:"AIModePicker",82970:"contactVisitButton",83728:"topLevelMobileComponents",83755:"AdminReactJson",83965:"GuestUpsellModal",84084:"userSignalsHelpers",84217:"icon-trash",84605:"personalSharingComponents",84969:"StudentGroupSignup",86227:"collectionFormsEditor",86279:"LennyApplication",86774:"BlockErrorFallback",87102:"icon-framer",87137:"tabSpacesPopup",87153:"icon-smartSheet",87543:"spreadsheetPrototype",87700:"WorkflowPageBlock",87771:"TrialEndModal",88208:"ConfigureViewsMainModuleAddViewButton",88472:"icon-box",88488:"AliasBlock",88873:"TabletSidebarButton",88895:"AllProjectsTooltip",89041:"teamHome",89253:"icon-evernote",89256:"icon-aiFace",89440:"monaco-editor",89509:"icon-personCropCircleBadgePlus",89920:"transport-support",90068:"AgentChatInput",90518:"InlineRenderError",90871:"duplicateActions",91325:"PublicPagesSection",91795:"CustomizeMenuConnectionsSettings",92143:"WorkflowEditorTopbar",92214:"prism",92222:"RelationPropertyCustomizeLayoutMenu",92579:"WorkflowSettingsViews",92668:"icon-pencilLine",92821:"workflowNavigationHelpers",92903:"slashMenu",93139:"workflowNavigationActions",94153:"BannersRenderer",94211:"GithubAIConnectorAnnouncementModal",94366:"notionCalendarLogos",94412:"LockedSidebarSection",94516:"MinimizableCollectionViewBlockHeaderActions",94611:"WorkflowTemplatesBuilderNotInProd",94802:"icon-docBadgePlus",94869:"GlobalInAppMessageListener",94891:"pageVerification",95264:"emoji-spanish",95281:"SecondarySidebar",95496:"tableSelectionOverlayActions",95699:"FullPageAI",95717:"icon-sliders",95737:"icon-arrowChevronSingleDown",96029:"icon-arrowInCircleDown",96166:"icon-book",96304:"mermaid",96316:"tinymce-word-paste-filter",96431:"workflowTemplateOnboardingPills",96772:"icon-checkmark",96773:"icon-microsoftOneNote",96966:"login",97101:"automationActionRegistry",97134:"PagePreview",97651:"icon-exclamationMarkTriangle",97891:"WorkflowContainerEmptyState",98012:"twitter",98288:"ExternalImportAndSyncIndicator",98396:"PageLayoutModuleErrorFallback",98682:"SearchMiniSearch",98713:"icon-monday",98814:"monaco-editor-react",98906:"DeletePaidWorkspaceModal",98942:"contextualizedOnboardingStageHelpers",99013:"workflowTemplateTourTooltip",99108:"collectionSettings",99171:"selectableBlockActions",99223:"RecordStore",99314:"legacyTransclusionActions",99355:"subscriptionDebugCommands",99426:"JiraAIConnectorAnnouncementModal",99718:"teamspaceMenus",99998:"icon-protonPass"}[e]||e)+"-"+{22:"b2430a1a5e175c33",48:"b3d50d3f260c1ebf",209:"9d20e57bbf063a68",287:"92abb9eef9285550",890:"93239bb1af22ceea",943:"703a2e8100f2635e",1042:"8fdeb91b2752e735",1046:"57871252816c75dc",1117:"b47e605f35a0b9c0",1122:"7281fc3227a9e4a3",1172:"7f70a85b4740a4d6",1423:"3aa7ddb5f2592ff3",1438:"1d50c438178d1f3a",1440:"f1afea4d073d2ad1",1560:"d3db3b07afeb8b34",1615:"d5d2f845c29451dc",1660:"646e5ce32eb1e551",1797:"3d78185367b08f57",1827:"7a890a071150a2fc",1978:"b4d69ed82eae53c3",2040:"2d34b37851c55994",2144:"8c64eb54cc195220",2147:"bc85a08b1b3963c7",2233:"5ed500b788f9fec4",2264:"01d86034a2027518",2268:"73e5bf159667bb10",2278:"ae1fb49dac2d7b3b",2287:"a62fd22d39dd834e",2411:"ad702cfed4816876",2485:"effc5d2bfcfd3cef",2501:"22eb357b99ec5ad1",2517:"ccbc831e30fbf99f",2569:"ec9db4bd80dbdc64",2597:"8958bc9c5e609e33",2611:"b9fba9a19ba2f4a2",2681:"d715e735e24549f0",2686:"7c1af7e136eb22c9",2703:"dab72022b37e14c9",2865:"9e5a1a428fa45141",2922:"4ad18f239030f984",3071:"f6c380119c40a01d",3151:"2c8788d90385e1cb",3184:"0ac3e28a18b5afec",3272:"f930bd919d69aa0b",3476:"f57e4103b438f5f9",3551:"229ca056d6bb3afe",3581:"d938afac2fcb9a1b",3622:"606924f3b1a6554f",3723:"629d17c495af97e0",3773:"d98f0cb107ebac6a",3791:"8a67128119b20bd1",3826:"cde1b70b5fef3c9d",3862:"933f97f99d0de46a",3953:"649f1f7a1045cf3c",3967:"0d306099b73c082f",4115:"b1513fe3f850fd83",4120:"82de7ce789b56499",4249:"51e17a58110c05fd",4289:"b26cc60c6c1ce745",4422:"b5d14402b9270bd0",4586:"aa2de80bcf5ce015",4590:"17e0144142bc5ca1",4643:"ce198765a9d0ed95",4694:"244ae3f8bec340ad",4750:"792a63f8c564e220",4876:"d2af6d811a68f701",4902:"d89669b43ea85d76",4964:"030f43897a5ba43f",5053:"ad3f3d6c6616e903",5130:"5a715a8b075d897f",5140:"4a81563a369383fd",5287:"16eb25a2e4d66bb4",5605:"e18b89e964dc6d30",5815:"42bae5602b594072",5821:"2cd57d72be832ecb",5834:"d2eecba939880ea4",5857:"d9ddcddf41b6290c",5938:"2e57bb5f5d8afafc",5999:"34c571d8c51e9414",6040:"587a0602a9181a40",6122:"fb9bb9b6d819fb29",6178:"22173e075950020b",6298:"ae09d1f27c742492",6452:"14967781fda795a7",6463:"8ffd9edc76a6dcf5",6637:"334c5b6c486ab56c",6893:"5e66fddb76790b25",6907:"2c049a46cb32967d",6960:"dcbb4bcb130070bf",6993:"c90196e36657b256",7091:"83bcb673ce290246",7097:"2516f0c62fdd196e",7103:"d68440ce64320081",7391:"00d762e4219722a3",7439:"8c905fd1aa159188",7457:"7151173d59f755df",7520:"3e13cd81dffe517e",7717:"127d9cf300aae09c",7759:"b0250318c9617569",7972:"b6cbbd49929c5630",7989:"04edb67c8f16240c",8142:"ad34c39dc56761bb",8243:"1dbf37c5656e1dd8",8247:"4417a5abd9a6c85a",8340:"01182982b1b5c656",8360:"cb1a88d8dc1df715",8418:"f4fffa66f8c7a39a",8461:"6ec7c6ce6e608fda",8589:"c7e1550dac92a597",8592:"90e8ddc57505478b",8675:"32cc098be4e99d49",8841:"6766bd1d3e4467ec",8859:"8e387eb4606fa0b6",8881:"76bf4d1d2205e06a",8953:"6093d7ef3e0b08b7",9066:"4d0c26368b7b5f50",9150:"c1f444520fb43fc3",9304:"8da75c928eefb0b5",9375:"9f5d6c477bc222ef",9423:"1da3914cca569dbf",9555:"ea5c03e8e641a5a9",9562:"915864a85aea649e",9647:"01447fe2dee89a0f",9832:"d4dcc734964ff7c4",10143:"44d687ead4dc80d9",10399:"3012dd006bcaed67",10408:"243acd7dc9462cf3",10494:"e7a48a045c042bd3",10583:"21dc2c750c6ffb4f",10809:"debc6d4cba785ceb",10815:"e82907da51cf4a2f",10831:"d8568f39804aad89",10871:"c608c0d819410d2c",10890:"497d56f646e156f1",10968:"8cb102118b9c5a3f",11072:"63d0f6bae65d4bb6",11097:"a23e1f03f72ba474",11126:"d8bfb9a0ec19c8ee",11235:"a224211be84f17c9",11341:"16211a0cdf5d2023",11415:"3f50d4e7367b7ba0",11674:"6a1aff671896d132",11721:"ebce55f89b1b37b2",11888:"eab84b4ce5b5219b",11922:"055ef0df2bbfef3c",11939:"980b87bb4fd46ad6",12039:"7435173b26e39ddb",12062:"5f1a42498e0ee62e",12071:"a248a850cf09c2c6",12119:"e870b55e7f81e0f8",12244:"7522a8bfbb212ce6",12437:"7565bce14ceeee8e",12564:"d4890063901ad50a",12720:"7df358ae8413318f",12978:"8c29fec9515945b0",13017:"78fdae69a5263826",13208:"445960192b0644da",13319:"80118cf4678d0c68",13326:"d2aa534d37d3cc1d",13402:"de3fb13f9755e5c4",13450:"58dc9c8ec3576cbe",13485:"fce5d55caf4df590",13569:"6a0ef2f0e831480c",13677:"2faed5f643369a79",13726:"cf736f0131fb023e",13788:"6fdcf3ddea33db41",14068:"4b2b6b0d3ad8992c",14079:"864e8f34af8f12a6",14201:"14dbe10184e1a2cf",14220:"a5435ef9d56246e2",14227:"2d4cdb5247981ac0",14246:"2b990a0a76e048af",14310:"5263879644cf2bc9",14329:"69802123dcb42208",14344:"6361d649666c9420",14358:"29735479ec8d1954",14491:"0e2f2d0cba83881b",14586:"4da056cad0821e06",14658:"0349e25940e079ec",14897:"8d73670396c2afbc",15135:"11f826787580dfaf",15399:"fe8f487e146cbbd4",15498:"46e1c29642f28da3",15529:"b5dfa51d13119de7",15538:"43aab666d7de6ce8",15566:"44da446def4673c5",15662:"7103defeb52e11aa",15780:"1d1a45f6f8ced26a",15830:"69199099df8fe13e",15891:"735d2b58a4ec074d",15961:"939f6906a05805d7",16006:"b20da619de495f31",16079:"08dfc83a79c99f01",16130:"f32cf57355f801f6",16134:"8323a7afc6a6750c",16345:"493ad1bd8612d37c",16443:"a2f01777f2fa0642",16453:"94cff9012fe35290",16471:"2ca78723661bf4a6",16697:"4bac076511aa2ac5",16708:"74d848a21671bf3e",16742:"414d3374b672bf02",16939:"dc4d4fbe1a283313",16999:"1f9af7da749fd490",17217:"9bba5f6ea6ad9308",17254:"89368fea6ff6f4b1",17546:"393a3cc5ee98c5f1",17586:"0ec2bae2e24c7e5b",17598:"c3ea47d9c549ced3",17603:"769ab6818bf3c834",18051:"4df174b210a35017",18071:"8ca9f90b15629abf",18183:"69964ddf4cf10d2e",18331:"1a2b3290c6575005",18710:"88b9c576d7a11871",18724:"840fc12820752a90",18766:"3b25bfec786fc1d6",18959:"c8568ea3b7714d10",19131:"5d4b0a8d4258bcf3",19227:"b489ad5e29abd7fd",19314:"05733c66ac039480",19356:"11a85c43a5c6c4a7",19596:"b5b1b797181b7d41",19617:"747c82e86581f144",19632:"22da1a049b6ba167",19801:"cba01b2a10d19237",19860:"8aeedfa36baa277c",19884:"093c4c90dd65ac5d",19980:"13a8c5ea3680573b",20108:"80f062b4909564d6",20148:"a7e1e9d111c3c126",20283:"7b32ed935cbd50a8",20488:"89d4cddfab7e3ab7",20512:"dae022a997cd1957",20589:"dd66bae8e498dac5",20873:"6d12ff8fb3ea6060",21042:"09f1ecd88405a258",21088:"f5e38bbbed503a4a",21118:"d878e09dcae4e881",21377:"b1f4b926e5392b82",21379:"b41aba3e6bc310df",21420:"1403ef34f10cccc7",21446:"11ca56afc74efb47",21491:"1561a9d6bbc389a5",21610:"d92c4a55a44e12be",21697:"53cf8a4a202ff695",21871:"7bad23ebd02dfdfc",21914:"d8de140248ec64e8",21932:"409b79e425ac8f98",22022:"b4548e35007c590a",22094:"0120fe7319ff2231",22114:"7327a228404d1525",22130:"b8fc5031e6b4c707",22542:"aa0f8a4e8a1f2711",22586:"731fa142730d2772",22844:"57a438a0df69c2f4",22900:"4faf6c932d9bd461",22903:"6ba5d69b0650427e",22931:"69e73665ebb36001",22970:"59fac9f567a4bfeb",23133:"648c8210ba09f938",23202:"70f5da0351c2032c",23754:"5d12b1ea1aa2b67d",23873:"2bdfb7fc6186b570",23904:"af5e3f0ab0662ac1",23930:"a545b3f41415c22e",24022:"6e565fe9b5eca107",24515:"81cb7a8aa2558a26",24587:"12bff5dad26c84bd",24598:"d83656e2c54582a1",24630:"c314553797409b13",24698:"afe90cb2dd21d57a",24708:"26df5ceb97905aa8",24722:"22d3f7a6f17c03e4",24735:"b9e1224ccf6a51e9",25049:"3a378c6f5d073963",25065:"8a712154d559e957",25171:"0597ff6b9d054cf4",25196:"87eb340028008ef6",25364:"c11ebca72b71bf33",25406:"4f48d090c7795a5c",25409:"4776a7f1405291ee",25556:"8402fd37622a9e74",25583:"a94cc26ed6054311",25618:"dd95391ba22c0be4",25647:"ac82d16915ce8eca",25700:"cadd9b064ab29f88",25783:"a01a5a5afe1b793f",25786:"424cc02322dfea45",25891:"22a5c2324c870a99",25966:"b23cc20ce12e284d",26018:"7b111dba61113cd3",26180:"cfc4cc8f67d59409",26207:"4049419e72aaba60",26346:"4b2125948ea25561",26357:"d0b52b7b80ebd0b2",26403:"0639cf10274efd7e",26414:"7de29998b6b2c7c0",26483:"ae822ef9ab5c108c",26545:"0990ce473c33d361",26590:"81a90aed52b7820a",26845:"494f96befe1d7102",26990:"cd0dc6df5b540d32",27056:"16aff484a476cfa3",27229:"dc59815a579123ab",27315:"a9f954b80c129da1",27506:"fa11cb6f36d925ad",27616:"fe72cad3c6bf53ef",27634:"fa9abec011be461f",27711:"ed65f83a20396847",27761:"e0e6de084d2bc770",27942:"ac3476a598b0e8b2",27991:"56d13fae3685f29e",28137:"70505080613414a3",28184:"2e2a9051166f51f1",28230:"f3ce45ae8ac1a53d",28271:"144d5b4e96b9afc3",28307:"2f9c79111d30ccf1",28366:"97c9a06a10f6e167",28387:"3e8b9e610e359b23",28464:"e6d0153b943b9d0d",28566:"1bd0ff3ebdb178a9",28721:"66b98ac20ebc1887",28763:"ba21fdbde4ce1c11",28958:"583f5b924a57c831",29021:"b6b7a0d39b599a10",29151:"bb0bce8f852da946",29252:"950bf5121823915e",29288:"a00764aa71f30766",29348:"d46b5ea62aa7e2f7",29366:"377e28d9287e8779",29390:"3c953c883a909e27",29422:"b4f98f6163cc7996",29459:"ae07fe7f5fa7c71d",29485:"d5703e70c94f71a5",29510:"3f8335f31330ee1d",29750:"4be693dad7b33fd9",29815:"d5f3e48dfbcec13d",29875:"e3553b13a51a60a5",29945:"6fd99a26abea9eb3",29995:"e5fb9fe34759e76e",30015:"7c52c031247611b0",30079:"05d52ed9547da906",30189:"4c04c0feaee9017c",30214:"37ad0bdc0de62685",30260:"655a8c60b589cdb0",30322:"4b3f148040901152",30431:"ca84fd2378637a57",30512:"bfd1265c735456f1",30603:"b83e9a0b56164e39",30720:"79003469425461da",30737:"4f252cf4dd6ac22e",30753:"d57ea658af1aa340",30848:"0052fc082b872a51",30873:"5b1ba9152772d17c",30970:"c37048bf1a1739e1",31003:"8a78ca1339919e95",31061:"6f1c46ef15ded26f",31131:"237f2e2bef5c1ce4",31276:"71c85ddb44af77d4",31331:"2f816f4a30a0445f",31343:"872c075d8f229f06",31388:"c16e646a55a857ed",31414:"8c87ed39d44fc1d9",31517:"3ff46cc824476390",31743:"469bfeadd469b3f2",31857:"bb8285506800cd9b",32148:"6b8d408482aff1c5",32179:"d711d099e7312778",32212:"dd2a60ccefa47d36",32221:"62f14c5353806cbe",32257:"3b884a9854b32d64",32296:"1c511ff7eed2a40b",32413:"b333e7eff1a4e6de",32568:"6f1752a482980176",32594:"885bfa9e25fbe366",32644:"cd8c26234a4b87cc",32680:"5738deeabb1df87c",32686:"c7219aff4e87a2e6",32715:"de5a60cfa2062ad1",32769:"9d9d37482419b963",33004:"626dfd52d54841a0",33081:"7b811b9124afff40",33110:"659eb467660abf99",33112:"e3f18d88f023f9a4",33168:"6edeef9e67696e46",33194:"b5bf083fdbbe9f0b",33277:"bcc8fb5b04486837",33325:"9d0f261ce6300af5",33429:"2434f8d271115bb1",33438:"4ee7e326f23832d3",33590:"184edd111367bfc8",33616:"b911a319d502cb25",33931:"dde902d8c900f030",34208:"ad702cfed4816876",34251:"2cebf10ba3c3d74b",34273:"9e7572951bdec148",34359:"0617244ff8f5f325",34420:"652f6844bf117ffa",34526:"4810a8e4d8932d4c",34616:"cf7de4dfacd1f1bd",34622:"5bdb85e256b6436d",34709:"706d1f03b569c1ed",34877:"77ec3a1433966308",34958:"cfd74213cfffc150",35014:"6f3188ef823268ef",35115:"641d10d5da0b94ec",35189:"7415095005e6dc8f",35266:"fc31b2bda7b57225",35364:"c0dfb5d9b6f5691e",35390:"49684c2568f6b572",35503:"03e9607c316a0d80",35537:"e5ba5530e19eb622",35578:"96f8eacc68111676",35599:"30d1b3534b181f3e",35658:"2ef8cbfabc9b24dc",35733:"166b6cde66a97dd6",35756:"ce7745f36299aae2",35818:"2cb7ee00539b0b2e",35837:"de52cc7e7c0322b5",35839:"70b788765cd9451e",35905:"1bbb8619a1e06bc7",35986:"500cc417f0025765",36193:"f5a8bb44497308e2",36264:"f94373c1024fe0f6",36417:"58e2b4ce9b6f470d",36425:"c52dff7d19308e24",36432:"fe9e82d833291197",36448:"ad39910c32d327a4",36733:"069f039d90d481f2",37043:"7100f8c4c578637b",37045:"0ab6fa6d9fb19bac",37062:"adb5b154b9879a27",37125:"676c9e52eb5ce2dc",37217:"eef94fbca4ccd46d",37303:"42ac1919d34907e0",37353:"cd2e7462a4f61dcc",37398:"876b780e2f1d0514",37565:"f527056a8a9895cf",37636:"83311b0ab995392f",37763:"bc97f1900449e83b",37972:"bd2ba8359786520d",38334:"bf3304579d6b974e",38405:"4961cd7214de5a98",38441:"75fb3a39fcbe67ea",38949:"fafec74448ce5855",39047:"bc4c2f155e44448d",39130:"0902e1c90bcdc37b",39301:"011d5132474cb3cf",39689:"56af75f696ae3251",39720:"e57f99ee90f422ba",39778:"2279c4127bc20587",39899:"b5235e1d1f07755e",39953:"eba0f85117c92b83",39958:"3060f286ff7b076c",40186:"7c300e4723c6897e",40198:"bb2b5b607841a643",40200:"ca56ec86ef91a8b1",40418:"3c56eaef4c313192",40432:"72c057d06744f273",40471:"848851574157b3d7",40502:"60b6359aff002e25",40589:"389591f43f9f0e52",40642:"5635c26544c15674",40766:"cddcc52fb4d0dd5c",40902:"f4b75088808c3e25",41091:"385016311c83b8df",41135:"fbc401844b12d7a0",41201:"5e4c5afd02039639",41265:"31fbdcbb2f7bb3e7",41412:"97269ae57b2199a4",41651:"15e55c33ddd42d9d",41670:"c88aa789fdaa6ba9",41720:"31332ec2588e5864",41740:"d3b8ae0e08ec94aa",41805:"972c0bea4fa4b3e2",41835:"3833c2793cf364a5",41837:"e5a5ed53c0c6ed98",42050:"a4979fff388d1c59",42074:"82847809ce2a74d9",42315:"bc2bb073c768d812",42399:"75ba0b1100524996",42440:"c6aec7b17292ba82",42481:"377548d849d29a7e",42668:"057ea70144c8d8c2",42683:"b30aeddb0a7b19b1",42690:"9d6110916ec9c78e",42704:"254ae5e6727167de",42838:"43b7a690c7a3e8a3",42951:"75abbb9c21b7f7dd",42962:"c1747c128e813044",42975:"9a0e00c182dd5490",43151:"de7430fe8c105d0a",43182:"b071f6db81e5a475",43249:"f8b156ee7569a710",43274:"d116862691025aa9",43396:"7a4bd447aec32f75",43459:"83436b4de366c542",43741:"6222a736882c2b20",43774:"aaa98de5eb6cf7a3",43862:"00a40eb48ca4ed25",43895:"644dab6e446c5544",43935:"276d35971d065604",43953:"dd1a64260b852e2d",43977:"a41ad901a738d7d3",44032:"98f296e5b851a3b3",44084:"c309ca8fdd48def6",44158:"76694d137e8cbf12",44225:"a606830607253252",44425:"f2ebbc4b9514ef10",44539:"2c406874ec7c60b5",44632:"8d82ee025225f50c",44680:"b712da883550e3ed",44711:"a182f1f74692f3d0",44745:"809addac2e1028d1",44763:"c9918c8942d272cf",44802:"383b9e75f01390de",44839:"78643d5a8532a8e3",44852:"74bee8b4c3ef888f",44962:"21118b3bb3fc0211",45016:"e8b264b459c9b905",45196:"48394f9ca7eec2a1",45248:"bcf828736911e442",45612:"dc4bfccfef7dd8a9",45620:"340a1eccbe3a94e8",45624:"fea59a75ebe79049",45666:"aa037f4d3ea6c57f",45758:"d9826ffa677f8ca5",45816:"765d4d412eb14060",45915:"eb1bd6c293971cda",46014:"4349b4abda326b79",46030:"bb87099d10b48869",46204:"48e021dfa199daa9",46283:"325c53ae7b214c01",46292:"4184b82798dd911b",46357:"63cb1e18654b7053",46414:"a4146df9998e9999",46418:"246adb6a2635fd8e",46726:"50123a0124c1e740",46827:"818be25add834a51",46913:"a4c0fa98cade5736",46933:"8378dd7e4fef2f60",46990:"26bcab70fe71ed59",47057:"6a9a5cc69f297a28",47108:"e9dc81683123ed5f",47161:"47411c8caf9a723c",47307:"c0337b27f3c228d7",47311:"cd945d2c79af25c0",47381:"6aa53d695755bfa2",47536:"2f31121231486a73",47674:"a1ee6299216c7d95",47779:"c5252a7d0a7a3bbc",47797:"cd0b8a707fe3616b",47934:"d239295e66b25185",48362:"ce80e064c11d724c",48486:"3c6b9ff86f4ced4e",48591:"9fcda53e3e1e6b71",48632:"89e8486f4bfd0548",48760:"ea5ba158ba554046",48861:"93b7cf90d3b9da6a",49044:"8285c9331efed60e",49102:"87d62d86afba9402",49121:"206af9c369224b42",49172:"86567f4f5e7f09e7",49175:"f59d2702e6673e76",49178:"81928b5f919b661c",49189:"3f8321b43e25e7e8",49222:"ba27d318ffecfc6e",49229:"e159e94f70e15553",49291:"f29cf0abb9d65799",49353:"dc04e3827ccb2b85",49373:"3d244cbb7ea100dd",49761:"445c767d0b7d8bed",49889:"42fda699803f7de7",49908:"d1127d349ffd51f9",49951:"fa2be158429b79bc",50086:"f83feda30c931fe4",50150:"92159cce3ddc52cd",50305:"644e9062777085e6",50335:"833ed771425ae054",50437:"9cf87b0fc5dde94c",50462:"b05dd92049a4bf2e",50568:"13295067afa4afec",50708:"b127d52e0e4d92f8",50710:"78f8e709ed3c6db6",51106:"8a9107fe06e65f88",51127:"5f35da1ee796e9df",51197:"14572fd08a48dcb9",51226:"1faf5c0f2739f0c5",51252:"a69281b6a6fa859b",51363:"b4f5cfd3ec1f8ba3",51466:"969624c54636f7d1",51609:"9fa3e799c0b1175f",51646:"18b4f8b4693e6b43",51680:"a475f960c8e1ad5a",51855:"915406104c56fb28",51859:"72d99c386375c9c0",51872:"30a7bd650f2c0fe0",52009:"5e244a101edcd5df",52056:"558d1e49bf9478d6",52084:"87d2484fd132ddd3",52274:"96ac385dc3e86974",52491:"15c66947000707ca",52614:"85ddd2aadef42e8d",52745:"56a2e4b09eba7754",52840:"618cc208d9c32479",52903:"ca4d7bfb8fdf34d3",53062:"ce82b2bf44c3c49e",53071:"d14a0543781e88d6",53095:"870bb8a2d811fc62",53147:"c57971a8e78d3f22",53179:"d7aeebe93476c682",53223:"7210fa26628d9545",53237:"d8641f16bfa1831c",53321:"3f9ec927c1551d27",53463:"001181fac41a882b",53491:"a565dd75c9211249",53631:"8440aee0af55dcdb",53638:"85eb04d075ef6a07",53928:"1f5343d3425b0524",53974:"4f20b1f6ba8f113f",53977:"6b5a20c10091fc8d",54087:"4ab3089d2223eacb",54134:"83b420cf619500a3",54189:"20391c26367e300c",54215:"35059ab09afd126a",54220:"b123765186dccec8",54398:"86236100fe335b94",54518:"3f4acd66b521cf13",54564:"530e88056351d518",54585:"617717563124c854",54759:"1f44b06165dccea9",54928:"5c203597ae574d0b",54980:"7ed0114a63adb7e0",54998:"399a1b9e9483a075",55072:"a8ce2a3d3cea095e",55222:"b21a8224e380b7c7",55373:"95a0f586e07912ea",55435:"c73fc9819afd601f",55570:"a3b570e8925d61d1",55571:"ba2fce1508cc762d",55632:"fdde8a62098f5fcb",55717:"a19d2c82bcd38339",55724:"dc46fc2577bb08c5",55776:"db5c363544892c13",55901:"4535be25ebd06a27",56051:"f75ae48d9fdeb70a",56188:"6f3f4b313b51c022",56197:"899d0241a8caa845",56301:"e94e13a56e00a02b",56302:"7cce2aa38275b5b3",56342:"080d7ed5d8268ef4",56353:"5a709e59843d9251",56388:"3fcb9efd97a7f2a0",56407:"08c363e3442ea7f7",56409:"252e751ab3b35e8c",56580:"b6b21bd7813ac32a",56591:"14de4c1d1f4eeec2",56780:"84fac78e5dc8e52a",56823:"9b7073d6c4f4711b",56859:"a4fd0a1457fad0fd",56940:"dc2855db2c0ab382",56952:"cdadd3609d94b850",56989:"3f6337d59ebe3dcd",57314:"c0693f98ec79a083",57478:"e4361e62f0238463",57507:"ff8e16d53a35d83d",57613:"32d1b7803d2a47fc",57667:"7690ff4ffe5f783a",57723:"afb2ed83f7edaa9b",57732:"e18bcb43060e51bb",57886:"7ddf5176a258d903",58034:"e8b2c3c3a11f3b88",58072:"3109401bd817b51b",58204:"b3043d6391826b09",58251:"8613c7c6db42a916",58319:"110ed7a24b66bb17",58391:"deb859e6cf69b50e",58431:"47485ac648c1261e",58468:"ebd404c0bc7b979d",58519:"f8ab9ea16ee4fc44",58550:"a589398bad966c86",58587:"fdb95f23c9e19ccd",58596:"72096d1e4f73e1bc",58601:"a0116457a00e1884",58657:"dad6b92672554872",58777:"847717510a9c5c0c",58790:"d025c64be6572c6c",58795:"06413a594889e370",58823:"f3170e4ed7f4cd88",59098:"baa10510302cd167",59111:"7db2ee5d5d2a8bda",59287:"2479f841380966a2",59337:"ccc75e6e3593f8e5",59355:"78e973abcbdc23b4",59388:"e14bcbd777cedd60",59419:"2bbf89e22554aef1",59426:"b312ab31fc65ed04",59430:"e1fb6c5886a71fda",59698:"acf775ec6344c4ac",59794:"3d1f9f123f5c1d04",59970:"cf79833a5a0a8313",60219:"4b4da4c2a9a8f96e",60245:"81134138d8fec5f1",60257:"4116b762b8f77b41",60453:"b4df06045e8cc0a8",60475:"cbc74c748cd841b5",60677:"95bdb0f52d6bb266",60747:"59121c7bb357f816",60858:"b1702205b09b58ea",60896:"c454dfa189b5a264",61177:"e134734b3139f4e8",61227:"02bc9875fcb37dd1",61242:"610017455b4c6728",61282:"5265e4c8a4cbf1ac",61299:"106c4dfe6afe87a4",61362:"32f00411d1acb4e9",61466:"7bd8cf5fab6a9566",61487:"e55e057352983050",61488:"8a41cbbed6952b6a",61492:"f7ba2157bf3bae21",61560:"6c3913d13ecf321e",61604:"de99113299e26b09",61660:"b5f3d42ac4917e25",61754:"a27e325479803c82",61843:"b1d30d6d39166312",61967:"d80256258226c436",61969:"3a4f2064fbcabcd3",62033:"13e6bfa2513630a2",62058:"57ff833ca8ef0623",62153:"59bd960672554a65",62207:"3c33c478618b7369",62230:"ec36dfea2edcfe73",62334:"9c4f72786f280bb9",62342:"069ce2677bfb74ea",62382:"678d0ee0e663a97c",62396:"da6793805381296b",62398:"253c73ae53eb349b",62475:"1219386ef57c4bfd",62516:"bddceb00d0f51105",62620:"7d9815f47739d7af",62623:"cb31fd0281db07ff",62850:"c2b271d1189235ef",62989:"a5d0788013e1521b",63137:"ee4b0e9adcc01cb9",63538:"c7eed9f7524ecd9f",63539:"b0c8cc9da0d10eb0",63882:"4ba30fc31445bac2",63990:"1f40462b62688c8c",64038:"d6357b80753f2b59",64070:"f9e93b1220a7b15b",64087:"d6103bc5b7e80987",64114:"2a90eb9d15151290",64165:"ebd4b9d69588d402",64199:"93dd22a17dcc6fa3",64253:"b6f42fb375199f9e",64387:"74cac7a7c10b5ce8",64457:"ff662600454f9d36",64477:"a55b5ba0ef32a541",64557:"f244d641d6f65174",64583:"b73d9424af6dcaae",64632:"111328672de64ed2",64888:"93c6da6ec27c174d",65015:"8316aff829a870b6",65129:"0f79fb46ba5df15c",65213:"1f2019b2227b27a8",65232:"8ec9405c1c993f59",65340:"148b230908ba6cb1",65441:"878458a402751ebe",65487:"e8be206d2d962352",65550:"cacad1d40510df06",65682:"f0e7d380f90bb8d1",65688:"cb7683f632b70f8d",65728:"0a5944101f2ff1f3",65922:"80837abbc2692bbc",66042:"c1995f27021fa956",66073:"da89107af9491f71",66169:"885749355531a375",66230:"780836fb803bf34c",66249:"9a5afe04e2ba7d00",66375:"851f9c82e7191d16",66446:"ffa6db8a070b115c",66573:"46e7708e7b85d5c4",66592:"5bdb40e3e9100afd",66805:"b0711b0c7e5bb0cd",66944:"dd6ce4bcc29acf9b",66960:"0106cdce49e3924e",67045:"35ef1306ca7100ac",67048:"8717cad33e84ef2b",67199:"99795a0a2a6ce3f1",67246:"df710523c0dae4a2",67378:"2a45a384bbbda08b",67435:"98cd2aa0c6f7ad5d",67601:"e36ff29bb76514ff",67608:"3fdac48437a156ee",67853:"e92b365be407f8e1",67876:"43bf81e8220dbc7b",67920:"c26b10c76d393b26",68070:"329d16c6c200b0c4",68117:"a1460fc8d021c868",68126:"5f3241dac387c4bd",68224:"e3b1d692f4ffc8ce",68332:"af37845955384cd4",68410:"684f247f251feb6b",68452:"fc36d666dd65675f",68540:"d84275c596f61ce3",68542:"d3c8f0525e7a1b0d",68548:"e459d3d7ef59e0e8",68563:"299d6c595941301b",68642:"75cd841b48a75abc",68744:"4cd80bfc206f7c29",68748:"b83309835c359c66",68849:"724a0e1972ad72b0",68977:"8816ed3cd6aa3cc0",69038:"841e7c6a3ede7a23",69062:"c8014d7cc2cbe2ff",69095:"a5010cc46f5ee2be",69184:"d82f85a2b36c7b19",69224:"cda899abd2c87cc6",69307:"e76a838dd82fe45e",69355:"20286931cc082ece",69734:"dbdf07bc77e200e4",69746:"e799d7a88632c38b",69849:"eee08e382949851f",69945:"8bcfbd033b7306b2",70074:"96f09ddfda77e55f",70193:"22952757e10b1c26",70198:"e8654ff145cd6084",70216:"879dfe7723cb50a0",70398:"5794c000f9e686a0",70522:"2848d1884575e5fc",70558:"6ce0254170e9f1cf",70593:"96f7fb705a936692",70605:"0db9bec6554246f6",70678:"d6df3b64cb2ebf2d",70728:"deb9586d85d9f571",70757:"1c9661c95b5fe6f6",70854:"a90e16defe81123d",70891:"d7411d39493158f7",70916:"d09aa02ea8929b1c",70959:"626bce6db34428e9",70998:"50c55eb1f37bc997",71104:"985f416c5a306026",71204:"19f4e48ccc0ba13c",71229:"75f8f1d063524e37",71300:"d139279c457b974a",71677:"4a93195226d61c00",71718:"32cea6d3eb4777bc",71768:"bb49329135158bed",71805:"e1a248cf52e4d3a7",71830:"26c0f46e01e1e913",72077:"f485a33c900c9979",72126:"a13ec87c013a09e3",72462:"d4a1672d9ff30ad7",72535:"de317a5f4682a24f",72612:"b7392b656337e158",72791:"5832876f9321a9e8",72883:"b9afb44c53354bac",72934:"8b5368af93a34d91",72982:"c774a91e118de7ce",73006:"b0cc8d421266a893",73090:"7d02feb518b57347",73109:"1f7617c4d7b85412",73502:"66bbd620c0e1f021",73585:"1be77e23887742a4",73604:"8036edc5ed84ad63",73664:"846377d30bade8c1",73718:"de24f32dbe60857e",73801:"706304f2a3d33557",73865:"34e95b92ea520540",73891:"1f2a7a4686ae5b6f",74015:"b2f1ebc142061ca4",74085:"a871c7bafad1ee65",74165:"46b77bc42546e9a3",74206:"1a21afc52aaef57c",74421:"85aac10393f73386",74562:"5196ced3dbf10217",74683:"c90e0f21e1fe1881",74688:"e23b5139ce9408d6",74728:"7dad028ce8cb3c51",74782:"64d62fc3f44ea33b",74911:"365fe7e7d4abf98b",75152:"ff20f67965a1c9bb",75155:"2146a5056643a858",75436:"5ae973878bc4220b",75528:"0acc5da08b9b029c",75681:"5ebd995f89d4762c",75831:"d78934005d47e455",76378:"877e89c704db1a31",76537:"8ce4f3738defb181",76630:"54b2db34800c533c",76690:"36c921670c38fb0d",76793:"06e2562171a65faf",76959:"cb8b19a23a35428b",76983:"b81c59c30236cf32",77177:"30ddc50c880f6ae0",77200:"ae2881fb8f3519ce",77258:"59d2b5619994a914",77282:"2931c0dd072011e4",77355:"862df6b6e1ae4991",77375:"9dba781129173b98",77466:"82a002cd71c7deb0",77651:"61f3e5e2e2c97b32",77704:"b75392631c64bd7a",77742:"60e784614316cebc",77757:"3be41642a7e232b3",77761:"df679e3a21c6aaad",77848:"342cba32b2623455",77957:"5ba4fa31300b1b02",77970:"866a301682d90e8c",78113:"f0cd2c52bd18e90b",78171:"c271127fcded1d06",78191:"31631abb69cc97f8",78205:"e8baace0a24bb2c0",78217:"80a82a912aab64cf",78369:"0363bc25dfe5cb3a",78389:"2397c66a1876de34",78472:"3eb45dd9326d6d13",78494:"fd3477a6e08e595b",78596:"9dddf5f54904eaaa",78708:"1f9d475f0b2ab4ee",78724:"fcd97ef64556a167",78731:"fc67431a4bce8f61",78862:"610e6f5cdc1da3c7",78902:"47eb0470dd55f63f",78984:"cd944d3c58283bbe",79239:"ca7eeac686518a19",79254:"890ed7ba14359ff8",79311:"f77ef253017691fa",79344:"b7f0a25b37aeea5c",79665:"fb029e5e27a1e4c0",79883:"193c2baa2dd1b693",80071:"c44f4a3318463280",80720:"becaa14928897bd6",80789:"a434ba548f6100bf",80931:"cfdf31d3d8835c4f",80946:"605191e200b1e2e9",80983:"e2e22da4ec9d7555",81074:"fcb8de63352133a9",81239:"0328fda4e9f4970e",81328:"d89f743850c29a63",81330:"170d7a8b5e47c5c7",81374:"a814dabbe8de8046",81438:"6714d6fbc3c879f3",81638:"9705b1f5052f5142",81750:"b2f1a7d0708c0a31",81764:"c9b0ff605ea34196",81768:"9165f74ca8b68356",81917:"f4170e1bd0b7e52a",81929:"69b20c3a10750b84",81954:"cbe5f37a445563b2",82028:"671a4dd48a268368",82094:"c695b3a0be89bd55",82106:"32e6abc3b7c05b8e",82116:"9e7ebc7321389b89",82120:"932e04e611f840e0",82237:"c350f77b796de390",82387:"48cb7284d495044d",82430:"2ec30096624e2980",82575:"3a3c117c04b17e9b",82619:"c1b1edffa21e71bd",82692:"02602f2a3a7911f3",82720:"54b456a76d290a97",82779:"05170c6e44b0d81a",82886:"962dc776d991c0a0",82926:"6aa99f0c7c8a7240",82970:"b13756cf43ab13c2",82985:"10f65d4773b29a42",83042:"9934bb06500aba69",83096:"7dcc37a171a29206",83295:"f79c1019ca190a86",83633:"dfe372d4507cbb1c",83728:"4912fb63699d8557",83750:"50bcb2a68cb317b5",83755:"b24e26775dd3e907",83965:"42e1455afd10c2c8",84084:"65cb461fb58b7b7c",84164:"f821e4446b46c59c",84217:"1f38b9b865c11348",84341:"42ba1f6c1a553498",84360:"63ba5d67c3d24d6f",84535:"65f3fcd319792c7e",84571:"1c93a86e2a2a5aa8",84605:"13035e7cf5e66fcc",84969:"cc28aa18e6432ffe",84977:"aa4bcb9592ddc82a",85128:"45597b7975a68709",85250:"16c77d5c900b0315",85416:"4fa39202d83fa409",85719:"c5e62ec1e3fc5235",86032:"59fe4960e185ff7c",86149:"ae4e67e1f0e6d2b8",86227:"b6b9528cc86405df",86279:"ddcfe6cb1a7700ac",86609:"b2b1e87021ca1c32",86763:"74a9b1ef77aa1012",86774:"83092becbb1d57dc",86830:"9034685c4e749332",86840:"ce2e5e1accea5927",87102:"cbc40b3811e766b5",87137:"b64aae9bf9a16bbb",87153:"97ad8f0993c4ef70",87253:"b81d474af8f401b8",87333:"66e41372a591179d",87344:"fffa79fe4f530ac7",87403:"01cc8cade4610e8b",87543:"de561d77c3c804be",87584:"7bca8fcee94310e2",87598:"eddf1fdc616e87b5",87602:"04e6edede11bc7d2",87661:"919f42ed634a8363",87700:"9eff2fee223e1b82",87771:"72e60b35a6c9fec0",88119:"d0852553b9e49367",88177:"d8bfe0c06acc9d7e",88208:"7b9c95631702cef8",88350:"ce987b7d19392a3b",88372:"6d9abb6cbebfc160",88472:"c3cb360ee57268fc",88488:"7ad4561335fc7189",88566:"70099b383f3838fc",88580:"c3ce39af488f7880",88590:"8f718f7ca95b9325",88865:"23c3743f49c9e8de",88873:"ffb45d9fc33d45df",88895:"bea7964924bd2f80",88931:"fd56f3e9b8520148",88990:"d32dde24d183f02a",89034:"ae7d4e1e037ad42d",89041:"896113ce1c5bfbe0",89150:"565de5f3c42298d0",89253:"03b823581bf6b6ef",89256:"4ec242298987b037",89306:"1095f975c9fdf3e9",89440:"9ff4d3b5608d4410",89491:"2caa0753ffa77691",89509:"9835df0260da4264",89708:"d15dfd3c754f56a0",89735:"e145e440d26b8f7a",89789:"e94c547cea2eb54c",89838:"eb18551a56ad67b3",89920:"9b113a3885c5f7fc",90068:"c742c8061416716e",90165:"234ccd678598ddf0",90501:"b4c53ba7ed595f96",90518:"bb03f046f75d48ef",90625:"251ae7eeadcee801",90825:"1a9fbf5d193e0ecc",90832:"67aeb40194ba39c4",90871:"5ec297288c1bb835",90910:"a593fabfea8e7906",90978:"f044730418ad98be",91062:"84c5b8cdbf589ff1",91067:"b6fa196656abd248",91121:"90857b55cde870ce",91145:"fb8f67818352ae94",91325:"8c8d61e95064a2dc",91710:"db24d97782b1ce0b",91757:"dc08916d164fa1c3",91795:"0853aeb4c0d19708",91898:"8361555004713ee7",91957:"23948465646d47fb",92143:"3cbb74b6a09618a0",92214:"d0915abbd7075921",92222:"c381d340fcdfcf81",92482:"77f6bf58bb80e401",92579:"24b6b801c0b3db72",92586:"2da35ad478964ff9",92658:"0da28f37df354aef",92668:"ccf26343c84c9c3a",92705:"08fa6827582a21d6",92821:"38ea843f5b318874",92845:"ea9521b0846874d3",92903:"8c9f4f4d29146b4c",92924:"682c29b78b0b8e7d",93052:"765fb4f5891914a6",93139:"55fa82173a742e69",93213:"675868b338bcc75d",93231:"a94bdf55989623ad",93282:"66adf0478131225d",93449:"101e9091e570922a",93518:"9888b2f16c993fe4",93534:"699b1b9502da67ba",93552:"4be4bacd1aa132b1",93559:"10572cd1f498947a",93687:"5bc60832700faa79",93862:"23f52cb012deef47",94055:"61f696d167cf6738",94097:"6f2347a1be02329c",94103:"28f97070432aba14",94153:"e426aa9a570c93d1",94211:"2b67a14150e7f142",94256:"857c2bb851b6aaef",94334:"1464f88ab21db39d",94366:"1f1c33693ff99445",94412:"c7f4e191c365a839",94476:"cca81950a9544fed",94516:"813b87eea9835250",94611:"c9a43001a529a27c",94763:"ce4a6be7c6e16fc6",94802:"7bfac5f6ebde806d",94826:"a4029159ba76ca6d",94869:"0163ca0b3c320c97",94891:"96e4b2fdad5564e3",95264:"e88b3e57d47cc05a",95281:"4fdf77da44a547af",95357:"8b136c27e7ccf537",95490:"858b7acd62bf6982",95496:"dca1e258727b5ee4",95541:"255e754dab66d4ec",95555:"62ad75215ef65c73",95600:"6f2deaf3c125f1e6",95605:"067f68a892761ea6",95699:"8bae24d21c759145",95717:"3bef9829dc95e3ec",95737:"24cfba574464be7a",95747:"1fc8802786bd6b96",95794:"3500a76decdc25fe",95803:"435a197b802d6a97",96029:"dae81ef5117bf98e",96166:"604b2a493434e009",96304:"8c3c406a976eccea",96316:"fbf22ee5d79a8df1",96346:"2356828cf0216ae4",96431:"12d6b6d52d822c0e",96642:"5aa0400c1137834c",96772:"5bfb97c534e3f4c2",96773:"e71a1b3d3c19cf6d",96966:"3c16317116522319",97101:"e9ac5f00675e176c",97134:"bcacbb75acca20e7",97308:"d3860d8422b58547",97517:"9995a1bfc895cf56",97535:"ad244a7b03f0dc8e",97651:"f1b681d7878ed69b",97727:"b4bc8fb0d31023dd",97812:"1f1d34d4ff074a15",97836:"0767c72cff2019ba",97891:"163433cc48ee2de0",98012:"b6d990d737c25522",98161:"84678ad534f251b4",98288:"4d2d3430c8321e15",98390:"b2ddbef7be1c471f",98396:"80483ef7af2a7462",98629:"2afe9d1f77708ad5",98659:"afb48bad69f411b6",98682:"7f85f6f4f9905fd8",98713:"cc356168bb27e001",98814:"28f56115b2270417",98873:"25835a4f97d0f96f",98906:"65351cea9eec3cc0",98942:"9619fb2c1a7b0016",99013:"c574717295e3e0fd",99043:"bae6eafd1ef9f140",99065:"1f04991cdbe72f2d",99108:"3bc867faacaa7213",99145:"68457ef58f8c43dd",99149:"140635987268cce8",99171:"7cfefde27d9dda9b",99223:"6074821d9797c8c0",99314:"09baecbc8d534aaf",99334:"9e418a473e15438d",99355:"4c46509a42b4717b",99426:"9726a0140d4eab78",99533:"16291e5bb3e8b800",99718:"79e26faf46baab69",99977:"7fb198715e2c702f",99998:"90be5cd37b1d3919"}[e]+".js"),s.miniCssF=e=>(({209:"TeamJoinLeaveButton",943:"CollectionViewBlock",1797:"consoleHelpers",8243:"AgentModelPicker",8953:"SidebarTrash",19884:"admin",21088:"AuthSyncListener",21871:"CollectionViewSettingsButton",23904:"PostsLaunchModal",24515:"RestrictedCollectionView",28307:"SidebarInboxButton",31061:"sidebar",33590:"PublicPageSidebarContent",35115:"sidebarMobile",40200:"CreatorProfile",40902:"SidebarLibraryButton",41135:"personalHomeTipsHelpers",43396:"posts",44745:"UnlistedCollectionViewMoreButton",44802:"personalHome",46283:"AgentChatView",46990:"BlockPropertyRouter",47108:"AIChatView",51363:"TeamHomeMoreMenu",52274:"UnlistedCollectionViewDismissButton",53977:"OutlinerToggleOpenSetupModalButton",58204:"TipsInAppModal",62516:"RecentsCachingListener",63137:"createPageInTeamSync",65129:"react-pdf",68548:"react-day-picker",69224:"CustomDBPanelEmptyState",72535:"CollectionViewBlockWorkflowControl",72982:"SidebarComponent",78217:"mainApp",79239:"LocalizedTemplates",81074:"imageEdit",82926:"AIModePicker",88873:"TabletSidebarButton",89041:"teamHome",89440:"monaco-editor",90068:"AgentChatInput",94412:"LockedSidebarSection",94611:"WorkflowTemplatesBuilderNotInProd",95699:"FullPageAI"}[e]||e)+"-"+{209:"5f55d314f6df5aa4",943:"5f55d314f6df5aa4",1797:"5f55d314f6df5aa4",6298:"e716dd6115ec2313",8243:"5f55d314f6df5aa4",8953:"5f55d314f6df5aa4",19884:"4c1c9bec6b0f3458",21088:"5f55d314f6df5aa4",21871:"5f55d314f6df5aa4",23904:"5f55d314f6df5aa4",24515:"5f55d314f6df5aa4",28307:"5f55d314f6df5aa4",31061:"5f55d314f6df5aa4",33590:"5f55d314f6df5aa4",35115:"5f55d314f6df5aa4",40200:"971f8c3cefd36d4a",40902:"5f55d314f6df5aa4",41135:"5f55d314f6df5aa4",42399:"bb851e1f054c26d2",43396:"5f55d314f6df5aa4",44745:"5f55d314f6df5aa4",44802:"5f55d314f6df5aa4",46283:"5f55d314f6df5aa4",46990:"5f55d314f6df5aa4",47108:"5f55d314f6df5aa4",49353:"41ece995bf847057",51363:"5f55d314f6df5aa4",52274:"5f55d314f6df5aa4",53977:"5f55d314f6df5aa4",58204:"5f55d314f6df5aa4",62516:"5f55d314f6df5aa4",63137:"5f55d314f6df5aa4",65129:"873fe2f746a92652",68548:"10f53484049bb20e",69224:"5f55d314f6df5aa4",72535:"5f55d314f6df5aa4",72982:"5f55d314f6df5aa4",78217:"5f55d314f6df5aa4",79239:"ff3aeefdbeab2881",81074:"99425a3fac7fea0a",82926:"5f55d314f6df5aa4",88873:"5f55d314f6df5aa4",89041:"5f55d314f6df5aa4",89440:"445e289bcbcea942",90068:"5f55d314f6df5aa4",94412:"5f55d314f6df5aa4",94611:"d2cea7f0edb90424",95699:"5f55d314f6df5aa4"}[e]+".css"),s.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),s.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o={},a="notion-next:",s.l=(e,t,n,r,i)=>{if(o[e])o[e].push(t);else{var c,l;if(void 0!==n)for(var d=document.getElementsByTagName("script"),u=0;u<d.length;u++){var p=d[u];if(p.getAttribute("src")==e||p.getAttribute("data-webpack")==a+n){c=p;break}}c||(l=!0,(c=document.createElement("script")).charset="utf-8",c.timeout=120,s.nc&&c.setAttribute("nonce",s.nc),c.setAttribute("data-webpack",a+n),i&&c.setAttribute("fetchpriority",i),c.src=e),o[e]=[t];var m=(t,n)=>{c.onerror=c.onload=null,clearTimeout(f);var a=o[e];if(delete o[e],c.parentNode&&c.parentNode.removeChild(c),a&&a.forEach((e=>e(n))),t)return t(n)},f=setTimeout(m.bind(null,void 0,{type:"timeout",target:c}),12e4);c.onerror=m.bind(null,c.onerror),c.onload=m.bind(null,c.onload),l&&document.head.appendChild(c)}},s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),s.p="/_assets/",(()=>{if("undefined"!=typeof document){var e=e=>new Promise(((t,n)=>{var o=s.miniCssF(e),a=s.p+o;if(((e,t)=>{for(var n=document.getElementsByTagName("link"),o=0;o<n.length;o++){var a=(i=n[o]).getAttribute("data-href")||i.getAttribute("href");if("stylesheet"===i.rel&&(a===e||a===t))return i}var r=document.getElementsByTagName("style");for(o=0;o<r.length;o++){var i;if((a=(i=r[o]).getAttribute("data-href"))===e||a===t)return i}})(o,a))return t();((e,t,n,o,a)=>{var r=document.createElement("link");r.rel="stylesheet",r.type="text/css",r.onerror=r.onload=n=>{if(r.onerror=r.onload=null,"load"===n.type)o();else{var i=n&&("load"===n.type?"missing":n.type),s=n&&n.target&&n.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+s+")");c.code="CSS_CHUNK_LOAD_FAILED",c.type=i,c.request=s,r.parentNode&&r.parentNode.removeChild(r),a(c)}},r.href=t,n?n.parentNode.insertBefore(r,n.nextSibling):document.head.appendChild(r)})(e,a,null,t,n)})),t={38792:0};s.f.miniCss=(n,o)=>{t[n]?o.push(t[n]):0!==t[n]&&{209:1,943:1,1797:1,6298:1,8243:1,8953:1,19884:1,21088:1,21871:1,23904:1,24515:1,28307:1,31061:1,33590:1,35115:1,40200:1,40902:1,41135:1,42399:1,43396:1,44745:1,44802:1,46283:1,46990:1,47108:1,49353:1,51363:1,52274:1,53977:1,58204:1,62516:1,63137:1,65129:1,68548:1,69224:1,72535:1,72982:1,78217:1,79239:1,81074:1,82926:1,88873:1,89041:1,89440:1,90068:1,94412:1,94611:1,95699:1}[n]&&o.push(t[n]=e(n).then((()=>{t[n]=0}),(e=>{throw delete t[n],e})))}}})(),(()=>{s.b=document.baseURI||self.location.href;var e={38792:0};s.f.j=(t,n,o)=>{var a=s.o(e,t)?e[t]:void 0;if(0!==a)if(a)n.push(a[2]);else{var r=new Promise(((n,o)=>a=e[t]=[n,o]));n.push(a[2]=r);var i=s.p+s.u(t),c=new Error;s.l(i,(n=>{if(s.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var o=n&&("load"===n.type?"missing":n.type),r=n&&n.target&&n.target.src;c.message="Loading chunk "+t+" failed.\n("+o+": "+r+")",c.name="ChunkLoadError",c.type=o,c.request=r,a[1](c)}}),"chunk-"+t,t,o)}},s.O.j=t=>0===e[t];var t=(t,n)=>{var o,a,[r,i,c]=n,l=0;if(r.some((t=>0!==e[t]))){for(o in i)s.o(i,o)&&(s.m[o]=i[o]);if(c)var d=c(s)}for(t&&t(n);l<r.length;l++)a=r[l],s.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return s.O(d)},n=globalThis.webpackChunknotion_next=globalThis.webpackChunknotion_next||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),s.nc=void 0;var c=s.O(void 0,[75134,22235,46474,64027],(()=>s(474618)));c=s.O(c)})();