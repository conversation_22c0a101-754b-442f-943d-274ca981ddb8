# Day-3 NumPy Assignment Solutions
# Section A: Fill in the Blanks
fill_in_the_blanks = {
    1: "NumPy",
    2: "np.array",
    3: "np.zeros",
    4: "np.ones",
    5: ".shape",
    6: ".dtype",
    7: ".reshape",
    8: "np.arange"
}

# Section B: Match the Columns
match_the_columns = {
    9: "b",  # np.zeros((3,2)) -> b. Creates a 3 × 2 array of zeros
    10: "c", # np.ones((2,4)) -> c. Creates a 2 × 4 array of ones
    11: "a", # np.array([1,2,3]) -> a. Converts a Python list to a NumPy array
    12: "d", # arr.shape -> d. Returns the dimensions (rows, cols) of an array
    13: "e"  # arr.dtype -> e. Returns the data type of an array’s elements
}

# Section C: True or False
true_false = {
    1: "T",  # np.arange(0, 10, 2) creates an array [0, 2, 4, 6, 8]
    2: "F",  # np.linspace(0, 1, 5) returns [0., 0.25, 0.5, 0.75, 1.]
    3: "F",  # np.ones((2,3), dtype=int) has float values (it has int values)
    4: "F",  # reshape() to change the total number of elements (cannot change total elements)
    5: "T"   # dtype of [1.0, 2.0] is float64
}

# Section D: Theory Questions
# 1. Purpose of np.array()
section_d_1 = "np.array() is used to create a NumPy array from a Python list or other sequence. It allows efficient numerical operations and is the foundation for all NumPy computations."
# 2. Importance of .dtype
section_d_2 = "Checking an array's .dtype is important because mathematical operations may behave differently depending on the data type (e.g., integer vs. float). It helps prevent unexpected results or errors."
# 3. Using reshape()
section_d_3 = "A real-life case for reshape() is converting a flat list of pixel values from an image into a 2D array (rows x columns) for image processing. For example, reshaping a 1D array of length 784 into a 28x28 array for MNIST digit images."

# Section E: Practical Coding Questions
print("Section E: Practical Coding Questions\n")

# 1. Even Numbers Array
import numpy as np
even_arr = np.arange(2, 21, 2)
print("1. Even numbers from 2 to 20:", even_arr)

# 2. Reshaping an Array
arr = np.array([1, 2, 3, 4, 5, 6])
arr_reshaped = arr.reshape(2, 3)
print("2. Reshaped array (2x3):\n", arr_reshaped)
print("New shape:", arr_reshaped.shape)

# 3. Basic Statistics
data = np.array([5, 10, 15, 20, 25])
print("3a. Sum:", np.sum(data))
print("3b. Mean:", np.mean(data))
print("3c. Max:", np.max(data))
print("3d. Min:", np.min(data))