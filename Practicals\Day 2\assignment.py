# Arithmetic and Comparison Operations Demonstration Program
# Author: <PERSON><PERSON>
# Description: Accepts two integers and demonstrates basic arithmetic and comparison operations


def get_integer(prompt):
    """Prompt the user for an integer input with validation."""
    while True:
        try:
            return int(input(prompt))
        except ValueError:
            print("Invalid input! Please enter an integer.")


def perform_arithmetic_operations(a, b):
    """Perform and display arithmetic operations between two integers."""
    print("\n--- Arithmetic Operations ---")
    print(f"{a} + {b} = {a + b}")
    print(f"{a} - {b} = {a - b}")
    print(f"{a} * {b} = {a * b}")

    try:
        print(f"{a} / {b} = {a / b}")
    except ZeroDivisionError:
        print(f"{a} / {b} = Error! Division by zero.")

    try:
        print(f"{a} // {b} = {a // b}")
    except ZeroDivisionError:
        print(f"{a} // {b} = Error! Division by zero.")

    try:
        print(f"{a} % {b} = {a % b}")
    except ZeroDivisionError:
        print(f"{a} % {b} = Error! Division by zero.")

    print(f"{a} ** {b} = {a ** b}")


def perform_comparison_operations(a, b):
    """Perform and display comparison operations between two integers."""
    print("\n--- Comparison Operations ---")
    print(f"{a} == {b} --> {a == b}")
    print(f"{a} != {b} --> {a != b}")
    print(f"{a} > {b}  --> {a > b}")
    print(f"{a} < {b}  --> {a < b}")
    print(f"{a} >= {b} --> {a >= b}")
    print(f"{a} <= {b} --> {a <= b}")


def main():
    """Main function to execute the program logic."""
    print("Welcome to the Python Arithmetic & Comparison Demo Program!")

    # Get two valid integers from the user
    num1 = get_integer("Enter the first integer: ")
    num2 = get_integer("Enter the second integer: ")

    # Perform operations
    perform_arithmetic_operations(num1, num2)
    perform_comparison_operations(num1, num2)

    print("\nThank you for using the demo program!")


# Entry point of the program
if __name__ == "__main__":
    main()
