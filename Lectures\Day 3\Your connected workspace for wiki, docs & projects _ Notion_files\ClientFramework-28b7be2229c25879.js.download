"use strict";(globalThis.webpackChunknotion_next=globalThis.webpackChunknotion_next||[]).push([[75134],{17022:(e,t,o)=>{o.d(t,{e:()=>r});let n;n=(0,o(624184).zB)(1,o(60053));const r=n.AutoListener},56366:(e,t,o)=>{o.d(t,{$y:()=>u,Fy:()=>c,K8:()=>a,O$:()=>i,r6:()=>r,uB:()=>s});let n;n=(0,o(624184).zB)(1,o(485671));const r=n.useComputedStoreInstance,s=n.useStoreInstance,i=n.useStoreState,a=n.useComputedStore,c=n.useComputedStoreWithValue,u=n.useStore_DEPRECATED},60053:(e,t,o)=>{o.r(t),o.d(t,{AutoListener:()=>i});o(16280),o(898992),o(803949);var n=()=>o(558842),r=()=>o(992202);const s=o(427652).jY;class i{static hasCurrentListener(){return Boolean(i.currentListener)}static logStoreAccess(e,t){const s=this.currentListener;(0,r().logStoreAccess)(e,s??void 0),s?s.logStoreAccess(e):this.ignoreCurrentListener||function(e,t){if(!(0,n().xx)())return;const r=(0,n().lz)(),s=o(496603).o8(t);{const t=`A component read ${e.constructor.name}'s state without subscribing to updates`;(0,o(624919).NK)(t,[[r],["state =",s]]),o(449412).Fg("A component read store state without subscribing to updates",{level:"error",extra:{"notion-component-info":r,"notion-store-name":e.constructor.name}})}}(e,t)}static withListenerIgnored(e){const t=i.currentListener,o=i.ignoreCurrentListener;i.currentListener=null,i.ignoreCurrentListener=!0;try{return e()}finally{i.currentListener=t,i.ignoreCurrentListener=o}}static withLogging(e){i.debug=!0;try{return e()}finally{i.debug=!1}}constructor(e){this.debug=!1,this.listenCycle=0,this.listenerVersionMap=new Map,this.args=void 0,this.isListening=!1,this.lastListener=null,this.listenerInfo=void 0,this.onChange=e=>{(i.debug||this.debug)&&console.warn("AutoListener",this.args.debugName||"unknown",`onChange ${this.listenCycle}:`,e),this.isListening||this.args.onChange(e)},this.args=e,this.listenerInfo=s&&e.debugName?{listenerName:e.debugName,listenerType:e.source??"unknown"}:void 0,this.args.debug&&(this.debug=this.args.debug)}destroy(e){this.listenerVersionMap.forEach(((t,o)=>{this.removeStoreListener(o,e)}))}startListener(e){this.lastListener=i.currentListener,i.currentListener=this,this.isListening=!0,e.incrementCycle&&this.listenCycle++}stopListener(e){var t;const o=null===(t=i.currentListener)||void 0===t?void 0:t.args.debugName;i.currentListener=this.lastListener,this.lastListener=null,this.isListening=!1,e.isEndOfCycle&&this.listenerVersionMap.forEach(((e,t)=>{e<this.listenCycle&&this.removeStoreListener(t,o)}))}logStoreAccess(e){var t;(i.debug||this.debug)&&i.withListenerIgnored((()=>{console.warn("Autolistener",this.args.debugName||"unknown",`logStoreAccess ${this.listenCycle}:`,e)})),this.addStoreListener(e,s?null===(t=i.currentListener)||void 0===t?void 0:t.listenerInfo:void 0),this.listenerVersionMap.set(e,this.listenCycle)}addStoreListener(e,t){(0,r().logListenerAdded)(e,this,6),this.listenerVersionMap.has(e)||(e.addListener(this.onChange,t),this.args.onAddListener&&this.args.onAddListener(e))}removeStoreListener(e,t){this.listenerVersionMap.has(e)&&(e.removeListener(this.onChange,t),(0,r().logListenerRemoved)(e,this),this.listenerVersionMap.delete(e),this.args.onRemoveListener&&this.args.onRemoveListener(e))}}i.debug=!1,i.currentListener=null,i.ignoreCurrentListener=!1},73526:(e,t,o)=>{o.r(t);o(944114);var n=()=>o(466103);(0,n().exposeDebugEnvironmentValue)("toggleKeyboardShortcutStackDebugging",(e=>()=>{const t=!e.KeyboardShortcutStackStore.debugPropagation;e.KeyboardShortcutStackStore.debugPropagation=t,console.log(`Keyboard shortcut stack debugging is now ${t?"enabled":"disabled"}.`)})),(0,n().exposeDebugEnvironmentValue)("logKeyboardShortcutStackState",(e=>()=>{e.KeyboardShortcutStackStore.logDOMNodes()})),(0,n().exposeDebugEnvironmentValue)("logKeyboardShortcutMap",(e=>()=>{const t=e.KeyboardShortcutStackStore.state.stack,n=(0,o(588165).a)(e.device,void 0);console.log("These are the current shortcut keybindings.","All unset and no-op bindings are passed through to the browser,","triggering its default behavior. Other bindings may or","may not trigger default browser behavior.\n\n");const r=Array.from(Object.entries(n));r.sort(((e,t)=>e[0]>t[0]?1:-1));for(const[e,s]of r){const n=e,r=[],i=s[0],a=s.slice(1);let c=!0;for(let e=t.length-1;e>=0;e--){const s=t[e],i=s.shortcuts[n];if(s.enable&&i){0===r.length&&(c=i===o(496603).D_);const e=c?" (no-op)":"";r.push(`${s.debugName}${e}`)}}const u=c?"font-weight: normal; color: gray":"font-weight: normal",l=r.length>0?r[0]:"unset",d=i?[`%c${n} (%c${i}%c): ${l}`,u,c?u:"font-weight: normal; color: blue",u]:[`%c${n}: ${l}`,u];if(a.length||r.length>1){console.groupCollapsed(...d),a.length&&console.log(`  Also triggered by ${a.join(", ")}.`);for(const e of r.slice(1))console.log(`  • Overrides ${e}.`);console.groupEnd()}else console.log(...d)}}))},93583:(e,t,o)=>{o.d(t,{A:()=>r});var n=o(296540);function r(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return(0,n.useCallback)((e=>{for(const o of t)"function"==typeof o?o(e):null!=o&&(o.current=e)}),[t])}},105751:(e,t,o)=>{o.d(t,{f:()=>l,getHtmlStreamQueueEntry:()=>d});o(16280),o(944114);var n=()=>o(149208),r=()=>o(534177);function s(){let e,t;return{promise:new Promise(((o,n)=>{e=o,t=n})),resolve:e,reject:t}}let i=!1;const a={},c={};function u(e,t){if(null!==e){if(a[e])throw new Error(`Duplicate HTML stream entry: ${e}`);a[e]=t,(c[e]??=s()).resolve(t)}else{for(const e of(0,r().uv)(c))c[e].reject(e);i=!0}}function l(e){return m(),a[e]??void 0}function d(e){m();const t=c[e]??=s();return i&&t.reject(e),t.promise}let h=!1;function m(){if(h)return;h=!0;const e=window[n().n];if(!e)throw new Error("HTML stream queue not found");for(let t=0;t<e.length;t+=2)u(e[t],e[t+1]);e.length=0,e.push=u}},110750:(e,t,o)=>{o.d(t,{AE:()=>d,Bd:()=>c,Ee:()=>l,Un:()=>m,WP:()=>u,XB:()=>p,Xz:()=>h});o(944114),o(898992),o(354520);var n=()=>o(857639),r=()=>o(427704),s=()=>o(496603),i=()=>o(939768);const a=["onboarding","root"];class c extends(()=>o(757695))().Store{constructor(){super(...arguments),this.previousState=void 0}getInitialState(){let e;try{if("/nativetab/updates"===window.location.pathname)e="updates";else e="home"}catch{e="home"}return{activeTab:e,modal:[],tabs:{home:{rootPage:{type:"native",id:"home"},pages:[{type:"web",route:{name:"root"},url:"/"}],nativeRootPageOverrideEnabled:!0},posts:{rootPage:{type:"web",route:{name:"posts"},url:r().GJ.posts},pages:[]},search:{rootPage:{type:"native",id:"search"},pages:[],nativeRootPageOverrideEnabled:!0},updates:{rootPage:{type:"web",route:{name:"nativeTab",tab:"updates",spaceId:void 0},url:`${r().GJ.nativeTab}/updates`},pages:[]},addPage:{rootPage:{type:"web",route:{name:"new"},url:r().GJ.newPage},pages:[]},ai:{rootPage:{type:"web",route:{name:"nativeTab",tab:"assistant",spaceId:void 0},url:`${r().GJ.nativeTab}/assistant`},pages:[]}}}}getPreviousState(){return this.previousState}setUp(e){const{nativeInboxEnabled:t}=e;if(t){const e=(0,s().mg)(this.state);e.tabs.updates.rootPage={type:"native",id:"inbox"},e.tabs.updates.nativeRootPageOverrideEnabled=!0,this.setState(e)}}updateWithWebPage(e){var t;const{page:o,action:r,updateMobileTabbedRouterArgs:i}=e,{url:c,route:u}=o,l=(null==i?void 0:i.silenceErrors)||!1;this.debug&&console.info(`TabbedRouterStore.updateWithWebPage ${r} ${u.name} ${c} clearNativeRootPageOverride: ${null==i?void 0:i.clearNativeRootPageOverride}`);const d=(0,s().mg)(this.state),m=d.tabs[d.activeTab];switch(r){case"pop":const e=((null===(t=d.modal)||void 0===t?void 0:t.length)??0)>0;d.modal=[];const o=(0,s().Kl)(m.pages,(e=>this.isWebPageEqual({page:e,otherRoute:u,otherUrl:c})));if(-1!==o)o<m.pages.length-1?m.pages=m.pages.slice(0,o+1):e||n().log({level:"error",from:"TabbedRouterStore",type:"updateWithPage",error:{message:`failed to pop page ${c} because it's the top page in the active tab and we don't have a modal`}});else if("web"===m.rootPage.type&&this.isWebPageEqual({page:m.rootPage,otherRoute:u,otherUrl:c}))m.pages=[];else{if("web"===m.rootPage.type?m.pages=m.pages.slice(0,-1):"native"===m.rootPage.type&&(m.pages.length>=1?m.pages=m.pages.slice(0,-1):m.nativeRootPageOverrideEnabled=!0),l)break;const e=`Unhandled pop - ${c} ${JSON.stringify(u)} is not in pages nor the rootPage`;this.debug&&console.error(`TabbedRouterStore.updateWithWebPage - ${e}`),n().log({level:"error",from:"TabbedRouterStore",type:"updateWithPage",error:{message:e}})}break;case"replace":if(d.modal&&d.modal.length>0){const e=d.tabs[d.activeTab].pages;if(e.length>0&&this.isWebPageEqual({page:e[e.length-1],otherUrl:c,otherRoute:u}))break;d.modal=[],m.nativeRootPageOverrideEnabled?(d.tabs[d.activeTab].pages=[{type:"web",route:u,url:c}],d.tabs[d.activeTab].nativeRootPageOverrideEnabled=void 0):d.tabs[d.activeTab].pages.push({type:"web",route:u,url:c});break}const r=m.rootPage;switch(r.type){case"web":m.pages=[],r.redirectedTo={route:u,url:c};break;case"native":const e=m.pages.length>0?m.pages[0]:void 0;m.pages=e?[{...e,redirectedTo:{route:u,url:c}}]:[{type:"web",route:u,url:c}]}break;case"push":const i=d.tabs[d.activeTab].pages;if(d.modal&&d.modal.length>0){const e=d.modal[d.modal.length-1];if("web"===e.type&&this.isWebPageEqual({page:e,otherUrl:c,otherRoute:u}))break;d.modal=[],n().log({level:"error",from:"TabbedRouterStore",type:"updateWithWebPage",error:{message:`Currently TabbedRouterStore doesn't support multiple pages in the modal yet, so the behavior is to clear the modal and push the page to the active tab's pages. Pushing a new page while a modal is present is unexpected. Page pushed: ${c}, modal page: ${e}`}})}if(i.length>0&&this.isWebPageEqual({page:i[i.length-1],otherUrl:c,otherRoute:u}))break;const h=i.filter((e=>{var t,o;if(null!==(t=e.redirectedTo)&&void 0!==t&&t.route){if(a.includes(null===(o=e.redirectedTo)||void 0===o?void 0:o.route.name))return!1}else if(a.includes(e.route.name))return!1;return!0}));h.push({type:"web",route:u,url:c}),d.tabs[d.activeTab].pages=h}("onboarding"===o.route.name||Boolean(null==i?void 0:i.clearNativeRootPageOverride))&&(m.nativeRootPageOverrideEnabled=void 0);const p=h(this.state);this.setState(d);const g=h(this.state);if(!l&&"replace"!==r&&(0,s().n4)(p,g)){const e=`topPage is the same. Web page ${r} ${u.name} ${c}`;this.debug&&console.error(`TabbedRouterStore.updateWithWebPage - ${e}`),n().log({level:"error",from:"TabbedRouterStore",type:"updateWithWebPage",error:{message:e}})}}updateWithNativePage(e){const{page:t,clearPages:o,navigationAction:n,navigationSource:r}=e,i=(0,s().mg)(this.state);i.navigationAction=n,i.navigationSource=r,i.modal=[];const a=i.tabs[i.activeTab],c=a.rootPage;this.debug&&console.info(`TabbedRouterStore.updateWithNativePage ${t.id}`),"native"===c.type&&c.id===t.id&&(a.nativeRootPageOverrideEnabled=!0,o&&(a.pages=[]),this.setState(i))}canGoBack(){return Boolean(p(this.state))}canGoForward(){return!1}setState(e){(0,s().n4)(this.state,e)||(this.previousState=this.state,this.instanceState=e,this.emit())}isWebPageEqual(e){const{page:t,otherUrl:o,otherRoute:n}=e;if(this.isRouteEqual(t.route,n))return!0;if(l(t)===o)return!0;if(t.redirectedTo){if(this.isRouteEqual(t.redirectedTo.route,n))return!0;if(t.redirectedTo.url===o)return!0}}isRouteEqual(e,t){if(e.name!==t.name)return!1;let o,n;if("nativeTab"===e.name){const o=t;return e.tab===o.tab&&((!o.spaceId||!e.spaceId||o.spaceId===e.spaceId)&&e.tab===o.tab)}return o=e,n=t,(0,s().n4)(o,n)}}function u(e){const t=h(e);let o,n;if("web"===t.type){const{route:e}=d(t);o=e.name,n=e}else o=t.id;const r={tab:e.activeTab,type:t.type,route:{name:o},tabDepth:g(e)};return n&&"blockId"in n&&(r.route.block_id=n.blockId),r}function l(e){let t=arguments.length>2?arguments[2]:void 0;const o=d(e,arguments.length>1&&void 0!==arguments[1]&&arguments[1]),n=i().ZO(o.url),r=t||e.queryParams;return r?i().O$(n,r):n}function d(e){return!(arguments.length>1&&void 0!==arguments[1]&&arguments[1])&&e.redirectedTo?e.redirectedTo:{route:e.route,url:e.url}}function h(e){return m(e).page}function m(e){var t;const o=e.tabs[e.activeTab];return(null===(t=e.modal)||void 0===t?void 0:t.length)?{page:e.modal[e.modal.length-1],type:"page"}:o.pages.length>0?"native"===o.rootPage.type&&o.nativeRootPageOverrideEnabled?{page:o.rootPage,type:"rootPage"}:{page:o.pages[o.pages.length-1],type:"page"}:{page:o.rootPage,type:"rootPage"}}function p(e){const t=e.tabs[e.activeTab];if(!t.nativeRootPageOverrideEnabled||"native"!==t.rootPage.type||0!==(e.modal??[]).length){if(e.modal.length>0){if(e.modal.length>1)return n().log({level:"error",from:"TabbedRouterStore",type:"getPreviousPage",error:{message:"found >1 modals and right now only 1 modal page is supported"}}),e.modal[e.modal.length-2];const t=(0,s().mg)(e);return t.modal=[],h(t)}return t.pages.length>0?t.pages.length>1?t.pages[t.pages.length-2]:t.rootPage:void 0}}function g(e){const t=e.tabs[e.activeTab];return t.nativeRootPageOverrideEnabled?1:1+t.pages.length}},118884:(e,t,o)=>{o.d(t,{X:()=>r});var n=o(296540);function r(e){const t=(0,n.useRef)(e);t.current=e,(0,n.useEffect)((()=>{const e=t.current;return()=>{e()}}),[])}},134134:(e,t,o)=>{o.d(t,{I:()=>n});console.log.bind(console);const n={log:()=>{}}},136590:(e,t,o)=>{o.d(t,{Ag:()=>c,aw:()=>u,yY:()=>a});var n=o(296540),r=()=>o(56366),s=o(474848);const i=(0,o(446943).C)({modernContextDefaultValue:void 0,displayName:"Environment",legacyContextKey:"environment"}),a=i.context,c=(0,n.createContext)(void 0);function u(e){var t;const o=(0,r().O$)(null===(t=e.value)||void 0===t?void 0:t.deviceStore);return(0,s.jsx)(i.Provider,{value:e.value,children:(0,s.jsx)(c.Provider,{value:o,children:e.children})})}c.displayName="DeviceContext"},138418:(e,t,o)=>{o.d(t,{A:()=>n});class n extends(()=>o(757695))().Store{getInitialState(){return{mode:"light"}}}},178624:(e,t,o)=>{o.d(t,{R:()=>r});let n;n=(0,o(624184).zB)(1,o(279106));const r=n.LocalStorageKeyStore},192191:(e,t,o)=>{o.r(t),o.d(t,{LegacyComponentRenderEffect:()=>n});class n extends(()=>o(60053))().AutoListener{constructor(e){const{displayName:t,name:o}=e.constructor;super({debugName:t||o,onChange:e.enqueueForceUpdate,debug:e.debug,source:"component"})}}},199834:(e,t,o)=>{o.d(t,{L:()=>s,m:()=>i});o(944114),o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698),o(898992),o(354520),o(581454);var n=()=>o(624919);const r={subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0};class s{constructor(e){this.node=void 0,this.onMutations=void 0,this.enableLogging=!1,this.unlockTokens=new Set,this.isLocked=!1,this.longRenderWarningTimeout=void 0,this.observer=void 0,this.isObserving=!1,this.queue=[],this.setNode=e=>{this.mutate((()=>{this.node=e||void 0}))},this.unlockForRender=e=>{this.unlockTokens.add(e),this.isLocked&&this.isObserving&&this.enableLogging&&this.startLongRenderWarning(),this.stopObservingAndHandleMutations()},this.onMutations=e.onMutations,this.enableLogging=e.enableLogging,this.observer="undefined"==typeof MutationObserver?void 0:new MutationObserver((e=>{this.queue.push(...this.filterMutationRecords(e)),this.stopObservingAndHandleMutations(),this.startObserving()}))}filterMutationRecords(e){return e.filter((e=>"attributes"!==e.type||e.target!==this.node))}mutate(e){const t={displayName:"DOMLock.mutate()"};try{return this.unlockForRender(t),e()}finally{this.lockAfterRender(t)}}lockAfterRender(e){this.unlockTokens.delete(e),this.isLocked&&0===this.unlockTokens.size&&(this.stopLongRenderWarning(),this.startObserving())}lock(e){this.isLocked=!0,this.lockAfterRender(e)}unlock(e){this.stopLongRenderWarning(),this.isLocked=!1,this.unlockForRender(e)}startObserving(){if(this.isObserving)return;const e=this.node;var t;e&&(null===(t=this.observer)||void 0===t||t.observe(e,r),this.isObserving=!0)}stopObservingAndHandleMutations(){if(this.observer&&this.isObserving){const e=this.queue.concat(this.filterMutationRecords(this.observer.takeRecords()));this.queue.length=0,this.observer.disconnect(),this.isObserving=!1,e.length>0&&this.onMutations(e)}}startLongRenderWarning(){if(this.longRenderWarningTimeout)return;const e=Date.now();this.longRenderWarningTimeout=window.setInterval((()=>{const t=Date.now()-e,r=Array.from(this.unlockTokens).map((e=>e.displayName)).join(", ");(0,n().NK)([`DOMLock: still unlocked after ${t}ms for ${this.unlockTokens.size} components: ${r}`,this]),o(449412).Fg(`DOMLock: Unlocked for a long time! components: ${r}`,{level:"error",extra:{timeUnlocked:t}})}),1e3)}stopLongRenderWarning(){this.longRenderWarningTimeout&&(window.clearInterval(this.longRenderWarningTimeout),this.longRenderWarningTimeout=void 0)}}function i(e,t){const o="See documentation for more details: https://dev.notion.so/notion/About-DOMLock-ContentEditableVoid-and-MaybeContentEditable-184b35e6e67f8092a306e41a781782d6";switch(e.type){case"attributes":if(e.target instanceof Element&&e.attributeName)return t&&(0,n().NK)(["Reverting mutation of attribute",e.attributeName,`from "${e.oldValue}" -> "${e.target.getAttribute(e.attributeName)}"`,"in component",(0,n().Qq)(e.target),e,o]),null===e.oldValue?void e.target.removeAttribute(e.attributeName):void e.target.setAttribute(e.attributeName,e.oldValue);break;case"characterData":return t&&(0,n().NK)(["Reverting mutation of characterData",`"${e.oldValue}" -> "${e.target.textContent}"`,"in component",(0,n().Qq)(e.target),e,o]),void(e.target.textContent=e.oldValue);case"childList":{t&&(0,n().NK)(["Reverting mutation of childList","in component",(0,n().Qq)(e.target),e,o]);let r=0;for(r=e.removedNodes.length-1;r>=0;r--)e.target.insertBefore(e.removedNodes[r],e.nextSibling);for(r=e.addedNodes.length-1;r>=0;r--){const t=e.addedNodes[r];t.parentNode&&t.parentNode.removeChild(t)}return}}console.error("DOMLock: unable to revert mutation",e,o)}},221844:(e,t,o)=>{o.d(t,{A:()=>a,Z:()=>i});o(944114),o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698),o(898992),o(354520);var n=()=>o(452540),r=()=>o(792071),s=()=>o(445120);function i(e){const{pointer:t,userId:o}=e;return`${(0,n().jV)(t)}:${o||""}`}const a=class{get size(){return this._size}constructor(e){this.data=void 0,this.cacheOverrides=void 0,this.cacheUsingThisInstanceAsAnOverride=void 0,this.cacheFallbacks=void 0,this.cachesUsingThisInstanceAsAFallback=void 0,this.appliedTransaction=void 0,this.recordEvents=new(o(752196).A),this.isExpired=!1,this.isFrozen=!1,this._size=0,this.snapshotData=void 0,this.isTemplatePreview=!1,this.isMockTextStore=!1,this.isTemporaryData=void 0,this.onOperationCallback=void 0,this.isSyntheticAssistantData=void 0,this.relatedAssistantSessionId=void 0,this.cacheLogicalTime=0,this.name=void 0;const{data:t,name:n,isTemporaryData:r,onOperationCallback:i,isSyntheticAssistantData:a,relatedAssistantSessionId:c}=e;this.data=t??new(s().b),this._size=this.data.size,this.cacheOverrides=[],this.cacheFallbacks=new Set,this.cachesUsingThisInstanceAsAFallback=new Set,this.appliedTransaction=!1,this.name=n,this.isTemporaryData=r??!1,this.onOperationCallback=i,this.isSyntheticAssistantData=a??!1,this.relatedAssistantSessionId=c}expire(){this.isExpired=!0}freeze(){this.isFrozen=!0}assertUnexpired(){if(this.isExpired)throw new(o(416845).yI4)(`InMemoryRecordCache "${this.name}" is expired! \n If you are using useLocalDraft() this is likely because the localDraftCache Hook did not run its cleanup function.`)}getEntry(e,t){return this.getEntryInternal({key:e,backfillSpaceIdOnPointer:!1,...t})}getEntryWithBackfilledSpaceId(e,t){return this.getEntryInternal({key:e,...t,backfillSpaceIdOnPointer:!0})}getEntryInternal(e){const{key:t,ignoreCache:o,ignoreAllCaches:n,backfillSpaceIdOnPointer:r}=e;if(!n)for(const c of this.cacheOverrides)if(c!==o){const e=c.getEntryInternal({key:t,ignoreCache:this,ignoreAllCaches:void 0,backfillSpaceIdOnPointer:r});if(e)return e}const s=this.data.getValue(t),i=this.data.getRole(t);let a=t.pointer;if(r){const e=this.data.getModelWithBackfilledSpaceId(t);e&&(a=e.pointer)}if(i)return{pointer:a,userId:t.userId,value:{value:s,role:i}};if(!n&&this.cacheUsingThisInstanceAsAnOverride&&this.cacheUsingThisInstanceAsAnOverride!==o)return this.cacheUsingThisInstanceAsAnOverride.getEntryInternal({key:t,ignoreCache:this,ignoreAllCaches:void 0,backfillSpaceIdOnPointer:r});if(!n)for(const c of this.cacheFallbacks)if(c!==o){const e=c.getEntryInternal({key:t,ignoreCache:this,ignoreAllCaches:void 0,backfillSpaceIdOnPointer:r});if(e)return e}}getRecord(e,t){const o=this.getEntry(e,t);if(o)return o.value}getRecordModel(e,t){var o;const n=this.getEntry(e,t);if(null!=n&&null!==(o=n.value)&&void 0!==o&&o.value)return r().Bj6.fromValue(e.pointer.table,n.value.value)}getModelAndRole(e){const t=this.getEntry(e);if(null!=t&&t.value)return{model:r().Bj6.fromValue(e.pointer.table,t.value.value),role:t.value.role}}checkRecordForTemporaryData(e){if(this.isTemporaryData&&this.data.getRole(e))return this;for(const t of this.cacheOverrides){if(!t.isTemporaryData)continue;if(t.getEntry(e,{ignoreCache:this}))return t}for(const t of this.cacheFallbacks){if(!t.isTemporaryData)continue;if(t.getEntry(e,{ignoreCache:this}))return t}}recordHasOverride(e){for(const t of this.cacheOverrides){if(t.getEntry(e,{ignoreCache:this}))return!0}return!1}makeGetRecordValueFn(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return o(813690).xb.fromMonomorphicFunctionUnsafe((o=>{const n=this.getEntry({pointer:o,userId:e},t);if(null!=n&&n.value.value)return n.value.value}))}makeGetRecordModelFn(e){return r().b4_.fromMonomorphicFunctionUnsafe((t=>this.getRecordModel({pointer:t,userId:e})),this.makeGetRecordValueFn(e))}makeGetRecordRoleFn(e){return t=>{const o=this.getRecord({pointer:t,userId:e});if(o&&o.role)return o.role}}getRole(e,t){const o=this.getRecord(e,t);if(o&&o.role)return o.role}hasRecord(e){return void 0!==this.getRole(e)}hasRecordWithVersion(e,t){return this.hasRecord(e)&&this.getVersion(e)===t}getVersion(e,t){const o=this.getRecord(e,t);return o&&o.value&&o.value.version?o.value.version:0}setRecord(e,t){this.assertUnexpired(),this.isFrozen||(this.cacheLogicalTime++,t?(this.hasRecord(e)||this._size++,this.data.setValueAndRole(e,t.value,t.role)):this.deleteRecord(e))}setModelAndRole(e,t,o){this.assertUnexpired(),this.isFrozen||(this.cacheLogicalTime++,this.hasRecord(e)||this._size++,this.data.setModelAndRole(e,t,o))}deleteRecord(e){this.assertUnexpired(),this.isFrozen||(this.cacheLogicalTime++,this.hasRecord(e)&&this._size--,this.data.delete(e))}addCacheOverride(e){this.cacheLogicalTime++,this.cacheOverrides.push(e),e.cacheUsingThisInstanceAsAnOverride=this,e.emitAll()}hasCacheOverride(e){return this.cacheOverrides.includes(e)}removeCacheOverride(e){this.cacheLogicalTime++,this.cacheOverrides=this.cacheOverrides.filter((t=>t!==e)),e.emitAll(),e.cacheUsingThisInstanceAsAnOverride=void 0}addCacheFallback(e){this.cacheLogicalTime++,this.cacheFallbacks.add(e),e.cachesUsingThisInstanceAsAFallback.add(this)}removeCacheFallback(e){this.cacheLogicalTime++,this.cacheFallbacks.delete(e),e.cachesUsingThisInstanceAsAFallback.delete(this)}forEachRecord(e,t,o){for(const{model:n,role:r,userId:s}of this.data)if("none"!==r&&e===s&&n&&t({model:n,role:r}),null!=o&&o.aborted)break}emitAll(){for(const{pointer:e}of this.data)this.emitRecord(e,[])}clearCache(){this.cacheLogicalTime++;const e=[];for(const{pointer:t}of this.data)e.push(t);this.appliedTransaction=!1,this.data=new(s().b);for(const t of e)this.emitRecord(t,[])}addListenerToRecord(e,t){const o=(0,n().jV)(e);this.recordEvents.addListener(o,t)}removeListenerToRecord(e,t){const o=(0,n().jV)(e);this.recordEvents.removeListener(o,t)}emitRecord(e,t){this.cacheLogicalTime++;const o=(0,n().jV)(e);this.recordEvents.emit(o,t),this.cacheUsingThisInstanceAsAnOverride&&this.cacheUsingThisInstanceAsAnOverride.emitRecord(e,t);for(const n of this.cachesUsingThisInstanceAsAFallback)n.emitRecord(e,t)}hasListener(e){var t;const o=(0,n().jV)(e);if(this.recordEvents.listenerCount(o)>0)return!0;if(null!==(t=this.cacheUsingThisInstanceAsAnOverride)&&void 0!==t&&t.hasListener(e))return!0;for(const n of this.cachesUsingThisInstanceAsAFallback)if(n.hasListener(e))return!0;return!1}}},223116:(e,t,o)=>{o.d(t,{$H:()=>r,CX:()=>i,Rb:()=>s,a9:()=>n});const n="adminContentSearchSettings.useContentSearch",r="settingsConsole.singleLegalHoldContentTab.useLegalHoldContent",s="sudoModeActions.privatePageRecordCache",i=[n,s]},225661:(e,t,o)=>{o.d(t,{s:()=>m});o(944114),o(898992),o(354520);var n=o(296540),r=()=>o(949054),s=()=>o(355543),i=o(474848);let a=[];const c=new(o(592328).A);function u(){return a.length>0?a[a.length-1]:null}function l(e){a=a.filter((t=>t!==e)),c.emit(a)}function d(){return document.activeElement instanceof HTMLElement||document.activeElement instanceof SVGElement?document.activeElement:null}let h=0;function m(e){let{children:t,active:o,onActiveChange:m}=e;const p=(0,s().w)((()=>++h)),g=(0,n.useRef)(null),f=(0,n.useRef)(null),[v,b]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{function e(){const e=u()===p;e!==v&&(b(e),null==m||m(e))}return c.addListener(e),()=>{c.removeListener(e)}}),[p,m,v]),(0,n.useEffect)((()=>{function e(){const e=g.current,t=d();e&&t&&!e.contains(t)&&(f.current=t)}function t(){const e=g.current,t=d(),o=e&&e.contains(t),n=t===document.body;f.current&&(o||n)&&f.current.focus({preventScroll:!0}),f.current=null}if(o){n=p,a.includes(n)||(a.push(n),c.emit(a));const o=g.current;let s=null;if(o&&!o.contains(d())){const t=(0,r().Kr)(o);t.length>0?(e(),t[0].focus()):s=function(e,t){const o=new MutationObserver((()=>{const n=(0,r().Kr)(e);n.length>0&&(o.disconnect(),t(n))}));return o.observe(e,{childList:!0,subtree:!0}),()=>{o.disconnect()}}(o,(t=>{e(),t[0].focus()}))}return()=>{var e;null===(e=s)||void 0===e||e(),u()===p&&(l(p),t())}}var n;u()===p&&(l(p),t())}),[o,p]),(0,n.useEffect)((()=>{if(v){function e(e){const t=g.current;if(!v||"Tab"!==e.key||!t)return;const o=document.activeElement;if(!(o instanceof HTMLElement||o instanceof SVGElement))return;const n=(0,r().Kr)(t);if(0===n.length)return;const s=n.indexOf(o);let i=null;-1===s?i=e.shiftKey?n.length-1:0:e.shiftKey&&0===s?i=n.length-1:e.shiftKey||s!==n.length-1||(i=0);const a=null!==i?n[i]:null;a&&(a.focus(),e.preventDefault())}return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}}),[v]),(0,i.jsx)("div",{ref:g,style:{display:"contents"},children:t})}},250454:(e,t,o)=>{o.d(t,{I:()=>n});o(944114);const n=new class{constructor(){this.longEventMetrics=[],this.shouldCollect=!1}setShouldCollect(e){this.shouldCollect=e}getShouldCollect(){return this.shouldCollect}generateUniqueId(){return Math.random().toString(36).substring(2,15)}start(e){const t=e?`rqf.${e}`:"rqf";performance.mark(`${t}.start`)}stop(e){const t=e?`rqf.${e}`:"rqf",o=performance.measure(t,`${t}.start`);this.add(o.duration)}add(e){e>500&&this.longEventMetrics.push({eventName:"render_queue_flush_long",eventProperties:{time:e}})}resetMetrics(){this.longEventMetrics=[]}getLongEventMetrics(){return this.longEventMetrics}}},265515:(e,t,o)=>{o.d(t,{G:()=>s});o(16280);class n extends(()=>o(757695))().Store{constructor(e){super(),this.getValue=e}getState(){return this.instanceState=this.getValue(),super.getState()}setState(){throw new Error("MapKeyStore is read-only")}getInitialState(){}}class r{constructor(e,t){this._stores=void 0,this._map=void 0,this._stores=e(),this._map=e(t)}delete(e){const t=this._map.delete(e);return t&&this.emitKey(e),this.deleteKeyStore(e),t}get(e){return this.getKeyStore(e).getState()}has(e){return this.getKeyStore(e).getState(),this._map.has(e)}set(e,t){const o=!this._map.has(e)||this._map.get(e)!==t;return this._map.set(e,t),o&&this.emitKey(e),this}setUnlessEqual(e,t,o){return!(this._map.has(e)&&o(this._map.get(e),t))&&(this.set(e,t),!0)}getKeyStore(e){let t=this._stores.get(e);return t||(t=new n((()=>this._map.get(e))),this._stores.set(e,t)),t}emitKey(e){const t=this._stores.get(e);null!=t&&t.listenerCount()?t.emit():t&&this._stores.delete(e)}deleteKeyStore(e){const t=this._stores.get(e);return!(!t||0!==t.listenerCount())&&this._stores.delete(e)}}class s extends r{constructor(e){super((e=>new Map(e??[])),e),this._keysStore=new n((()=>this._map.keys())),this[Symbol.toStringTag]="MapStore"}forEach(e,t){for(const[o,n]of this)e.apply(t,[n,o,this])}clear(){if(0===this.size)return;const e=Array.from(this._map.keys());this._map.clear();for(const t of e)this.emitKey(t);for(const t of this._stores.keys())this.deleteKeyStore(t);this._keysStore.emit()}get size(){return this._keysStore.getState(),this._map.size}*entries(){this.keys();for(const e of this._map.entries())this.get(e[0]),yield e}keys(){return this._keysStore.getState()}*values(){this.keys();for(const[e,t]of this.entries())yield t}[Symbol.iterator](){return this.entries()}set(e,t){const o=this._map.size;super.set(e,t);return this._map.size!==o&&this.emitIterable(),this}delete(e){const t=super.delete(e);return t&&this.emitIterable(),t}emitIterable(){this._keysStore.emit()}}},266625:(e,t,o)=>{o.d(t,{y:()=>s});var n=o(296540),r=()=>o(496603);function s(e,t){const o=(0,n.useRef)(Math.random().toString(36).substr(2,9)),s=t,i=(0,n.useRef)();return(0,n.useEffect)((()=>{if(i&&i.current&&r().n4(i.current,s))return;const t=o.current;e&&(e({type:"mount",id:t,props:s}),i.current=s)}),[e,s]),e?o.current:void 0}},272061:(e,t,o)=>{o.d(t,{B:()=>a,J:()=>s});o(944114),o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698);var n=()=>o(534177),r=()=>o(496506);function s(e,t){const o=new Map,s=new Set,a={nodes:[],edges:[]},c=new Map;let u=0;function l(e){let t=e;return s.has(t)&&(t=`${t}_${++u}`),s.add(t),t}function d(e){let t=o.get(e.value);return t||(t=function(e){var t;let s,a=null===(t=e.value.constructor)||void 0===t?void 0:t.name;a||(a="Unknown"),"component"===e.kind?("debugName"in e.value&&(a=e.value.debugName),s={type:"component",id:l(a),label:a}):"store"===e.kind?e.value instanceof r().ComputedStore?(e.value.debugName&&(a=e.value.debugName),s={type:"computedstore",id:l(a),label:a}):s={type:"store",id:l(a),label:a}:(0,n().HB)(e);for(const o of i)o(s,e.value,(e=>{c.set(s,e)}));return o.set(e.value,s),s}(e)),t}for(const[n,r]of e.entries())for(const e of r)a.edges.push({from:d({kind:"store",value:n}).id,to:d({kind:"component",value:e}).id});for(const[n,r]of t.entries())for(const e of r)a.edges.push({from:d({kind:"store",value:n}).id,to:d({kind:"store",value:e}).id});for(const[n,r]of c.entries()){const e=o.get(r);e&&(n.parentUIStoreId=e.id)}return a.nodes=Array.from(o.values()),a}const i=[];function a(e){i.push(e)}},279106:(e,t,o)=>{o.r(t),o.d(t,{LocalStorageKeyStore:()=>i});o(16280),o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698);var n=()=>o(496603);const r="notion_test_local_storage_key",s="notion_123";class i{constructor(e){let{key:t,namespace:a,important:c,trackingType:u}=e;if(this.lruStore=void 0,this.key=void 0,this.emitter=new(o(592328).A),this._canPersistToLocalStorage=void 0,this.canPersistToLocalStorage=()=>{if(void 0===this._canPersistToLocalStorage){let e;try{this.lruStore.set(r,s),e=this.lruStore.get(r,{disableLRU:!0}),e&&this.lruStore.remove(r)}catch{}this._canPersistToLocalStorage=e===s}return this._canPersistToLocalStorage},this.handleStorage=e=>{if("string"!=typeof e.key)return;if(this.lruStore.parseRawKeyToOwnedKey(e.key)===this.key){if(null===e.newValue&&e.oldValue)return void this.emit();if(e.newValue&&null===e.oldValue)return void this.emit();if(null===e.oldValue&&null===e.newValue)return;if(e.newValue&&e.oldValue)try{const t=JSON.parse(e.oldValue).value,o=JSON.parse(e.newValue).value;n().n4(t,o)||this.emit()}catch{o(449412).O8(new Error("Malformed value(s) found in localStorage"),{from:"LocalStorageKeyStore",type:"ParseError",data:{key:e.key,oldValue:e.oldValue,newValue:e.newValue}})}}},i.keysWithStores.has(`${a}:${t}`))throw new Error("Please create only one LocalStorageKeyStore per key.");i.keysWithStores.add(`${a}:${t}`),this.key=t,this.lruStore=new(o(419494).Ay)({namespace:a,important:c,trackingType:u,onHasPermissionForTrackingTypeChange:()=>{this.emit()}})}getState(){return o(60053).AutoListener.logStoreAccess(this,this.getDebugInfo()),this.lruStore.get(this.key)}get state(){return this.getState()}setState(e){const t=this.lruStore.get(this.key,{disableLRU:!0});n().n4(t,e)||(void 0!==e?this.lruStore.set(this.key,e):this.lruStore.remove(this.key),this.emit())}update(e){this.setState(e(this.state))}emit(){this.emitter.emit(this)}addListener(e){const t=this.emitter.listenerCount();this.emitter.addListener(e),0===t&&1===this.emitter.listenerCount()&&window.addEventListener("storage",this.handleStorage)}removeListener(e){const t=this.emitter.listenerCount();this.emitter.removeListener(e),1===t&&0===this.emitter.listenerCount()&&window.removeEventListener("storage",this.handleStorage)}getDebugInfo(){return this.lruStore.get(this.key,{disableLRU:!0})}static reset_TEST_ONLY(){i.keysWithStores=new Set([])}}i.keysWithStores=new Set([])},285719:(e,t,o)=>{o.d(t,{q:()=>r});var n=o(296540);function r(e){const[t,o]=(0,n.useState)(Date.now());return(0,n.useEffect)((()=>{const t=setInterval((()=>{o(Date.now())}),e);return()=>clearInterval(t)}),[e]),t}},299972:(e,t,o)=>{o.d(t,{A:()=>n});class n extends(()=>o(757695))().Store{getInitialState(){return{}}canGoBack(){return void 0!==this.state.historyState&&this.state.historyState.index>0}canGoForward(){return!this.state.historyState||this.state.historyState.index<window.history.length-1}}},311029:(e,t,o)=>{o.d(t,{B:()=>r});let n=0;function r(){return`id_${(++n).toString(36)}`}},319285:(e,t,o)=>{o.d(t,{A:()=>f,x:()=>h});o(16280),o(944114),o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698),o(898992),o(354520);var n=o(296540),r=o(440961),s=()=>o(100622),i=()=>o(821062),a=()=>o(624919);let c;c=(0,o(624184).zB)(1,o(192191));const u=c.LegacyComponentRenderEffect;var l=()=>o(992202),d=()=>o(908006);const h=["environment","pageContext","contentEditableContext","themeContext"];class m extends n.Component{createComputedStore(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=new(o(496506).ComputedStore)(e,{...t,debugName:t.debugName?`${this.getComponentName()}.${t.debugName}`:this.makeUniqueDebugNameForComputedStore(),source:"Component"});return this.computedStores.push(n),n}makeUniqueDebugNameForComputedStore(){this.latestComputedStoreId=this.latestComputedStoreId+1;return`${this.getComponentName()}.createComputedStore_${this.latestComputedStoreId}`}get registries(){return[]}constructor(e,t){super(e,t),this.legacyContextKeys=h,this.debug=!1,this.profile=!1,this.storeTypes=void 0,this.stores={},this.componentIsMounted=!1,this.componentIsUpdating=!1,this.autoListener=void 0,this.computedStores=[],this.onUnmountCallbacks=new Set,this.latestComputedStoreId=0,this.domLockToken=void 0,this.enqueueForceUpdate=e=>{this.isDebug()&&console.info(`${this.constructor.name}.enqueueForceUpdate`,e),l().logComponentForceUpdateScheduled(this,e),d().default.enqueueComponentRender(this.dequeueForceUpdate)},this.dequeueForceUpdate=Object.assign((()=>new Promise((e=>{this.componentIsMounted&&!this.componentIsUpdating?(this.onUnmountCallbacks.add(e),this.forceUpdate((()=>{this.onUnmountCallbacks.delete(e),e()}))):e()}))),{componentName:this.getComponentName()}),this.profilerId=void 0,this.autoListener=new u(this),this.domLockToken={displayName:this.getComponentName()}}get environment(){return this.context.environment}get pageContext(){return this.context.pageContext}get contentEditableContext(){return this.context.contentEditableContext}get theme(){const e=this.context.themeContext;if(null!=e&&e.mode)return(0,s().O4)({theme:e.mode});const{ThemeModeStore:t}=this.environment;return(0,s().O4)({theme:t.state})}getNode(){return r.findDOMNode(this)}mutateDOM(e){var t;const o=null===(t=this.contentEditableContext)||void 0===t?void 0:t.domLock;return o?o.mutate(e):e()}UNSAFE_willMount(e){}UNSAFE_willUpdate(e){}UNSAFE_willMountOrUpdate(e){}UNSAFE_willReceiveProps(e){}didMount(){}didUpdate(e){}didMountOrUpdate(){}willUnmount(){}renderComponent(){return null}UNSAFE_componentWillMount(){var e;this.isDebug()&&console.info(`${this.constructor.name}.UNSAFE_willMount()`),this.createStores(this.props);for(const t of this.registries)t&&this.onUnmountCallbacks.add(t.register(this));this.autoListener.startListener({incrementCycle:!0}),null===(e=this.contentEditableContext)||void 0===e||e.domLock.unlockForRender(this.domLockToken),this.componentIsUpdating=!0,this.UNSAFE_willMount(this.props),this.UNSAFE_willMountOrUpdate(this.props),this.autoListener.stopListener({isEndOfCycle:!1})}componentDidMount(){this.isDebug()&&console.info(`${this.constructor.name}.didMount()`),this.componentIsMounted=!0,this.componentIsUpdating=!1,l().logComponentRender(this);try{this.didMount(),this.didMountOrUpdate()}finally{var e;null===(e=this.contentEditableContext)||void 0===e||e.domLock.lockAfterRender(this.domLockToken)}}UNSAFE_componentWillReceiveProps(e){if(!(0,i().A)(this.props,e))for(const t of this.computedStores)t.setShouldRecompute();this.UNSAFE_willReceiveProps(e)}shouldComponentUpdate(e,t,o){if(!(0,i().A)(this.props,e))return!0;if(this.state!==t)return!0;for(const n of this.legacyContextKeys)if(this.context[n]!==o[n])return!0;return!1}UNSAFE_componentWillUpdate(e){var t;if(this.isDebug()){console.info(`${this.constructor.name}.UNSAFE_willUpdate()`,e);const t=o(496603).sb(Object.keys(e).concat(Object.keys(this.props))).filter((t=>e[t]!==this.props[t]));t.length&&console.info("changed:",t)}d().default.removeRenderFromQueue(this.dequeueForceUpdate),this.autoListener.startListener({incrementCycle:!0}),null===(t=this.contentEditableContext)||void 0===t||t.domLock.unlockForRender(this.domLockToken),this.componentIsUpdating=!0,this.assignStores(e),this.UNSAFE_willUpdate(e),this.UNSAFE_willMountOrUpdate(e),this.autoListener.stopListener({isEndOfCycle:!1})}componentDidUpdate(e){var t;this.isDebug()&&console.info(`${this.constructor.name}.didUpdate()`),this.componentIsUpdating=!1,l().logComponentRender(this),this.didUpdate(e),this.didMountOrUpdate(),null===(t=this.contentEditableContext)||void 0===t||t.domLock.lockAfterRender(this.domLockToken)}componentWillUnmount(){var e;this.isDebug()&&console.info(`${this.constructor.name}.willUnmount()`),this.componentIsMounted=!1,this.componentIsUpdating=!1,null===(e=this.contentEditableContext)||void 0===e||e.domLock.lockAfterRender(this.domLockToken);for(const t of this.onUnmountCallbacks)t();this.onUnmountCallbacks=new Set,this.autoListener.destroy(),this.computedStores=[],this.willUnmount()}render(){this.isDebug()&&console.info(`${this.constructor.name}.render()`),this.autoListener.startListener({incrementCycle:!1});let e=this.renderComponent();return this.autoListener.stopListener({isEndOfCycle:!0}),this.profile&&e&&(e=n.createElement(n.Profiler,{id:this.getProfilerId(),onRender:g},[e])),e||null}createStores(e){const t=this.storeTypes;if(t)for(const o in t)if(e[o])this.stores[o]=e[o];else if(t[o]&&!this.stores[o]){const e=t[o];this.stores[o]=new e}}assignStores(e){const t=this.storeTypes;if(t)for(const o in t)e[o]&&(this.stores[o]=e[o])}isDebug(){return this.debug||this.props.debug}getProfilerId(){return this.profilerId||(this.profilerId=`${this.constructor.name}#${p++}`),this.profilerId}getComponentName(){return"displayName"in this.constructor&&"string"==typeof this.constructor.displayName?this.constructor.displayName:"UnknownClassComponent"}}m.contextType=o(446943).Z;let p=0;function g(e,t,o){console.info(`Render time for ${e} ${t}: ${o} ms`)}(0,a().EX)({canFormat:e=>Boolean(e&&e instanceof m),header(e){const{span:t,object:o,table:n,tr:r,td:s,CONTAINER_STYLE:i}=a().iY,{props:c,stores:u}=e,l=Object.keys(c).length>0?[" props: ",o(c)]:[],d=Object.keys(u).length>0?[" stores: ",o(u)]:[];return t(i,t({},"<",o(e,{useDefaultFormatter:!0}),...l,...d," />"),n({},r({},s({},"DOM node:"),s({},o(e.getNode())))))},hasBody:()=>!1,body:()=>null});const f=m},352246:(e,t,o)=>{o.d(t,{t:()=>n});o(944114),o(296540);"undefined"!=typeof window&&localStorage.getItem("__rerenderDefenderFender");const n=new class{setShouldCollect(e){}getShouldCollect(){return this.shouldCollect}constructor(){this.shouldCollect=!1,this.shouldCollect=!1}add(e,t,o){}log(e,t,o,n){}resetMetrics(){}getMetrics(){return[]}}},355543:(e,t,o)=>{o.d(t,{w:()=>i});var n=o(296540),r=()=>o(17022);const s=Symbol("EMPTY_VALUE");function i(e){const t=(0,n.useRef)(s);return t.current===s&&(t.current=r().e.withListenerIgnored((()=>e()))),t.current}},395361:(e,t,o)=>{o.d(t,{U:()=>s,Y:()=>i});var n=o(296540),r=()=>o(496603);function s(e,t){return(0,n.useRef)(r().sg(e,t)).current}function i(e,t){const o=(0,n.useRef)(e);o.current=e;const s=(0,n.useMemo)((()=>r().sg((function(){return o.current(...arguments)}),t)),[t]);return s}},396182:(e,t,o)=>{o.d(t,{I:()=>i});o(944114);var n=o(296540),r=()=>o(992202);class s{constructor(e,t){this.isUnmounted=!1,this.afterRerenderThunks=[],this.dequeueRerender=void 0,this.dispatch=t;const o=()=>new Promise((e=>{this.isUnmounted?e():(this.afterRerenderThunks.push(e),this.dispatch((e=>e+1)))}));o.componentName=e,this.dequeueRerender=o}enqueueRerender(e,t,n){o(134134).I.log("enqueueRender",t,n),r().isRecording()&&r().logComponentForceUpdateScheduled({componentName:e,debugName:t},n);const s=this.dequeueRerender;s.componentName=e,o(908006).default.enqueueComponentRender(s)}resolveRenderQueuePromises(){if(!this.afterRerenderThunks.length)return;const e=this.afterRerenderThunks;try{for(let t=0;t<e.length;t++)e[t]()}finally{e.length=0}}onUnmount(){this.isUnmounted=!0,this.resolveRenderQueuePromises()}}function i(){const[e,t]=(0,n.useState)(0);(0,n.useDebugValue)(e);const o=(0,n.useRef)(void 0);(0,n.useLayoutEffect)((()=>{var e;return null===(e=o.current)||void 0===e?void 0:e.resolveRenderQueuePromises()})),(0,n.useEffect)((()=>()=>{var e;return null===(e=o.current)||void 0===e?void 0:e.onUnmount()}),[]);return(0,n.useCallback)(((e,n,r)=>{(o.current??=new s(e,t)).enqueueRerender(e,n,r)}),[])}},396487:(e,t,o)=>{o.d(t,{I:()=>r});var n=()=>o(711059);class r extends(()=>o(757695))().Store{getInitialState(){return{}}isPatchUpdate(){return Boolean(this.state.appUpdate&&"ready"===this.state.appUpdate.state&&this.state.appUpdate.type===n().UpdateType.Minor)}isSilentUpdate(){return Boolean(this.state.appUpdate&&"ready"===this.state.appUpdate.state&&this.state.appUpdate.type===n().UpdateType.Silent)}isMajorUpdate(){return this.state.appUpdate&&"ready"===this.state.appUpdate.state&&this.state.appUpdate.type===n().UpdateType.Major||this.state.electronUpdate&&"ready"===this.state.electronUpdate.state&&this.state.electronUpdate.type===n().UpdateType.Major}}},401497:(e,t,o)=>{o.d(t,{$4:()=>c,DP:()=>s,IS:()=>a,eP:()=>i});o(16280);var n=o(296540),r=()=>o(560198);function s(){const e=(0,n.useContext)(r().Ke);if(!e)throw new Error("useTheme: no theme context found");return(0,n.useDebugValue)(e),e}function i(){return s().mode}function a(e,t){const o=0!==e.length?s():void 0,r=(0,n.useMemo)((()=>e(o)),[o,...t]);return(0,n.useDebugValue)(r),r}function c(e){return e}},402673:(e,t,o)=>{o.d(t,{$:()=>n});const n="function"==typeof requestIdleCallback?(e,t)=>requestIdleCallback(e,{timeout:t}):setTimeout},427652:(e,t,o)=>{o.d(t,{EZ:()=>u,N7:()=>c,jY:()=>n,m:()=>l,nH:()=>a});const n="undefined"!=typeof window&&"true"===localStorage.getItem("__enableDebugStoreMap"),r=new FinalizationRegistry((e=>{c(e)})),s={};function i(){}(0,o(604341).exposeDebugValue)("getDebugStoreMap",(function(){return s}));const a=n?function(e,t){const o=e.debugName;if(o){if(!s[o]){const n=new WeakRef(e);s[o]={count:0,store:n,listeners:{},type:t},r.register(e,o)}s[o].count+=1}}:i,c=n?function(e){if(!e||!s[e])return;const t=s[e].store;delete s[e];const o=null==t?void 0:t.deref();o&&r.unregister(o)}:i,u=n?function(e,t){let{listenerName:o,listenerType:n}=t;const r=s[e];if(!r)return;const{listeners:i}=r;i[o]||(i[o]={listenerType:n})}:i,l=n?function(e,t){const o=s[e];if(!o)return;const{listeners:n}=o;delete n[t]}:i},445356:(e,t,o)=>{o.d(t,{A:()=>r});var n=o(296540);class r extends n.Component{constructor(e){super(e),this.state={hasError:!1}}componentDidCatch(e,t){this.props.onError&&this.props.onError(e,t),this.setState({hasError:!0,error:e,errorInfo:t})}clearErrorState(){this.setState({hasError:!1})}render(){return this.state.hasError&&this.props.fallback?this.props.fallback({error:this.state.error,errorInfo:this.state.errorInfo,clearErrorState:this.clearErrorState.bind(this)}):this.props.children}}},446943:(e,t,o)=>{o.d(t,{C:()=>a,Z:()=>s});var n=o(296540),r=o(474848);const s=(0,n.createContext)(Object.create(null));function i(e){let{children:t,legacyContextKey:o,value:i}=e;const a=(0,n.useContext)(s),c=(0,n.useMemo)((()=>({...a,[o]:i})),[o,i,a]);return(0,r.jsx)(s.Provider,{value:c,children:t})}function a(e){const t=(0,n.createContext)(e.modernContextDefaultValue);t.displayName=e.displayName;const o=function(o){let{children:n,value:s}=o;return(0,r.jsx)(t.Provider,{value:s,children:(0,r.jsx)(i,{legacyContextKey:e.legacyContextKey,value:s,children:n})})};return o.displayName=`${e.displayName}.UnifiedProvider`,{Provider:o,Consumer:t.Consumer,context:t,partialMergedContextType:null}}s.displayName="UnifiedLegacyMergedContext"},452446:(e,t,o)=>{o.d(t,{q:()=>s,r:()=>r});const n=(0,o(446943).C)({displayName:"PageContext",legacyContextKey:"pageContext",modernContextDefaultValue:void 0}),r=n.context,s=n.Provider},466103:(e,t,o)=>{o.r(t),o.d(t,{exposeDebugEnvironmentValue:()=>i,exposeWindowDebugValue:()=>c,setEnvironment:()=>a});o(944114);var n=()=>o(604341);let r;const s=[];function i(e,t){s.push({name:e,getter:t}),r&&(0,n().exposeDebugValue)(e,t(r))}function a(e){r=e;for(const{name:t,getter:o}of s)(0,n().exposeDebugValue)(t,o(r))}function c(e,t){Object.defineProperty(window,`$${e}`,{get:t,enumerable:!0,configurable:!0})}},484714:(e,t,o)=>{o.d(t,{WS:()=>a,Y0:()=>i,v3:()=>s});o(16280);var n=o(296540),r=()=>o(136590);function s(){const e=(0,n.useContext)(r().yY);if(!e)throw new Error("No ClientEnvironment provided.");return e}function i(){const e=(0,n.useContext)(r().Ag);if(!e)throw new Error("No DeviceContext provided.");return e}function a(){return i().isPhone}},485671:(e,t,o)=>{o.r(t),o.d(t,{useComputedStore:()=>y,useComputedStoreInstance:()=>f,useComputedStoreWithValue:()=>w,useStoreInstance:()=>v,useStoreState:()=>S,useStore_DEPRECATED:()=>C});o(16280);var n=o(296540),r=()=>o(591779),s=()=>o(17022),i=()=>o(134134),a=()=>o(992202);"undefined"!=typeof window&&localStorage.getItem("__useSlowComponentNameLookup");function c(){return"UnknownFunctionComponent"}const u=c;var l=()=>o(396182),d=()=>o(496506);const h="no debugName: ",m={useComputedStore:`${h}UnknownFunctionComponent.useComputedStore`,useComputedStoreInstance:`${h}UnknownFunctionComponent.useComputedStoreInstance`,useStoreState:`${h}UnknownFunctionComponent.useStoreState`},p=c===c;function g(e,t,o){return t||(p?m[o]:`${h}${e}.${o}`)}function f(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=arguments.length>3?arguments[3]:void 0;const i=g(u(),o.debugName||s,"useComputedStoreInstance");(0,n.useDebugValue)(i);const a=(0,n.useCallback)(e,t),c=(0,n.useRef)();c.current||(c.current=new(d().ComputedStore)(a,{...o,debugName:i,source:"useComputedStore"}),c.current.addListener(d().ZD));const l=c.current;return(0,n.useEffect)((()=>(l.addListener(d().ZD),()=>l.removeListener(d().ZD))),[l]),(0,r()._$)(t,r().MR)&&l.updateStoreInstance(a,Boolean(o.useDeepEqual)),l}function v(e,t){const o=(0,n.useRef)();if(e)return o.current=void 0,e;if(!t)throw new Error("useStore: no store instance, and no store constructor");return o.current||(o.current=new t),o.current}const b={debugName:"UNKNOWN",componentName:"UNKNOWN"};function S(e,t){const o=u(),r=g(o,t,"useStoreState"),c=(0,l().I)(),d=(0,n.useRef)(!1);(0,n.useLayoutEffect)((()=>{if(!e)return;const t=(0,a().isRecording)()?{debugName:r,componentName:o}:b,n=function(e){i().I.log("useSubscription listener called",{debugName:r,disabled:d.current},e),d.current||c(o,r,e)};return e.addListener(n,{listenerName:o,listenerType:"component"}),i().I.log("useStoreState: addListener",r,e),(0,a().logListenerAdded)(e,t,3),()=>{i().I.log("useStoreState: removeListener",r,e),e.removeListener(n,o),(0,a().logListenerRemoved)(e,t)}}),[e,r,o,c]),d.current=!0;try{const t=s().e.withListenerIgnored((()=>null==e?void 0:e.getState()));return(0,n.useDebugValue)(t),t}finally{d.current=!1}}function y(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3?arguments[3]:void 0;const s=g(u(),r&&o.debugName?`${o.debugName} - ${r}`:o.debugName||r,"useComputedStore");(0,n.useDebugValue)(s);return S(f(e,t,o,s),s)}function w(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3?arguments[3]:void 0;const s=g(u(),r&&o.debugName?`${o.debugName} - ${r}`:o.debugName||r,"useComputedStore");(0,n.useDebugValue)(s);const i=f(e,t,o,s);return[S(i,s),i]}function C(e,t){const o=v(e,t);(0,n.useDebugValue)(o.constructor.name);return[S(o),(0,n.useCallback)((e=>"function"==typeof e?o.update(e):o.setState(e)),[o]),o]}},496506:(e,t,o)=>{o.d(t,{ComputedStore:()=>r,ZD:()=>s,g5:()=>i});let n;n=(0,o(624184).zB)(1,o(585556));const r=n.ComputedStore,s=n.NO_OP_SUBSCRIBER,i=(n.getUniqueStoreId,n.USE_COMPREHENSIVE_STORE_MAP_STORAGE_KEY,n.getComputedStoreStats)},507707:(e,t,o)=>{o.d(t,{A:()=>s});const n=2,r=4;function s(e,t){if(!e&&!t)return 0;if(!t)return-1;if(!e)return 1;const o=e.compareDocumentPosition(t);return(o&r)===r?-1:(o&n)===n?1:0}},543906:(e,t,o)=>{o.d(t,{A:()=>r});var n=o(296540);function r(e,t){const o=(0,n.useRef)(!1);(0,n.useEffect)((()=>{!o.current&&t&&(o.current=!0,e())}),[t,e])}},560198:(e,t,o)=>{o.d(t,{Ke:()=>u,VD:()=>l,_u:()=>d,wX:()=>h});var n=o(296540),r=()=>o(100622),s=()=>o(484714),i=()=>o(56366),a=o(474848);const c=(0,o(446943).C)({displayName:"ThemeContext",legacyContextKey:"themeContext",modernContextDefaultValue:void 0}),u=c.context;function l(e){return e.device.prefersDarkInterface?"dark":"light"}const d=window.notionTheme;function h(e){const t=(0,s().v3)(),o=(0,i().K8)((()=>e.mode?e.mode:"system"===d?l(t):d||t.ThemeModeStore.state),[e.mode,t]),u=(0,n.useMemo)((()=>(0,r().O4)({theme:o})),[o]);return(0,a.jsx)(c.Provider,{value:u,children:e.children})}},585556:(e,t,o)=>{o.r(t),o.d(t,{ComputedStore:()=>w,NO_OP_SUBSCRIBER:()=>m,USE_COMPREHENSIVE_STORE_MAP_STORAGE_KEY:()=>p,getComputedStoreStats:()=>y,getUniqueStoreId:()=>h});o(581454);var n=()=>o(496603),r=()=>o(498212),s=()=>o(624919),i=()=>o(134134),a=()=>o(906510),c=()=>o(352246),u=()=>o(427652),l=()=>o(60053);const d=u().jY;function h(e){return(0,r().gB)(e.toString()).substring(0,8)}function m(){}const p="__useComprehensiveStoreMap",g="undefined"!=typeof window&&"true"===localStorage.getItem(p);let f=0,v=0,b=0,S=0;function y(){return{computedStoreCount:f,computedStoreSubscriptionCount:v,computedStoreRecomputesTotal:b,computedStoreRerendersTotal:S}}(0,o(604341).exposeDebugValue)("getComputedStoreStats",y);class w{constructor(e,t){this.debug=!1,this.debugName=void 0,this.computeFn=void 0,this.autoListener=void 0,this.emitter=void 0,this.shouldRecompute=!1,this.useDeepEqual=!1,this.recomputeSchedule=void 0,this.options=void 0,this.handleChange=()=>{if("lazy"===this.recomputeSchedule)this.setShouldRecompute(),o(908006).default.enqueueComputedStoreRecompute((0,o(720665).Zg)(this.recomputeState,"debugName",this.debugName));else if("eager"===this.recomputeSchedule)this.recomputeState();else if("debounce"===this.recomputeSchedule.type){var e;null===(e=this.recomputeStateDebounced)||void 0===e||e.call(this)}},this.recomputeState=()=>{this.shouldRecompute=!1;const e=this.lastState;this.listenerCount()>0&&this.autoListener.startListener({incrementCycle:!0});const t={startMs:0,recomputeEndMs:0,comparisonEndMs:0};w.profiling&&(t.startMs=performance.now());const r=this.computeFn();w.profiling&&(t.recomputeEndMs=performance.now()),this.listenerCount()>0&&this.autoListener.stopListener({isEndOfCycle:!0});if(!(this.useDeepEqual?n().n4:o(821062).A)(e,r)){if(c().t.getShouldCollect()&&!this.useDeepEqual&&(0,n().n4)(e,r)){let e=`${this.computeFn.toString().replace(/\s+/g," ").replace(/__WEBPACK_IMPORTED_MODULE_\d+__/g,"").replace("_stores_","").replace(/'["default"]'/g,"").replace('[\\"default\\"]',"")}`;e.length>100&&(e=`${e.slice(0,100)}...`),c().t.add(this.options.debugName,e,r)}(w.debug||this.debug)&&i().I.log("ComputedStore.recomputeState emit",this.debugName,this),b+=1,S+=1,this.lastState=r,this.emit()}else(w.debug||this.debug)&&i().I.log("ComputedStore.recomputeState unchanged",this.debugName,this),b+=1;if(this.debugName.startsWith("ExperimentStore.")||a().S3.increment("ComputedStore.recomputeState",this.debugName),w.profiling){t.comparisonEndMs=performance.now();const e=t.recomputeEndMs-t.startMs,o=t.comparisonEndMs-t.recomputeEndMs,n=w.profilingMap.get(this.debugName);n?w.profilingMap.set(this.debugName,{runs:n.runs+1,totalRecomputeTimeMs:n.totalRecomputeTimeMs+e,totalComparisonTimeMs:n.totalComparisonTimeMs+o}):w.profilingMap.set(this.debugName,{runs:1,totalRecomputeTimeMs:e,totalComparisonTimeMs:o})}},this.computeFn=e,this.options=t,this.debugName=g?`${t.debugName}${d?`.${e.toString().replace(/\s+/g,"").slice(0,100)}.${(0,r().Ay)()}`:""}`:`${t.debugName}${d?`.${h(e)}`:""}`,t.debug&&(this.debug=!0),this.autoListener=new(l().AutoListener)({onChange:this.handleChange,debugName:this.debugName,debug:this.debug,source:"computed-store"}),this.emitter=new(o(592328).A),this.useDeepEqual=Boolean(t.useDeepEqual),t.recomputeSchedule&&"object"==typeof t.recomputeSchedule&&"debounce"===t.recomputeSchedule.type&&(this.recomputeStateDebounced=(0,n().sg)(this.recomputeState,t.recomputeSchedule.debounceMs,{maxWait:t.recomputeSchedule.maxWait}),this.lastState=t.recomputeSchedule.initialValue),"useComputedStore"!==t.source&&"Component"!==t.source||(this.debugName.startsWith("ExperimentStore.")||a().S3.increment("ComputedStore.constructor",this.debugName),f+=1),this.recomputeSchedule=(null==t?void 0:t.recomputeSchedule)??"lazy"}getState(){return l().AutoListener.logStoreAccess(this,this.getDebugInfo()),this.listenerCount()>0?(this.shouldRecompute&&(this.recomputeStateDebounced??this.recomputeState)(),this.lastState):this.computeFn()}get state(){return this.getState()}addListener(e,t){const o=this.emitter.listenerCount();this.emitter.addListener(e);const n=this.emitter.listenerCount();if(e!==m){const e=n-o;v+=e,d&&t&&e>0&&(0,u().EZ)(this.debugName,t)}0===o&&1===n&&((this.recomputeStateDebounced??this.recomputeState)(),(0,u().nH)(this,"computed-store"))}removeListener(e,t){const o=this.emitter.listenerCount();var n;(this.emitter.removeListener(e),e!==m&&(v-=1,t&&(0,u().m)(this.debugName,t)),1===o&&0===this.emitter.listenerCount())&&(null===(n=this.recomputeStateDebounced)||void 0===n||n.cancel(),"useComputedStore"!==this.options.source&&"Component"!==this.options.source||((0,u().N7)(this.debugName),f-=1),this.autoListener.destroy(this.debugName))}emit(){o(992202).logStoreEmit(this),this.emitter.emit(this)}listenerCount(){return this.emitter.listenerCount()}updateStoreInstance(e,t){const o=Boolean(t);this.useDeepEqual===o&&this.computeFn===e||((w.debug||this.debug)&&i().I.log("ComputedStore.updateStoreInstance changed",this.debugName),this.computeFn=e,this.useDeepEqual=o,this.setShouldRecompute())}setShouldRecompute(){this.shouldRecompute=!0}enqueueRecompute(){this.handleChange()}recompute(){this.recomputeState()}getDebugInfo(){return{hasListeners:this.listenerCount()>0,lastState:this.lastState}}static profileStart(){w.profilingMap.clear(),w.profiling=!0}static profileEnd(){w.profiling=!1;const e=Array.from(w.profilingMap.entries()).sort(((e,t)=>{const o=e[1].totalRecomputeTimeMs+e[1].totalComparisonTimeMs;return t[1].totalRecomputeTimeMs+t[1].totalComparisonTimeMs-o}));console.log("ComputedStore profile",e.map((e=>{let[t,o]=e;return{name:t,...o}})))}static profileFor(e){w.profileStart(),setTimeout((()=>{w.profileEnd()}),e)}}w.debug=!1,w.profiling=!1,w.profilingMap=new Map,(0,s().EX)({canFormat:e=>Boolean(e&&e instanceof w),header(e){const{span:t,object:o,objectSummary:n,CONTAINER_STYLE:r}=s().iY,i=e.lastState;return t(r,o(e,{useDefaultFormatter:!0}),"(",i&&"object"==typeof i?n(i):o(i),")")},hasBody:()=>!1,body:()=>null})},588165:(e,t,o)=>{o.d(t,{a:()=>c,c:()=>u});var n=()=>o(185991),r=()=>o(496603),s=()=>o(662303);const i="¥",a="\\";function c(e,t){return{enter:["enter"],shiftEnter:["shift+enter"],commandEnter:["command+enter"],commandShiftEnter:["command+shift+enter"],commandSlash:["command+/"],commandS:["command+S"],commandJ:["command+j"],createAIChatThread:["command+shift+;"],delete:r().oE(["delete",e.isApple?"ctrl+d":void 0]),deleteToEndOfLine:r().oE([e.isApple?"ctrl+k":void 0]),deleteNextWord:r().oE([e.isWindows?"ctrl+delete":void 0]),space:["space"],backspace:r().oE(["backspace","shift+backspace",e.isWindows?void 0:"alt+backspace","command+backspace","ctrl+backspace"]),esc:["esc"],left:r().oE(["left","shift+left","command+shift+left",e.isWindows||e.isLinux?"ctrl+left":void 0,"ctrl+shift+left","alt+left","alt+shift+left"]),right:r().oE(["right","shift+right","command+shift+right",e.isWindows||e.isLinux?"ctrl+right":void 0,"ctrl+shift+right","alt+right","alt+shift+right"]),up:r().oE(["up","shift+up","alt+up","alt+shift+up","command+up",e.isApple?"ctrl+p":void 0]),down:r().oE(["down","shift+down","alt+down","alt+shift+down","command+down",e.isApple?"ctrl+n":void 0]),moveUp:["command+shift+up"],moveDown:["command+shift+down"],peekUp:[e.isWindows||e.isLinux?"alt+k":"ctrl+shift+k"],peekDown:[e.isWindows||e.isLinux?"alt+j":"ctrl+shift+j"],untab:["shift+tab"],tab:["tab"],ungroup:["command+shift+g"],group:["command+g"],home:["home","shift+home"],end:["end","shift+end"],selectAll:["command+a"],redo:["command+shift+z","command+y"],undo:r().oE(["command+z",e.isWindows?"alt+backspace":void 0]),toggleUnderline:["command+u"],toggleHighlight:["command+shift+h"],toggleBold:["command+b"],toggleItalics:["command+i"],toggleCode:["command+e"],toggleStrike:["command+shift+x","command+shift+s"],duplicate:["command+d"],duplicateSchema:["command+shift+d"],cut:["command+x"],copy:["command+c"],paste:["command+v"],openLinkMenuOrOpenSearch:["command+k"],toggleInPageFindReplace:["command+alt+f"],openEquationMenu:["command+shift+e"],goBack:["command+["],goForward:["command+]"],newTab:["command+t"],commandLeft:["command+left"],commandRight:["command+right"],goUp:["command+shift+u"],quickFind:["command+p"],search:["command+f"],goToBeginningOfLine:["ctrl+a","ctrl+shift+a"],goToEndOfLine:["ctrl+e","ctrl+shift+e"],goForwardOneChar:["ctrl+f","ctrl+shift+f"],goBackwardOneChar:["ctrl+b","ctrl+shift+b"],comment:["command+shift+m"],suggest:["command+shift+alt+x"],caption:["command+alt+m"],react:["command+alt+r"],rename:["command+shift+r"],copyLinkToCurrentPage:["command+l"],copyLinkToPageInCommandSearch:["command+shift+c"],copyLinkToBlock:[e.isApple?"command+ctrl+l":"alt+shift+l"],copyCurrentPageLinkifiedBlockTitle:["command+alt+l"],toggleSidebar:"ja-JP"===s().locale?[`command+${i}`,`command+${a}`,"command+code:IntlRo"]:["command+\\"],toggleUpdateSidebar:"ja-JP"===s().locale?[`command+shift+${i}`,`command+shift+${a}`,"command+code:IntlRo"]:["command+shift+\\"],toggleBothSidebars:["command+."],toggleAISidebar:["command+;"],openCommentsTabInUpdateSidebar:["ctrl+alt+9"],openUpdatesTabInUpdateSidebar:["ctrl+alt+0"],zoomIn:["command+="],zoomOut:["command+-"],zoomReset:["command+0"],settings:["command+,"],newPage:["command+n"],newPageAndDictate:[e.isApple?"command+ctrl+n":"ctrl+alt+n"],dictate:["command+o"],backbutton:["backbutton"],toggleDarkMode:["command+shift+l"],openFile:["command+alt+o"],moveTo:["command+shift+p"],toggleAllToggles:["command+alt+t"],switchSpacesPreTabs:["command+1","command+2","command+3","command+4","command+5","command+6","command+7","command+8","command+9"],switchSpacesPostTabs:e.isApple?["ctrl+shift+0","ctrl+shift+1","ctrl+shift+2","ctrl+shift+3","ctrl+shift+4","ctrl+shift+5","ctrl+shift+6","ctrl+shift+7","ctrl+shift+8","ctrl+shift+9"]:["alt+shift+0","alt+shift+1","alt+shift+2","alt+shift+3","alt+shift+4","alt+shift+5","alt+shift+6","alt+shift+7","alt+shift+8","alt+shift+9"],turnIntoType:e.isApple?["command+alt+0","command+alt+1","command+alt+2","command+alt+3","command+alt+4","command+alt+5","command+alt+6","command+alt+7","command+alt+8","command+alt+9"]:["command+shift+0","command+shift+1","command+shift+2","command+shift+3","command+shift+4","command+shift+5","command+shift+6","command+shift+7","command+shift+8","command+shift+9"],toggleRecordingInputLatency:r().oE([e.isApple?"command+alt+ctrl+m":void 0]),keypress:["keypress"],togglePropertyVisibility:["command+alt+p"],toggleFavorite:[e.isApple?"command+ctrl+shift+f":"ctrl+alt+shift+f"],toggleAllUpdates:[e.isApple?"command+alt+u":"ctrl+alt+u"],toggleAllTeams:[e.isApple?"command+alt+a":"ctrl+alt+a"],openHome:[e.isApple?"command+ctrl+h":"ctrl+alt+h"],openShareMenu:[e.isApple?"command+shift+o":"ctrl+shift+o"],openInSidePeek:["alt+click"],openExperimentSettings:[e.isApple?"command+alt+shift+e":"ctrl+alt+shift+e"],notionAiCommandSearchDefault:[(null==t?void 0:t.notionAiShortcut)??(0,n().u)(e.isApple)]}}function u(e,t,o){return c(e,o)[t]}},588316:(e,t,o)=>{o.d(t,{A:()=>s});var n=o(296540);function r(e){try{return e.matches(":focus-within")}catch(t){return!1}}function s(){const e=(0,n.useRef)(null),t=(0,n.useRef)(null),[o,s]=(0,n.useState)(!1),i=(0,n.useCallback)((()=>{const t=e.current;t&&s(r(t))}),[]),a=(0,n.useCallback)((o=>{if(e.current=o,o){var n;const e=()=>{s(r(o))},i=()=>{s(r(o))};o.addEventListener("focusin",e),o.addEventListener("focusout",i),null===(n=t.current)||void 0===n||n.call(t),t.current=()=>{o.removeEventListener("focusin",e),o.removeEventListener("focusout",i)}}else{var a;null===(a=t.current)||void 0===a||a.call(t),t.current=null}i()}),[i]);return(0,n.useEffect)((()=>()=>{var e;return null===(e=t.current)||void 0===e?void 0:e.call(t)}),[]),(0,n.useEffect)((()=>{if(o)return document.addEventListener("pointerdown",i),document.addEventListener("keydown",i),()=>{document.removeEventListener("pointerdown",i),document.removeEventListener("keydown",i)}}),[o,i]),[a,o]}},590966:(e,t,o)=>{o.r(t),o.d(t,{CurrentUser:()=>n});class n{constructor(e){this._id=void 0,this._loggedInUserIds=void 0,this._adminUserId=void 0,this.args=e,this._id=null==e?void 0:e.id,this._loggedInUserIds=(null==e?void 0:e.loggedInUserIds)||[],this._adminUserId=null==e?void 0:e.adminUserId}get id(){return this._id}get loggedInUserIds(){return this._loggedInUserIds}get adminUserId(){return this._adminUserId}isLoggedIn(){var e;return Boolean(null===(e=this.args)||void 0===e?void 0:e.id)}}},609990:(e,t,o)=>{o.d(t,{X:()=>s});o(581454);var n=o(440961),r=()=>o(496603);class s extends(()=>o(757695))().Store{constructor(){super(...arguments),this.debugPropagation=!1}getInitialState(){return{stack:[]}}getDebugInfoForStackItem(e){const t=this.state.stack[e],o=t.enable?"":" (disabled)";return[`#${e} ${t.debugName}${o}:`,n.findDOMNode(t.listener)]}logDOMNodes(){console.info("The following components are listening for keyboard shortcuts. Shortcuts propagate from the bottom up.");for(let e=0;e<this.state.stack.length;e++)console.info(...this.getDebugInfoForStackItem(e))}logDebugState(){console.info(this.state.stack.map((e=>({shortcuts:r().z7(r().mg(e.shortcuts),r().b0),node:n.findDOMNode(e.listener),debugName:e.debugName,enable:e.enable,listener:e.listener}))))}}},624184:(e,t,o)=>{o.d(t,{PJ:()=>n,zB:()=>r});o(16280);function n(){return!1}function r(e,t){if(1!==e)throw new Error(`Required NOTION_REACTIVITY_VERSION=${e}, but is 1`);return t}globalThis.window},624919:(e,t,o)=>{o.d(t,{EX:()=>p,Iz:()=>i,NK:()=>u,Qq:()=>a,iY:()=>m});o(944114),o(898992),o(354520),o(672577),o(803949),o(581454);var n=()=>o(604341),r=()=>o(763824),s=()=>o(534177);async function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"magenta";const n=e=>e===1/0?3e3:e===-1/0?-3e3:e,s=e.width||(e.right??e.left)-e.left||50,i=e.height||(e.bottom??e.top)-e.top||50,a=window.document.createElement("DIV");a.style.position="absolute",a.style.zIndex="20000",a.style.top=`${n(e.top)}px`,a.style.left=`${n(e.left)}px`,a.style.minWidth=`${n(s)}px`,a.style.minHeight=`${n(i)}px`,a.style.border=`1px solid ${o}`,a.style.background=o,a.style.font="10px monaco",a.style.opacity="0.3",a.style.pointerEvents="none",a.style.userSelect="none",a.textContent=t,window.document.body.appendChild(a),await(0,r().wR)(1e3),a.remove()}function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const o=e[Object.keys(e).find((e=>e.startsWith("__reactInternalInstance$")))];if(!o)return;if(o._currentElement){let e=o._currentElement._owner;for(let o=0;o<t;o++)e=e._currentElement._owner;return e._instance}const n=e=>{let t=e.return;for(;"string"==typeof t.type;)t=t.return;return t};let r=n(o);for(let s=0;s<t;s++)r=n(r);return r.stateNode}function c(e){return"string"==typeof e?[e]:e}function u(e,t){const o=["%c NOTION%c WARNING %c","background: black; color: white;","background: black; color: orange","font-weight: normal",...c(e)];if(t){console.groupCollapsed(...o);for(const e of t)console.log(...c(e));console.trace(),console.groupEnd()}else console.warn(...o)}function l(e){return function(t){for(var o=arguments.length,n=new Array(o>1?o-1:0),r=1;r<o;r++)n[r-1]=arguments[r];return f([e,t,...n.filter(s().O9)])}}(0,n().exposeDebugValue)("debugDrawRect",i),(0,n().exposeDebugValue)("debugReactComponentFromDOMNode",a);const d=Math.floor(20/6);function h(e){return function(t){for(var o=arguments.length,n=new Array(o>1?o-1:0),r=1;r<o;r++)n[r-1]=arguments[r];return m.div({...t,style:`font-weight: bold; font-size: ${12+d*e}px; line-height: 1.2em; `+(t.style??"")},...n)}}const m={CONTAINER_STYLE:{style:"background: hsla(0, 5%, 50%, 0.09); padding: 0px 3px; border-radius: 3px; margin: 2px;"},div:l("div"),span:l("span"),ol:l("ol"),li:l("li"),table:l("table"),tr:l("tr"),td:l("td"),h1:h(1),h2:h(2),h3:h(3),h4:h(4),h5:h(5),h6:h(6),object:(e,t)=>void 0!==e?f(["object",{object:e,config:t}]):"undefined",tiny:e=>e&&"object"==typeof e?Array.isArray(e)?`[…] (${e.length})`:"{…}":m.object(e),objectSummary:function(e){let{asJSON:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{asJSON:!1};const{span:o,tiny:n,object:r}=m;if(!e||"object"!=typeof e)return r(e);if(t)try{const t=JSON.stringify(e),n=t.slice(0,50),s=n.length<t.length?"…":void 0;return o({},r(e)," ",n,s)}catch(c){}if(Array.isArray(e)){const t=e.slice(0,3);return o({},r(e)," [",...t.map(((e,r)=>o({},n(e),r===t.length-1?void 0:", "))),t.length<e.length?", …":void 0,"]")}const s=Object.entries(e),i=s.slice(0,4),a=i.map(((e,t)=>{let[r,s]=e;return o({},r,": ",n(s),t===i.length-1?void 0:", ")}));return o({},r(e)," {",...a,i.length<s.length?", …":void 0,"}")},countUniques(e){if(1===e.length)return m.object(e[0]);const t=new Map;e.forEach((e=>{const o=t.get(e)??0;t.set(e,o+1)}));const o=Array.from(t).map((e=>{let[t,o]=e;return{value:t,count:o}})).sort(((e,t)=>t.count-e.count));return m.autoTable({rows:o,header:!1})},autoTable(e){const{rows:t,header:o}=e,n={style:"vertical-align: top"},r={style:"padding: 2px 0.25em; font-weight: bold; border-bottom: 1px solid #000"},i={style:"white-space: pre; border-bottom: 1px solid #000"},a=m,c=Array.isArray(o)?o:"object"==typeof o?(0,s().uv)(o):(0,s().uv)(t[0]??{}),u=!1!==o?a.tr(n,...c.map((e=>"object"!=typeof o||Array.isArray(o)?a.td(r,String(e)):a.td(r,o[e])))):void 0,l=t.map((e=>a.tr(n,...c.map((t=>a.td(i,a.object(e[t])))))));return 0===l.length&&l.push(a.tr(n,a.td(i,"(no rows)"))),a.table(a.CONTAINER_STYLE,u,...l)},maxHeight(e){const t="number"==typeof e?`${e}px`:e;for(var o=arguments.length,n=new Array(o>1?o-1:0),r=1;r<o;r++)n[r-1]=arguments[r];return m.div({style:`max-height: ${t}; overflow: auto;`},...n)}};function p(e){let t=globalThis.devtoolsFormatters||[];e.id&&(t=t.filter((t=>t.id!==e.id))),t.unshift(function(e){const{canFormat:t,header:o,body:n,hasBody:r,id:s}=e;return{id:s,header(e,n){try{return null!=n&&n.useDefaultFormatter?null:t(e)?o(e,n):null}catch(r){throw console.error("DevTools Formatter.header() error",r),r}},hasBody(e,o){try{return(null==o||!o.useDefaultFormatter)&&!!t(e)&&r(e,o)}catch(n){throw console.error("DevTools Formatter.hasBody() error",n),n}},body(e,t){try{return n(e,t)}catch(o){throw console.error("DevTools Formatter.body() error",o),o}}}}(e)),globalThis.devtoolsFormatters=t}const g=Symbol("DevToolsHyperscriptPassthrough");function f(e){const t=e;return t&&(t[g]=!0),t}p({canFormat:e=>Boolean(e&&"object"==typeof e&&g in e),header:e=>e,hasBody:()=>!1,body:()=>null});p({canFormat:e=>Boolean(e&&e instanceof o(244641).c9),header(e){const{span:t,CONTAINER_STYLE:o}=m;return t(o,t({},e.toFormat("yyyy-MM-dd HH:mm:ss.SSS (ZZ)")))},hasBody:()=>!0,body(e){const{table:t,tr:o,td:n,CONTAINER_STYLE:r}=m;return t(r,o({},n({},"time zone:"),n({},e.zoneName)),o({},n({},"relative:"),n({},e.toRelative({round:!1})??"")),o({},n({},"ISO:"),n({},e.toISO())),o({},n({},"since epoch (ms):"),n({},e.toMillis().toString())))}})},628182:(e,t,o)=>{o.d(t,{U4:()=>c,nJ:()=>u,s1:()=>a});let n;function r(e){if(void 0!==n)return n;const t="undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.supports&&window.CSS.supports("padding-top","env(safe-area-inset-top)"),o=e.isIOS&&e.isMobileBrowser&&!e.isSafari;return n=t&&!o,n}function s(e,t){return r(e)?parseInt(window.getComputedStyle(document.documentElement).getPropertyValue(t).replace("px",""),10):0}var i=()=>o(757695);const a=44,c=i().Store.createValue({supportsNativeSafeAreaConfig:!1,top:0,bottom:0,left:0,right:0});class u extends i().Store{constructor(e,t){if(super(),this.environment=void 0,this.fullWindowEl=void 0,this.window=void 0,this.visibilityChangedResizeDeadline=0,this.updateWindowSize=()=>{this.setState(this.getCurrentState()),this.visibilityChangedResizeDeadline=0},this.updateWindowSizeDebounced=o(496603).sg(this.updateWindowSize,300),this.window=t,this.environment=e,this.environment.device.isMobileBrowser){const e=this.window.document.createElement("div");e.style.position="fixed",e.style.height="100vh",e.style.top="0px",e.style.pointerEvents="none",this.window.document.body.appendChild(e),this.fullWindowEl=e}this.window.document.addEventListener("visibilitychange",(()=>{this.visibilityChangedResizeDeadline=Date.now()+200})),this.window.addEventListener("resize",(()=>{if(0===this.instanceState.width||this.visibilityChangedResizeDeadline&&Date.now()<this.visibilityChangedResizeDeadline)return this.updateWindowSize(),void this.updateWindowSizeDebounced.cancel();this.updateWindowSizeDebounced()})),this.updateWindowSize()}getSafePaddingTopCSS(e){return`calc(${e}px + ${this.state.paddingTopCSS})`}getSafePaddingLeftCSS(e){return`calc(${e}px + ${this.state.paddingLeftCSS})`}getSafePaddingRightCSS(e){return`calc(${e}px + ${this.state.paddingRightCSS})`}getSafePaddingBottomCSS(e){return`calc(${e}px + ${this.state.paddingBottomCSS})`}getSafePaddingTopPx(e){return e+this.state.paddingTop}getSafePaddingLeftPx(e){return e+this.state.paddingLeft}getSafePaddingRightPx(e){return e+this.state.paddingRight}getSafePaddingBottomPx(e){return e+this.state.paddingBottom}isLandscape(){return this.state.width===this.state.largestDimension}isPortrait(){return!this.isLandscape()}getCurrentState(){const{device:e}=this.environment,t=r(e),o=e.isIOS?this.window.document.documentElement.clientWidth:this.window.innerWidth,n=e.isIOS?this.window.document.documentElement.clientHeight:this.window.innerHeight,i=this.state&&this.state.largestDimension?this.state.largestDimension:0,a=Math.max(this.window.innerWidth,this.window.innerHeight,i);let u,l,d,h,m,p,g,f;if(e.isAndroid&&e.isMobileNative&&c.state.supportsNativeSafeAreaConfig){const{top:e,bottom:t,left:o,right:n}=c.state;u=e,l=o,d=n,h=t,m=`${e}px`,p=`${o}px`,g=`${n}px`,f=`${t}px`}else u=function(e){return s(e,"--safe-area-inset-top")}(e),l=function(e){return s(e,"--safe-area-inset-left")}(e),d=function(e){return s(e,"--safe-area-inset-right")}(e),h=function(e){return s(e,"--safe-area-inset-bottom")}(e),m=t?"env(safe-area-inset-top)":"0px",p=t?"env(safe-area-inset-left)":"0px",g=t?"env(safe-area-inset-right)":"0px",f=t?"env(safe-area-inset-bottom)":"0px";if(e.isMobileBrowser){this.window.document.body.style.height=`${window.innerHeight}px`;const e=this.window.document.querySelector("html");e&&(e.style.height=`${window.innerHeight}px`);const t=this.window.document.querySelector("body");t&&(t.style.height=`${window.innerHeight}px`);const o=this.window.document.querySelector("#notion-app");o&&o instanceof HTMLElement&&(o.style.height=`${window.innerHeight}px`)}return document.documentElement.style.setProperty("--full-viewport-height",`${n}px`),{width:o,height:n,paddingLeft:l,paddingTop:u,paddingBottom:h,paddingRight:d,paddingTopCSS:m,paddingLeftCSS:p,paddingRightCSS:g,paddingBottomCSS:f,largestDimension:a}}}},662303:(e,t,o)=>{o.d(t,{D:()=>i,J:()=>a,locale:()=>c});const n={locale:o(402390).q,messages:{},routes:{}};function r(){const e=window.LOCALE_SETUP;return e?{value:e}:{error:!0}}let s=n;if("undefined"!=typeof window&&"undefined"!=typeof navigator){const e=Boolean(window.__isElectron),t=/ReactNative/.test(navigator.userAgent)||/MobileNative/.test(navigator.userAgent);if(t&&/WebKit/.test(navigator.userAgent)){const e=r();e.error||(s=e.value)}else if(t){const e=function(){const e=r();if(e.error)return e;const t="ko"===e.value.locale.split("-")[0];for(const o of window.navigator.languages){const r=o.split("-")[0];if(t){if("en"===r)return{value:n};if("ko"===r)return e}if(e.value.locale.toLowerCase()===o.toLowerCase())return e}for(const o of window.navigator.languages){const t=o.split("-")[0];if(e.value.locale.split("-")[0].toLowerCase()===t.toLowerCase())return e}return{error:!0}}();e.error||(s=e.value)}else if(e){const e=function(e,t){if(e.error)return{value:n};if(t===e.value.locale)return e;if("en-US"===t)return{value:n};for(const o of window.navigator.languages){const t=o.split("-")[0];if("en"===t)return{value:n};if(e.value.locale.split("-")[0]===t)return e}return{error:!0}}(r(),o(994310).A.get("preferredLocale"));e.error||(s=e.value)}else{const e=r();e.error||(s=e.value)}}const i=s.messages,a=s.routes,c=s.locale},672993:(e,t,o)=>{o.r(t),o.d(t,{Store:()=>a});var n=()=>o(624919),r=()=>o(992202),s=()=>o(427652);const i=s().jY;class a{constructor(e){this.debug=!1,this.instanceState=void 0,this.emitter=new(o(592328).A),this.debugName=void 0,this.instanceState=this.getInitialState(),r().logStoreCreated(this,this.instanceState),this.debugName=e??this.constructor.name,(0,s().nH)(this,"store")}getState(){return o(60053).AutoListener.logStoreAccess(this,this.instanceState),this.instanceState}get state(){return this.getState()}setState(e){(0,o(821062).A)(this.instanceState,e)||(this.instanceState=e,r().logStoreSet(this,e),this.emit())}reset(){this.setState(this.getInitialState())}update(e){this.setState(e(this.state))}emit(){r().logStoreEmit(this),this.debug&&(console.groupCollapsed("emit:",this),console.log("store state:",this.instanceState),console.trace(),console.groupEnd()),this.emitter.emit(this)}addListener(e,t){const o=i?this.emitter.listenerCount():0;if(this.emitter.addListener(e),i){const e=this.emitter.listenerCount();t&&e-o>0&&this.debugName&&(0,s().EZ)(this.debugName,t)}}removeListener(e,t){this.emitter.removeListener(e),i&&t&&this.debugName&&(0,s().m)(this.debugName,t)}listenerCount(){return this.emitter.listenerCount()}waitUntil(e){return e()?Promise.resolve(void 0):new Promise((t=>{const o=()=>{e()&&(this.removeListener(o),t(void 0))};this.addListener(o)}))}getInitialState(){return{}}static createValue(e,t){return new(a.createClass(e,t))}static createClass(e,t){const o=class extends a{constructor(){super((null==t?void 0:t.name)||"StoreWithInitialState"),this.debug=Boolean(null==t?void 0:t.debug)}getInitialState(){return e instanceof Function?e():e}};return null!=t&&t.name&&Object.defineProperty(o,"name",{value:t.name}),o}}a.debug=!0,(0,n().EX)({canFormat:e=>Boolean(e&&e instanceof a),header(e){const{span:t,object:o,objectSummary:r,CONTAINER_STYLE:s}=n().iY,i=e.instanceState;return t(s,o(e,{useDefaultFormatter:!0}),"(",i&&"object"==typeof i?r(i):o(i),")")},hasBody:()=>!1,body:()=>null})},711740:(e,t,o)=>{o.d(t,{L:()=>n});class n{constructor(e){this.inMemoryRecordCache=void 0,this._persistedRecordCache=void 0,this.inMemoryRecordCache=e.inMemoryRecordCache,this._persistedRecordCache=e.persistedRecordCache}get persistedRecordCache(){return this._persistedRecordCache}disablePersistedRecordCache(){this._persistedRecordCache=void 0}}},721338:(e,t,o)=>{o.d(t,{P:()=>n});class n extends(()=>o(757695))().Store{getInitialState(){return{isComposing:!1}}}},721908:(e,t,o)=>{o.d(t,{d:()=>i});o(16280);var n=o(296540),r=()=>o(496603),s=()=>o(355543);function i(e,t){const o=(0,n.useRef)(e),i=(0,s().w)((()=>({fn:r().nF((function(){return o.current(...arguments)}),t),wait:t})));if((0,n.useEffect)((()=>{o.current=e}),[e]),i.wait!==t)throw new Error(`You must never change debounce wait (initial=${i.wait}, attempted=${t})`);return i.fn}},725252:(e,t,o)=>{o.d(t,{L:()=>r});o(16280);var n=o(296540);function r(e){const{value:t,validateLoaded:o}=e,r=e.waitTimeInMs||500,[s,i]=(0,n.useState)(!0),a=(0,n.useRef)(),c=void 0!==t&&(!o||s&&o(t));return(0,n.useEffect)((()=>(a.current=window.setTimeout((()=>{i(!1),a.current=void 0}),r),()=>{a.current&&(clearTimeout(a.current),a.current=void 0)})),[r]),(0,n.useEffect)((()=>{c&&a.current&&(i(!1),clearTimeout(a.current),a.current=void 0)}),[c]),void 0!==t&&c?{status:"resolved",value:t}:s?{status:"pending",value:void 0}:{status:"rejected",value:void 0,error:new Error("Reached end of timeout before value became defined.")}}},757695:(e,t,o)=>{o.d(t,{Store:()=>r});let n;n=(0,o(624184).zB)(1,o(672993));const r=n.Store},790748:(e,t,o)=>{o.d(t,{b:()=>r});var n=o(296540);function r(e,t,o){const r=(0,n.useMemo)(t,o);(0,n.useLayoutEffect)((()=>null==e?void 0:e.register(r)),[r,e])}},792485:(e,t,o)=>{o.d(t,{S:()=>s});var n=o(296540),r=()=>o(452446);function s(){return(0,n.useContext)(r().r)}},804773:(e,t,o)=>{o.d(t,{Y:()=>a,e:()=>i});o(944114),o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698),o(898992),o(803949);var n=o(296540),r=o(474848);const s=(0,n.createContext)(void 0);function i(){const e=(0,n.useContext)(s),t=(0,n.useRef)(null),o=(0,n.useRef)(null),r=(0,n.useCallback)((t=>{if(!e)return;const{itemRefs:o,activeRef:n,setActiveRef:r,direction:s}=e;if("vertical"===s&&"ArrowUp"!==t.key&&"ArrowDown"!==t.key&&"Home"!==t.key&&"End"!==t.key||"horizontal"===s&&"ArrowLeft"!==t.key&&"ArrowRight"!==t.key&&"Home"!==t.key&&"End"!==t.key)return;const i=o.current;if(null===i||void 0===n)return;const{sortedElements:a,nodeToRef:c}=function(e){const t=[],o=new Map;return e.forEach((e=>{e.current&&"true"!==e.current.getAttribute("aria-disabled")&&"true"!==e.current.getAttribute("disabled")&&(t.push(e.current),o.set(e.current,e))})),{sortedElements:t.sort(((e,t)=>e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING?-1:1)),nodeToRef:o}}(i),u=a.findIndex((e=>e===n.current));let l=u;-1===u?"vertical"===s&&"ArrowDown"===t.key||"horizontal"===s&&"ArrowRight"===t.key?l=0:("vertical"===s&&"ArrowUp"===t.key||"horizontal"===s&&"ArrowLeft"===t.key)&&(l=a.length-1):"vertical"===s&&"ArrowDown"===t.key||"horizontal"===s&&"ArrowRight"===t.key?l=(u+1)%a.length:"vertical"===s&&"ArrowUp"===t.key||"horizontal"===s&&"ArrowLeft"===t.key?l=(u-1+a.length)%a.length:"Home"===t.key?l=0:"End"===t.key&&(l=a.length-1);const d=a[l],h=c.get(d);var m;h&&(r(h),null===(m=h.current)||void 0===m||m.focus(),t.preventDefault())}),[e]),i=(0,n.useCallback)((()=>{var o;if(!e)return;const{itemRefs:n,setActiveRef:r}=e;null!==(o=n.current)&&void 0!==o&&o.has(t)&&r(t)}),[e]),a=(0,n.useCallback)((n=>{if(t.current=n,o.current&&(window.cancelAnimationFrame(o.current),o.current=null),!e)return;const{itemRefs:r,activeRef:s,setActiveRef:i}=e;var a,c;n?null===(a=r.current)||void 0===a||a.add(t):(null===(c=r.current)||void 0===c||c.delete(t),void 0!==s&&s===t&&(o.current=window.requestAnimationFrame((()=>{o.current=null,void 0===s||s.current&&document.contains(s.current)||i(void 0)}))))}),[e]);return{isTabbable:!e||(e.activeRef===t||void 0===e.activeRef),itemRef:a,onKeyDown:r,onFocus:i}}function a(e){let{direction:t,children:o}=e;const[i,a]=(0,n.useState)(void 0),c=(0,n.useRef)(new Set),u=(0,n.useMemo)((()=>({itemRefs:c,activeRef:i,setActiveRef:a,direction:t})),[i,t]);return(0,r.jsx)(s.Provider,{value:u,children:o})}s.displayName="FocusNavigatorContext"},807005:(e,t,o)=>{o.d(t,{Z:()=>s,v:()=>r});const n=(0,o(446943).C)({displayName:"RestrictedContentContext",legacyContextKey:"restrictedContentContext",modernContextDefaultValue:void 0}),r=n.Provider,s=n.context},819121:(e,t,o)=>{o.d(t,{A:()=>i,p:()=>s});var n=o(296540);let r;function s(e){try{return function(){if(void 0===r)try{r=!(!CSS||!CSS.supports)&&CSS.supports("selector(:focus-visible)")}catch{r=!1}return r}()?e.matches(":focus-visible"):e.matches(":focus")}catch(t){return!1}}function i(){const e=(0,n.useRef)(null),t=(0,n.useRef)(null),[o,r]=(0,n.useState)(!1),[i,a]=(0,n.useState)(!1),c=(0,n.useCallback)((()=>{const t=e.current;t&&a(s(t))}),[]),u=(0,n.useCallback)((o=>{if(e.current=o,o){var n;const e=()=>{r(!0),a(s(o))},i=()=>{r(!1),a(s(o))};o.addEventListener("focus",e),o.addEventListener("blur",i),null===(n=t.current)||void 0===n||n.call(t),t.current=()=>{o.removeEventListener("focus",e),o.removeEventListener("blur",i)}}else{var i;null===(i=t.current)||void 0===i||i.call(t),t.current=null}c()}),[c]);return(0,n.useEffect)((()=>()=>{var e;return null===(e=t.current)||void 0===e?void 0:e.call(t)}),[]),(0,n.useEffect)((()=>{if(o)return document.addEventListener("pointerdown",c),document.addEventListener("keydown",c),()=>{document.removeEventListener("pointerdown",c),document.removeEventListener("keydown",c)}}),[o,c]),[u,i]}},833744:(e,t,o)=>{o.d(t,{L:()=>r});var n=o(296540);function r(e){let{closeHandler:t,ref:o,active:r,excludedSelectors:s,ignoreKeydown:i}=e;const a=(0,n.useRef)(void 0);(0,n.useEffect)((()=>{if(r){const e=e=>{0===e.button&&e.target instanceof Node?a.current=e.target:a.current=void 0},n=e=>{const n=o.current,r=e.target,i=a.current;if(a.current=void 0,n&&r){for(const e of s||[]){const t=document.querySelectorAll(e);for(let e=0;e<t.length;++e){if(t[e].contains(r))return}}i&&(n===i||n.contains(i))||n===r||n.contains(r)||t()}},r=e=>{i||"Esc"!==e.key&&"Escape"!==e.key||t()};return window.addEventListener("mousedown",e),window.addEventListener("click",n),window.addEventListener("keydown",r),()=>{window.removeEventListener("mousedown",e),window.removeEventListener("click",n),window.removeEventListener("keydown",r)}}}),[t,o,r,s,i])}},852507:(e,t,o)=>{o.d(t,{l:()=>s,w:()=>i});var n=o(296540),r=()=>o(17022);function s(e){const t=(0,n.useRef)(!1);!1===t.current&&(t.current=!0,r().e.withListenerIgnored((()=>e())))}function i(e){const t=(0,n.useRef)(!1);(0,n.useEffect)((()=>{if(!1===t.current)return t.current=!0,e()}),[e])}},872994:(e,t,o)=>{o.d(t,{e:()=>i,y:()=>a});var n=o(296540),r=o(474848);const s=(0,o(446943).C)({modernContextDefaultValue:void 0,displayName:"ContentEditableContext",legacyContextKey:"contentEditableContext"});function i(e){const{children:t,...o}=e,i=(0,n.useMemo)((()=>"void"in o?void 0:o),Object.values(o));return(0,r.jsx)(s.Provider,{value:i,children:t})}function a(){return(0,n.useContext)(s.context)}},905343:(e,t,o)=>{o.d(t,{A:()=>n});o(944114),o(898992),o(672577);const n=new class{constructor(){this.traces=void 0,this.traces=[]}addTrace(e){this.traces.find((t=>t.type===e.type&&t.name===e.name&&t.start===e.start&&t.end===e.end))||this.traces.push(e)}}},906510:(e,t,o)=>{o.d(t,{S3:()=>a});o(944114),o(898992),o(803949);class n{mark(e){}onMark(e){}increment(e,t,o){}add(e,t,o){}getMetrics(){return{metricTotals:{},metrics:{}}}}class r{constructor(){this.metricTotals={},this.metrics={},this.callbacks=[],this.subNameFilter=e=>e}mark(e){this.callbacks.forEach((t=>t(e))),this.flush(e,this.metricTotals,this.metrics),this.metricTotals={},this.metrics={}}onMark(e){this.callbacks.push(e)}increment(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;this.add(e,t,{count:o,sum:o})}add(e,t,o){this.metricTotals[e]=this.metricTotals[e]||{count:0,sum:0},this.metricTotals[e].count+=o.count,this.metricTotals[e].sum+=o.sum;const n=this.subNameFilter(t);this.metrics[e]=this.metrics[e]||{},this.metrics[e][n]=this.metrics[e][n]||{count:0,sum:0},this.metrics[e][n].count+=o.count,this.metrics[e][n].sum+=o.sum}setSubNameFilter(e){this.subNameFilter=e}}class s extends r{constructor(){super(...arguments),this.accumulatedMetrics={metricTotals:{},metrics:{}}}flush(e,t,o){for(const[n,r]of Object.entries(t))this.accumulatedMetrics.metricTotals[n]=this.accumulatedMetrics.metricTotals[n]||[],this.accumulatedMetrics.metricTotals[n].push({mark:e,data:r});for(const[n,r]of Object.entries(o))this.accumulatedMetrics.metrics[n]=this.accumulatedMetrics.metrics[n]||[],this.accumulatedMetrics.metrics[n].push({mark:e,data:r})}getMetrics(){return this.accumulatedMetrics}}const i="undefined"!=typeof window&&"true"===localStorage.getItem("NotionPerformanceCounter.debug");const a=function(){if(i){const e=new s,t=/[0-9a-f]{8}-?[0-9a-f]{4}-?[0-9a-f]{4}-?[0-9a-f]{4}-?[0-9a-f]{12}/gi;return e.setSubNameFilter((e=>e.replace(t,"UUID"))),e}return new n}();(0,o(604341).exposeDebugValue)("NotionPerformanceCounter",a)},908006:(e,t,o)=>{o.r(t),o.d(t,{default:()=>c});o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698),o(581454);var n=()=>o(496603),r=()=>o(502800),s=()=>o(250454);function i(e,t,o){e.set(t,(e.get(t)??0)+o)}function a(e,t){return t[1]-e[1]}const c=new class{constructor(){this.debug=!1,this.debugStatsPerFlush=void 0,this.debugLogMinComponentRerenders=void 0,this.pauseCount=0,this.renderIsQueued=!1,this.pendingAnimationFrame=void 0,this.computedStoreQueue=new Set,this.computedStoreQueueDebugNameCounters=new Map,this.componentRenderQueue=new Set,this.flushQueue=new Set,this.renderRemovedQueue=new Set,this.currentlyRendering=new Set,this.flushSync=void 0,this.maybeInstrumentStoreRecompute=(e,t)=>{if(!this.debug||!t)return void e();const o=performance.now();e();const n=performance.now()-o,{debugName:r}=e,s=t.get(r);void 0===s?t.set(r,n):t.set(r,s+n)},this.processRenderQueueCallback=async e=>{if(!this.renderRemovedQueue.has(e))try{this.currentlyRendering.add(e),await e()}catch(t){this.throttledLog({level:"error",from:"RenderQueue",type:"componentRender",error:(0,r().convertErrorToLog)(t)})}finally{this.currentlyRendering.delete(e)}},this.throttledLog=n().nF((e=>{console.info(e)}),5e3),this.flush=this.flush.bind(this)}isPaused(){return this.pauseCount>0}enqueueComputedStoreRecompute(e){this.debug&&!this.computedStoreQueue.has(e)&&i(this.computedStoreQueueDebugNameCounters,e.debugName,1),this.computedStoreQueue.add(e),this.enqueueFlush()}enqueueComponentRender(e){this.componentRenderQueue.add(e),this.enqueueFlush()}enqueueFlush(){this.pendingAnimationFrame&&document.hidden&&(window.cancelAnimationFrame(this.pendingAnimationFrame),this.renderIsQueued=!1,this.pendingAnimationFrame=void 0),this.renderIsQueued||0!==this.pauseCount||(this.renderIsQueued=!0,this.flushAfterAnimationFrame())}flushAfterAnimationFrame(){document.hidden?Promise.resolve().then(this.flush):this.pendingAnimationFrame=window.requestAnimationFrame(this.flush)}removeRenderFromQueue(e){this.renderRemovedQueue.add(e)}afterNextFlush(e){return new Promise((t=>{this.flushQueue.add((()=>{e&&e(),t()})),this.enqueueFlush()}))}pause(){this.afterNextFlush((()=>{this.pauseCount++}))}unpause(){this.pauseCount--,0!==this.pauseCount||this.renderIsQueued||(this.renderIsQueued=!0,this.flushAfterAnimationFrame())}async flush(){try{this.flushSync||(this.flushSync=(await Promise.resolve().then(o.t.bind(o,440961,19))).flushSync);const t=this.flushSync,c=this.debug?{computedStoreRecomputes:0,componentRerenders:0,computedStoreCounters:new Map,computedStoreDurationCounters:new Map,componentRenderCounters:new Map,totalStoreRecomputeTime:0,startTime:performance.now(),storeRecomputeStart:0}:void 0;let u;s().I.getShouldCollect()&&document.hasFocus()&&(u=s().I.generateUniqueId(),s().I.start(u));do{for(;this.computedStoreQueue.size>0;){const t=this.computedStoreQueue;if(this.computedStoreQueue=new Set,this.debug&&c){const{computedStoreQueueDebugNameCounters:e}=this;this.computedStoreQueueDebugNameCounters=new Map;for(const[t,o]of e.entries())i(c.computedStoreCounters,t,o);c.storeRecomputeStart=performance.now()}for(const o of t)try{this.maybeInstrumentStoreRecompute(o,null==c?void 0:c.computedStoreDurationCounters)}catch(e){this.throttledLog({level:"error",from:"RenderQueue",type:"computedStoreRecompute",error:(0,r().convertErrorToLog)(e)})}this.debug&&c&&(c.totalStoreRecomputeTime+=performance.now()-c.storeRecomputeStart,c.computedStoreRecomputes+=t.size)}const n=this.componentRenderQueue;let s;if(this.componentRenderQueue=new Set,this.renderRemovedQueue.clear(),t((()=>{s=Promise.all(Array.from(n.values()).map(this.processRenderQueueCallback))})),s){if((await(0,o(763824).nQ)(1e4,s)).timeout){const e=[...this.currentlyRendering].map((e=>e.componentName));this.currentlyRendering.clear(),this.throttledLog({level:"error",from:"RenderQueue",type:"rerenderTimeOut",data:{miscDataToConvertToString:{componentNames:e}}})}}if(this.debug&&c){c.componentRerenders+=n.size;for(const{componentName:e}of n.values())i(c.componentRenderCounters,e,1)}const a=this.flushQueue;this.flushQueue=new Set;for(const t of a)try{t()}catch(e){this.throttledLog({level:"error",from:"RenderQueue",type:"afterNextFlush",error:(0,r().convertErrorToLog)(e)})}}while(this.componentRenderQueue.size>0||this.flushQueue.size>0||this.computedStoreQueue.size>0);if(s().I.getShouldCollect()&&u&&s().I.stop(u),this.debug&&c&&c.componentRerenders>(this.debugLogMinComponentRerenders??5)){if(console.groupCollapsed("Flushed render queue",{recomputes:c.computedStoreRecomputes,rerenders:c.componentRerenders,totalMs:Math.floor(performance.now()-c.startTime),recomputeMs:Math.floor(c.totalStoreRecomputeTime)}),c.computedStoreRecomputes>0){console.log("Recomputed stores:");const e=Object.fromEntries([...c.computedStoreCounters.entries()].sort(a));if(c.totalStoreRecomputeTime>=1)for(const[t,o]of c.computedStoreCounters)e[t]={count:o,duration:n().LI(c.computedStoreDurationCounters.get(t)??0,3)};console.table(e)}if(c.componentRerenders>0){console.log("Rerenders:");const e=Object.fromEntries([...c.componentRenderCounters.entries()].sort(a));console.table(e)}console.groupEnd(),this.debugStatsPerFlush=[...this.debugStatsPerFlush??[],c]}}finally{this.renderIsQueued=!1,this.pendingAnimationFrame=void 0}}clearDebugStats(){this.debugStatsPerFlush=void 0}getDebugStatsPerFlush(){return this.debugStatsPerFlush??[]}setDebugLogMinComponentRerenders(e){this.debugLogMinComponentRerenders=e}serializeDebugStatsPerFlush(){return this.getDebugStatsPerFlush().map((e=>({computedStoreRecomputes:e.computedStoreRecomputes,componentRerenders:e.componentRerenders,computedStoreCounters:Object.fromEntries([...e.computedStoreCounters.entries()].sort(a)),computedStoreDurationCounters:Object.fromEntries([...e.computedStoreDurationCounters.entries()].sort(a)),componentRenderCounters:Object.fromEntries([...e.componentRenderCounters.entries()].sort(a)),totalStoreRecomputeTime:e.totalStoreRecomputeTime,startTime:e.startTime,storeRecomputeStart:e.storeRecomputeStart})))}}},959180:(e,t,o)=>{o.d(t,{A:()=>s,g:()=>r});o(898992),o(354520);class n extends(()=>o(965828))().O{getMembersByDOMOrder(e){let{filter:t}=e;return super.filter(t).sort(((e,t)=>(0,o(507707).A)(e.getNode(),t.getNode())))}}function r(e){if(e){if(e instanceof Element)return e;if("getNode"in e){const t=e.getNode();if(t instanceof Element)return t}}}const s=n},965828:(e,t,o)=>{o.d(t,{O:()=>n});o(944114),o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698),o(898992),o(803949);class n{constructor(){this.members=new Set}register(e){return this.members.add(e),()=>this.unregister(e)}unregister(e){this.members.delete(e)}getSize(){return this.members.size}hasMembers(){return this.getSize()>0}find(e){for(const t of this.members)if(e(t))return t}filter(e){const t=[];for(const o of this.members)e(o)&&t.push(o);return t}forEach(e){this.members.forEach(e)}}},968526:(e,t,o)=>{o.r(t),o.d(t,{TimeSeries:()=>v,useTimeSeriesPalette:()=>b});o(898992),o(354520),o(430670),o(581454);var n=()=>o(622297),r=()=>o(774726),s=o(296540),i=()=>o(869190),a=()=>o(512062),c=()=>o(720665),u=()=>o(662303),l=()=>o(401497),d=o(474848);const h=7,m=0,p=25,g=16,f=5;const v=(0,o(810073).A)((function(e){const{width:t,height:a,layers:v,showTooltip:b,hideTooltip:S,tooltipData:y,tooltipTop:w=0,tooltipLeft:C=0,curveType:k="curveLinear",renderTooltipSubLabel:E}=e,R=(0,l().DP)(),L=(0,s.useMemo)((()=>{let t;try{t=(0,o(229907).lT)(e.startDate,e.endDate)}catch(s){return[]}const n=(0,c().$z)(e.data,(e=>e.ds)),r=Object.fromEntries(v.map((e=>[e.key,0])));return t.map((e=>{const t=n.get(e);return{ds:e,values:t?t[0].values:r}}))}),[v,e.data,e.endDate,e.startDate]),T=(0,s.useMemo)((()=>Math.max(...L.flatMap((e=>v.map((t=>{let{key:o}=t;return e.values[o]})))))),[L,v]),A=(0,s.useMemo)((()=>{const e=Math.max(0,Math.floor(Math.log10(T)));return g+10+8*e}),[T]),N=(0,s.useMemo)((()=>L.length<f?m+24:m),[L.length]),x={domain:[0,T],range:[a-p,h]},I=(0,n().A)(x),M=L.map((e=>new Date(e.ds).valueOf())),D={domain:[Math.min(...M),Math.max(...M)],range:[A,t-N]},U=(0,n().A)(D),[O,P]=function(e,t){const o=e.range[1]-e.range[0],n=t.range[0]-t.range[1],r=e.domain[0],i=e.domain[1],a=i-r,u=t.domain[1];return[(0,s.useMemo)((()=>{if(a<c().nD*(f+1))return Array.from({length:1+a/c().nD},((e,t)=>r+t*c().nD));const e=Math.floor(o/312*9),t=Math.max(1,Math.floor(a/e)),n=Math.ceil(t/c().nD)*c().nD;return Array.from({length:e},((e,t)=>r+(2*t+1)*n)).filter((e=>e<i))}),[o,a,r,i]),(0,s.useMemo)((()=>{const e=Math.floor(n/142*6),t=Math.max(1,Math.floor(u/e)+1);return Array.from({length:e},((e,o)=>(o+1)*t)).filter((e=>e<=u))}),[n,u])]}(D,x),_=(0,s.useMemo)((()=>(0,o(890479).A)((e=>new Date(e.ds).getTime())).center),[]),$=(0,s.useCallback)((e=>{const{x:t}=(0,o(834391).A)(e)||{x:0},n=U.invert(t),r=_(L,n),s=L[r],i=U(new Date(s.ds));b({tooltipData:s,tooltipLeft:i,tooltipTop:I(s.values[v[0].key])})}),[L,b,U,I,_,v]);return(0,d.jsxs)("div",{children:[(0,d.jsxs)("svg",{width:t,height:a,children:[v.map((e=>{let{key:t,stroke:n,fill:r}=e;return(0,d.jsx)(o(879060).A,{data:L,x:e=>U(new Date(e.ds).valueOf()),y:e=>I(e.values[t]),yScale:I,stroke:n,fill:r,curve:o(309852)[k]},t)})),(0,d.jsx)(o(437143).A,{width:t,height:a,fill:"transparent",onTouchStart:$,onTouchMove:$,onMouseMove:$,onMouseLeave:S}),y&&(0,d.jsxs)("g",{children:[(0,d.jsx)(r().A,{from:{x:A,y:w},to:{x:C,y:w},stroke:R.icon.tertiary,strokeWidth:1,pointerEvents:"none",strokeDasharray:"5,2"}),(0,d.jsx)(r().A,{from:{x:C,y:w},to:{x:C,y:a-p},stroke:R.icon.tertiary,strokeWidth:1,pointerEvents:"none",strokeDasharray:"5,2"}),(0,d.jsx)("circle",{cx:C,cy:w,r:5,fill:v[0].stroke,pointerEvents:"none"})]}),(0,d.jsx)(o(889065).A,{left:A,scale:I,stroke:R.icon.secondary,tickStroke:R.icon.tertiary,tickLength:10,hideZero:!0,tickValues:P,tickFormat:e=>`${e}`,tickLabelProps:()=>({dx:"-0.25em",dy:"0.25em",fontSize:12,fill:R.text.primary,textAnchor:"end"})}),(0,d.jsx)(o(940767).A,{top:a-p,scale:U,stroke:R.icon.secondary,tickStroke:R.icon.tertiary,tickValues:O,tickLength:10,tickFormat:e=>(0,i().W_)(e.valueOf(),"month_day",u().locale,"UTC"),tickLabelProps:()=>({fontSize:12,fill:R.text.primary,textAnchor:"middle"})})]}),y&&(0,d.jsx)("div",{children:(0,d.jsx)(o(966514).A,{top:w-5,left:C-4,style:{...o(534725).k,background:"#36352F",padding:6,borderRadius:3,color:"white",fontSize:12,boxShadow:"0px 4px 8px rgba(0, 0, 0, 0.04), 0px 0px 2px rgba(0, 0, 0, 0.06), 0px 0px 1px rgba(0, 0, 0, 0.04)",zIndex:109},children:(0,d.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:7},children:[(0,d.jsx)("div",{children:`On ${(0,i().W_)(o(604995).C6.isoToUnixMs(y.ds,"UTC")??Date.now(),"medium",u().locale,"UTC")}`}),v.map((e=>{let{key:t,stroke:o,renderLabel:n}=e;return(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{style:{display:"flex",gap:5,alignItems:"center"},children:[(0,d.jsx)("div",{style:{background:o,borderRadius:3,width:12,height:12}}),(0,d.jsx)("div",{children:n(y.values[t])})]}),E&&E()]},t)}))]})})})]})}));function b(){const e=(0,l().DP)();return{blueLayer:{stroke:e.blueColor,fill:"dark"===e.mode?e.palette.blue[300]:e.palette.blue[50]},yellowLayer:{stroke:a().M.light.yellow[300],fill:"dark"===e.mode?e.palette.yellow[300]:e.palette.yellow[50]}}}},992202:(e,t,o)=>{o.r(t),o.d(t,{INTERNAL_TESTING_USE_ONLY__getStateLog:()=>_,captureStack:()=>v,getForceUpdateCauses:()=>B,getSerializedDebugGraph:()=>q,isRecording:()=>P,logComponentForceUpdateScheduled:()=>T,logComponentRender:()=>A,logListenerAdded:()=>N,logListenerRemoved:()=>x,logStoreAccess:()=>L,logStoreCreated:()=>E,logStoreEmit:()=>k,logStoreSet:()=>R,logTypingLag:()=>I,renderLatestGraph:()=>H,resume:()=>U,runWithTimer:()=>w,startRecordingReactivityLog:()=>M,stop:()=>D,toggleRecordingInputLatency:()=>O,updateReactivityRecordingOptions:()=>$});o(16280),o(944114),o(517642),o(658004),o(733853),o(845876),o(432475),o(515024),o(731698),o(898992),o(672577),o(581454);var n=()=>o(697938),r=()=>o(496603),s=()=>o(534177),i=()=>o(720665),a=()=>o(496506),c=()=>o(624919),u=()=>o(272061),l=()=>o(908006);const d=30,h=10,m=25,p=/webpack-internal:\/+/g,g=/node_modules\//g,f=/\(<anonymous>\)/g;function v(e){if(!y.captureStacks)return;const t=Error.stackTraceLimit;try{var o;Error.stackTraceLimit=m;const t=new Error("capture stack frame");let r=((null===(o=t.stack)||void 0===o?void 0:o.replace(p,"").split("\n"))||[]).slice(e).map((e=>e.trim()));for(let e=r.length-1;e>=0;e--)if(n=r[e],!Boolean(n.match(g)||n.match(f))){const t=r.length-(e+1);r=r.slice(0,e+1),t>0&&r.push(`(Omitted ${t} framework frames in node_modules)`);break}return r.join("\n")}finally{Error.stackTraceLimit=t}var n}class b{constructor(){this.isActive=!1,this.captureStacks=!0,this.log=[],this.inputEvents=[],this.storeEmits=[],this.componentForceUpdates=[],this.componentRenders=[],this.storeListeners=new Map,this.latestPerformanceDebugGraph=void 0}}let S,y=new b;function w(e,t){let o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];function n(){console.info("Starting perf"),M({captureStacks:o}),setTimeout((()=>{l().default.afterNextFlush((()=>{console.info("Stopping perf"),D(),console.info("Store emits:"),function(){const e={};r().__(y.storeEmits,(t=>{let{storeName:o}=t;e[o]||(e[o]=0),e[o]++}));const t=Object.keys(e).map((t=>({storeName:t,emitCount:e[t]}))),o=r().Ul(t,(e=>{let{emitCount:t}=e;return-1*t})),n=r().di(o,0,d);console.table(n),t.length>d&&console.info(`+ ${t.length-d} more updates`)}(),console.info("Component force updates:"),function(){const e=c().iY,t=r().$z(y.componentForceUpdates,(e=>e.stack)),o=Object.entries(t),n=r().Ul(o,(e=>{let[t,o]=e;return-o.length})),s=n.map((t=>{let[o,n]=t;const r=n.length,s=n.map((e=>e.componentName)),i=n.map((e=>e.storeName));return{Count:r,Components:e.countUniques(s),Stores:e.countUniques(i),StoreUpdateStack:e.div({},o)}}));console.log(c().iY.autoTable({rows:s,header:!0})),n.length>d&&console.info(`+ ${n.length-d} more updates`);console.info("Note: an 'undefined' storeName means the component updated for some reason other than a store emit.")}(),console.info("Component renders:"),function(){const e={};r().__(y.componentRenders,(t=>{let{componentName:o}=t;e[o]||(e[o]=0),e[o]++}));const t=Object.keys(e).map((t=>({componentName:t,renderCount:e[t]}))),o=r().Ul(t,(e=>{let{renderCount:t}=e;return-1*t})),n=r().di(o,0,d);console.table(n),t.length>d&&console.info(`+ ${t.length-d} more updates`)}(),console.info("Why components rendered after stores changed:"),console.info("  To fix, remove either codepath"),function(){const e=B(),t=r().$z(y.componentForceUpdates,(t=>{var o;const n=e.get(t);return`${(null==n||null===(o=n.emitted)||void 0===o?void 0:o.stack)||t.stack}:${null==n?void 0:n.listenStack}`})),o=c().iY,n=(0,s().WP)(t).map((t=>{let[,n]=t;const r=e.get(n[0]),s=n.length,i=n.map((e=>e.componentName)),a=n.map((e=>(null==r?void 0:r.emitted.storeName)||e.storeName||"(unknown)")),c=(null==r?void 0:r.listenStack)||"(unknown)",u=(null==r?void 0:r.emitted.stack)||n[0].stack;return{Count:s,Components:o.countUniques(i),Stores:o.countUniques(a),ListenStack:o.div({},c),EmitStack:o.div({},u)}})).sort(((e,t)=>t.Count-e.Count)),i=n.slice(0,h),a={Count:"Count",Components:"Component",Stores:"Store",ListenStack:o.div({},o.div({},"ListenStack"),o.div({style:"white-space: auto"},"One (of possibly many) reason(s) why this component is subscribed to this store")),EmitStack:o.div({},o.div({},"EmitStack"),o.div({style:"white-space: auto"},"One (of possibly many) trigger(s) that caused the component's listener on this store to be woken up"))};console.info(o.autoTable({rows:i,header:a})),i.length<n.length&&(console.groupCollapsed(`+ ${n.length-h} more updates with causes`),console.info(o.autoTable({rows:n.slice(h),header:a})),console.groupEnd());const u=q();y.latestPerformanceDebugGraph=u,console.log('Performance debug graph (visualize this with "__console.performanceHelpers.renderLatestGraph()" or "notion ts-node src/tools/renderPerformanceDebugGraph.ts"):',u)}(),console.info("Input latency:"),K()}))}),t)}0===e?n():setTimeout(n,e)}function C(e){return y.isActive&&y.log.push(e),e}function k(e){y.isActive&&y.storeEmits.push(C({type:"store.emit",store:e,storeName:Y(e),stack:v(3)}))}function E(e,t){y.isActive&&C({type:"store.created",store:e,state:t,stack:v(0)})}function R(e,t){y.isActive&&C({type:"store.set",store:e,state:t,stack:v(4)})}function L(e,t){y.isActive&&C({type:"store.access",store:e,listener:t,stack:v(0)})}function T(e,t){y.isActive&&y.componentForceUpdates.push(C({type:"component.forceUpdate",component:e,componentName:"componentName"in e?e.componentName:e.constructor.displayName||e.constructor.name,store:t,storeName:null==t?void 0:t.constructor.name,stack:v(2)}))}function A(e){y.isActive&&y.componentRenders.push(C({type:"component.render",componentName:e.constructor.displayName||e.constructor.name,stack:v(2)}))}function N(e,t,o){if(y.isActive){const n=C({type:"store.listeners.added",store:e,listener:t,stack:v(o)});let r=y.storeListeners.get(e);r||(r=new Map,y.storeListeners.set(e,r)),r.has(t)||r.set(t,n.stack)}}function x(e,t){if(y.isActive){C({type:"store.listeners.removed",store:e,listener:t,stack:v(0)});const o=y.storeListeners.get(e);if(!o)return;o.delete(t),0===o.size&&y.storeListeners.delete(e)}}function I(e,t){var o;y.isActive&&(o={type:"input.latency",metricType:e,timeMs:t},y.isActive&&y.inputEvents.push(o))}function M(e){y=new b,y.captureStacks=e.captureStacks,y.isActive=!0}function D(){y.isActive=!1}function U(){y.isActive=!0}function O(){if(y.isActive)return clearTimeout(S),S=void 0,D(),void K();console.info(`Recording input latency for ${i().Xb}ms (press Ctrl+Alt+Command+M again to end)`),M({captureStacks:!1}),clearTimeout(S),S=window.setTimeout((()=>{D()}),i().Xb)}function P(){return y.isActive}function _(){if(!y.isActive)throw new Error("performanceHelpers.start() must be called before getStateLog()");return y.log.slice()}function $(e){if(!y.isActive)throw new Error("performanceHelpers.start() must be called before getStateLog()");y.captureStacks=e.captureStacks}function F(e,t){return`${t}\n\nComputedStore listener:\n${e}`}function j(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set;return function*(){if(!("autoListener"in e))return;const o=e.autoListener;for(const[n,r]of y.storeListeners){if(t.has(n))continue;if(!r.has(o))continue;t.add(n);const s=y.storeEmits.find((e=>e.store===n));if(!s)continue;const i=r.get(o);yield{computedStore:e,store:n,storeEmit:s,listenStack:i,seen:t}}}()}function W(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:new Set;for(const{store:n}of j(e,o)){let r=t.get(n);r||(r=new Set,t.set(n,r)),r.add(e),n instanceof a().ComputedStore&&W(n,t,o)}}function V(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set;const o=new Map;for(const{store:n,listenStack:r,storeEmit:s}of j(e,t))if(n instanceof a().ComputedStore){const e=V(n,t);for(const[t,n]of e.entries()){let e=r;e&&n.stack&&(e=F(e,n.stack)),o.set(t,{stack:e,deepEmit:s})}}else o.set(n,{stack:r,deepEmit:s});return o}function*z(){for(const t of y.componentForceUpdates){var e;const{component:o,store:n}=t;if(!n)continue;const r="autoListener"in o?o.autoListener:o,s=Q(r,o),i=(null===(e=y.storeListeners.get(n))||void 0===e?void 0:e.get(r))||`No listen stack available for listener ${s}`,a=y.storeEmits.find((e=>e.store===n));a&&(yield{forceUpdate:t,component:o,store:n,listenStack:i,storeEmit:a})}}function q(){const e=new Map,t=new Map;for(const{component:o,store:n}of z()){let r=t.get(n);r||(r=new Set,t.set(n,r)),r.add(o),n instanceof a().ComputedStore&&W(n,e)}return(0,u().J)(t,e)}function B(){const e=new Map;for(const t of z()){const{store:o,forceUpdate:n}=t;let{listenStack:r,storeEmit:s}=t;if(s.store instanceof a().ComputedStore){const e=V(s.store);for(const[t,o]of e){s={type:"store.emit",store:o.deepEmit.store,stack:o.stack&&s.stack?`${o.stack}\n\nThen ComputedStore emitted:\n${s.stack}`:void 0,storeName:`${o.deepEmit.storeName} via ${s.storeName}`},o.stack&&(r=F(r,o.stack));break}}e.set(n,{listeningTo:o,listenStack:r,emitted:s})}return e}function K(){const e=r().$z(y.inputEvents,(e=>e.metricType)),t=Object.entries(e).map((e=>{let[t,o]=e;const n=o.map((e=>e.timeMs)),s=r().Ul(n);return{type:t,count:n.length,min:s[0],max:s[s.length-1],mean:parseFloat(r().i2(n).toFixed(2)),p50:s[Math.floor(.5*s.length)],p75:s[Math.floor(.75*s.length)],p95:s[Math.floor(.95*s.length)]}}));console.table(r().Ul(t,"type"))}function Q(e,t){return"debugName"in e?e.debugName:`${e.constructor.name} of ${"debugName"in t?t.debugName:t.constructor.name}`}function H(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!y.latestPerformanceDebugGraph)throw new Error("No latest performance debug graph exists! Please use runWithTimer() first.");{const t=(0,n().E)(y.latestPerformanceDebugGraph,e);window.open(`https://dreampuf.github.io/GraphvizOnline/#${encodeURIComponent(t)}`,"_blank")}}function Y(e){return e.debugName?e.debugName:e.constructor.name}}}]);