gapi.loaded_2(function(_){var window=this;
_.Ps=function(a){return"rtl"==_.Bs(a,"direction")};_.Qs=function(a,b,c,d){this.left=a;this.top=b;this.width=c;this.height=d};_.g=_.Qs.prototype;_.g.clone=function(){return new _.Qs(this.left,this.top,this.width,this.height)};_.g.intersects=function(a){return this.left<=a.left+a.width&&a.left<=this.left+this.width&&this.top<=a.top+a.height&&a.top<=this.top+this.height};
_.g.contains=function(a){return a instanceof _.os?a.x>=this.left&&a.x<=this.left+this.width&&a.y>=this.top&&a.y<=this.top+this.height:this.left<=a.left&&this.left+this.width>=a.left+a.width&&this.top<=a.top&&this.top+this.height>=a.top+a.height};_.g.distance=function(a){var b=a.x<this.left?this.left-a.x:Math.max(a.x-(this.left+this.width),0);a=a.y<this.top?this.top-a.y:Math.max(a.y-(this.top+this.height),0);return Math.sqrt(b*b+a*a)};_.g.getSize=function(){return new _.rd(this.width,this.height)};
_.g.getCenter=function(){return new _.os(this.left+this.width/2,this.top+this.height/2)};_.g.ceil=function(){this.left=Math.ceil(this.left);this.top=Math.ceil(this.top);this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};_.g.floor=function(){this.left=Math.floor(this.left);this.top=Math.floor(this.top);this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};
_.g.round=function(){this.left=Math.round(this.left);this.top=Math.round(this.top);this.width=Math.round(this.width);this.height=Math.round(this.height);return this};_.g.translate=function(a,b){a instanceof _.os?(this.left+=a.x,this.top+=a.y):(this.left+=a,typeof b==="number"&&(this.top+=b));return this};_.g.scale=function(a,b){b=typeof b==="number"?b:a;this.left*=a;this.width*=a;this.top*=b;this.height*=b;return this};_.Rs=function(a){return _.Bs(a,"position")};
_.Ss=function(a,b,c){if(b instanceof _.os){var d=b.x;b=b.y}else d=b,b=c;a.style.left=_.Is(d,!1);a.style.top=_.Is(b,!1)};_.Ts=function(a,b){a=a.style;"opacity"in a?a.opacity=b:"MozOpacity"in a?a.MozOpacity=b:"filter"in a&&(a.filter=b===""?"":"alpha(opacity="+Number(b)*100+")")};_.Us=function(){if(_.Fd){var a=/Windows NT ([0-9.]+)/;return(a=a.exec(_.Jc()))?a[1]:"0"}return _.Ed?(a=/1[0|1][_.][0-9_.]+/,(a=a.exec(_.Jc()))?a[0].replace(/_/g,"."):"10"):_.Id?(a=/Android\s+([^\);]+)(\)|;)/,(a=a.exec(_.Jc()))?a[1]:""):_.Jd||_.Kd||_.Ld?(a=/(?:iPhone|CPU)\s+OS\s+(\S+)/,(a=a.exec(_.Jc()))?a[1].replace(/_/g,"."):""):""}();var Vs;Vs=function(a){return(a=a.exec(_.Jc()))?a[1]:""};_.Ws=function(){if(_.yh)return Vs(/Firefox\/([0-9.]+)/);if(_.yd||_.zd||_.xd)return _.Vd;if(_.Ch){if(_.bd()||_.cd()){var a=Vs(/CriOS\/([0-9.]+)/);if(a)return a}return Vs(/Chrome\/([0-9.]+)/)}if(_.Dh&&!_.bd())return Vs(/Version\/([0-9.]+)/);if(_.zh||_.Ah){if(a=/Version\/(\S+).*Mobile\/(\S+)/.exec(_.Jc()))return a[1]+"."+a[2]}else if(_.Bh)return(a=Vs(/Android\s+([0-9.]+)/))?a:Vs(/Version\/([0-9.]+)/);return""}();
var Xs,Ys,at;_.Zd.prototype.uH=_.pb(2,function(){return _.ge(this.getWindow())});Xs=function(a,b){return new _.os(a.x-b.x,a.y-b.y)};Ys=function(a){var b=_.$d(a),c=_.Bs(a,"position"),d=c=="fixed"||c=="absolute";for(a=a.parentNode;a&&a!=b;a=a.parentNode)if(a.nodeType==11&&a.host&&(a=a.host),c=_.Bs(a,"position"),d=d&&c=="static"&&a!=b.documentElement&&a!=b.body,!d&&(a.scrollWidth>a.clientWidth||a.scrollHeight>a.clientHeight||c=="fixed"||c=="absolute"||c=="relative"))return a;return null};
_.Zs=function(a){for(var b=new _.ms(0,Infinity,Infinity,0),c=_.ae(a),d=c.ub().body,e=c.ub().documentElement,f=_.ps(c.Bc);a=Ys(a);)if((!_.Cd||a.clientHeight!=0||a!=d)&&a!=d&&a!=e&&_.Bs(a,"overflow")!="visible"){var h=_.Ds(a),k=new _.os(a.clientLeft,a.clientTop);h.x+=k.x;h.y+=k.y;b.top=Math.max(b.top,h.y);b.right=Math.min(b.right,h.x+a.clientWidth);b.bottom=Math.min(b.bottom,h.y+a.clientHeight);b.left=Math.max(b.left,h.x)}d=f.scrollLeft;f=f.scrollTop;b.left=Math.max(b.left,d);b.top=Math.max(b.top,f);
c=c.uH();b.right=Math.min(b.right,d+c.width);b.bottom=Math.min(b.bottom,f+c.height);return b.top>=0&&b.left>=0&&b.bottom>b.top&&b.right>b.left?b:null};_.$s=function(a){var b=_.Ds(a);a=_.Ks(a);return new _.Qs(b.x,b.y,a.width,a.height)};at=function(a,b){return(b&8&&_.Ps(a)?b^4:b)&-9};
_.bt=function(a,b,c,d,e,f,h,k,l){var m;if(m=c.offsetParent){var n=m.tagName=="HTML"||m.tagName=="BODY";if(!n||_.Rs(m)!="static"){var p=_.Ds(m);if(!n){n=_.Ps(m);var q;if(q=n){q=_.Dh&&_.Dc(_.Ws,10)>=0;var r=_.Md&&_.Dc(_.Us,10)>=0,w=_.Ch&&_.Dc(_.Ws,85)>=0;q=_.Bd||q||r||w}n=q?-m.scrollLeft:n&&_.Bs(m,"overflowX")!="visible"?m.scrollWidth-m.clientWidth-m.scrollLeft:m.scrollLeft;p=Xs(p,new _.os(n,m.scrollTop))}}}m=p||new _.os;p=_.$s(a);if(n=_.Zs(a))w=new _.Qs(n.left,n.top,n.right-n.left,n.bottom-n.top),
n=Math.max(p.left,w.left),q=Math.min(p.left+p.width,w.left+w.width),n<=q&&(r=Math.max(p.top,w.top),w=Math.min(p.top+p.height,w.top+w.height),r<=w&&(p.left=n,p.top=r,p.width=q-n,p.height=w-r));q=_.ae(a);n=_.ae(c);q.ub()!=n.ub()&&(q=q.ub().body,n=_.Hs(q,n.getWindow()),n=Xs(n,_.Ds(q)),p.left+=n.x,p.top+=n.y);a=at(a,b);b=p.left;a&4?b+=p.width:a&2&&(b+=p.width/2);b=new _.os(b,p.top+(a&1?p.height:0));b=Xs(b,m);e&&(b.x+=(a&4?-1:1)*e.x,b.y+=(a&1?-1:1)*e.y);if(h)if(l)var u=l;else if(u=_.Zs(c))u.top-=m.y,u.right-=
m.x,u.bottom-=m.y,u.left-=m.x;e=u;l=b.clone();u=at(c,d);d=_.Ks(c);a=k?k.clone():d.clone();k=l;l=a;k=k.clone();l=l.clone();a=0;if(f||u!=0)u&4?k.x-=l.width+(f?f.right:0):u&2?k.x-=l.width/2:f&&(k.x+=f.left),u&1?k.y-=l.height+(f?f.bottom:0):f&&(k.y+=f.top);h&&(e?(f=k,u=l,a=0,(h&65)==65&&(f.x<e.left||f.x>=e.right)&&(h&=-2),(h&132)==132&&(f.y<e.top||f.y>=e.bottom)&&(h&=-5),f.x<e.left&&h&1&&(f.x=e.left,a|=1),h&16&&(b=f.x,f.x<e.left&&(f.x=e.left,a|=4),f.x+u.width>e.right&&(u.width=Math.min(e.right-f.x,b+
u.width-e.left),u.width=Math.max(u.width,0),a|=4)),f.x+u.width>e.right&&h&1&&(f.x=Math.max(e.right-u.width,e.left),a|=1),h&2&&(a|=(f.x<e.left?16:0)|(f.x+u.width>e.right?32:0)),f.y<e.top&&h&4&&(f.y=e.top,a|=2),h&32&&(b=f.y,f.y<e.top&&(f.y=e.top,a|=8),f.y+u.height>e.bottom&&(u.height=Math.min(e.bottom-f.y,b+u.height-e.top),u.height=Math.max(u.height,0),a|=8)),f.y+u.height>e.bottom&&h&4&&(f.y=Math.max(e.bottom-u.height,e.top),a|=2),h&8&&(a|=(f.y<e.top?64:0)|(f.y+u.height>e.bottom?128:0)),h=a):h=256,
a=h);f=new _.Qs(0,0,0,0);f.left=k.x;f.top=k.y;f.width=l.width;f.height=l.height;h=a;h&496||(_.Ss(c,new _.os(f.left,f.top)),a=f.getSize(),_.sd(d,a)||(f=a,c=c.style,_.Bd?c.MozBoxSizing="border-box":_.Cd?c.WebkitBoxSizing="border-box":c.boxSizing="border-box",c.width=Math.max(f.width,0)+"px",c.height=Math.max(f.height,0)+"px"));return h};
_.mt=function(a,b){for(var c in a)if(a[c]==b)return!0;return!1};_.nt=function(a){if(a.nodeType==1)return _.Gs(a);a=a.changedTouches?a.changedTouches[0]:a;return new _.os(a.clientX,a.clientY)};_.ot=function(a){this.ta=a;this.Aa=a.wc()};_.ot.prototype.Nv=function(){pt(this)};
var pt=function(a){var b=a.ta.kd();if(a.Aa.anchorBox&&b&&b.getIframeEl())b=_.Ds(b.getIframeEl()),a.Aa.anchorBox.left+=b.x,a.Aa.anchorBox.top+=b.y;else{b=a.Aa.anchor;if(b!="_default"&&b!="_iframe"){var c=_.vs(b);if(c)a.Aa.anchorBox=_.$s(c);else{_.Vf.error("Anchor not found in DOM: "+b+'. Falling back to "_default".');a.Aa.anchor="_default";return}}b=="_iframe"&&(b=_.ge(),a.Aa.anchorBox=new _.Qs(0,0,b.width,b.height))}a.Aa.anchor=""};_.ot.prototype.onBeforeParentOpen=_.ot.prototype.Nv;_.qt=function(a){_.ot.call(this,a)};_.y(_.qt,_.ot);_.g=_.qt.prototype;_.g.open=function(){var a=this.Aa,b=document.createElement("ins");document.getElementById(a.container).appendChild(b);b.style.display="block";_.xs(b,a.containerCss);this.ta.setSiteEl(b);this.ta.eh(b)};_.g.Pd=function(){document.getElementById(this.ta.id).style.height=this.ta.height+"px"};_.g.close=function(){_.re(this.ta.getSiteEl());_.re(this.Fh);this.Fh=null};_.g.WK=_.jb(6);
_.g.aj=function(){if(this.Fh)return this.Fh;var a=this.Aa;!a.anchorBox&&a.anchor&&pt(this);var b=this.ta.kd();if(a.anchor=="_default"&&b){var c=b.getSiteEl();c=_.$s(_.vs(c));a.anchorBox=c}if(!a.anchorBox)return _.Vf.error("No anchor box defined."),null;a=new _.os(a.anchorBox.left,a.anchorBox.top);b&&(b=_.Hs(b.getIframeEl(),window),this.To=new _.os,this.To.x=b.x,this.To.y=b.y,a.x+=b.x,a.y+=b.y,_.rt(a));this.FE=a;b=_.st(this,!0);this.Fh=document.createElement("ins");this.Fh.style.cssText=b;document.body.appendChild(this.Fh);
return this.Fh};_.st=function(a,b){var c=a.Aa;return"position: absolute !important;background-color: transparent !important;left: "+a.FE.x+"px !important;top: "+a.FE.y+"px !important;width: "+c.anchorBox.width+"px !important;height: "+c.anchorBox.height+"px !important;z-index: -10000 !important;display: "+(b?"block":"none")+" !important;"};
_.tt=function(a,b){var c=0,d=0;if(b.pageX||b.pageY)c=b.pageX,d=b.pageY;else if(b.clientX||b.clientY)c=b.target?b.target:b.srcElement,d=c.ownerDocument&&c.ownerDocument.parentWindow?c.ownerDocument.parentWindow:window,_.yd?(c=d.document.documentElement.scrollLeft,d=d.document.documentElement.scrollTop):(c=d.pageXOffset,d=d.pageYOffset),c=b.clientX+c,d=b.clientY+d;b=new _.os(c,d);a=_.$s(a);return a=new _.ms(a.top,a.left+a.width,a.top+a.height,a.left),a.contains(b)};
_.rt=function(a){var b=window,c=document.body,d=_.Ds(c);b=c.currentStyle||b.getComputedStyle(c,"");b.position&&b.position!="static"&&(a.x-=d.x,a.y-=d.y)};_.ut=function(a){var b=a.ta.kd()&&a.ta.kd().getSiteEl();b=b&&b.style.zIndex?parseInt(b.style.zIndex,10)+1:0;return Math.min(2147483647,Math.max(2E9,a.Aa.zIndex||b))};var vt,wt,xt;vt={"bottom-center":1,"bottom-end":13,"bottom-left":1,"bottom-right":5,"bottom-start":9,"left-bottom":1,"left-center":0,"left-top":0,"right-bottom":5,"right-center":4,"right-top":4,"top-center":0,"top-end":12,"top-left":0,"top-right":4,"top-start":8};wt={"bottom-center":!0,"top-center":!0};xt={"left-center":!0,"right-center":!0};
_.zt=function(a,b,c,d,e){e=e||{x:0,y:0};if(wt[b]){var f=_.Ks(a).width/2;e.x=d=="top-right"||d=="bottom-right"?e.x+f:e.x-f}wt[d]&&(f=_.Ks(c).width/2,e.x+=f);xt[b]&&(f=_.Ks(a).height/2,e.y=d=="right-bottom"||d=="left-bottom"?e.y+f:e.y-f);xt[d]&&(e.y+=_.Ks(c).height/2);_.bt(c,vt[d],a,vt[b],new _.os(e.x,e.y));d=_.it(a,function(h){if(h==document)return!1;h=_.Rs(h);return!!h&&h!="static"});c=b=0;d&&(c=_.Ds(d),b=-c.x,c=-c.y);a=a.style;parseInt(a.left,10)<b&&(a.left=b+"px");parseInt(a.top,10)<c&&(a.top=c+
"px")};_.At=function(a){_.ot.call(this,a.ta);this.nh=a;this.kF=[]};_.y(_.At,_.qt);_.g=_.At.prototype;_.g.Nv=function(){this.nh.Nv()};_.g.open=function(){this.nh.open();(this.Aa.closeClickDetection||this.Aa.hideClickDetection)&&Bt(this)};_.g.Pd=function(a){this.nh.Pd(a)};_.g.PX=function(a){this.nh.onRenderStart&&this.nh.onRenderStart(a)};_.g.close=function(){if(this.Aa.closeClickDetection||this.Aa.hideClickDetection)_.Bb(this.kF,function(a){_.Kj(a)}),this.kF=[];this.nh.close()};
var Bt=function(a){_.Bb(["click","touchstart"],(0,_.z)(function(b){this.kF.push(_.Dj(document,b,(0,_.z)(this.E7,this),!0))},a))};_.At.prototype.E7=function(a){_.tt(this.ta.getSiteEl(),a)||(this.Aa.hideClickDetection&&this.nh.show?this.nh.show(!1):this.close())};_.At.prototype.onBeforeParentOpen=_.At.prototype.Nv;_.At.prototype.open=_.At.prototype.open;_.At.prototype.onready=_.At.prototype.Pd;_.At.prototype.onRenderStart=_.At.prototype.PX;_.At.prototype.close=_.At.prototype.close;
var Mda,L7,M7;for(_.K7=function(a){return{wc:function(){return a},kd:function(){return a.openerIframe}}},Mda=function(a){(new _.ot(_.K7(a))).Nv()},L7="bubble circlepicker float hover hover-menu slide-menu".split(" "),M7=0;M7<L7.length;M7++)_.Tn[L7[M7]]=Mda;
_.GG=function(a){_.ot.call(this,a.ta);this.nh=a;this.Mt=null};_.y(_.GG,_.qt);_.GG.prototype.open=function(){this.Aa.targetPos=this.Aa.targetPos||"top-start";this.Aa.anchorPos=this.Aa.anchorPos||"bottom-start";var a=this.aj(),b=this.ta.getSiteEl();b?(b.style.visibility="hidden",b.style.position="absolute",a.parentNode.appendChild(b)):this.nh.open()};
_.GG.prototype.Pd=function(){if(this.Aa.closeClickDetection){var a=this,b=function(e){_.tt(a.ta.getSiteEl(),e)||(a.ta.close(),a.Mt=null)};document.j7?(document.j7("click",b),this.Mt=function(){document.removeEventListener("click",b,!1)}):document.attachEvent&&(document.attachEvent("onclick",b),this.Mt=function(){document.detachEvent("onclick",b)})}var c=document.getElementById(this.ta.id),d=this.ta.getSiteEl();c.style.height=this.ta.height+"px";(c=this.aj())&&_.zt(d,this.Aa.targetPos,c,this.Aa.anchorPos,
{x:this.Aa.leftOffset||0,y:this.Aa.topOffset||0});d.style.visibility="visible"};_.GG.prototype.close=function(){this.nh.close();this.Mt&&this.Mt()};_.GG.prototype.open=_.GG.prototype.open;_.GG.prototype.onready=_.GG.prototype.Pd;_.GG.prototype.close=_.GG.prototype.close;_.HG=function(a){_.ot.call(this,a);this.Na=document.createElement("div")};_.y(_.HG,_.qt);_.g=_.HG.prototype;_.g.create=function(a){var b={position:"absolute",top:"-10000px",zIndex:_.ut(this)};this.Aa.width&&(b.width=this.Aa.width+"px");for(var c in b)this.Na.style[c]=b[c];(a||document.body).appendChild(this.Na)};_.g.open=function(a){this.ta.Ph("updateContainer",(0,_.z)(this.rl,this));this.create(a);this.ta.eh(this.Na);this.ta.setSiteEl(this.Na)};
_.g.Pd=function(){var a=IG(this);if(a){var b=document.getElementById(this.ta.getId());b.style.height=a.height+"px";b.style.width=a.width+"px";this.ta.width=a.width;this.ta.height=a.height;b.style.boxShadow="0 4px 16px rgba(0, 0, 0, 0.3)"}};_.g.close=function(){this.Na.parentNode&&this.Na.parentNode.removeChild(this.Na)};
_.g.rl=function(a,b,c){var d=this.ta.getSiteEl();d&&(a?(this.Bi(b,c),d.style.opacity=0,d.style.display="",window.setTimeout((0,_.z)(function(){JG(d,!0);d.style.opacity=1},this),0)):(d.style.display="none",JG(d,!1),d.style.opacity=0))};var JG=function(a,b){for(var c=0;c<KG.length;c++)a.style[KG[c]]=b?"opacity .13s linear":""};
_.HG.prototype.Bi=function(a,b){var c=this.ta.kd();a+=10;b+=10;c&&(c=_.Hs(c.getSiteEl(),window),a+=c.x,b+=c.y);if(c=IG(this)){var d=_.ge(window),e=_.qs(document);if(d.width&&(a=Math.min(a,d.width+e.x-c.width-8),b+c.height>d.height+e.y-8)){b-=20+c.height;var f=window,h=f.document;d=0;if(h){d=h.body;var k=h.documentElement;if(k&&d)if(f=_.fe(f).height,_.he(h)&&k.scrollHeight)d=k.scrollHeight!=f?k.scrollHeight:k.offsetHeight;else{h=k.scrollHeight;var l=k.offsetHeight;k.clientHeight!=l&&(h=d.scrollHeight,
l=d.offsetHeight);d=h>f?h>l?h:l:h<l?h:l}else d=0}b=Math.max(b,Math.min(e.y+1,d-c.height))}}c=this.ta.getSiteEl();c.style.left=a+"px";c.style.top=b+"px"};var IG=function(a){return a.ta.width&&a.ta.height?{width:a.ta.width,height:a.ta.height}:(a=a.ta.getIframeEl())&&a.offsetWidth&&a.offsetHeight?{width:a.offsetWidth,height:a.offsetHeight}:null},KG=["transition","WebkitTransition","MozTransition","OTranstion","msTransition"];_.HG.prototype.open=_.HG.prototype.open;_.HG.prototype.onready=_.HG.prototype.Pd;
_.HG.prototype.close=_.HG.prototype.close;_.LG=function(a){_.ot.call(this,a)};_.y(_.LG,_.qt);_.LG.prototype.open=function(){var a=document.createElement("div");_.xs(a,{top:"-10000px",position:"absolute",zIndex:_.ut(this)});this.aj().parentNode.appendChild(a);this.ta.setSiteEl(a);this.ta.eh(a)};
_.LG.prototype.Pd=function(){var a=document.getElementById(this.ta.id);a.style.height=this.ta.height+"px";a.style.width=this.ta.width+"px";a.style.boxShadow="0 4px 16px rgba(0, 0, 0, 0.3)";var b=this.ta.getSiteEl();b.style.lineHeight=0;var c=this.aj(),d=this.Aa.targetPos||"top-start",e=this.Aa.anchorPos||"bottom-start",f=this.Aa.leftOffset||0,h=this.Aa.topOffset||0;_.zt(b,d,c,e,{x:f,y:h});var k=_.ge(window),l=_.qs(document),m=b.offsetLeft<l.x||b.offsetLeft+b.offsetWidth>k.width+l.x;k=b.offsetTop<
l.y||b.offsetTop+b.offsetHeight>k.height+l.y;d=MG(d,m,k);e=MG(e,m,k);_.zt(b,d,c,e,{x:f*(m?-1:1),y:h*(k?-1:1)});b.style.visibility="visible";this.uB=_.Dj(document,"mouseover",(0,_.z)(function(n){n.target===a&&this.zn&&(window.clearTimeout(this.zn),this.zn=null)},this));this.jJ=_.Dj(document,"mouseout",(0,_.z)(function(n){n.target===a&&(this.zn=window.setTimeout((0,_.z)(this.ta.close,this.ta),1E3))},this))};
var MG=function(a,b,c){a=a.split("-");for(var d=0;d<2;d++)b&&NG[a[d]]&&(a[d]=NG[a[d]]),c&&OG[a[d]]&&(a[d]=OG[a[d]]);return a.join("-")};_.LG.prototype.close=function(){this.uB&&(_.Kj(this.uB),_.Kj(this.jJ),this.jJ=this.uB=null);this.zn&&(window.clearTimeout(this.zn),this.zn=null);_.qt.prototype.close.call(this)};_.LG.prototype.uB=null;_.LG.prototype.jJ=null;_.LG.prototype.zn=null;var NG={end:"start",left:"right",right:"left",start:"end"},OG={top:"bottom",bottom:"top"};_.LG.prototype.open=_.LG.prototype.open;
_.LG.prototype.onready=_.LG.prototype.Pd;_.LG.prototype.close=_.LG.prototype.close;
_.Sn.hover=function(a){var b=new _.HG(_.K7(a));b.create(a.where);a.where=b.Na;a.onClose=function(){b.close()};a.onRestyle=function(c){if(c.updateContainer){var d=c.updateContainer;b.rl(d.visible,d.x,d.y)}c.width&&(b.ta.width=c.width);c.height&&(b.ta.height=c.height)};a.onCreate=function(c){b.ta=c;c.kd=function(){return a.openerIframe};c.register("_ready",(0,_.z)(b.Pd,b),_.dn);c.updateContainer=function(d,e,f){b.rl(d,e,f)}}};
var Vt;_.Ut={};Vt={};_.Wt=function(a,b){this.H0=a===_.Ut&&b||"";this.m6=Vt};_.Wt.prototype.toString=function(){return this.H0};_.Xt=function(a){return a instanceof _.Wt&&a.constructor===_.Wt&&a.m6===Vt?a.H0:"type_error:Const"};
_.EF=function(){};_.EF.prototype.next=function(){return _.FF};_.FF={done:!0,value:void 0};_.GF=function(a){return{value:a,done:!1}};_.EF.prototype.Ah=function(){return this};
_.WH=function(a){a.Fra=!0;return a};_.XH=_.WH(function(a){return typeof a==="number"});_.YH=_.WH(function(a){return typeof a==="string"});_.ZH=_.WH(function(a){return typeof a==="boolean"});
var YJ,wK,zK,CK,HK,KK,XK,eL,iL,QK,$J;_.WJ=function(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};YJ=function(a){return XJ[a]||""};_.bK=function(a){if(!_.ZJ)return $J(a);a=aK.test(a)?a.replace(aK,YJ):a;a=atob(a);for(var b=new Uint8Array(a.length),c=0;c<a.length;c++)b[c]=a.charCodeAt(c);return b};_.dK=function(a){return cK&&a!=null&&a instanceof Uint8Array};
_.eK=function(a,b){var c=a.length;if(c!==b.length)return!1;for(var d=0;d<c;d++)if(a[d]!==b[d])return!1;return!0};_.fK=function(a,b,c){return typeof Symbol==="function"&&typeof Symbol()==="symbol"?(c===void 0?0:c)&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol():b};_.kK=function(a,b){_.gK||_.hK in a||iK(a,jK);a[_.hK]|=b};_.lK=function(a,b){_.gK||_.hK in a||iK(a,jK);a[_.hK]=b};_.mK=function(a,b){a[_.hK]&=~b};_.nK=function(a){if(4&a)return 512&a?512:1024&a?1024:0};
_.oK=function(){return typeof BigInt==="function"};_.rK=function(a){return a[pK]===qK};_.tK=function(a,b){return b===void 0?a.uF!==sK&&!!(2&(a.V[_.hK]|0)):!!(2&b)&&a.uF!==sK};_.uK=function(a,b){if(typeof b!=="number"||b<0||b>=a.length)throw Error();};_.vK=function(a,b){if(typeof b!=="number"||b<0||b>a.length)throw Error();};wK=function(a){return a};
_.yK=function(a){var b=a;if((0,_.YH)(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if((0,_.XH)(b)&&!Number.isSafeInteger(b))throw Error(String(b));return xK?BigInt(a):a=(0,_.ZH)(a)?a?"1":"0":(0,_.YH)(a)?a.trim()||"0":String(a)};zK=function(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};CK=function(a){var b=a>>>0;_.AK=b;_.BK=(a-b)/4294967296>>>0};
_.EK=function(a){if(a<0){CK(-a);var b=_.Aa(_.DK(_.AK,_.BK));a=b.next().value;b=b.next().value;_.AK=a>>>0;_.BK=b>>>0}else CK(a)};_.GK=function(a,b){var c=b*4294967296+(a>>>0);return Number.isSafeInteger(c)?c:_.FK(a,b)};
_.FK=function(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else _.oK()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+HK(c)+HK(a));return c};HK=function(a){a=String(a);return"0000000".slice(a.length)+a};
_.IK=function(a){if(a.length<16)_.EK(Number(a));else if(_.oK())a=BigInt(a),_.AK=Number(a&BigInt(4294967295))>>>0,_.BK=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");_.BK=_.AK=0;for(var c=a.length,d=b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),_.BK*=1E6,_.AK=_.AK*1E6+d,_.AK>=4294967296&&(_.BK+=Math.trunc(_.AK/4294967296),_.BK>>>=0,_.AK>>>=0);b&&(b=_.Aa(_.DK(_.AK,_.BK)),a=b.next().value,b=b.next().value,_.AK=a,_.BK=b)}};_.DK=function(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};
KK=function(a,b){if(a!=null){var c;var d=(c=JK)!=null?c:JK={};c=d[a]||0;c>=b||(d[a]=c+1,a=Error(),_.WJ(a,"incident"),_.sh(a))}};_.LK=function(a){return Array.prototype.slice.call(a)};_.MK=function(a){if(a!=null&&typeof a!=="boolean")throw Error("ob`"+_.jd(a)+"`"+a);return a};_.NK=function(a){return a==null||typeof a==="string"?a:void 0};
_.PK=function(a,b,c,d){if(a!=null&&typeof a==="object"&&_.rK(a))return a;if(!Array.isArray(a))return c?d&2?((a=b[_.OK])||(a=new b,_.kK(a.V,34),a=b[_.OK]=a),b=a):b=new b:b=void 0,b;c=a[_.hK]|0;d=c|d&32|d&2;d!==c&&_.lK(a,d);return new b(a)};_.SK=function(a){var b=QK(RK);return b?a[b]:void 0};_.UK=function(a,b){var c=QK(RK),d;_.gK&&c&&((d=a[c])==null?void 0:d[b])!=null&&KK(TK,3)};
XK=function(a,b){var c=c===void 0?!1:c;if(QK(VK)&&QK(RK)&&void 0===VK){var d=a.V,e=d[RK];if(!e)return;if(e=e.FZ)try{e(d,b,WK);return}catch(f){_.sh(f)}}c&&(a=a.V,(c=QK(RK))&&c in a&&(a=a[c])&&delete a[b])};
_.$K=function(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f=[],h=a.length,k=4294967295,l=!1,m=!!(b&64),n=m?b&128?0:-1:void 0;if(!(b&1)){var p=h&&a[h-1];p!=null&&typeof p==="object"&&p.constructor===Object?(h--,k=h):p=void 0;if(m&&!(b&128)&&!e){l=!0;var q;k=((q=YK)!=null?q:wK)(k-n,n,a,p)+n}}q=void 0;for(var r=0;r<h;r++){var w=a[r];if(w!=null&&(w=c(w,d))!=null)if(m&&r>=k){var u=r-n,x=void 0;((x=q)!=null?x:q={})[u]=w}else f[r]=w}if(p)for(var A in p)h=p[A],h!=null&&(h=c(h,d))!=null&&(r=+A,w=void 0,
m&&!Number.isNaN(r)&&(w=r+n)<k?f[w]=h:(r=void 0,((r=q)!=null?r:q={})[A]=h));q&&(l?f.push(q):f[k]=q);e&&(_.lK(f,b&16761025|34),QK(RK)&&(a=_.SK(a))&&"function"==typeof _.ZK&&a instanceof _.ZK&&(f[RK]=a.V7()));return f};
_.bL=function(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return(0,_.aL)(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[_.hK]|0;return a.length===0&&b&1?void 0:_.$K(a,b,_.bL)}if(_.rK(a))return _.cL(a);if("function"==typeof _.dL&&a instanceof _.dL)return a.KE();return}return a};_.cL=function(a){a=a.V;return _.$K(a,a[_.hK]|0,_.bL)};
_.fL=function(a,b,c){var d=d===void 0?0:d;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-16760833|(b&1023)<<14)}else{if(!Array.isArray(a))throw Error("pb");e=a[_.hK]|0;4096&e&&!(2&e)&&eL();if(e&256)throw Error("rb");if(e&64)return d!==0||e&4096||_.lK(a,e|4096),a;if(c&&(e|=128,c!==a[0]))throw Error("sb");a:{c=a;e|=64;var f=c.length;if(f){var h=f-1,k=c[h];if(k!=null&&typeof k==="object"&&k.constructor===Object){b=e&128?0:-1;h-=b;if(h>=1024)throw Error("ub");for(var l in k)f=+l,f<h&&(c[f+b]=k[l],
delete k[l]);e=e&-16760833|(h&1023)<<14;break a}}if(b){l=Math.max(b,f-(e&128?0:-1));if(l>1024)throw Error("vb");e=e&-16760833|(l&1023)<<14}}}e|=64;d===0&&(e|=4096);_.lK(a,e);return a};eL=function(){KK(gL,5)};
iL=function(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[_.hK]|0;a.length===0&&c&1?a=void 0:c&2||(!b||8192&c||16&c?a=_.hL(a,c,b&&!(c&16)):(_.kK(a,34),c&4&&Object.freeze(a)));return a}if(_.rK(a))return b=a.V,c=b[_.hK]|0,_.tK(a,c)?a:_.hL(b,c);if("function"==typeof _.dL&&a instanceof _.dL)return a};_.hL=function(a,b,c){c!=null||(c=!!(34&b));return _.$K(a,b,iL,c,!0)};_.jL=function(a){var b=a.V,c=b[_.hK]|0;if(!_.tK(a,c))return a;a=new a.constructor(_.hL(b,c));_.mK(a.V,2);return a};
_.kL=function(a){if(a.uF!==sK)return!1;var b=a.V;b=_.hL(b,b[_.hK]|0);_.mK(b,2);a.V=b;a.uF=void 0;return!0};_.lL=function(a){if(!_.kL(a)&&_.tK(a,a.V[_.hK]|0))throw Error();};_.mL=function(a,b,c,d,e){var f=c+(e?0:-1),h=a.length-1;if(h>=1+(e?0:-1)&&f>=h){var k=a[h];if(k!=null&&typeof k==="object"&&k.constructor===Object)return k[c]=d,b}if(f<=h)return a[f]=d,b;if(d!==void 0){var l;h=((l=b)!=null?l:b=a[_.hK]|0)>>14&1023||536870912;c>=h?d!=null&&(f={},a[h+(e?0:-1)]=(f[c]=d,f)):a[f]=d}return b};
_.pL=function(a,b,c,d,e,f,h,k,l){var m=b;h===1||(h!==4?0:2&b||!(16&b)&&32&d)?_.nL(b)||(b|=!a.length||k&&!(8192&b)||32&d&&!(8192&b||16&b)?2:256,b!==m&&_.lK(a,b),Object.freeze(a)):(h===2&&_.nL(b)&&(a=_.LK(a),m=0,b=_.oL(b,d),_.mL(c,d,e,a,f)),_.nL(b)||(l||(b|=16),b!==m&&_.lK(a,b)));return a};_.sL=function(a,b,c){a=_.qL(a,b,c);return Array.isArray(a)?a:_.rL};_.tL=function(a,b){2&b&&(a|=2);return a|1};_.nL=function(a){return!!(2&a)&&!!(4&a)||!!(256&a)};
_.uL=function(a,b,c,d){_.lL(a);var e=a.V;_.mL(e,e[_.hK]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a};_.vL=function(a,b,c,d,e){a=_.qL(a,d,e,function(f){return _.PK(f,c,!1,b)});if(a!=null)return a};
_.wL=function(a,b,c,d,e,f,h,k,l){var m=_.tK(a,c);f=m?1:f;k=!!k||f===3;m=l&&!m;(f===2||m)&&_.kL(a)&&(b=a.V,c=b[_.hK]|0);a=_.sL(b,e,h);var n=a===_.rL?7:a[_.hK]|0,p=_.tL(n,c);if(l=!(4&p)){var q=a,r=c,w=!!(2&p);w&&(r|=2);for(var u=!w,x=!0,A=0,D=0;A<q.length;A++){var E=_.PK(q[A],d,!1,r);if(E instanceof d){if(!w){var N=_.tK(E);u&&(u=!N);x&&(x=N)}q[D++]=E}}D<A&&(q.length=D);p|=4;p=x?p&-8193:p|8192;p=u?p|8:p&-9}p!==n&&(_.lK(a,p),2&p&&Object.freeze(a));if(m&&!(8&p||!a.length&&(f===1||(f!==4?0:2&p||!(16&p)&&
32&c)))){_.nL(p)&&(a=_.LK(a),p=_.oL(p,c),c=_.mL(b,c,e,a,h));d=a;m=p;for(n=0;n<d.length;n++)q=d[n],p=_.jL(q),q!==p&&(d[n]=p);m|=8;p=m=d.length?m|8192:m&-8193;_.lK(a,p)}return a=_.pL(a,p,b,c,e,h,f,l,k)};_.oL=function(a,b){return a=(2&b?a|2:a&-3)&-273};
_.xL=function(a,b,c,d,e,f,h,k){_.lL(a);var l=a.V;a=_.wL(a,l,l[_.hK]|0,c,b,2,d,!0);h&&k?(f!=null||(f=a.length-1),_.uK(a,f),a.splice(f,h),a.length||_.mK(a,8192)):(h?_.vK(a,f):e=e!=null?e:new c,f!=void 0?a.splice(f,h,e):a.push(e),f=c=a===_.rL?7:a[_.hK]|0,_.tK(e)?(c&=-9,a.length===1&&(c&=-8193)):c|=8192,c!==f&&_.lK(a,c))};QK=function(a){return a};
$J=function(a){var b=a.length,c=b*3/4;c%3?c=Math.floor(c):_.yc("=.",a[b-1])&&(c=_.yc("=.",a[b-2])?c-2:c-1);var d=new Uint8Array(c),e=0;_.Oh(a,function(f){d[e++]=f});return e!==c?d.subarray(0,e):d};var cK,aK,XJ;cK=typeof Uint8Array!=="undefined";_.ZJ=!_.yd&&typeof btoa==="function";aK=/[-_.]/g;XJ={"-":"+",_:"/",".":"="};var yL,RK,TK,gL,pK,VK;_.gK=typeof Symbol==="function"&&typeof Symbol()==="symbol";yL=_.fK("jas",void 0,!0);_.OK=_.fK(void 0,"0di");RK=_.fK(void 0,Symbol());TK=_.fK(void 0,"0ub");gL=_.fK(void 0,"0actk");pK=_.fK("m_m","Ura",!0);VK=_.fK();var jK,iK,zL;jK={Uba:{value:0,configurable:!0,writable:!0,enumerable:!1}};iK=Object.defineProperties;_.hK=_.gK?yL:"Uba";zL=[];_.lK(zL,7);_.rL=Object.freeze(zL);var qK,sK;qK={};sK={};_.AL=Object.freeze({});var xK=typeof _.Xa.BigInt==="function"&&typeof _.Xa.BigInt(0)==="bigint";var DL,BL,EL,CL;_.aL=_.WH(function(a){return xK?a>=BL&&a<=CL:a[0]==="-"?zK(a,DL):zK(a,EL)});DL=Number.MIN_SAFE_INTEGER.toString();BL=xK?BigInt(Number.MIN_SAFE_INTEGER):void 0;EL=Number.MAX_SAFE_INTEGER.toString();CL=xK?BigInt(Number.MAX_SAFE_INTEGER):void 0;_.AK=0;_.BK=0;var JK=void 0;_.FL=typeof BigInt==="function"?BigInt.asIntN:void 0;_.GL=typeof BigInt==="function"?BigInt.asUintN:void 0;_.HL=Number.isSafeInteger;_.IL=Number.isFinite;_.JL=Math.trunc;var WK={Dsa:!0};var YK;_.KL=_.yK(0);_.LL=function(a,b,c){return _.qL(a.V,b,c)};_.qL=function(a,b,c,d){if(b===-1)return null;var e=b+(c?0:-1),f=a.length-1;if(!(f<1+(c?0:-1))){if(e>=f){var h=a[f];if(h!=null&&typeof h==="object"&&h.constructor===Object){c=h[b];var k=!0}else if(e===f)c=h;else return}else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return k?h[b]=d:a[e]=d,d}return c}};_.ML=function(a,b,c,d){a=a.V;return _.vL(a,a[_.hK]|0,b,c,d)!==void 0};_.NL=function(a,b,c){this.V=_.fL(a,b,c)};_.NL.prototype.toJSON=function(){return _.cL(this)};_.OL=function(a,b){if(b==null||b=="")return new a;b=JSON.parse(b);if(!Array.isArray(b))throw Error("Fb");_.kK(b,32);return new a(b)};_.NL.prototype.getExtension=function(a){_.UK(this.V,a.Qh);XK(this,a.Qh);return a.ctor?a.Kz(this,a.ctor,a.Qh,a.Vu):a.Kz(this,a.Qh,a.defaultValue,a.Vu)};
_.PL=function(a,b){_.UK(a.V,b.Qh);XK(a,b.Qh);a=b.ctor?b.Kz(a,b.ctor,b.Qh,b.Vu):b.Kz(a,b.Qh,null,b.Vu);return a===null?void 0:a};_.NL.prototype.hasExtension=function(a){_.UK(this.V,a.Qh);XK(this,a.Qh);return a.ctor?_.ML(this,a.ctor,a.Qh,a.Vu):_.PL(this,a)!==void 0};_.NL.prototype.clone=function(){var a=this,b=a.V;a=new a.constructor(_.hL(b,b[_.hK]|0));_.mK(a.V,2);return a};_.NL.prototype[pK]=qK;_.NL.prototype.toString=function(){return this.V.toString()};
_.aM=function(a,b){return/-[a-z]/.test(b)?!1:_.ZL&&a.dataset?b in a.dataset:a.hasAttribute?a.hasAttribute("data-"+_.$L(b)):!!a.getAttribute("data-"+_.$L(b))};_.$L=function(a){return String(a).replace(/([A-Z])/g,"-$1").toLowerCase()};_.ZL=!_.yd&&!_.vh();
_.bM=function(a,b){this.K7=a[_.Xa.Symbol.iterator]();this.Tca=b};_.bM.prototype[Symbol.iterator]=function(){return this};_.bM.prototype.next=function(){var a=this.K7.next();return{value:a.done?void 0:this.Tca.call(void 0,a.value),done:a.done}};_.cM=function(a){this.vG=a};_.cM.prototype.Ah=function(){return new _.dM(this.vG())};_.cM.prototype[Symbol.iterator]=function(){return new _.eM(this.vG())};_.cM.prototype.hx=_.jb(44);_.dM=function(a){this.zv=a};_.y(_.dM,_.EF);_.dM.prototype.next=function(){return this.zv.next()};_.dM.prototype[Symbol.iterator]=function(){return new _.eM(this.zv)};_.dM.prototype.hx=_.jb(43);_.eM=function(a){_.cM.call(this,function(){return a});this.zv=a};_.y(_.eM,_.cM);_.eM.prototype.next=function(){return this.zv.next()};
var iN,lN,qN,rN,sN,tN,xN,yN,JN,MN,NN,ON,QN,RN,UN,EN,mN,qO;_.hN=function(a){var b=a.V,c=b[_.hK]|0;return _.tK(a,c)?a:new a.constructor(_.hL(b,c))};iN=function(){var a=_.AK,b=_.BK;b&2147483648?_.oK()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=_.Aa(_.DK(a,b)),a=b.next().value,b=b.next().value,a="-"+_.FK(a,b)):a=_.FK(a,b);return a};_.jN=function(a){a=Error(a);_.WJ(a,"warning");return a};_.kN=function(a){if(a!=null&&typeof a!=="number")throw Error("nb`"+typeof a+"`"+a);return a};
lN=function(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)};_.nN=function(a){switch(typeof a){case "bigint":return!0;case "number":return(0,_.IL)(a);case "string":return mN.test(a);default:return!1}};_.oN=function(a){if(!(0,_.IL)(a))throw _.jN("enum");return a|0};_.pN=function(a){if(typeof a!=="number")throw _.jN("int32");if(!(0,_.IL)(a))throw _.jN("int32");return a|0};
qN=function(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return(0,_.IL)(a)?a|0:void 0};rN=function(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337};sN=function(a){if(rN(a))return a;_.IK(a);return iN()};tN=function(a){var b=(0,_.JL)(Number(a));if((0,_.HL)(b))return _.yK(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return _.oK()?_.yK((0,_.FL)(64,BigInt(a))):_.yK(sN(a))};
_.uN=function(a){var b=(0,_.JL)(Number(a));if((0,_.HL)(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return sN(a)};_.vN=function(a){a=(0,_.JL)(a);if(!(0,_.HL)(a)){_.EK(a);var b=_.AK,c=_.BK;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);b=_.GK(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a};_.wN=function(a){a=(0,_.JL)(a);if((0,_.HL)(a))a=String(a);else{var b=String(a);rN(b)?a=b:(_.EK(a),a=iN())}return a};xN=function(a){return(0,_.HL)(a)?_.yK(_.vN(a)):_.yK(_.wN(a))};
yN=function(a,b){b=b===void 0?0:b;if(!_.nN(a))throw _.jN("int64");var c=typeof a;switch(b){case 512:switch(c){case "string":return _.uN(a);case "bigint":return String((0,_.FL)(64,a));default:return _.wN(a)}case 1024:switch(c){case "string":return tN(a);case "bigint":return _.yK((0,_.FL)(64,a));default:return xN(a)}case 0:switch(c){case "string":return _.uN(a);case "bigint":return _.yK((0,_.FL)(64,a));default:return _.vN(a)}default:return _.rb(b,"Unknown format requested type for int64")}};
_.zN=function(a,b){return a==null?a:yN(a,b===void 0?0:b)};_.AN=function(a){var b=typeof a;if(a==null)return a;if(b==="bigint")return _.yK((0,_.FL)(64,a));if(_.nN(a))return b==="string"?tN(a):xN(a)};_.BN=function(a){if(typeof a!=="string")throw Error();return a};_.CN=function(a){if(a!=null&&typeof a!=="string")throw Error();return a};
_.DN=function(a,b,c,d,e){_.lL(a);var f=a.V,h=f[_.hK]|0;if(c==null)return _.mL(f,h,b,void 0,e),a;var k=c===_.rL?7:c[_.hK]|0,l=k,m=_.nL(k),n=m||Object.isFrozen(c);m||(k=0);n||(c=_.LK(c),l=0,k=_.oL(k,h),n=!1);k|=5;var p;m=(p=_.nK(k))!=null?p:0;for(p=0;p<c.length;p++){var q=c[p],r=d(q,m);Object.is(q,r)||(n&&(c=_.LK(c),l=0,k=_.oL(k,h),n=!1),c[p]=r)}k!==l&&(n&&(c=_.LK(c),k=_.oL(k,h)),_.lK(c,k));_.mL(f,h,b,c,e);return a};
_.FN=function(a){if(_.gK){var b;return(b=a[EN])!=null?b:a[EN]=new Map}if(EN in a)return a[EN];b=new Map;Object.defineProperty(a,EN,{value:b});return b};_.GN=function(a,b,c,d){var e=a.get(d);if(e!=null)return e;for(var f=e=0;f<d.length;f++){var h=d[f];_.qL(b,h,void 0)!=null&&(e!==0&&(c=_.mL(b,c,e,void 0,void 0)),e=h)}a.set(d,e);return e};_.HN=function(a){return a===_.AL?2:4};_.IN=function(a){return JSON.stringify(_.cL(a))};
JN=function(){this.tI=!1;this.nn=null;this.Vj=void 0;this.Pc=1;this.Jn=this.wn=0;this.BS=this.Bh=null};_.g=JN.prototype;_.g.Pw=function(){if(this.tI)throw new TypeError("Generator is already running");this.tI=!0};_.g.Hp=function(){this.tI=!1};_.g.Lv=function(a){this.Vj=a};_.g.bx=function(a){this.Bh={dS:a,QV:!0};this.Pc=this.wn||this.Jn};_.g.return=function(a){this.Bh={return:a};this.Pc=this.Jn};_.KN=function(a,b,c){a.Pc=c;return{value:b}};JN.prototype.Wg=function(a){this.Pc=a};
_.LN=function(a){a.wn=0;var b=a.Bh.dS;a.Bh=null;return b};MN=function(a){this.qb=new JN;this.wea=a};MN.prototype.Lv=function(a){this.qb.Pw();if(this.qb.nn)return NN(this,this.qb.nn.next,a,this.qb.Lv);this.qb.Lv(a);return ON(this)};var PN=function(a,b){a.qb.Pw();var c=a.qb.nn;if(c)return NN(a,"return"in c?c["return"]:function(d){return{value:d,done:!0}},b,a.qb.return);a.qb.return(b);return ON(a)};
MN.prototype.bx=function(a){this.qb.Pw();if(this.qb.nn)return NN(this,this.qb.nn["throw"],a,this.qb.Lv);this.qb.bx(a);return ON(this)};NN=function(a,b,c,d){try{var e=b.call(a.qb.nn,c);if(!(e instanceof Object))throw new TypeError("Iterator result "+e+" is not an object");if(!e.done)return a.qb.Hp(),e;var f=e.value}catch(h){return a.qb.nn=null,a.qb.bx(h),ON(a)}a.qb.nn=null;d.call(a.qb,f);return ON(a)};
ON=function(a){for(;a.qb.Pc;)try{var b=a.wea(a.qb);if(b)return a.qb.Hp(),{value:b.value,done:!1}}catch(c){a.qb.Vj=void 0,a.qb.bx(c)}a.qb.Hp();if(a.qb.Bh){b=a.qb.Bh;a.qb.Bh=null;if(b.QV)throw b.dS;return{value:b.return,done:!0}}return{value:void 0,done:!0}};QN=function(a){this.next=function(b){return a.Lv(b)};this.throw=function(b){return a.bx(b)};this.return=function(b){return PN(a,b)};this[Symbol.iterator]=function(){return this}};
RN=function(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new Promise(function(d,e){function f(h){h.done?d(h.value):Promise.resolve(h.value).then(b,c).then(f,e)}f(a.next())})};_.SN=function(a){return RN(new QN(new MN(a)))};_.TN={};EN=_.fK(void 0,"1oa");_.C={};mN=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;_.VN=function(a,b,c,d){_.lL(a);var e=a.V;_.mL(e,e[_.hK]|0,b,c,d);return a};_.WN=function(a,b){return _.qL(a.V,b,void 0,lN)};
_.XN=function(a,b){a=a.V;return _.GN(_.FN(a),a,void 0,b)};_.YN=function(a,b,c){return _.XN(a,b)===c?c:-1};_.ZN=function(a,b,c,d){var e=a.V,f=e[_.hK]|0;b=_.vL(e,f,b,c,d);if(b==null)return b;f=e[_.hK]|0;if(!_.tK(a,f)){var h=_.jL(b);h!==b&&(_.kL(a)&&(e=a.V,f=e[_.hK]|0),b=h,_.mL(e,f,c,b,d))}return b};_.$N=function(a,b,c,d,e,f){_.xL(a,b,c,f,e,d,1);return a};_.aO=function(a,b,c,d,e){var f=a.V;return _.wL(a,f,f[_.hK]|0,b,c,d,e,!1,!0)};_.bO=function(a,b,c,d){c==null&&(c=void 0);_.VN(a,b,c,d);return a};
_.cO=function(a,b,c,d){_.lL(a);var e=a.V,f=e[_.hK]|0;if(c==null)return _.mL(e,f,b,void 0,d),a;for(var h=c===_.rL?7:c[_.hK]|0,k=h,l=_.nL(h),m=l||Object.isFrozen(c),n=!0,p=!0,q=0;q<c.length;q++){var r=c[q];l||(r=_.tK(r),n&&(n=!r),p&&(p=r))}l||(h=n?13:5,h=p?h&-8193:h|8192);m&&h===k||(c=_.LK(c),k=0,h=_.oL(h,f));h!==k&&_.lK(c,h);_.mL(e,f,b,c,d);return a};
_.dO=function(a,b){a=_.LL(a,b);a!=null&&(typeof a==="bigint"?(0,_.aL)(a)?a=Number(a):(a=(0,_.FL)(64,a),a=(0,_.aL)(a)?Number(a):String(a)):a=_.nN(a)?typeof a==="number"?_.vN(a):_.uN(a):void 0);return a};_.eO=function(a,b,c){return qN(_.LL(a,b,c))};_.fO=function(a,b,c){return _.NK(_.LL(a,b,c))};_.gO=function(a,b,c){a=_.LL(a,b,c);return a==null?a:(0,_.IL)(a)?a|0:void 0};_.hO=function(a,b,c){c=c===void 0?0:c;var d;return(d=_.eO(a,b))!=null?d:c};
_.iO=function(a,b){var c=c===void 0?"":c;var d;return(d=_.fO(a,b))!=null?d:c};_.jO=function(a,b){var c=c===void 0?0:c;var d;return(d=_.gO(a,b))!=null?d:c};_.kO=function(a,b,c,d){return _.VN(a,b,_.MK(c),d)};_.lO=function(a,b,c,d){return _.VN(a,b,c==null?c:_.pN(c),d)};_.mO=function(a,b,c,d,e){return _.VN(a,b,_.zN(c,d===void 0?0:d),e)};_.nO=function(a,b,c,d){return _.VN(a,b,_.CN(c),d)};_.oO=function(a,b,c,d){return _.VN(a,b,c==null?c:_.oN(c),d)};
_.dL=function(a,b){if(b!==_.TN)throw Error("mb");this.La=a;if(a!=null&&a.length===0)throw Error("lb");};_.pO=function(){return UN||(UN=new _.dL(null,_.TN))};_.dL.prototype.KE=function(){var a=this.La;if(a==null)a="";else if(typeof a!=="string"){if(_.ZJ){for(var b="",c=0,d=a.length-10240;c<d;)b+=String.fromCharCode.apply(null,a.subarray(c,c+=10240));b+=String.fromCharCode.apply(null,c?a.subarray(c):a);a=btoa(b)}else a=_.Nh(a);a=this.La=a}return a};
_.dL.prototype.isEmpty=function(){return this.La==null};_.dL.prototype.gta=function(){var a=qO(this);return a?a.length:0};_.rO=function(a,b){if(!a.La||!b.La||a.La===b.La)return a.La===b.La;if(typeof a.La==="string"&&typeof b.La==="string"){var c=a.La,d=b.La;b.La.length>a.La.length&&(d=a.La,c=b.La);if(c.lastIndexOf(d,0)!==0)return!1;for(b=d.length;b<c.length;b++)if(c[b]!=="=")return!1;return!0}c=qO(a);b=qO(b);return _.eK(c,b)};
qO=function(a){if(_.TN!==_.TN)throw Error("mb");var b=a.La;b==null||_.dK(b)||(typeof b==="string"?b=_.bK(b):(_.jd(b),b=null));return b==null?b:a.La=b};_.dL.prototype.DV=function(a){if(typeof a==="string")a=a?new _.dL(a,_.TN):_.pO();else if(a instanceof Uint8Array)a=new _.dL(a,_.TN);else if(!(a instanceof _.dL))return!1;return _.rO(this,a)};
var V3;V3=function(a,b){this.zd=a;this.Ef=b};_.W3=function(a){this.w=this.Kb=a;this.n=null;this.slf=0;this.ssh=!1;this.sen=!0;this.shl=this.itm=null};V3.prototype.onReady=function(a){this.zd.or(this.Ef,a)};V3.prototype.Pv=function(){this.zd.oh(this.Ef)};V3.prototype.onClose=function(){this.zd.oc(this.Ef)};V3.prototype.onError=function(a,b,c,d,e){this.zd.oe(this.Ef,a,b,c,d,e)};_.W3.prototype.sm=function(a,b){this.Kb.wp(new V3(a,this));this.n=b};_.W3.prototype.sh=function(){this.Kb.show()};_.W3.prototype.hi=function(){this.Kb.hide()};_.W3.prototype.cl=function(){this.Kb.close()};_.W3.prototype.en=function(){this.Kb.enable()};_.W3.prototype.di=function(){this.Kb.disable()};_.W3.prototype.hl=function(a){this.Kb.f$(a)};_.W3.prototype.vr=function(a,b){this.Kb.L$(a,b)};
var T7=function(a,b,c){this.Fj=a||{};this.dI=b||0;this.Kba=c||0;a={};b=(0,_.z)(this.qT,this);a.fc=b;b=(0,_.z)(this.pK,this);a.rc=b;b=(0,_.z)(this.gN,this);a.sc=b;b=(0,_.z)(this.Yu,this);a.hc=b;b=(0,_.z)(this.At,this);a.cc=b;b=(0,_.z)(this.BU,this);a.os=b;b=(0,_.z)(this.AU,this);a.or=b;b=(0,_.z)(this.yU,this);a.oh=b;b=(0,_.z)(this.wU,this);a.oc=b;b=(0,_.z)(this.xU,this);a.oe=b;b=(0,_.z)(this.zU,this);a.oi=b;this.zd=a},U7=function(){T7.call(this)},Vda=function(a){if(window.___jsl.man)a(window.___jsl.man);
else{var b=function(){var d=new U7;window.___jsl.man=d;a(d)};if(window.gbar){var c=function(){if(window.gbar.wg){var d=new V7;window.___jsl.man=d;a(d)}else b()};window.gbar.wg?c():window.gbar.qm?window.gbar.qm(c):b()}else b()}},W7=function(){return window.___jsl.man},Wda=function(a){var b=a,c;return function(){if(b){var d=b;b=void 0;c=d.apply(this,arguments)}return c}},X7=function(a){return document.body==a?"body":a.__cardid||null},Y7=function(a){var b=X7(a);b||(b=a.__cardid=Xda++);return String(b)},
Zda=function(a){var b=a.className||"getAttribute"in a&&a.getAttribute("class");return b&&Yda.test(b)||a.getAttribute&&_.aM(a,"hovercardId")||"getAttribute"in a&&a.getAttribute("oid")&&_.ei("card/p")==36?!0:a.tagName.toUpperCase()=="G:HOVERCARD"},$7=function(a,b){var c={};_.ij(c,Z7,_.ei("iframes/card")||{},_.ei("card")||{});for(var d=[],e=a;e;e=e.parentNode){var f=X7(e);f&&b[f]&&d.push(b[f])}_.Bb(d.reverse(),function(h){_.ij(c,h)});b=a.tagName.toUpperCase()=="G:HOVERCARD"?"":"data-";d=a.attributes;
for(e=0;e<d.length;e++)_.wc(d[e].name,b)&&(c[d[e].name.substring(b.length)]=d[e].value);"getAttribute"in a&&a.getAttribute("oid")&&_.ei("card/p")==36&&(c.ytid=a.getAttribute("oid"));!c.userid&&a.tagName.toUpperCase()=="A"&&a.pathname&&(b=a.pathname.match(/^\/?(\d+)$/),/\.google\.com$/.test(a.hostname)&&b&&(c.userid=b[1]));c.hl||(c.hl=_.ei("lang")||_.Nq().hl||void 0);c.m=c.entity;c.src=c.source;delete c.entity;delete c.source;return c},a8=function(a,b){b=b[a];typeof b!=="number"&&(b=Z7[a]);return b<
0?0:b},$da=function(a){a.zd.os(a.Ef)},b8=function(a){this.Ef=a;this.jf=0;this.rD=!1;this.WR=!0;this.hn=null},c8=function(a){return a.jf==5||a.jf==4};b8.prototype.isEnabled=function(){return this.WR};b8.prototype.vI=function(){return this.rD};b8.prototype.yc=function(a){this.WR=a};_.g=T7.prototype;_.g.pK=function(a,b,c){try{a+=b!=null?"_"+b:"",c.sm(this.zd,a),this.Fj[a]=new b8(c)}catch(d){return!1}return!0};_.g.qT=function(a,b){return(a=this.Fj[a+(b!=null?"_"+b:"")])?a.Ef:null};
_.g.gN=function(a){var b=d8(this,a);if(b&&(b.jf==2||b.jf==3)&&b.isEnabled()&&!b.vI()){try{a.sh()}catch(c){this.reportError(c,"am","shc")}b.rD=!0}};_.g.Yu=function(a){var b=d8(this,a);if(b&&(b.jf==2||b.jf==3||c8(b))&&b.vI()){try{a.hi()}catch(c){this.reportError(c,"am","hic")}b.rD=!1}};_.g.At=function(a){var b=d8(this,a);if(b&&b.jf!=5){try{this.Yu(a),a.cl()}catch(c){this.reportError(c,"am","clc")}e8(this,b)}};_.g.BU=function(a){(a=d8(this,a))&&a.jf==0&&(aea(this,a),a.jf=1)};
var aea=function(a,b){a.dI?(a=setTimeout((0,_.z)(function(){c8(b)||f8(this,b)},a),a.dI),b.hn=a):f8(a,b)},f8=function(a,b){var c=a.Kba-a.dI;c>0&&(a=setTimeout((0,_.z)(function(){c8(b)||(b.jf=4,this.At(b.Ef))},a),c),b.hn=a)},g8=function(a){a.hn!=null&&(clearTimeout(a.hn),a.hn=null)};_.g=T7.prototype;_.g.AU=function(a){(a=d8(this,a))&&!c8(a)&&a.jf==1&&(g8(a),a.jf=3)};_.g.yU=function(a){(a=d8(this,a))&&!c8(a)&&(a.rD=!1)};
_.g.wU=function(a){var b=d8(this,a);if(b&&!c8(b)){try{this.Yu(a)}catch(c){this.reportError(c,"am","oc")}e8(this,b)}};_.g.xU=function(a,b,c,d,e,f){(a=d8(this,a))&&!c8(a)&&(this.reportError(c,d,e,a,b,f),a.jf=4,this.At(a.Ef))};_.g.zU=function(a,b){(a=d8(this,a))&&!c8(a)&&b>=2&&b<=4&&!c8(a)&&(g8(a),a.jf=2)};var e8=function(a,b){g8(b);b.jf=5;a=a.Fj;for(var c in a)a[c]==b&&delete a[c]},d8=function(a,b){return a.Fj[b.n]},V7=function(){this.zd=window.gbar.wg};_.g=V7.prototype;
_.g.pK=function(a,b,c){return this.zd.rc(a,b,c)};_.g.qT=function(a,b){return this.zd.fc(a,b)};_.g.gN=function(a){this.zd.sc(a)};_.g.Yu=function(a){this.zd.hc(a)};_.g.At=function(a){this.zd.cc(a)};_.g.BU=function(a){this.zd.os(a)};_.g.AU=function(a,b){this.zd.or(a,b)};_.g.yU=function(a){this.zd.oh(a)};_.g.wU=function(a){this.zd.oc(a)};_.g.xU=function(a,b,c,d,e,f){this.zd.oe(a,b,c,d,e,f)};_.g.zU=function(a,b,c,d){this.zd.oi(a,b,c,d)};_.eb(U7,T7);U7.prototype.reportError=function(){};var bea={vka:"https://www.google.com",Yna:"https://support.google.com",rma:"https://play.google.com"},h8=function(){var a=this;this.EK=[];this.FK=[];this.initialize=Wda(function(){return _.SN(function(b){if(b.Pc==1)return typeof document==="undefined"||document.requestStorageAccessFor===void 0||navigator.permissions===void 0||navigator.permissions.query===void 0||location.hostname.match(".+\\.google\\.com$")?b.return(Promise.resolve()):_.KN(b,cea(a),2);a.EK.length>0&&document.addEventListener("click",
a.sZ);b.Pc=0})});this.sZ=function(){if(!(a.FK.length>0)){for(var b=_.Aa(a.EK),c=b.next();!c.done;c=b.next()){c=c.value;try{a.FK.push(document.requestStorageAccessFor(c))}catch(d){}}Promise.all(a.FK).then(function(){}).catch(function(){}).finally(function(){a.reset()})}}};h8.prototype.reset=function(){document.removeEventListener("click",this.sZ)};
var cea=function(a){var b,c,d,e;return _.SN(function(f){switch(f.Pc){case 1:b=_.Aa(Object.values(bea)),c=b.next();case 2:if(c.done){f.Wg(0);break}d=c.value;f.wn=5;return _.KN(f,navigator.permissions.query({name:"top-level-storage-access",requestedOrigin:d}),7);case 7:e=f.Vj;e.state!=="granted"&&a.EK.push(d);f.Pc=3;f.wn=0;break;case 5:_.LN(f);f.Wg(0);break;case 3:c=b.next(),f.Wg(2)}})};(new h8).initialize();var dea=function(a){this.Sfa=a;this.Ab=null;a.then((0,_.z)(function(){},this),function(){},this)},eea=function(a,b){a.Sfa.then(function(c){var d=c.startFeedback;if(!d)throw Error("ad`startFeedback");return d.apply(c,b)})},fea=function(a,b,c){for(var d=Array(arguments.length-2),e=2;e<arguments.length;e++)d[e-2]=arguments[e];e=i8(a,b).then(function(f){return f.apply(null,d)},function(f){f=Error("bd`"+b+"`"+a,{cause:f});delete j8[b];return _.Ck(f)});return new dea(e)},j8={},i8=function(a,b){var c=j8[b];
if(c)return c;c=(c=_.wb(b))?_.Bk(c):(new _.xk(function(d,e){var f=(new _.Zd(document)).createElement("SCRIPT");f.async=!0;_.Hh(f,_.jc(_.Xt(a)));f.onload=f.onreadystatechange=function(){f.readyState&&f.readyState!="loaded"&&f.readyState!="complete"||d()};f.onerror=function(h){e(Error("cd`"+b+"`"+a,{cause:h}))};(document.head||document.getElementsByTagName("head")[0]).appendChild(f)})).then(function(){var d=_.wb(b);if(!d)throw Error("dd`"+b+"`"+a);return d});return j8[b]=c};var k8=function(a){this.ur=a;this.Ab=null};k8.prototype.Sga=function(a){eea(this.ur,arguments)};var l8=new _.Wt(_.Ut,"https://www.gstatic.com/feedback/js/help/prod/service/lazy.min.js");i8(l8,"help.service.Lazy.create").zD(function(){});var m8={contactid:!0,cdu:!0,cmp:!0,email:!0,hl:!0,n:!0,m:!0,p:!0,src:!0,userid:!0,sp:!0,ytid:!0};_.ij({nm:!0,s:!0,pr:!0,v:!0},m8);var n8=function(){this.hv=_.Hk();this.hv.promise.then(function(a){_.xs(a.getIframeEl(),{border:"none","margin-bottom":"-4px"});_.xs(a.getSiteEl(),{"border-radius":"4px",overflow:"hidden","box-shadow":"rgba(0, 0, 0, 0.3) 0px 4px 16px"})})};_.g=n8.prototype;_.g.XH=function(a){_.an.open(a,this.hv.resolve)};_.g.gV=function(){this.hv.promise.then(function(a){return a.close()})};_.g.WH=function(a,b,c,d){this.hv.promise.then(function(e){return e.send(a,c,d,b)})};
_.g.VH=function(a,b,c){this.hv.promise.then(function(d){return d.restyle({updateContainer:{visible:a,x:b,y:c}})})};_.g.addOnOpenerHandler=function(a,b,c){_.an.addOnOpenerHandler(a,c,b)};var Yda=RegExp("(?:^|\\s)g-(?:hovercard|profile)(?:$|\\s)"),Z7={loadDelay:400,hoverDelay:500,closeDelay:500},Xda=0;var o8=function(a){this.vt=a;this.Ef=new _.W3(this);this.Ja=null;this.oI=!1;this.tW=0;this.rt=!1};_.g=o8.prototype;
_.g.load=function(a){$da(this.qb);a=_.gi(a,function(f,h){return m8[h]&&f!=null});a.origin=window.location.protocol+"//"+window.location.host;var b=this,c=this.vt,d={_event:function(f){if(!(f.timestamp<c.AI)){if(f.event=="sgcp_ams")c.QA=!0,c.vB=!1;else if(c.QA&&f.event=="mouseover")c.vB=!0;else if(c.QA&&f.event=="mouseout")c.vB=!1;else if(f.event=="sgcp_amh")c.QA=!1,c.vB||p8(c);else{var h=!1;switch(f.event){case "calendar":h=c.Gi.scheduleEventHandler;break;case "chat":h=c.Gi.sendChatHandler;break;
case "email":h=c.Gi.sendEmailHandler;break;case "feedback":h=c.Gi.feedbackHandler;break;case "videoChat":h=c.Gi.videoChatHandler}h&&h(c.Gi["hovercard-id"]||c.Gi.userid||c.Gi.email)}if(f.event=="mouseover"||f.event=="sgcp_ams")window.clearTimeout(c.nj),c.nj=null;if(f.cpid){for(h=document.getElementById(f.cpid);h&&h.parentNode.tagName!="BODY";)h=h.parentNode;c.CI=h}f.fromCard&&f.event=="mouseout"&&p8(c)}},_ready:(0,_.z)(this.Pd,this),version:function(f){c.Cf(c.Xk,{type:"circles_changed",version:f.v})},
loaded:function(f){f.ri==b.tW&&b.xca()},rendered:function(){var f=b.vt.Mo,h=_.qs(document);b.rt&&(b.rl(!0,f.x+h.x,f.y+h.y),b.rt=!1,f=b.vt,f.Cf(f.Xk,{type:"show",frame:b.Ja}))},renderfailed:function(){b.rt=!1;b.rl(!1,0,0)},disposed:function(){b.Ja.close()},cardAction:function(f){q8(c,f)}},e=":card";!_.ei("iframes/card/url")&&_.ei("iframes/hovercard/url")&&(e=":hovercard");a=_.Gm(_.ek(_.dk(new _.uo({disableMultiLevelParentRelay:!0,hover:!0}),d),_.dn),a).Ei("hover").setUrl(e);_.ei("card/relayOpenTop")&&
(_.Hm(a,-1),(new _.jo(a.T)).VK("_default"));_.an.open(a.value(),(0,_.z)(function(f){this.Ja=f;_.xs(f.getIframeEl(),{border:"none","margin-bottom":"-4px"});_.xs(f.getSiteEl(),{"border-radius":"4px",overflow:"hidden","box-shadow":"rgba(0, 0, 0, 0.3) 0px 4px 16px"})},this))};_.g.Pd=function(){this.oI=!0;this.rl(!1,0,0);this.qb.onReady({});var a=this.vt;a.xe&&a.OI(a.xe)};_.g.ki=function(){return this.oI};_.g.wp=function(a){this.qb=a};_.g.f$=function(a){this.Ja.send("getHealthc",void 0,a,_.dn)};
_.g.L$=function(a,b){this.Ja.send("getVarc",a,b,_.dn)};_.g.rl=function(a,b,c){this.Ja.updateContainer?this.Ja.updateContainer(a,b,c):this.Ja.restyle({updateContainer:{visible:a,x:b,y:c}})};_.g.show=function(){this.rl(!0,0,-1E4);this.Ja.send("render",void 0,void 0,_.dn);this.rt=!0};_.g.hide=function(){this.Ja.send("hide",void 0,void 0,_.dn);this.rl(!1,0,0);var a=this.vt;a.Cf(a.Xk,{type:"hide"});a.Xk=null;a.Gi=null;this.rt=!1};_.g.close=function(){this.Ja.send("dispose",void 0,void 0,_.dn)};
_.g.enable=function(){};_.g.disable=function(){};var r8=function(){this.yX=0;this.vH=[];this.fk={};this.Gy={};this.qc={};this.Vc=this.xe=this.Zf=null;this.JB=_.cn;this.AI=this.nj=this.Xg=this.Yq=this.Gi=this.Xk=null;this.Mo={x:0,y:0};this.vB=this.QA=!1;this.CI=null;this.gQ=new Map},s8=function(a,b,c,d){var e=Y7(b);e=a.qc[e]||(a.qc[e]={});e[c]||(e[c]=d=(0,_.z)(d,a),b.addEventListener?b.addEventListener(c,d,c=="focus"||c=="blur"):(c=="focus"?c="focusin":c=="blur"&&(c="focusout"),b.attachEvent("on"+c,d)))};
r8.prototype.Pk=function(a,b){var c=this.qc[Y7(a)];c&&c[b]&&(a.addEventListener?a.removeEventListener(b,c[b],b=="focus"||b=="blur"):a.detachEvent(b=="focus"?"onfocusin":b=="blur"?"onfocusout":"on"+b,c[b]),delete c[b])};var t8=function(a,b){var c=a.qc[b.id];if(c)for(var d in c)c.hasOwnProperty(d)&&a.Pk(b,d)};_.g=r8.prototype;
_.g.Bx=function(a,b,c){if(a=a||document.body){this.yX++;var d=Y7(a);b&&(this.fk[d]=b);c&&(this.Gy[d]=c);s8(this,a,"mouseover",this.vU);s8(this,a,"mouseout",this.GH);s8(this,a,"mousedown",this.uU);s8(this,a,"focus",this.vU);s8(this,a,"blur",this.GH);s8(this,document.body,"mouseout",this.GH);s8(this,document.body,"mousedown",this.uU);c&&c.preload&&(b=this.xe=document.createElement("div"),this.Vc=$7(b,this.Gy),u8(this))}else window.setTimeout((0,_.z)(this.Bx,this),100)};
_.g.GN=function(a){if(a=a||document.body)if(p8(this,0),a!=document.body?t8(this,a):this.Pk(document.body,"mouseover"),a=Y7(a),delete this.fk[a],delete this.Gy[a],!(--this.yX>0)){t8(this,document.body);var b=this.Zf;this.PH();this.Zf=null;window.setTimeout(function(){var c=W7();c&&b&&c.At(b.Ef)},100)}};_.g.ye=function(a){this.vH.push(a)};_.g.gp=function(a){_.gj(this.vH,a)};_.g.Cf=function(a,b){for(var c=[];a;){var d=X7(a);d&&this.fk[d]&&c.push(this.fk[d]);a=a.parentNode}_.Eh(c,this.vH);_.Bb(c,function(e){e(b)})};
_.g.vU=function(a){this.AI=Date.now();var b=a.target||a.srcElement;if(b&&b.tagName!="IFRAME"){for(;b&&!Zda(b);)if(b=b.parentNode,!b||b.nodeType!=1)return;if(b)if(this.Xk==b||this.xe==b)this.nj&&(window.clearTimeout(this.nj),this.nj=null);else{this.xe=b;s8(this,b,"mousemove",this.zaa);a.type=="focus"||a.type=="focusin"?(a=_.nt(b),this.Mo.x=a.x,this.Mo.y=a.y+b.offsetHeight):(this.Mo.x=a.clientX,this.Mo.y=a.clientY);this.Yq=Date.now();a=this.Vc=$7(b,this.Gy);var c=a8("hoverDelay",a);this.Zf?this.Zf.ki()&&
(window.clearTimeout(this.Xg),this.Xg=window.setTimeout((0,_.z)(this.OI,this,b),c-a8("loadDelay",a))):u8(this)}}};_.g.GH=function(a){this.AI=Date.now();if(a.type!="blur"||a.target==this.Xk||a.target==this.xe){if(a=a.relatedTarget||a.toElement){if(a.tagName=="IFRAME")return;if(this.CI)for(;a&&a.tagName!="BODY";){if(a==this.CI)return;a=a.parentNode}}p8(this)}};_.g.uU=function(){p8(this,0)};_.g.zaa=function(a){this.Mo.x=a.clientX;this.Mo.y=a.clientY};
var u8=function(a){a.Xg&&(window.clearTimeout(a.Xg),a.Xg=null);if(a.xe&&(a.Cf(a.xe,{type:"hover",config:a.Vc}),!a.Zf)){var b=a.Zf=new o8(a);Vda((0,_.z)(function(c){c.pK("card",gea++,b.Ef)&&b.load(this.Vc||{})},a))}};
r8.prototype.OI=function(a){this.Xg&&(window.clearTimeout(this.Xg),this.Xg=null);if(this.xe==a){var b=this.Vc||{},c=a8("hoverDelay",b)-a8("loadDelay",b)-Date.now()+this.Yq;c>0?this.Xg=window.setTimeout((0,_.z)(this.OI,this,a),c):(this.Cf(a,{type:"hover",config:b}),this.Vc.feedbackHandler||(this.Vc.feedbackHandler=this.Tga),this.Vc.overrideFeedback=!0,this.Vc.scheduleEventHandler&&(this.Vc.overrideCalendar=!0),this.Vc.sendChatHandler&&(this.Vc.overrideChat=!0),this.Vc.sendEmailHandler&&(this.Vc.overrideEmail=
!0),this.Vc.videoChatHandler&&(this.Vc.overrideVideoChat=!0),c=this.Zf,a=this.n0.bind(this,a),c.oI&&(c.xca=a,b.ri=++c.tW,c.Ja.send("loadData",b,void 0,_.dn)))}};
r8.prototype.Tga=function(){var a={};a={apiKey:a.apiKey||a.apiKey,asxUiUri:a.asxUiUri||a.asxUiUri,environment:a.environment||a.environment,flow:a.flow||a.flow,frdProductData:a.frdProductData||a.frdProductData,frdProductDataSerializedJspb:a.Iqa||a.frdProductDataSerializedJspb,helpCenterPath:a.helpCenterPath||a.helpCenterPath,locale:a.locale||a.locale||"en".replace(/-/g,"_"),nonce:a.nonce||a.nonce,productData:a.productData||a.productData,receiverUri:a.receiverUri||a.receiverUri,renderApiUri:a.renderApiUri||
a.renderApiUri,theme:a.theme||a.theme,window:a.window||a.window};a=fea(l8,"help.service.Lazy.create","5003140",a);(new k8(a)).Sga({productVersion:"gapi",customZIndex:2000000002})};r8.prototype.n0=function(a){if(this.xe===a&&this.Zf&&this.Zf.ki()&&this.Yq){var b=a8("hoverDelay",this.Vc||{})-Date.now()+this.Yq;b>0?window.setTimeout((0,_.z)(this.n0,this,a),b):(this.PH(),this.Xk=this.xe,this.Gi=this.Vc,this.Pk(this.xe,"mousemove"),this.Yq=this.Vc=this.xe=null,W7().gN(this.Zf.Ef))}};
var p8=function(a,b){a.xe&&a.Pk(a.xe,"mousemove");a.xe=null;a.Vc=null;a.Yq=null;a.Xg&&(window.clearTimeout(a.Xg),a.Xg=null);!a.nj&&a.Xk&&(a.nj=window.setTimeout((0,_.z)(a.PH,a),typeof b==="number"?b:a8("closeDelay",a.Gi||{})))};r8.prototype.PH=function(){this.nj&&(window.clearTimeout(this.nj),this.nj=null);this.Xk&&W7().Yu(this.Zf.Ef)};var q8=function(a,b){a.Cf(null,b);a.LJ&&a.LJ.send("cardAction",b,void 0,a.JB)};
r8.prototype.pF=function(a,b,c){var d={};d.frame=a;d.filter=b||_.cn;d.callback=c||function(){};this.gQ.set(String(_.uh(a)),d);a.register("cardAction",(0,_.z)(function(e){q8(this,e);d.callback(e)},this),d.filter)};r8.prototype.qF=function(a){var b=this;this.JB=a||_.cn;_.an.addOnOpenerHandler(function(c){b.LJ=c;b.LJ.register("cardAction",function(){return b.qy},b.JB)},void 0,this.JB)};
r8.prototype.qy=function(a){this.gQ.forEach(function(b){return b.frame.send("cardAction",a,void 0,b.filter)});this.Zf&&this.Zf.Ja.send("cardAction",a,void 0,_.dn)};var gea=0;_.v8=function(){var a={},b=new r8,c=new n8;a.Bx=function(d,e,f){b.Bx(d,e,f)};a.GN=function(d){b.GN(d)};a.ye=function(d){b.ye(d)};a.gp=function(d){b.gp(d)};a.pF=function(d,e,f){b.pF(d,e,f)};a.qF=function(d){b.qF(d)};a.qy=function(d){b.qy(d)};a.va=function(d,e){e.origin=window.location.protocol+"//"+window.location.host;var f=_.an.openChild({url:":card",where:document.getElementById(d),queryParams:e,messageHandlers:{_ready:function(){f.send("loadData",e,void 0,_.dn)},loaded:function(){f.send("render",
void 0,void 0,_.dn)}},messageHandlersFilter:_.dn})};a.gV=function(){c.gV()};a.VH=function(d,e,f){c.VH(d,e,f)};a.WH=function(d,e,f,h){c.WH(d,e,f,h)};a.XH=function(d){c.XH(d)};a.Aba=function(d,e,f){c.addOnOpenerHandler(d,e,f)};a.Cba=function(){return _.dn};a.Dba=function(){return _.cn};return a}();_.t("gapi.card.watch",_.v8.Bx);_.t("gapi.card.unwatch",_.v8.GN);_.t("gapi.card.addCallback",_.v8.ye);_.t("gapi.card.removeCallback",_.v8.gp);_.t("gapi.card.render",_.v8.va);_.t("gapi.card.connectChild",_.v8.pF);_.t("gapi.card.connectOpener",_.v8.qF);_.t("gapi.card.broadcast",_.v8.qy);_.t("gapi.card.iframeClose",_.v8.close);_.t("gapi.card.iframeRestyle",_.v8.VH);_.t("gapi.card.iframeSend",_.v8.WH);_.t("gapi.card.iframeSetup",_.v8.XH);_.t("gapi.card.iframeAddOnOpenerHandler",_.v8.Aba);
_.t("gapi.card.iframeGetCrossOriginFilter",_.v8.Cba);_.t("gapi.card.iframeGetSameOriginFilter",_.v8.Dba);
});
// Google Inc.
