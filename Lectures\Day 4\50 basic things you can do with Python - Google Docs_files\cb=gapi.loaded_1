gapi.loaded_1(function(_){var window=this;
_.Fg=(window.gapi||{}).load;
_.lo=_.Be(_.Me,"rw",_.Ce());
var mo=function(a,b){(a=_.lo[a])&&a.state<b&&(a.state=b)};var no=function(a){a=(a=_.lo[a])?a.oid:void 0;if(a){var b=_.ye.getElementById(a);b&&b.parentNode.removeChild(b);delete _.lo[a];no(a)}};_.oo=function(a){a=a.container;typeof a==="string"&&(a=document.getElementById(a));return a};_.po=function(a){var b=a.clientWidth;return"position:absolute;top:-10000px;width:"+(b?b+"px":a.style.width||"300px")+";margin:0px;border-style:none;"};
_.qo=function(a,b){var c={},d=a.wc(),e=b&&b.width,f=b&&b.height,h=b&&b.verticalAlign;h&&(c.verticalAlign=h);e||(e=d.width||a.width);f||(f=d.height||a.height);d.width=c.width=e;d.height=c.height=f;d=a.getIframeEl();e=a.getId();mo(e,2);a:{e=a.getSiteEl();c=c||{};var k;if(_.Me.oa&&(k=d.id)){f=(f=_.lo[k])?f.state:void 0;if(f===1||f===4)break a;no(k)}(f=e.nextSibling)&&f.dataset&&f.dataset.gapistub&&(e.parentNode.removeChild(f),e.style.cssText="");f=c.width;h=c.height;var l=e.style;l.textIndent="0";l.margin=
"0";l.padding="0";l.background="transparent";l.borderStyle="none";l.cssFloat="none";l.styleFloat="none";l.lineHeight="normal";l.fontSize="1px";l.verticalAlign="baseline";e=e.style;e.display="inline-block";d=d.style;d.position="static";d.left="0";d.top="0";d.visibility="visible";f&&(e.width=d.width=f+"px");h&&(e.height=d.height=h+"px");c.verticalAlign&&(e.verticalAlign=c.verticalAlign);k&&mo(k,3)}(k=b?b.title:null)&&a.getIframeEl().setAttribute("title",k);(b=b?b.ariaLabel:null)&&a.getIframeEl().setAttribute("aria-label",
b)};_.ro=function(a){var b=a.getSiteEl();b&&b.removeChild(a.getIframeEl())};_.so=function(a){a.where=_.oo(a);var b=a.messageHandlers=a.messageHandlers||{},c=function(e){_.qo(this,e)};b._ready=c;b._renderstart=c;var d=a.onClose;a.onClose=function(e){d&&d.call(this,e);_.ro(this)};a.onCreate=function(e){e=e.getIframeEl();e.style.cssText=_.po(e)}};
_.bj=function(a){var b=window;a=(a||b.location.href).match(RegExp(".*(\\?|#|&)usegapi=([^&#]+)"))||[];return"1"===decodeURIComponent(a[a.length-1]||"")};
_.to=function(a,b){a.T.where=b;return a};_.uo=function(){_.ck.apply(this,arguments)};_.y(_.uo,_.ck);
_.vo=_.Ce();
_.Eo={};window.iframer=_.Eo;
var Go=function(a){var b=[new Fo];if(b.length===0)throw Error("j");if(b.map(function(c){if(c instanceof Fo)c=c.MY;else throw Error("j");return c}).every(function(c){return"data-gapiscan".indexOf(c)!==0}))throw Error("k`data-gapiscan");a.setAttribute("data-gapiscan","true")},Fo=function(){this.MY=Ho[0].toLowerCase()},Io,Jo,Ko,Lo,Mo,Qo,Ro;Fo.prototype.toString=function(){return this.MY};Io=function(a){if(_.Ae.test(Object.keys))return Object.keys(a);var b=[],c;for(c in a)_.De(a,c)&&b.push(c);return b};
Jo={button:!0,div:!0,span:!0};Ko=function(a){var b=_.Be(_.Me,"sws",[]);return _.Om.call(b,a)>=0};Lo=function(a){return _.Be(_.Me,"watt",_.Ce())[a]};Mo=function(a){return function(b,c){return a?_.tm()[c]||a[c]||"":_.tm()[c]||""}};_.No={callback:1,clientid:1,cookiepolicy:1,openidrealm:-1,includegrantedscopes:-1,requestvisibleactions:1,scope:1};_.Oo=!1;
_.Po=function(){if(!_.Oo){for(var a=document.getElementsByTagName("meta"),b=0;b<a.length;++b){var c=a[b].name.toLowerCase();if(_.wc(c,"google-signin-")){c=c.substring(14);var d=a[b].content;_.No[c]&&d&&(_.vo[c]=d)}}if(window.self!==window.top){a=document.location.toString();for(var e in _.No)_.No[e]>0&&(b=_.Ge(a,e,""))&&(_.vo[e]=b)}_.Oo=!0}e=_.Ce();_.Ee(_.vo,e);return e};Qo=function(a){var b;a.match(/^https?%3A/i)&&(b=decodeURIComponent(a));a=b?b:a;return _.$l(document,a)};
Ro=function(a){a=a||"canonical";for(var b=document.getElementsByTagName("link"),c=0,d=b.length;c<d;c++){var e=b[c],f=e.getAttribute("rel");if(f&&f.toLowerCase()==a&&(e=e.getAttribute("href"))&&(e=Qo(e))&&e.match(/^https?:\/\/[\w\-_\.]+/i)!=null)return e}return window.location.href};_.So=function(){return window.location.origin||window.location.protocol+"//"+window.location.host};_.To=function(a,b,c,d){return(a=typeof a=="string"?a:void 0)?Qo(a):Ro(d)};
_.Uo=function(a,b,c){a==null&&c&&(a=c.db,a==null&&(a=c.gwidget&&c.gwidget.db));return a||void 0};_.Vo=function(a,b,c){a==null&&c&&(a=c.ecp,a==null&&(a=c.gwidget&&c.gwidget.ecp));return a||void 0};_.Wo=function(a,b,c){return _.To(a,b,c,b.action?void 0:"publisher")};var Xo,Yo,Zo,$o,ap,bp,dp,cp;Xo={se:"0"};Yo={post:!0};Zo={style:"position:absolute;top:-10000px;width:450px;margin:0px;border-style:none"};$o="onPlusOne _ready _close _open _resizeMe _renderstart oncircled drefresh erefresh".split(" ");ap=_.Be(_.Me,"WI",_.Ce());bp=["style","data-gapiscan"];
dp=function(a){for(var b=_.Ce(),c=a.nodeName.toLowerCase().indexOf("g:")!=0,d=a.attributes.length,e=0;e<d;e++){var f=a.attributes[e],h=f.name,k=f.value;_.Om.call(bp,h)>=0||c&&h.indexOf("data-")!=0||k==="null"||"specified"in f&&!f.specified||(c&&(h=h.substr(5)),b[h.toLowerCase()]=k)}a=a.style;(c=cp(a&&a.height))&&(b.height=String(c));(a=cp(a&&a.width))&&(b.width=String(a));return b};
_.fp=function(a,b,c,d,e,f){if(c.rd)var h=b;else h=document.createElement("div"),b.dataset.gapistub=!0,h.style.cssText="position:absolute;width:450px;left:-10000px;",b.parentNode.insertBefore(h,b);f.siteElement=h;h.id||(h.id=_.ep(a));b=_.Ce();b[">type"]=a;_.Ee(c,b);a=_.xm(d,h,e);f.iframeNode=a;f.id=a.getAttribute("id")};_.ep=function(a){_.Be(ap,a,0);return"___"+a+"_"+ap[a]++};cp=function(a){var b=void 0;typeof a==="number"?b=a:typeof a==="string"&&(b=parseInt(a,10));return b};var Ho=_.gd(["data-"]),jp,kp,lp,mp,np=/(?:^|\s)g-((\S)*)(?:$|\s)/,op={plusone:!0,autocomplete:!0,profile:!0,signin:!0,signin2:!0};jp=_.Be(_.Me,"SW",_.Ce());kp=_.Be(_.Me,"SA",_.Ce());lp=_.Be(_.Me,"SM",_.Ce());mp=_.Be(_.Me,"FW",[]);
var pp=function(a,b){return(typeof a==="string"?document.getElementById(a):a)||b},tp=function(a,b){var c;qp.ps0=(new Date).getTime();rp("ps0");a=pp(a,_.ye);var d=_.ye.documentMode;if(a.querySelectorAll&&(!d||d>8)){d=b?[b]:Io(jp).concat(Io(kp)).concat(Io(lp));for(var e=[],f=0;f<d.length;f++){var h=d[f];e.push(".g-"+h,"g\\:"+h)}d=a.querySelectorAll(e.join(","))}else d=a.getElementsByTagName("*");a=_.Ce();for(e=0;e<d.length;e++){f=d[e];var k=f;h=b;var l=k.nodeName.toLowerCase(),m=void 0;if(k.hasAttribute("data-gapiscan"))h=
null;else{var n=l.indexOf("g:");n==0?m=l.substr(2):(n=(n=String(k.className||k.getAttribute("class")))&&np.exec(n))&&(m=n[1]);h=!m||!(jp[m]||kp[m]||lp[m])||h&&m!==h?null:m}h&&(op[h]||f.nodeName.toLowerCase().indexOf("g:")==0||Io(dp(f)).length!=0)&&(Go(f),_.Be(a,h,[]).push(f))}for(p in a)mp.push(p);qp.ps1=(new Date).getTime();rp("ps1");if(b=mp.join(":"))try{_.Fe.load(b,void 0)}catch(q){_.Vf.log(q);return}e=[];for(c in a){d=a[c];var p=0;for(b=d.length;p<b;p++)f=d[p],sp(c,f,dp(f),e,b)}};var up=function(a,b){var c=Lo(a);b&&c?(c(b),(c=b.iframeNode)&&c.setAttribute("data-gapiattached",!0)):_.Fe.load(a,function(){var d=Lo(a),e=b&&b.iframeNode,f=b&&b.userParams;e&&d?(d(b),e.setAttribute("data-gapiattached",!0)):(d=_.Fe[a].go,a=="signin2"?d(e,f):d(e&&e.parentNode,f))})},sp=function(a,b,c,d,e,f,h){switch(vp(b,a,f)){case 0:a=lp[a]?a+"_annotation":a;d={};d.iframeNode=b;d.userParams=c;up(a,d);break;case 1:if(b.parentNode){for(var k in c){if(f=_.De(c,k))f=c[k],f=!!f&&typeof f==="object"&&(!f.toString||
f.toString===Object.prototype.toString||f.toString===Array.prototype.toString);if(f)try{c[k]=_.Rf(c[k])}catch(x){delete c[k]}}k=!0;c.dontclear&&(k=!1);delete c.dontclear;var l;f={};var m=l=a;a=="plus"&&c.action&&(l=a+"_"+c.action,m=a+"/"+c.action);(l=_.Xe("iframes/"+l+"/url"))||(l=":im_socialhost:/:session_prefix::im_prefix:_/widget/render/"+m+"?usegapi=1");for(n in Xo)f[n]=n+"/"+(c[n]||Xo[n])+"/";var n=_.$l(_.ye,l.replace(_.sm,Mo(f)));m="iframes/"+a+"/params/";f={};_.Ee(c,f);(l=_.Xe("lang")||_.Xe("gwidget/lang"))&&
(f.hl=l);Yo[a]||(f.origin=_.So());f.exp=_.Xe(m+"exp");if(m=_.Xe(m+"location"))for(l=0;l<m.length;l++){var p=m[l];f[p]=_.xe.location[p]}switch(a){case "plus":case "follow":f.url=_.Wo(f.href,c,null);delete f.href;break;case "plusone":m=(m=c.href)?Qo(m):Ro();f.url=m;f.db=_.Uo(c.db,void 0,_.Xe());f.ecp=_.Vo(c.ecp,void 0,_.Xe());delete f.href;break;case "signin":f.url=Ro()}_.Me.ILI&&(f.iloader="1");delete f["data-onload"];delete f.rd;for(var q in Xo)f[q]&&delete f[q];f.gsrc=_.Xe("iframes/:source:");q=
_.Xe("inline/css");typeof q!=="undefined"&&e>0&&q>=e&&(f.ic="1");q=/^#|^fr-/;e={};for(var r in f)_.De(f,r)&&q.test(r)&&(e[r.replace(q,"")]=f[r],delete f[r]);r=_.Xe("iframes/"+a+"/params/si")=="q"?f:e;q=_.Po();for(var w in q)!_.De(q,w)||_.De(f,w)||_.De(e,w)||(r[w]=q[w]);w=[].concat($o);r=_.Xe("iframes/"+a+"/methods");_.Nm(r)&&(w=w.concat(r));for(u in c)_.De(c,u)&&/^on/.test(u)&&(a!="plus"||u!="onconnect")&&(w.push(u),delete f[u]);delete f.callback;e._methods=w.join(",");var u=_.Zl(n,f,e);w=h||{};w.allowPost=
1;w.attributes=Zo;w.dontclear=!k;h={};h.userParams=c;h.url=u;h.type=a;_.fp(a,b,c,u,w,h);b=h.id;c=_.Ce();c.id=b;c.userParams=h.userParams;c.url=h.url;c.type=h.type;c.state=1;_.lo[b]=c;b=h}else b=null;b&&((c=b.id)&&d.push(c),up(a,b))}},vp=function(a,b,c){if(a&&a.nodeType===1&&b){if(c)return 1;if(lp[b]){if(Jo[a.nodeName.toLowerCase()])return(a=a.innerHTML)&&a.replace(/^[\s\xa0]+|[\s\xa0]+$/g,"")?0:1}else{if(kp[b])return 0;if(jp[b])return 1}}return null};_.Be(_.Fe,"platform",{}).go=function(a,b){tp(a,b)};var wp=_.Be(_.Me,"perf",_.Ce()),qp=_.Be(wp,"g",_.Ce()),xp=_.Be(wp,"i",_.Ce()),yp,zp,Ap,rp,Cp,Dp,Ep;_.Be(wp,"r",[]);yp=_.Ce();zp=_.Ce();Ap=function(a,b,c,d){yp[c]=yp[c]||!!d;_.Be(zp,c,[]);zp[c].push([a,b])};rp=function(a,b,c){var d=wp.r;typeof d==="function"?d(a,b,c):d.push([a,b,c])};Cp=function(a,b,c,d){if(b=="_p")throw Error("H");_.Bp(a,b,c,d)};_.Bp=function(a,b,c,d){Dp(b,c)[a]=d||(new Date).getTime();rp(a,b,c)};Dp=function(a,b){a=_.Be(xp,a,_.Ce());return _.Be(a,b,_.Ce())};
Ep=function(a,b,c){var d=null;b&&c&&(d=Dp(b,c)[a]);return d||qp[a]};(function(){function a(h){this.t={};this.tick=function(k,l,m){this.t[k]=[m!=void 0?m:(new Date).getTime(),l];if(m==void 0)try{window.console.timeStamp("CSI/"+k)}catch(n){}};this.getStartTickTime=function(){return this.t.start[0]};this.tick("start",null,h)}var b;if(window.performance)var c=(b=window.performance.timing)&&b.responseStart;var d=c>0?new a(c):new a;window.__gapi_jstiming__={Timer:a,load:d};if(b){var e=b.navigationStart;e>0&&c>=e&&(window.__gapi_jstiming__.srt=c-e)}if(b){var f=window.__gapi_jstiming__.load;
e>0&&c>=e&&(f.tick("_wtsrt",void 0,e),f.tick("wtsrt_","_wtsrt",c),f.tick("tbsd_","wtsrt_"))}try{b=null,window.chrome&&window.chrome.csi&&(b=Math.floor(window.chrome.csi().pageT),f&&e>0&&(f.tick("_tbnd",void 0,window.chrome.csi().startE),f.tick("tbnd_","_tbnd",e))),b==null&&window.gtbExternal&&(b=window.gtbExternal.pageT()),b==null&&window.external&&(b=window.external.pageT,f&&e>0&&(f.tick("_tbnd",void 0,window.external.startE),f.tick("tbnd_","_tbnd",e))),b&&(window.__gapi_jstiming__.pt=b)}catch(h){}})();if(window.__gapi_jstiming__){window.__gapi_jstiming__.GP={};window.__gapi_jstiming__.Qea=1;var Fp=function(a,b,c){var d=a.t[b],e=a.t.start;if(d&&(e||c))return d=a.t[b][0],e=c!=void 0?c:e[0],Math.round(d-e)},Gp=function(a,b,c){var d="";window.__gapi_jstiming__.srt&&(d+="&srt="+window.__gapi_jstiming__.srt,delete window.__gapi_jstiming__.srt);window.__gapi_jstiming__.pt&&(d+="&tbsrt="+window.__gapi_jstiming__.pt,delete window.__gapi_jstiming__.pt);try{window.external&&window.external.tran?d+="&tran="+
window.external.tran:window.gtbExternal&&window.gtbExternal.tran?d+="&tran="+window.gtbExternal.tran():window.chrome&&window.chrome.csi&&(d+="&tran="+window.chrome.csi().tran)}catch(p){}var e=window.chrome;if(e&&(e=e.loadTimes)&&typeof e==="function"&&(e=e())){e.wasFetchedViaSpdy&&(d+="&p=s");if(e.wasNpnNegotiated){d+="&npn=1";var f=e.npnNegotiatedProtocol;f&&(d+="&npnv="+(encodeURIComponent||escape)(f))}e.wasAlternateProtocolAvailable&&(d+="&apa=1")}var h=a.t,k=h.start;e=[];f=[];for(var l in h)if(l!=
"start"&&l.indexOf("_")!=0){var m=h[l][1];m?h[m]&&f.push(l+"."+Fp(a,l,h[m][0])):k&&e.push(l+"."+Fp(a,l))}delete h.start;if(b)for(var n in b)d+="&"+n+"="+b[n];(b=c)||(b="https:"==document.location.protocol?"https://csi.gstatic.com/csi":"http://csi.gstatic.com/csi");return[b,"?v=3","&s="+(window.__gapi_jstiming__.sn||"gwidget")+"&action=",a.name,f.length?"&it="+f.join(","):"",d,"&rt=",e.join(",")].join("")},Hp=function(a,b,c){a=Gp(a,b,c);if(!a)return"";b=new Image;var d=window.__gapi_jstiming__.Qea++;
window.__gapi_jstiming__.GP[d]=b;b.onload=b.onerror=function(){window.__gapi_jstiming__&&delete window.__gapi_jstiming__.GP[d]};b.src=a;b=null;return a};window.__gapi_jstiming__.report=function(a,b,c){var d=document.visibilityState,e="visibilitychange";d||(d=document.webkitVisibilityState,e="webkitvisibilitychange");if(d=="prerender"){var f=!1,h=function(){if(!f){b?b.prerender="1":b={prerender:"1"};if((document.visibilityState||document.webkitVisibilityState)=="prerender")var k=!1;else Hp(a,b,c),
k=!0;k&&(f=!0,document.removeEventListener(e,h,!1))}};document.addEventListener(e,h,!1);return""}return Hp(a,b,c)}};var Ip={g:"gapi_global",m:"gapi_module",w:"gwidget"},Jp=function(a,b){this.type=a?a=="_p"?"m":"w":"g";this.name=a;this.Cs=b};Jp.prototype.key=function(){switch(this.type){case "g":return this.type;case "m":return this.type+"."+this.Cs;case "w":return this.type+"."+this.name+this.Cs}};
var Kp=new Jp,Lp=navigator.userAgent.match(/iPhone|iPad|Android|PalmWebOS|Maemo|Bada/),Mp=_.Be(wp,"_c",_.Ce()),Np=Math.random()<(_.Xe("csi/rate")||0),Pp=function(a,b,c){for(var d=new Jp(b,c),e=_.Be(Mp,d.key(),_.Ce()),f=zp[a]||[],h=0;h<f.length;++h){var k=f[h],l=k[0],m=a,n=b,p=c;k=Ep(k[1],n,p);m=Ep(m,n,p);e[l]=k&&m?m-k:null}yp[a]&&Np&&(Op(Kp),Op(d))},Qp=function(a,b){b=b||[];for(var c=[],d=0;d<b.length;d++)c.push(a+b[d]);return c},Op=function(a){var b=_.xe.__gapi_jstiming__;b.sn=Ip[a.type];var c=new b.Timer(0);
a:{switch(a.type){case "g":var d="global";break a;case "m":d=a.Cs;break a;case "w":d=a.name;break a}d=void 0}c.name=d;d=!1;var e=a.key(),f=Mp[e];c.tick("_start",null,0);for(var h in f)c.tick(h,"_start",f[h]),d=!0;Mp[e]=_.Ce();d&&(h=[],h.push("l"+(_.Xe("isPlusUser")?"1":"0")),d="m"+(Lp?"1":"0"),h.push(d),a.type=="m"?h.push("p"+a.Cs):a.type=="w"&&(e="n"+a.Cs,h.push(e),a.Cs=="0"&&h.push(d+e)),h.push("u"+(_.Xe("isLoggedIn")?"1":"0")),a=Qp("",h),a=Qp("abc_",a).join(","),b.report(c,{e:a}))};
Ap("blt","bs0","bs1");Ap("psi","ps0","ps1");Ap("rpcqi","rqe","rqd");Ap("bsprt","bsrt0","bsrt1");Ap("bsrqt","bsrt1","bsrt2");Ap("bsrst","bsrt2","bsrt3");Ap("mli","ml0","ml1");Ap("mei","me0","me1",!0);Ap("wcdi","wrs","wcdi");Ap("wci","wrs","wdc");Ap("wdi","wrs","wrdi");Ap("wdt","bs0","wrdt");Ap("wri","wrs","wrri",!0);Ap("wrt","bs0","wrrt");Ap("wji","wje0","wje1",!0);Ap("wjli","wjl0","wjl1");Ap("whi","wh0","wh1",!0);Ap("wai","waaf0","waaf1",!0);Ap("wadi","wrs","waaf1",!0);Ap("wadt","bs0","waaf1",!0);
Ap("wprt","wrt0","wrt1");Ap("wrqt","wrt1","wrt2");Ap("wrst","wrt2","wrt3",!0);Ap("fbprt","fsrt0","fsrt1");Ap("fbrqt","fsrt1","fsrt2");Ap("fbrst","fsrt2","fsrt3",!0);Ap("fdns","fdns0","fdns1");Ap("fcon","fcon0","fcon1");Ap("freq","freq0","freq1");Ap("frsp","frsp0","frsp1");Ap("fttfb","fttfb0","fttfb1");Ap("ftot","ftot0","ftot1",!0);var Rp=wp.r;if(typeof Rp!=="function"){for(var Sp;Sp=Rp.shift();)Pp.apply(null,Sp);wp.r=Pp};var Tp=["div"],Up="onload",Vp=!0,Wp=!0,Xp=function(a){return a},Yp=null,Zp=function(a){var b=_.Xe(a);return typeof b!=="undefined"?b:_.Xe("gwidget/"+a)},cq,dq,eq,fq,gq,hq,iq,oq,jq,pq,qq,rq,sq,tq,kq,mq,uq,lq,vq,wq,xq,yq,zq,Aq;Yp=_.Xe();_.Xe("gwidget");var $p=Zp("parsetags");Up=$p==="explicit"||$p==="onload"?$p:Up;var aq=Zp("google_analytics");typeof aq!=="undefined"&&(Vp=!!aq);var bq=Zp("data_layer");typeof bq!=="undefined"&&(Wp=!!bq);cq=function(){var a=this&&this.getId();a&&(_.Me.drw=a)};
dq=function(){_.Me.drw=null};eq=function(a){return function(b){var c=a;typeof b==="number"?c=b:typeof b==="string"&&(c=b.indexOf("px"),c!=-1&&(b=b.substring(0,c)),c=parseInt(b,10));return c}};fq=function(a){typeof a==="string"&&(a=window[a]);return typeof a==="function"?a:null};gq=function(){return Zp("lang")||"en-US"};
hq=function(a){if(!_.Ta.wb("attach")){var b={},c=_.Ta.wb("inline"),d;for(d in c)c.hasOwnProperty(d)&&(b[d]=c[d]);b.open=function(e){var f=e.wc().renderData.id;f=document.getElementById(f);if(!f)throw Error("I");return c.attach(e,f)};_.Ta.Fc("attach",b)}a.style="attach"};iq=function(){var a={};a.width=[eq(450)];a.height=[eq(24)];a.onready=[fq];a.lang=[gq,"hl"];a.iloader=[function(){return _.Me.ILI},"iloader"];return a}();
oq=function(a){var b={};b.Qe=a[0];b.Pp=-1;b.uta="___"+b.Qe+"_";b.Nha="g:"+b.Qe;b.Cra="g-"+b.Qe;b.pZ=[];b.config={};b.xy=[];b.N1={};b.FD={};var c=function(e){for(var f in e)if(_.De(e,f)){b.config[f]=[fq];b.xy.push(f);var h=e[f],k=null,l=null,m=null;typeof h==="function"?k=h:h&&typeof h==="object"&&(k=h.rra,l=h.GD,m=h.NN);m&&(b.xy.push(m),b.config[m]=[fq],b.N1[f]=m);k&&(b.config[f]=[k]);l&&(b.FD[f]=l)}},d=function(e){for(var f={},h=0;h<e.length;++h)f[e[h].toLowerCase()]=1;f[b.Nha]=1;b.Uca=f};a[1]&&
(b.parameters=a[1]);(function(e){b.config=e;for(var f in iq)iq.hasOwnProperty(f)&&!b.config.hasOwnProperty(f)&&(b.config[f]=iq[f])})(a[2]||{});a[3]&&c(a[3]);a[4]&&d(a[4]);a[5]&&(b.Pm=a[5]);b.nta=a[6]===!0;b.qea=a[7];b.Bha=a[8];b.Uca||d(Tp);b.EJ=function(e){b.Pp++;Cp("wrs",b.Qe,String(b.Pp));var f=[],h=e.element,k=e.config,l=":"+b.Qe;l==":plus"&&e.Ed&&e.Ed.action&&(l+="_"+e.Ed.action);var m=jq(b,k),n={};_.Ee(_.Po(),n);for(var p in e.Ed)e.Ed[p]!=null&&(n[p]=e.Ed[p]);p={container:h.id,renderData:e.Lea,
style:"inline",height:k.height,width:k.width};hq(p);b.Pm&&(f[2]=p,f[3]=n,f[4]=m,b.Pm("i",f));l=_.Ta.open(l,p,n,m);e=e.m8;kq(l,k);lq(l,h);mq(b,l,e);nq(b.Qe,b.Pp.toString(),l);f[5]=l;b.Pm&&b.Pm("e",f)};return b};
jq=function(a,b){for(var c={},d=a.xy.length-1;d>=0;--d){var e=a.xy[d],f=b[a.N1[e]||e]||b[e],h=b[e];h&&f!==h&&(f=function(l,m){return function(n){m.apply(this,arguments);l.apply(this,arguments)}}(f,h));f&&(c[e]=f)}for(var k in a.FD)a.FD.hasOwnProperty(k)&&(c[k]=pq(c[k]||function(){},a.FD[k]));c.drefresh=cq;c.erefresh=dq;return c};
pq=function(a,b){return function(c){var d=b(c);if(d){var e=c.href||null;if(Vp){if(window._gat)try{var f=window._gat._getTrackerByName("~0");f&&f._getAccount()!="UA-XXXXX-X"?f._trackSocial("Google",d,e):window._gaq&&window._gaq.push(["_trackSocial","Google",d,e])}catch(k){}if(window.ga&&window.ga.getAll)try{var h=window.ga.getAll();for(f=0;f<h.length;f++)h[f].send("social","Google",d,e)}catch(k){}}if(Wp&&window.dataLayer)try{window.dataLayer.push({event:"social",socialNetwork:"Google",socialAction:d,
socialTarget:e})}catch(k){}}a.call(this,c)}};qq=function(a){return _.Ln&&a instanceof _.Ln};rq=function(a){return qq(a)?"_renderstart":"renderstart"};sq=function(a){return qq(a)?"_ready":"ready"};tq=function(){return!0};kq=function(a,b){if(b.onready){var c=!1,d=function(){c||(c=!0,b.onready.call(null))};a.register(sq(a),d,tq);a.register(rq(a),d,tq)}};
mq=function(a,b,c){var d=a.Qe,e=String(a.Pp),f=!1,h=function(){f||(f=!0,b.getIframeEl(),c&&Cp("wrdt",d,e),Cp("wrdi",d,e))};b.register(rq(b),h,tq);var k=!1;a=function(){k||(k=!0,h(),c&&Cp("wrrt",d,e),Cp("wrri",d,e))};b.register(sq(b),a,tq);qq(b)?b.register("widget-interactive-"+b.id,a,tq):_.$f.register("widget-interactive-"+b.id,a);_.$f.register("widget-csi-tick-"+b.id,function(l,m,n){l==="wdc"?Cp("wdc",d,e,n):l==="wje0"?Cp("wje0",d,e,n):l==="wje1"?Cp("wje1",d,e,n):l=="wh0"?_.Bp("wh0",d,e,n):l=="wh1"?
_.Bp("wh1",d,e,n):l=="wcdi"&&_.Bp("wcdi",d,e,n)})};uq=function(a){return typeof a=="number"?a+"px":a=="100%"?a:null};lq=function(a,b){var c=function(d){d=d||a;var e=uq(d.width);e&&b.style.width!=e&&(b.style.width=e);(d=uq(d.height))&&b.style.height!=d&&(b.style.height=d)};qq(a)?a.setParam("onRestyle",c):(a.register("ready",c,tq),a.register("renderstart",c,tq),a.register("resize",c,tq))};vq=function(a,b){for(var c in iq)if(iq.hasOwnProperty(c)){var d=iq[c][1];d&&!b.hasOwnProperty(d)&&(b[d]=a[d])}return b};
wq=function(a,b){var c={},d;for(d in a)a.hasOwnProperty(d)&&(c[a[d][1]||d]=(a[d]&&a[d][0]||Xp)(b[d.toLowerCase()],b,Yp));return c};xq=function(a){if(a=a.qea)for(var b=0;b<a.length;b++)(new Image).src=a[b]};yq=function(a,b){var c=b.userParams,d=b.siteElement;d||(d=(d=b.iframeNode)&&d.parentNode);if(d&&d.nodeType===1){var e=wq(a.config,c);a.pZ.push({element:d,config:e,Ed:vq(e,wq(a.parameters,c)),xsa:3,m8:!!c["data-onload"],Lea:b})}b=a.pZ;for(a=a.EJ;b.length>0;)a(b.shift())};
zq=function(a,b){a.Pp++;Cp("wrs",a.Qe,String(a.Pp));var c=b.userParams,d=wq(a.config,c),e=[],f=b.iframeNode,h=b.siteElement,k=jq(a,d),l=wq(a.parameters,c);_.Ee(_.Po(),l);l=vq(d,l);c=!!c["data-onload"];var m=_.an,n=_.Ce();n.renderData=b;n.height=d.height;n.width=d.width;n.id=b.id;n.url=b.url;n.iframeEl=f;n.where=n.container=h;n.apis=["_open"];n.messageHandlers=k;n.messageHandlersFilter=_.dn;_.so(n);f=l;a.Pm&&(e[2]=n,e[3]=f,e[4]=k,a.Pm("i",e));k=m.attach(n);k.id=b.id;k.iM(k,n);kq(k,d);lq(k,h);mq(a,
k,c);nq(a.Qe,a.Pp.toString(),k);e[5]=k;a.Pm&&a.Pm("e",e)};Aq=function(a,b){var c=b.url;a.Bha||_.bj(c)?zq(a,b):_.Ta.open?yq(a,b):(0,_.Fg)("iframes",function(){yq(a,b)})};
_.Bq=function(a){var b=oq(a);xq(b);_.Yf(b.Qe,function(d){Aq(b,d)});jp[b.Qe]=!0;var c={va:function(d,e,f){var h=e||{};h.type=b.Qe;e=h.type;delete h.type;var k=pp(d);if(k){d={};for(var l in h)_.De(h,l)&&(d[l.toLowerCase()]=h[l]);d.rd=1;(l=!!d.ri)&&delete d.ri;sp(e,k,d,[],0,l,f)}else _.Vf.log("gapi."+e+".render: missing element "+typeof d==="string"?d:"")},go:function(d){tp(d,b.Qe)},zsa:function(){var d=_.Be(_.Me,"WI",_.Ce()),e;for(e in d)delete d[e]}};a=function(){Up==="onload"&&c.go()};if(!Ko(b.Qe)){if(!_.Wf())try{a()}catch(d){}_.Xf(a)}_.t("gapi."+
b.Qe+".go",c.go);_.t("gapi."+b.Qe+".render",c.va);return c};var Cq=function(){var a=window;return!!a.performance&&!!a.performance.getEntries},nq=function(a,b,c){if(Cq()){var d=function(){var f=!1;return function(){if(f)return!0;f=!0;return!1}}(),e=function(){d()||window.setTimeout(function(){var f=c.getIframeEl().src;var h=f.indexOf("#");h!=-1&&(f=f.substring(0,h));f=window.performance.getEntriesByName(f);f.length<1?f=null:(f=f[0],f=f.responseStart==0?null:f);if(f){h=Math.round(f.requestStart);var k=Math.round(f.responseStart),l=Math.round(f.responseEnd);
Cp("wrt0",a,b,Math.round(f.startTime));Cp("wrt1",a,b,h);Cp("wrt2",a,b,k);Cp("wrt3",a,b,l)}},1E3)};c.register(rq(c),e,tq);c.register(sq(c),e,tq)}};_.t("gapi.widget.make",_.Bq);
_.af=_.af||{};_.af.Hv=function(a,b,c){for(var d=[],e=2,f=arguments.length;e<f;++e)d.push(arguments[e]);return function(){for(var h=d.slice(),k=0,l=arguments.length;k<l;++k)h.push(arguments[k]);return b.apply(a,h)}};_.af.lB=function(a){var b,c,d={};for(b=0;c=a[b];++b)d[c]=c;return d};
_.af=_.af||{};
(function(){function a(c,d){return String.fromCharCode(d)}var b={0:!1,10:!0,13:!0,34:!0,39:!0,60:!0,62:!0,92:!0,8232:!0,8233:!0,65282:!0,65287:!0,65308:!0,65310:!0,65340:!0};_.af.escape=function(c,d){if(c){if(typeof c==="string")return _.af.aG(c);if(typeof c==="Array"){var e=0;for(d=c.length;e<d;++e)c[e]=_.af.escape(c[e])}else if(typeof c==="object"&&d){d={};for(e in c)c.hasOwnProperty(e)&&(d[_.af.aG(e)]=_.af.escape(c[e],!0));return d}}return c};_.af.aG=function(c){if(!c)return c;for(var d=[],e,f,
h=0,k=c.length;h<k;++h)e=c.charCodeAt(h),f=b[e],f===!0?d.push("&#",e,";"):f!==!1&&d.push(c.charAt(h));return d.join("")};_.af.ota=function(c){return c?c.replace(/&#([0-9]+);/g,a):c}})();
_.Ta.Ma={};_.Ta.Ma.Mi={};_.Ta.Ma.Mi.z7=function(a){try{return!!a.document}catch(b){}return!1};_.Ta.Ma.Mi.ZT=function(a){var b=a.parent;return a!=b&&_.Ta.Ma.Mi.z7(b)?_.Ta.Ma.Mi.ZT(b):a};_.Ta.Ma.Mi.sra=function(a){var b=a.userAgent||"";a=a.product||"";return b.indexOf("Opera")!=0&&b.indexOf("WebKit")==-1&&a=="Gecko"&&b.indexOf("rv:1.")>0};
_.Ta.Ma.Mi.Hv=function(a,b,c){for(var d=[],e=2,f=arguments.length;e<f;++e)d.push(arguments[e]);return function(){for(var h=d.slice(),k=0,l=arguments.length;k<l;++k)h.push(arguments[k]);return b.apply(a,h)}};
var Jq,Kq,Lq,Mq,Pq,Qq,Rq,Sq,Tq,Uq,Vq,Wq,Xq;Jq=function(){_.$f.register("_noop_echo",function(){this.callback(_.Ta.w$(_.Ta.rm[this.f]))})};Kq=function(){window.setTimeout(function(){_.$f.call("..","_noop_echo",_.Ta.aea)},0)};Lq=function(a,b,c){var d=function(e){var f=Array.prototype.slice.call(arguments,0),h=f[f.length-1];if(typeof h==="function"){var k=h;f.pop()}f.unshift(b,a,k,c);_.$f.call.apply(_.$f,f)};d._iframe_wrapped_rpc_=!0;return d};
Mq=function(a){_.Ta.dC[a]||(_.Ta.dC[a]={},_.$f.register(a,function(b,c){var d=this.f;if(!(typeof b!="string"||b in{}||d in{})){var e=this.callback,f=_.Ta.dC[a][d],h;f&&Object.hasOwnProperty.call(f,b)?h=f[b]:Object.hasOwnProperty.call(_.Ta.Pq,a)&&(h=_.Ta.Pq[a]);if(h)return d=Array.prototype.slice.call(arguments,1),h._iframe_wrapped_rpc_&&e&&d.push(e),h.apply({},d)}_.Vf.error(['Unregistered call in window "',window.name,'" for method "',a,'", via proxyId "',b,'" from frame "',d,'".'].join(""));return null}));
return _.Ta.dC[a]};_.Nq=function(){var a={};var b=window.location.href;var c=b.indexOf("?"),d=b.indexOf("#");b=(d===-1?b.substr(c+1):[b.substr(c+1,d-c-1),"&",b.substr(d+1)].join("")).split("&");c=window.decodeURIComponent?decodeURIComponent:unescape;d=b.length;for(var e=0;e<d;++e){var f=b[e].indexOf("=");if(f!==-1){var h=b[e].substring(0,f);f=b[e].substring(f+1);f=f.replace(/\+/g," ");try{a[h]=c(f)}catch(k){}}}return a};_.Oq=function(){return _.xe.location.origin||_.xe.location.protocol+"//"+_.xe.location.host};
Pq=function(a){_.Me.h=a};Qq=function(a){_.Me.bsh=a};Rq=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};Sq=function(a){return typeof a==="object"&&/\[native code\]/.test(a.push)};
Tq=function(a,b,c){if(b&&typeof b==="object")for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&d==="___goc"&&typeof b[d]==="undefined"||(a[d]&&b[d]&&typeof a[d]==="object"&&typeof b[d]==="object"&&!Sq(a[d])&&!Sq(b[d])?Tq(a[d],b[d]):b[d]&&typeof b[d]==="object"?(a[d]=Sq(b[d])?[]:{},Tq(a[d],b[d])):a[d]=b[d])};
Uq=function(){var a=window.location.hostname;return a?/(^|\.)(2mdn|ampproject|android|appspot|blogger|blogspot|chrome|chromium|doubleclick|gcpnode|ggpht|gmail|google|google-analytics|googleadservices|googleapis|googleapis-cn|googleoptimize|googlers|googlesource|googlesyndication|googletagmanager|googletagservices|googleusercontent|googlevideo|gstatic|tiltbrush|waze|withgoogle|youtube|ytimg)(\.com?|\.net|\.org)?(\.[a-z][a-z]|\.cat)?$/.test(a):!1};
Vq=function(a){try{var b=(new Function("return ("+a+"\n)"))()}catch(c){}if(typeof b==="object")return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return b};
Wq=function(a,b){if(a&&!/^\s+$/.test(a)){for(;a.charCodeAt(a.length-1)==0;)a=a.substring(0,a.length-1);var c=a,d=Rq("dm");d.push(20);try{var e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(21),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(22),e;a=a.replace(RegExp("([^\"',{}\\s]+?)\\s*:\\s*(.*?)[,}\\s]","g"),function(h,k,l){l=l.startsWith('"')?"%DOUBLE_QUOTE%"+l.substring(1):l;l=l.endsWith('"')?l.slice(0,-1)+"%DOUBLE_QUOTE%":l;return"%DOUBLE_QUOTE%"+
k+"%DOUBLE_QUOTE%:"+l});a=a.replace(/\\'/g,"%SINGLE_QUOTE%");a=a.replace(/"/g,'\\"');a=a.replace(/'/g,'"');a=a.replace(/%SINGLE_QUOTE%/g,"'");a=a.replace(/%DOUBLE_QUOTE%/g,'"');try{e=window.JSON.parse(a)}catch(h){}if(typeof e==="object")return d.push(23),e;try{e=window.JSON.parse("{"+a+"}")}catch(h){}if(typeof e==="object")return d.push(24),e;a=document.getElementsByTagName("script")||[];var f;a.length>0&&(f=a[0].nonce||a[0].getAttribute("nonce"));if(f&&f===b||!f&&Uq())if(e=Vq(c),d.push(25),typeof e===
"object")return e;return{}}};Xq=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&typeof a[a.length-1].___goc==="undefined"&&(c=a.pop());Tq(c,b);a.push(c)};
_.Yq=function(a,b){var c;if(typeof a==="string"){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;_.di(!0);d=window.___gcfg;b=Rq("cu");a=window.___gu;d&&d!==a&&(Xq(b,d),window.___gu=d);d=Rq("cu");e=document.getElementsByTagName("script")||[];a=[];f=[];f.push.apply(f,Rq("us"));for(h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&k.src.indexOf(f[l])==0&&a.push(k);a.length==0&&e.length>0&&e[e.length-1].src&&a.push(e[e.length-1]);for(e=0;e<
a.length;++e)a[e].getAttribute("gapi_processed")||(a[e].setAttribute("gapi_processed",!0),(f=a[e])?(h=f.nodeType,f=h==3||h==4?f.nodeValue:f.textContent||""):f=void 0,h=a[e].nonce||a[e].getAttribute("nonce"),(f=Wq(f,h))&&d.push(f));c&&Xq(b,c);a=Rq("cd");c=0;for(d=a.length;c<d;++c)Tq(_.di(),a[c],!0);a=Rq("ci");c=0;for(d=a.length;c<d;++c)Tq(_.di(),a[c],!0);c=0;for(d=b.length;c<d;++c)Tq(_.di(),b[c],!0)};var Zq,$q=window.location.href,ar=$q.indexOf("?"),br=$q.indexOf("#");
Zq=(br===-1?$q.substr(ar+1):[$q.substr(ar+1,br-ar-1),"&",$q.substr(br+1)].join("")).split("&");for(var cr=window.decodeURIComponent?decodeURIComponent:unescape,dr=0,er=Zq.length;dr<er;++dr){var fr=Zq[dr].indexOf("=");if(fr!==-1){Zq[dr].substring(0,fr);var gr=Zq[dr].substring(fr+1);gr=gr.replace(/\+/g," ");try{cr(gr)}catch(a){}}};if(window.ToolbarApi)hr=window.ToolbarApi,hr.Ia=window.ToolbarApi.getInstance,hr.prototype=window.ToolbarApi.prototype,_.g=hr.prototype,_.g.openWindow=hr.prototype.openWindow,_.g.oQ=hr.prototype.closeWindow,_.g.B_=hr.prototype.setOnCloseHandler,_.g.XP=hr.prototype.canClosePopup,_.g.yZ=hr.prototype.resizeWindow;else{var hr=function(){};hr.Ia=function(){!ir&&window.external&&window.external.GTB_IsToolbar&&(ir=new hr);return ir};_.g=hr.prototype;_.g.openWindow=function(a){return window.external.GTB_OpenPopup&&
window.external.GTB_OpenPopup(a)};_.g.oQ=function(a){window.external.GTB_ClosePopupWindow&&window.external.GTB_ClosePopupWindow(a)};_.g.B_=function(a,b){window.external.GTB_SetOnCloseHandler&&window.external.GTB_SetOnCloseHandler(a,b)};_.g.XP=function(a){return window.external.GTB_CanClosePopup&&window.external.GTB_CanClosePopup(a)};_.g.yZ=function(a,b){return window.external.GTB_ResizeWindow&&window.external.GTB_ResizeWindow(a,b)};var ir=null;window.ToolbarApi=hr;window.ToolbarApi.getInstance=hr.Ia};var jr=/^[-_.0-9A-Za-z]+$/,kr={open:"open",onready:"ready",close:"close",onresize:"resize",onOpen:"open",onReady:"ready",onClose:"close",onResize:"resize",onRenderStart:"renderstart"},lr={onBeforeParentOpen:"beforeparentopen"},mr={onOpen:function(a){var b=a.wc();a.eh(b.container||b.element);return a},onClose:function(a){a.remove()}},nr=function(){_.Ta.hV++;return["I",_.Ta.hV,"_",(new Date).getTime()].join("")},or,pr,qr,tr,ur,vr,wr,yr,xr;_.Ta.Vn=function(a){var b=_.Ce();_.Ee(_.jm,b);_.Ee(a,b);return b};
or=function(a){return a instanceof Array?a.join(","):a instanceof Object?_.Rf(a):a};pr=function(a){var b=_.ei("googleapis.config/elog");if(b)try{b(a)}catch(c){}};qr=function(a){a&&a.match(jr)&&_.Yq("googleapis.config/gcv",a)};_.rr=function(a,b){b=b||{};for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c]);return b};
_.sr=function(a,b,c,d,e){var f=[],h;for(h in a)if(a.hasOwnProperty(h)){var k=b,l=c,m=a[h],n=d,p=Mq(h);p[k]=p[k]||{};n=_.Ta.Ma.Mi.Hv(n,m);m._iframe_wrapped_rpc_&&(n._iframe_wrapped_rpc_=!0);p[k][l]=n;f.push(h)}if(e)for(var q in _.Ta.Pq)_.Ta.Pq.hasOwnProperty(q)&&f.push(q);return f.join(",")};tr=function(a,b,c){var d={};if(a&&a._methods){a=a._methods.split(",");for(var e=0;e<a.length;e++){var f=a[e];d[f]=Lq(f,b,c)}}return d};
ur=function(a){if(a&&a.disableMultiLevelParentRelay)a=!1;else{var b;if(b=_.Eo&&_.Eo._open&&a.style!="inline"&&a.inline!==!0)a=a.container,b=!(a&&(typeof a=="string"&&document.getElementById(a)||document==(a.ownerDocument||a.document)));a=b}return a};vr=function(a,b){var c={};b=b.params||{};for(var d in a)d.charAt(0)=="#"&&(c[d.substring(1)]=a[d]),d.indexOf("fr-")==0&&(c[d.substring(3)]=a[d]),b[d]=="#"&&(c[d]=a[d]);for(var e in c)delete a["fr-"+e],delete a["#"+e],delete a[e];return c};
wr=function(a){if(a.charAt(0)==":"){a="iframes/"+a.substring(1);var b=_.ei(a);a={};_.Ee(b,a);(b=a.url)&&(a.url=_.vm(b));a.params||(a.params={});return a}return{url:_.vm(a)}};yr=function(a){function b(){}b.prototype=xr.prototype;a.prototype=new b};
xr=function(a,b,c,d,e,f,h,k){this.config=wr(a);this.openParams=this.GB=b||{};this.params=c||{};this.methods=d;this.uD=!1;zr(this,b.style);this.callbacks={};Ar(this,function(){var l;(l=this.GB.style)&&_.Ta.Rw[l]?l=_.Ta.Rw[l]:l?(_.Vf.warn(['Missing handler for style "',l,'". Continuing with default handler.'].join("")),l=null):l=mr;if(l){if(typeof l==="function")var m=l(this);else{var n={};for(m in l){var p=l[m];n[m]=typeof p==="function"?_.Ta.Ma.Mi.Hv(l,p,this):p}m=n}for(var q in e)l=m[q],typeof l===
"function"&&Br(this,e[q],_.Ta.Ma.Mi.Hv(m,l))}f&&Br(this,"close",f)});this.Ik=this.ac=h;this.KJ=(k||[]).slice();h&&this.KJ.unshift(h.getId())};xr.prototype.wc=function(){return this.GB};xr.prototype.YG=function(){return this.params};xr.prototype.Wz=function(){return this.methods};xr.prototype.kd=function(){return this.Ik};
var zr=function(a,b){a.uD||((b=b&&!_.Ta.Rw[b]&&_.Ta.KF[b])?(a.JF=[],b(function(){a.uD=!0;for(var c=a.JF.length,d=0;d<c;++d)a.JF[d].call(a)})):a.uD=!0)},Ar=function(a,b){a.uD?b.call(a):a.JF.push(b)};xr.prototype.ye=function(a,b){Ar(this,function(){Br(this,a,b)})};var Br=function(a,b,c){a.callbacks[b]=a.callbacks[b]||[];a.callbacks[b].push(c)};xr.prototype.gp=function(a,b){Ar(this,function(){var c=this.callbacks[a];if(c)for(var d=c.length,e=0;e<d;++e)if(c[e]===b){c.splice(e,1);break}})};
xr.prototype.fi=function(a,b){var c=this.callbacks[a];if(c)for(var d=Array.prototype.slice.call(arguments,1),e=c.length,f=0;f<e;++f)try{var h=c[f].apply({},d)}catch(k){_.Vf.error(['Exception when calling callback "',a,'" with exception "',k.name,": ",k.message,'".'].join("")),pr(k)}return h};var Cr=function(a){return typeof a=="number"?{value:a,tG:a+"px"}:a=="100%"?{value:100,tG:"100%",YV:!0}:null};xr.prototype.send=function(a,b,c){_.Ta.XZ(this,a,b,c)};
xr.prototype.register=function(a,b){var c=this;c.ye(a,function(d){b.call(c,d)})};var Dr=function(a,b,c,d,e,f,h){var k=this;xr.call(this,a,b,c,d,kr,e,f,h);this.id=b.id||nr();this.qw=b.rpctoken&&String(b.rpctoken)||Math.round(_.Pi()*1E9);this.nba=vr(this.params,this.config);this.hG={};Ar(this,function(){k.fi("open");_.rr(k.hG,k)})};yr(Dr);_.g=Dr.prototype;
_.g.eh=function(a,b){if(!this.config.url)return _.Vf.error("Cannot open iframe, empty URL."),this;var c=this.id;_.Ta.rm[c]=this;var d=_.rr(this.methods);d._ready=this.FB;d._close=this.close;d._open=this.WX;d._resizeMe=this.zZ;d._renderstart=this.QX;var e=this.nba;this.qw&&(e.rpctoken=this.qw);e._methods=_.sr(d,c,"",this,!0);this.el=a=typeof a==="string"?document.getElementById(a):a;d={id:c};if(b){d.attributes=b;var f=b.style;if(typeof f==="string"){if(f){var h=[];f=f.split(";");for(var k=f.length,
l=0;l<k;++l){var m=f[l];if(m.length!=0||l+1!=k)m=m.split(":"),m.length==2&&m[0].match(/^[ a-zA-Z_-]+$/)&&m[1].match(/^[ +.%0-9a-zA-Z_-]+$/)?h.push(m.join(":")):_.Vf.error(['Iframe style "',f[l],'" not allowed.'].join(""))}h=h.join(";")}else h="";b.style=h}}this.wc().allowPost&&(d.allowPost=!0);this.wc().forcePost&&(d.forcePost=!0);d.queryParams=this.params;d.fragmentParams=e;d.paramsSerializer=or;this.ji=_.xm(this.config.url,a,d);a=this.ji.getAttribute("data-postorigin")||this.ji.src;_.Ta.rm[c]=this;
_.$f.OC(this.id,this.qw);_.$f.Oj(this.id,a);return this};_.g.Ph=function(a,b){this.hG[a]=b};_.g.getId=function(){return this.id};_.g.getIframeEl=function(){return this.ji};_.g.getSiteEl=function(){return this.el};_.g.setSiteEl=function(a){this.el=a};_.g.FB=function(a){var b=tr(a,this.id,"");this.Ik&&typeof this.methods._ready=="function"&&(a._methods=_.sr(b,this.Ik.getId(),this.id,this,!1),this.methods._ready(a));_.rr(a,this);_.rr(b,this);this.fi("ready",a)};
_.g.QX=function(a){this.fi("renderstart",a)};_.g.close=function(a){a=this.fi("close",a);delete _.Ta.rm[this.id];return a};_.g.remove=function(){var a=document.getElementById(this.id);a&&a.parentNode&&a.parentNode.removeChild(a)};
_.g.WX=function(a){var b=tr(a.params,this.id,a.proxyId);delete a.params._methods;a.openParams.anchor=="_parent"&&(a.openParams.anchor=this.el);if(ur(a.openParams))new Er(a.url,a.openParams,a.params,b,b._onclose,this,a.openedByProxyChain);else{var c=new Dr(a.url,a.openParams,a.params,b,b._onclose,this,a.openedByProxyChain),d=this;Ar(c,function(){var e={childId:c.getId()},f=c.hG;f._toclose=c.close;e._methods=_.sr(f,d.id,c.id,c,!1);b._onopen(e)})}};
_.g.zZ=function(a){if(this.fi("resize",a)===void 0&&this.ji){var b=Cr(a.width);b!=null&&(this.ji.style.width=b.tG);a=Cr(a.height);a!=null&&(this.ji.style.height=a.tG);this.ji.parentElement&&(b!=null&&b.YV||a!=null&&a.YV)&&(this.ji.parentElement.style.display="block")}};
var Er=function(a,b,c,d,e,f,h){var k=this;xr.call(this,a,b,c,d,lr,e,f,h);this.url=a;this.Jp=null;this.eK=nr();Ar(this,function(){k.fi("beforeparentopen");var l=_.rr(k.methods);l._onopen=k.Rda;l._ready=k.FB;l._onclose=k.Pda;k.params._methods=_.sr(l,"..",k.eK,k,!0);l={};for(var m in k.params)l[m]=or(k.params[m]);_.Eo._open({url:k.config.url,openParams:k.GB,params:l,proxyId:k.eK,openedByProxyChain:k.KJ})})};yr(Er);Er.prototype.H$=function(){return this.Jp};
Er.prototype.Rda=function(a){this.Jp=a.childId;var b=tr(a,"..",this.Jp);_.rr(b,this);this.close=b._toclose;_.Ta.rm[this.Jp]=this;this.Ik&&this.methods._onopen&&(a._methods=_.sr(b,this.Ik.getId(),this.Jp,this,!1),this.methods._onopen(a))};Er.prototype.FB=function(a){var b=String(this.Jp),c=tr(a,"..",b);_.rr(a,this);_.rr(c,this);this.fi("ready",a);this.Ik&&this.methods._ready&&(a._methods=_.sr(c,this.Ik.getId(),b,this,!1),this.methods._ready(a))};
Er.prototype.Pda=function(a){if(this.Ik&&this.methods._onclose)this.methods._onclose(a);else return a=this.fi("close",a),delete _.Ta.rm[this.Jp],a};
var Fr=function(a,b,c,d,e,f,h){xr.call(this,a,b,c,d,lr,f,h);this.id=b.id||nr();this.oha=e;d._close=this.close;this.onClosed=this.JX;this.d2=0;Ar(this,function(){this.fi("beforeparentopen");var k=_.rr(this.methods);this.params._methods=_.sr(k,"..",this.eK,this,!0);k={};k.queryParams=this.params;a=_.om(_.ye,this.config.url,this.id,k);var l=e.openWindow(a);this.canAutoClose=function(m){m(e.XP(l))};e.B_(l,this);this.d2=l})};yr(Fr);
Fr.prototype.close=function(a){a=this.fi("close",a);this.oha.oQ(this.d2);return a};Fr.prototype.JX=function(){this.fi("close")};_.Eo.send=function(a,b,c){_.Ta.XZ(_.Eo,a,b,c)};
(function(){function a(h){return _.Ta.Rw[h]}function b(h,k){_.Ta.Rw[h]=k}function c(h){h=h||{};h.height==="auto"&&(h.height=_.zm());var k=window&&hr&&hr.Ia();k?k.yZ(h.width||0,h.height||0):_.Eo&&_.Eo._resizeMe&&_.Eo._resizeMe(h)}function d(h){qr(h)}_.Ta.rm={};_.Ta.Rw={};_.Ta.KF={};_.Ta.hV=0;_.Ta.dC={};_.Ta.Pq={};_.Ta.QB=null;_.Ta.PB=[];_.Ta.aea=function(h){var k=!1;try{if(h!=null){var l=window.parent.frames[h.id];k=l.iframer.id==h.id&&l.iframes.openedId_(_.Eo.id)}}catch(m){}try{_.Ta.QB={origin:this.origin,
referer:this.referer,claimedOpenerId:h&&h.id,claimedOpenerProxyChain:h&&h.proxyChain||[],sameOrigin:k};for(h=0;h<_.Ta.PB.length;++h)_.Ta.PB[h](_.Ta.QB);_.Ta.PB=[]}catch(m){pr(m)}};_.Ta.w$=function(h){var k=h&&h.Ik,l=null;k&&(l={},l.id=k.getId(),l.proxyChain=h.KJ);return l};Jq();if(window.parent!=window){var e=_.Nq();e.gcv&&qr(e.gcv);var f=e.jsh;f&&Pq(f);_.rr(tr(e,"..",""),_.Eo);_.rr(e,_.Eo);Kq()}_.Ta.wb=a;_.Ta.Fc=b;_.Ta.lga=d;_.Ta.resize=c;_.Ta.U9=function(h){return _.Ta.KF[h]};_.Ta.pL=function(h,
k){_.Ta.KF[h]=k};_.Ta.xZ=c;_.Ta.Gga=d;_.Ta.uA={};_.Ta.uA.get=a;_.Ta.uA.set=b;_.Ta.allow=function(h,k){Mq(h);_.Ta.Pq[h]=k||window[h]};_.Ta.vqa=function(h){delete _.Ta.Pq[h]};_.Ta.open=function(h,k,l,m,n,p){arguments.length==3?m={}:arguments.length==4&&typeof m==="function"&&(n=m,m={});var q=k.style==="bubble"&&hr?hr.Ia():null;return q?new Fr(h,k,l,m,q,n,p):ur(k)?new Er(h,k,l,m,n,p):new Dr(h,k,l,m,n,p)};_.Ta.close=function(h,k){_.Eo&&_.Eo._close&&_.Eo._close(h,k)};_.Ta.ready=function(h,k,l){arguments.length==
2&&typeof k==="function"&&(l=k,k={});var m=h||{};"height"in m||(m.height=_.zm());m._methods=_.sr(k||{},"..","",_.Eo,!0);_.Eo&&_.Eo._ready&&_.Eo._ready(m,l)};_.Ta.KT=function(h){_.Ta.QB?h(_.Ta.QB):_.Ta.PB.push(h)};_.Ta.Tda=function(h){return!!_.Ta.rm[h]};_.Ta.d$=function(){return["https://ssl.gstatic.com/gb/js/",_.ei("googleapis.config/gcv")].join("")};_.Ta.RY=function(h){var k={mouseover:1,mouseout:1};if(_.Eo._event)for(var l=0;l<h.length;l++){var m=h[l];m in k&&document.addEventListener(m,function(n){_.Eo._event({event:n.type,
timestamp:(new Date).getTime()})},!0)}};_.Ta.XZ=function(h,k,l,m){var n=this,p=[];l!==void 0&&p.push(l);m&&p.push(function(q){m.call(n,[q])});h[k]&&h[k].apply(h,p)};_.Ta.CROSS_ORIGIN_IFRAMES_FILTER=function(){return!0};_.Ta.y7=function(h,k,l){var m=Array.prototype.slice.call(arguments);_.Ta.KT(function(n){n.sameOrigin&&(m.unshift("/"+n.claimedOpenerId+"|"+window.location.protocol+"//"+window.location.host),_.$f.call.apply(_.$f,m))})};_.Ta.Fea=function(h,k){_.$f.register(h,k)};_.Ta.sga=Pq;_.Ta.f_=
Qq;_.Ta.MW=pr;_.Ta.iV=_.Eo})();_.t("iframes.allow",_.Ta.allow);_.t("iframes.callSiblingOpener",_.Ta.y7);_.t("iframes.registerForOpenedSibling",_.Ta.Fea);_.t("iframes.close",_.Ta.close);_.t("iframes.getGoogleConnectJsUri",_.Ta.d$);_.t("iframes.getHandler",_.Ta.wb);_.t("iframes.getDeferredHandler",_.Ta.U9);_.t("iframes.getParentInfo",_.Ta.KT);_.t("iframes.iframer",_.Ta.iV);_.t("iframes.open",_.Ta.open);_.t("iframes.openedId_",_.Ta.Tda);_.t("iframes.propagate",_.Ta.RY);_.t("iframes.ready",_.Ta.ready);_.t("iframes.resize",_.Ta.resize);
_.t("iframes.setGoogleConnectJsVersion",_.Ta.lga);_.t("iframes.setBootstrapHint",_.Ta.f_);_.t("iframes.setJsHint",_.Ta.sga);_.t("iframes.setHandler",_.Ta.Fc);_.t("iframes.setDeferredHandler",_.Ta.pL);_.t("IframeBase",xr);_.t("IframeBase.prototype.addCallback",xr.prototype.ye);_.t("IframeBase.prototype.getMethods",xr.prototype.Wz);_.t("IframeBase.prototype.getOpenerIframe",xr.prototype.kd);_.t("IframeBase.prototype.getOpenParams",xr.prototype.wc);_.t("IframeBase.prototype.getParams",xr.prototype.YG);
_.t("IframeBase.prototype.removeCallback",xr.prototype.gp);_.t("Iframe",Dr);_.t("Iframe.prototype.close",Dr.prototype.close);_.t("Iframe.prototype.exposeMethod",Dr.prototype.Ph);_.t("Iframe.prototype.getId",Dr.prototype.getId);_.t("Iframe.prototype.getIframeEl",Dr.prototype.getIframeEl);_.t("Iframe.prototype.getSiteEl",Dr.prototype.getSiteEl);_.t("Iframe.prototype.openInto",Dr.prototype.eh);_.t("Iframe.prototype.remove",Dr.prototype.remove);_.t("Iframe.prototype.setSiteEl",Dr.prototype.setSiteEl);
_.t("Iframe.prototype.addCallback",Dr.prototype.ye);_.t("Iframe.prototype.getMethods",Dr.prototype.Wz);_.t("Iframe.prototype.getOpenerIframe",Dr.prototype.kd);_.t("Iframe.prototype.getOpenParams",Dr.prototype.wc);_.t("Iframe.prototype.getParams",Dr.prototype.YG);_.t("Iframe.prototype.removeCallback",Dr.prototype.gp);_.t("IframeProxy",Er);_.t("IframeProxy.prototype.getTargetIframeId",Er.prototype.H$);_.t("IframeProxy.prototype.addCallback",Er.prototype.ye);_.t("IframeProxy.prototype.getMethods",Er.prototype.Wz);
_.t("IframeProxy.prototype.getOpenerIframe",Er.prototype.kd);_.t("IframeProxy.prototype.getOpenParams",Er.prototype.wc);_.t("IframeProxy.prototype.getParams",Er.prototype.YG);_.t("IframeProxy.prototype.removeCallback",Er.prototype.gp);_.t("IframeWindow",Fr);_.t("IframeWindow.prototype.close",Fr.prototype.close);_.t("IframeWindow.prototype.onClosed",Fr.prototype.JX);_.t("iframes.util.getTopMostAccessibleWindow",_.Ta.Ma.Mi.ZT);_.t("iframes.handlers.get",_.Ta.uA.get);_.t("iframes.handlers.set",_.Ta.uA.set);
_.t("iframes.resizeMe",_.Ta.xZ);_.t("iframes.setVersionOverride",_.Ta.Gga);_.t("iframes.CROSS_ORIGIN_IFRAMES_FILTER",_.Ta.CROSS_ORIGIN_IFRAMES_FILTER);_.t("IframeBase.prototype.send",xr.prototype.send);_.t("IframeBase.prototype.register",xr.prototype.register);_.t("Iframe.prototype.send",Dr.prototype.send);_.t("Iframe.prototype.register",Dr.prototype.register);_.t("IframeProxy.prototype.send",Er.prototype.send);_.t("IframeProxy.prototype.register",Er.prototype.register);
_.t("IframeWindow.prototype.send",Fr.prototype.send);_.t("IframeWindow.prototype.register",Fr.prototype.register);_.t("iframes.iframer.send",_.Ta.iV.send);
var St=_.Ta.Fc,Tt={open:function(a){var b=_.oo(a.wc());return a.eh(b,{style:_.po(b)})},attach:function(a,b){var c=_.oo(a.wc()),d=b.id,e=b.getAttribute("data-postorigin")||b.src,f=/#(?:.*&)?rpctoken=(\d+)/.exec(e);f=f&&f[1];a.id=d;a.qw=f;a.el=c;a.ji=b;_.Ta.rm[d]=a;b=_.rr(a.methods);b._ready=a.FB;b._close=a.close;b._open=a.WX;b._resizeMe=a.zZ;b._renderstart=a.QX;_.sr(b,d,"",a,!0);_.$f.OC(a.id,a.qw);_.$f.Oj(a.id,e);c=_.Ta.Vn({style:_.po(c)});for(var h in c)Object.prototype.hasOwnProperty.call(c,h)&&
(h=="style"?a.ji.style.cssText=c[h]:a.ji.setAttribute(h,c[h]))}};Tt.onready=_.qo;Tt.onRenderStart=_.qo;Tt.close=_.ro;St("inline",Tt);
_.Eh=function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(_.kd(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var h=0;h<f;h++)a[e+h]=d[h]}else a.push(d)}};_.Fh=function(a,b){b=b||a;for(var c=0,d=0,e={};d<a.length;){var f=a[d++],h=_.vb(f)?"o"+_.uh(f):(typeof f).charAt(0)+f;Object.prototype.hasOwnProperty.call(e,h)||(e[h]=!0,b[c++]=f)}b.length=c};_.Gh=function(a){for(var b in a)return!1;return!0};
_.Hh=function(a,b){a.src=_.kc(b);(b=_.Gc("script",a.ownerDocument))&&a.setAttribute("nonce",b)};_.Ih=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}return b};var Jh,Kh,Mh;Jh={};Kh=null;_.Lh=_.Bd||_.Cd||!_.Dh&&typeof _.Xa.atob=="function";_.Nh=function(a,b){b===void 0&&(b=0);Mh();b=Jh[b];for(var c=Array(Math.floor(a.length/3)),d=b[64]||"",e=0,f=0;e<a.length-2;e+=3){var h=a[e],k=a[e+1],l=a[e+2],m=b[h>>2];h=b[(h&3)<<4|k>>4];k=b[(k&15)<<2|l>>6];l=b[l&63];c[f++]=m+h+k+l}m=0;l=d;switch(a.length-e){case 2:m=a[e+1],l=b[(m&15)<<2]||d;case 1:a=a[e],c[f]=b[a>>2]+b[(a&3)<<4|m>>4]+l+d}return c.join("")};
_.Oh=function(a,b){function c(l){for(;d<a.length;){var m=a.charAt(d++),n=Kh[m];if(n!=null)return n;if(!_.xc(m))throw Error("w`"+m);}return l}Mh();for(var d=0;;){var e=c(-1),f=c(0),h=c(64),k=c(64);if(k===64&&e===-1)break;b(e<<2|f>>4);h!=64&&(b(f<<4&240|h>>2),k!=64&&b(h<<6&192|k))}};
Mh=function(){if(!Kh){Kh={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++){var d=a.concat(b[c].split(""));Jh[c]=d;for(var e=0;e<d.length;e++){var f=d[e];Kh[f]===void 0&&(Kh[f]=e)}}}};
var ki;_.ji=function(a){this.Bc=a||{cookie:""}};_.g=_.ji.prototype;_.g.isEnabled=function(){if(!_.Xa.navigator.cookieEnabled)return!1;if(!this.isEmpty())return!0;this.set("TESTCOOKIESENABLED","1",{YI:60});if(this.get("TESTCOOKIESENABLED")!=="1")return!1;this.remove("TESTCOOKIESENABLED");return!0};
_.g.set=function(a,b,c){var d=!1;if(typeof c==="object"){var e=c.Gsa;d=c.secure||!1;var f=c.domain||void 0;var h=c.path||void 0;var k=c.YI}if(/[;=\s]/.test(a))throw Error("z`"+a);if(/[;\r\n]/.test(b))throw Error("A`"+b);k===void 0&&(k=-1);this.Bc.cookie=a+"="+b+(f?";domain="+f:"")+(h?";path="+h:"")+(k<0?"":k==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+k*1E3)).toUTCString())+(d?";secure":"")+(e!=null?";samesite="+e:"")};
_.g.get=function(a,b){for(var c=a+"=",d=(this.Bc.cookie||"").split(";"),e=0,f;e<d.length;e++){f=_.zc(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};_.g.remove=function(a,b,c){var d=this.Fl(a);this.set(a,"",{YI:0,path:b,domain:c});return d};_.g.lg=function(){return ki(this).keys};_.g.Ye=function(){return ki(this).values};_.g.isEmpty=function(){return!this.Bc.cookie};_.g.Zb=function(){return this.Bc.cookie?(this.Bc.cookie||"").split(";").length:0};
_.g.Fl=function(a){return this.get(a)!==void 0};_.g.clear=function(){for(var a=ki(this).keys,b=a.length-1;b>=0;b--)this.remove(a[b])};ki=function(a){a=(a.Bc.cookie||"").split(";");for(var b=[],c=[],d,e,f=0;f<a.length;f++)e=_.zc(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));return{keys:b,values:c}};_.li=new _.ji(typeof document=="undefined"?null:document);
_.ti={};_.ui=function(a){return _.ti[a||"token"]||null};
_.cj=function(a){a&&typeof a.dispose=="function"&&a.dispose()};_.dj=function(){this.Lg=this.Lg;this.Qo=this.Qo};_.dj.prototype.Lg=!1;_.dj.prototype.isDisposed=function(){return this.Lg};_.dj.prototype.dispose=function(){this.Lg||(this.Lg=!0,this.ua())};_.dj.prototype[Symbol.dispose]=function(){this.dispose()};_.fj=function(a,b){_.ej(a,_.bb(_.cj,b))};_.ej=function(a,b){a.Lg?b():(a.Qo||(a.Qo=[]),a.Qo.push(b))};_.dj.prototype.ua=function(){if(this.Qo)for(;this.Qo.length;)this.Qo.shift()()};
var nj;nj=function(a,b){for(var c in a)if(b.call(void 0,a[c],c,a))return!0;return!1};_.oj=function(a){this.src=a;this.je={};this.ox=0};_.qj=function(a,b){this.type="function"==typeof _.pj&&a instanceof _.pj?String(a):a;this.currentTarget=this.target=b;this.defaultPrevented=this.dw=!1};_.qj.prototype.stopPropagation=function(){this.dw=!0};_.qj.prototype.preventDefault=function(){this.defaultPrevented=!0};_.rj=function(a,b){_.qj.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.XJ=!1;this.pointerId=0;this.pointerType="";this.timeStamp=0;this.Df=null;a&&this.init(a,b)};_.eb(_.rj,_.qj);
_.rj.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.offsetX=_.Cd||a.offsetX!==void 0?a.offsetX:a.layerX,
this.offsetY=_.Cd||a.offsetY!==void 0?a.offsetY:a.layerY,this.clientX=a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||(c=="keypress"?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.XJ=_.Ed?a.metaKey:a.ctrlKey;this.pointerId=a.pointerId||0;this.pointerType=
a.pointerType;this.state=a.state;this.timeStamp=a.timeStamp;this.Df=a;a.defaultPrevented&&_.rj.N.preventDefault.call(this)};_.rj.prototype.stopPropagation=function(){_.rj.N.stopPropagation.call(this);this.Df.stopPropagation?this.Df.stopPropagation():this.Df.cancelBubble=!0};_.rj.prototype.preventDefault=function(){_.rj.N.preventDefault.call(this);var a=this.Df;a.preventDefault?a.preventDefault():a.returnValue=!1};_.sj="closure_listenable_"+(Math.random()*1E6|0);_.tj=function(a){return!(!a||!a[_.sj])};var uj=0;var vj=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.Jf=e;this.key=++uj;this.iw=this.vy=!1},wj=function(a){a.iw=!0;a.listener=null;a.proxy=null;a.src=null;a.Jf=null};_.oj.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.je[f];a||(a=this.je[f]=[],this.ox++);var h=xj(a,b,d,e);h>-1?(b=a[h],c||(b.vy=!1)):(b=new vj(b,this.src,f,!!d,e),b.vy=c,a.push(b));return b};_.oj.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.je))return!1;var e=this.je[a];b=xj(e,b,c,d);return b>-1?(wj(e[b]),Array.prototype.splice.call(e,b,1),e.length==0&&(delete this.je[a],this.ox--),!0):!1};
_.yj=function(a,b){var c=b.type;if(!(c in a.je))return!1;var d=_.gj(a.je[c],b);d&&(wj(b),a.je[c].length==0&&(delete a.je[c],a.ox--));return d};_.oj.prototype.removeAll=function(a){a=a&&a.toString();var b=0,c;for(c in this.je)if(!a||c==a){for(var d=this.je[c],e=0;e<d.length;e++)++b,wj(d[e]);delete this.je[c];this.ox--}return b};_.oj.prototype.Hq=function(a,b,c,d){a=this.je[a.toString()];var e=-1;a&&(e=xj(a,b,c,d));return e>-1?a[e]:null};
_.oj.prototype.hasListener=function(a,b){var c=a!==void 0,d=c?a.toString():"",e=b!==void 0;return nj(this.je,function(f){for(var h=0;h<f.length;++h)if(!(c&&f[h].type!=d||e&&f[h].capture!=b))return!0;return!1})};var xj=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.iw&&f.listener==b&&f.capture==!!c&&f.Jf==d)return e}return-1};var zj,Aj,Bj,Fj,Hj,Ij,Jj,Lj;zj="closure_lm_"+(Math.random()*1E6|0);Aj={};Bj=0;_.Dj=function(a,b,c,d,e){if(d&&d.once)return _.Cj(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.Dj(a,b[f],c,d,e);return null}c=_.Ej(c);return _.tj(a)?a.na(b,c,_.vb(d)?!!d.capture:!!d,e):Fj(a,b,c,!1,d,e)};
Fj=function(a,b,c,d,e,f){if(!b)throw Error("B");var h=_.vb(e)?!!e.capture:!!e,k=_.Gj(a);k||(a[zj]=k=new _.oj(a));c=k.add(b,c,d,h,f);if(c.proxy)return c;d=Hj();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)_.vi||(e=h),e===void 0&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(Ij(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("C");Bj++;return c};
Hj=function(){var a=Jj,b=function(c){return a.call(b.src,b.listener,c)};return b};_.Cj=function(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)_.Cj(a,b[f],c,d,e);return null}c=_.Ej(c);return _.tj(a)?a.rr(b,c,_.vb(d)?!!d.capture:!!d,e):Fj(a,b,c,!0,d,e)};
_.Kj=function(a){if(typeof a==="number"||!a||a.iw)return!1;var b=a.src;if(_.tj(b))return b.DN(a);var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(Ij(c),d):b.addListener&&b.removeListener&&b.removeListener(d);Bj--;(c=_.Gj(b))?(_.yj(c,a),c.ox==0&&(c.src=null,b[zj]=null)):wj(a);return!0};Ij=function(a){return a in Aj?Aj[a]:Aj[a]="on"+a};
Jj=function(a,b){if(a.iw)a=!0;else{b=new _.rj(b,this);var c=a.listener,d=a.Jf||a.src;a.vy&&_.Kj(a);a=c.call(d,b)}return a};_.Gj=function(a){a=a[zj];return a instanceof _.oj?a:null};Lj="__closure_events_fn_"+(Math.random()*1E9>>>0);_.Ej=function(a){if(typeof a==="function")return a;a[Lj]||(a[Lj]=function(b){return a.handleEvent(b)});return a[Lj]};_.mj(function(a){Jj=a(Jj)});
_.Mj=function(a,b){var c=a.length-b.length;return c>=0&&a.indexOf(b,c)==c};_.Zd.prototype.O=_.pb(1,function(a){return _.be(this.Bc,a)});_.Nj=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)_.Nj(a,b[f],c,d,e);else d=_.vb(d)?!!d.capture:!!d,c=_.Ej(c),_.tj(a)?a.Ac(b,c,d,e):a&&(a=_.Gj(a))&&(b=a.Hq(b,c,d,e))&&_.Kj(b)};_.Oj=function(){_.dj.call(this);this.rk=new _.oj(this);this.U6=this;this.PJ=null};_.eb(_.Oj,_.dj);_.Oj.prototype[_.sj]=!0;_.g=_.Oj.prototype;_.g.Yn=function(){return this.PJ};
_.g.aD=function(a){this.PJ=a};_.g.addEventListener=function(a,b,c,d){_.Dj(this,a,b,c,d)};_.g.removeEventListener=function(a,b,c,d){_.Nj(this,a,b,c,d)};
_.g.dispatchEvent=function(a){var b,c=this.Yn();if(c)for(b=[];c;c=c.Yn())b.push(c);c=this.U6;var d=a.type||a;if(typeof a==="string")a=new _.qj(a,c);else if(a instanceof _.qj)a.target=a.target||c;else{var e=a;a=new _.qj(d,c);_.ij(a,e)}e=!0;var f;if(b)for(f=b.length-1;!a.dw&&f>=0;f--){var h=a.currentTarget=b[f];e=h.iu(d,!0,a)&&e}a.dw||(h=a.currentTarget=c,e=h.iu(d,!0,a)&&e,a.dw||(e=h.iu(d,!1,a)&&e));if(b)for(f=0;!a.dw&&f<b.length;f++)h=a.currentTarget=b[f],e=h.iu(d,!1,a)&&e;return e};
_.g.ua=function(){_.Oj.N.ua.call(this);this.tK();this.PJ=null};_.g.na=function(a,b,c,d){return this.rk.add(String(a),b,!1,c,d)};_.g.rr=function(a,b,c,d){return this.rk.add(String(a),b,!0,c,d)};_.g.Ac=function(a,b,c,d){return this.rk.remove(String(a),b,c,d)};_.g.DN=function(a){return _.yj(this.rk,a)};_.g.tK=function(){this.rk&&this.rk.removeAll(void 0)};
_.g.iu=function(a,b,c){a=this.rk.je[String(a)];if(!a)return!0;a=a.concat();for(var d=!0,e=0;e<a.length;++e){var f=a[e];if(f&&!f.iw&&f.capture==b){var h=f.listener,k=f.Jf||f.src;f.vy&&this.DN(f);d=h.call(k,c)!==!1&&d}}return d&&!c.defaultPrevented};_.g.Hq=function(a,b,c,d){return this.rk.Hq(String(a),b,c,d)};_.g.hasListener=function(a,b){return this.rk.hasListener(a!==void 0?String(a):void 0,b)};
var Gr;Gr=function(){var a=_.Jc();if(_.Qc())return _.Yc(a);a=_.Nc(a);var b=_.Xc(a);return _.Pc()?b(["Version","Opera"]):_.Sc()?b(["Edge"]):_.Tc()?b(["Edg"]):_.Mc("Silk")?b(["Silk"]):_.Wc()?b(["Chrome","CriOS","HeadlessChrome"]):(a=a[2])&&a[1]||""};_.Hr=function(a){return _.Dc(Gr(),a)>=0};_.Jr=function(){return _.Sb&&_.Kc?_.Kc.mobile:!_.Ir()&&(_.Mc("iPod")||_.Mc("iPhone")||_.Mc("Android")||_.Mc("IEMobile"))};
_.Ir=function(){return _.Sb&&_.Kc?!_.Kc.mobile&&(_.Mc("iPad")||_.Mc("Android")||_.Mc("Silk")):_.Mc("iPad")||_.Mc("Android")&&!_.Mc("Mobile")||_.Mc("Silk")};_.Kr=function(){return!_.Jr()&&!_.Ir()};
var ct;ct=function(a,b,c){return arguments.length<=2?Array.prototype.slice.call(a,b):Array.prototype.slice.call(a,b,c)};_.dt=function(a,b,c,d){return Array.prototype.splice.apply(a,ct(arguments,1))};_.et=function(a,b,c){if(a!==null&&b in a)throw Error("h`"+b);a[b]=c};_.ft=function(a,b){var c=b||document;c.getElementsByClassName?a=c.getElementsByClassName(a)[0]:(c=document,a=a?(b||c).querySelector(a?"."+a:""):_.ce(c,"*",a,b)[0]||null);return a||null};
_.gt=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b.nextSibling)};_.it=function(a,b,c){a&&!c&&(a=a.parentNode);for(c=0;a;){if(b(a))return a;a=a.parentNode;c++}return null};_.jt=function(a){_.dj.call(this);this.ug=a;this.mc={}};_.eb(_.jt,_.dj);var kt=[];_.jt.prototype.na=function(a,b,c,d){return this.Dv(a,b,c,d)};
_.jt.prototype.Dv=function(a,b,c,d,e){Array.isArray(b)||(b&&(kt[0]=b.toString()),b=kt);for(var f=0;f<b.length;f++){var h=_.Dj(a,b[f],c||this.handleEvent,d||!1,e||this.ug||this);if(!h)break;this.mc[h.key]=h}return this};_.jt.prototype.rr=function(a,b,c,d){return lt(this,a,b,c,d)};var lt=function(a,b,c,d,e,f){if(Array.isArray(c))for(var h=0;h<c.length;h++)lt(a,b,c[h],d,e,f);else{b=_.Cj(b,c,d||a.handleEvent,e,f||a.ug||a);if(!b)return a;a.mc[b.key]=b}return a};
_.jt.prototype.Ac=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)this.Ac(a,b[f],c,d,e);else c=c||this.handleEvent,d=_.vb(d)?!!d.capture:!!d,e=e||this.ug||this,c=_.Ej(c),d=!!d,b=_.tj(a)?a.Hq(b,c,d,e):a?(a=_.Gj(a))?a.Hq(b,c,d,e):null:null,b&&(_.Kj(b),delete this.mc[b.key]);return this};_.jt.prototype.removeAll=function(){_.Zb(this.mc,function(a,b){this.mc.hasOwnProperty(b)&&_.Kj(a)},this);this.mc={}};_.jt.prototype.ua=function(){_.jt.N.ua.call(this);this.removeAll()};
_.jt.prototype.handleEvent=function(){throw Error("K");};
var Xu,Yu,Zu,$u,av,cv,dv,ev,fv,hv;_.Vu=function(a,b){for(var c in a)if(!(c in b)||a[c]!==b[c])return!1;for(var d in b)if(!(d in a))return!1;return!0};_.Wu=!1;Xu=function(a){try{_.Wu&&window.console&&window.console.log&&window.console.log(a)}catch(b){}};Yu=function(a){try{window.console&&window.console.warn&&window.console.warn(a)}catch(b){}};Zu=function(a,b){if(!a)return-1;if(a.indexOf)return a.indexOf(b,void 0);for(var c=0,d=a.length;c<d;c++)if(a[c]===b)return c;return-1};
$u=function(a,b){function c(){}if(!a)throw Error("N");if(!b)throw Error("O");c.prototype=b.prototype;a.prototype=new c;a.prototype.constructor=a};av=function(a){return Object.prototype.toString.call(a)==="[object Function]"};_.bv=function(a){var b={};if(a)for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c]);return b};cv=function(a){var b=location.hash;a=new RegExp("[&#]"+a+"=([^&]*)");b=decodeURIComponent(b);b=a.exec(b);return b==null?"":b[1].replace(/\+/g," ")};
dv=function(a,b,c){if(a.addEventListener)a.addEventListener(b,c,!1);else if(a.attachEvent)a.attachEvent("on"+b,c);else throw Error("P`"+b);};ev={token:1,id_token:1};fv=function(){var a=navigator.userAgent.toLowerCase();return a.indexOf("msie")!=-1&&parseInt(a.split("msie")[1],10)==8};_.gv=window.JSON;hv=function(a){this.PN=a||[];this.qc={}};
hv.prototype.addEventListener=function(a,b){if(!(Zu(this.PN,a)>=0))throw Error("R`"+a);if(!av(b))throw Error("S`"+a);this.qc[a]||(this.qc[a]=[]);Zu(this.qc[a],b)<0&&this.qc[a].push(b)};hv.prototype.removeEventListener=function(a,b){if(!(Zu(this.PN,a)>=0))throw Error("R`"+a);av(b)&&this.qc[a]&&this.qc[a].length&&(b=Zu(this.qc[a],b),b>=0&&this.qc[a].splice(b,1))};
hv.prototype.dispatchEvent=function(a){var b=a.type;if(!(b&&Zu(this.PN,b)>=0))throw Error("T`"+b);if(this.qc[b]&&this.qc[b].length)for(var c=this.qc[b].length,d=0;d<c;d++)this.qc[b][d](a)};var iv,jv,lv,pv,qv,Hv,Iv,Kv,Lv,Nv,Rv,Sv,Tv,Xv;iv={};jv={};_.kv=function(){if(_.ed()&&!_.Hr("118"))return!1;var a=_.Wc()&&!_.Tc()&&!_.Uc(),b=_.$c()||_.Kr();return"IdentityCredential"in window&&a&&b&&_.Hr("132")&&(_.Kr()||_.$c())};lv={google:{fedcmConfigUrl:"https://accounts.google.com/o/fedcm/config.json",authServerUrl:"https://accounts.google.com/o/oauth2/auth",idpIFrameUrl:"https://accounts.google.com/o/oauth2/iframe"}};_.mv=function(a,b){if(a=lv[a])return a[b]};
_.nv=function(a,b){if(!a)throw Error("U");if(!b.authServerUrl)throw Error("V");if(!b.idpIFrameUrl)throw Error("W");lv[a]={authServerUrl:b.authServerUrl,idpIFrameUrl:b.idpIFrameUrl};b.fedcmConfigUrl?lv[a].fedcmConfigUrl=b.fedcmConfigUrl:a==="google"&&(lv[a].fedcmConfigUrl="https://accounts.google.com/o/fedcm/config.json")};_.ov=void 0;
pv=function(a){a.style.position="absolute";a.style.width="1px";a.style.height="1px";a.style.left="-9999px";a.style.top="-9999px";a.style.right="-9999px";a.style.bottom="-9999px";a.style.display="none";a.setAttribute("aria-hidden","true")};qv=function(){this.Ki=window;this.Sy=this.An=this.Zv=this.xi=null};
qv.prototype.open=function(a,b,c,d){rv(this);this.Zv?(this.An&&(this.An(),this.An=null),sv(this)):this.Zv="authPopup"+Math.floor(Math.random()*1E6+1);a:{this.xi=this.Ki.open(a,this.Zv,b);try{this.xi.focus();if(this.xi.closed||typeof this.xi.closed=="undefined")throw Error("Y");_.ov=this.xi}catch(e){d&&setTimeout(d,0);this.xi=null;break a}c&&(this.An=c,tv(this))}};
var rv=function(a){try{if(a.xi==null||a.xi.closed)a.xi=null,a.Zv=null,sv(a),a.An&&(a.An(),a.An=null)}catch(b){a.xi=null,a.Zv=null,sv(a)}},tv=function(a){a.Sy=window.setInterval(function(){rv(a)},300)},sv=function(a){a.Sy&&(window.clearInterval(a.Sy),a.Sy=null)};jv=jv||{};var uv=function(a,b){this.Yb=a;this.kI=b;this.Qc=null;this.uo=!1};uv.prototype.start=function(){if(!this.uo&&!this.Qc){var a=this;this.Qc=window.setTimeout(function(){a.clear();a.uo||(a.Yb(),a.uo=!0)},jv.WT(this.kI))}};
uv.prototype.clear=function(){this.Qc&&(window.clearTimeout(this.Qc),this.Qc=null)};var vv=function(a,b){var c=jv.dt;this.oba=jv.Ts;this.W1=c;this.Yb=a;this.kI=b;this.Qc=null;this.uo=!1;var d=this;this.X1=function(){document[d.oba]||(d.clear(),d.start())}};vv.prototype.start=function(){if(!this.uo&&!this.Qc){dv(document,this.W1,this.X1);var a=this;this.Qc=window.setTimeout(function(){a.clear();a.uo||(a.Yb(),a.uo=!0)},jv.WT(this.kI))}};
vv.prototype.clear=function(){var a=this.W1,b=this.X1,c=document;if(c.removeEventListener)c.removeEventListener(a,b,!1);else if(c.detachEvent)c.detachEvent("on"+a,b);else throw Error("Q`"+a);this.Qc&&(window.clearTimeout(this.Qc),this.Qc=null)};jv.Ts=null;jv.dt=null;
jv.Qba=function(){var a=document;typeof a.hidden!=="undefined"?(jv.Ts="hidden",jv.dt="visibilitychange"):typeof a.msHidden!=="undefined"?(jv.Ts="msHidden",jv.dt="msvisibilitychange"):typeof a.webkitHidden!=="undefined"&&(jv.Ts="webkitHidden",jv.dt="webkitvisibilitychange")};jv.Qba();jv.k8=function(a,b){return jv.Ts&&jv.dt?new vv(a,b):new uv(a,b)};jv.WT=function(a){return Math.max(1,a-(new Date).getTime())};
var wv=function(a,b){document.cookie="G_ENABLED_IDPS="+a+";domain=."+b+";expires=Fri, 31 Dec 9999 12:00:00 GMT;path=/"},xv=function(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;n=m=0}function b(p){for(var q=h,r=0;r<64;r+=4)q[r/4]=p[r]<<24|p[r+1]<<16|p[r+2]<<8|p[r+3];for(r=16;r<80;r++)p=q[r-3]^q[r-8]^q[r-14]^q[r-16],q[r]=(p<<1|p>>>31)&4294967295;p=e[0];var w=e[1],u=e[2],x=e[3],A=e[4];for(r=0;r<80;r++){if(r<40)if(r<20){var D=x^w&(u^x);var E=1518500249}else D=
w^u^x,E=1859775393;else r<60?(D=w&u|x&(w|u),E=2400959708):(D=w^u^x,E=3395469782);D=((p<<5|p>>>27)&4294967295)+D+A+E+q[r]&4294967295;A=x;x=u;u=(w<<30|w>>>2)&4294967295;w=p;p=D}e[0]=e[0]+p&4294967295;e[1]=e[1]+w&4294967295;e[2]=e[2]+u&4294967295;e[3]=e[3]+x&4294967295;e[4]=e[4]+A&4294967295}function c(p,q){if(typeof p==="string"){p=unescape(encodeURIComponent(p));for(var r=[],w=0,u=p.length;w<u;++w)r.push(p.charCodeAt(w));p=r}q||(q=p.length);r=0;if(m==0)for(;r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64;for(;r<
q;)if(f[m++]=p[r++],n++,m==64)for(m=0,b(f);r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64}function d(){var p=[],q=n*8;m<56?c(k,56-m):c(k,64-(m-56));for(var r=63;r>=56;r--)f[r]=q&255,q>>>=8;b(f);for(r=q=0;r<5;r++)for(var w=24;w>=0;w-=8)p[q++]=e[r]>>w&255;return p}for(var e=[],f=[],h=[],k=[128],l=1;l<64;++l)k[l]=0;var m,n;a();return{reset:a,update:c,digest:d,Si:function(){for(var p=d(),q="",r=0;r<p.length;r++)q+="0123456789ABCDEF".charAt(Math.floor(p[r]/16))+"0123456789ABCDEF".charAt(p[r]%16);return q}}},yv=
window.crypto,zv=!1,Av=0,Bv=1,Cv=0,Dv="",Ev=function(a){a=a||window.event;var b=a.screenX+a.clientX<<16;b+=a.screenY+a.clientY;b*=(new Date).getTime()%1E6;Bv=Bv*b%Cv;if(++Av==3)if(a=window,b=Ev,a.removeEventListener)a.removeEventListener("mousemove",b,!1);else if(a.detachEvent)a.detachEvent("onmousemove",b);else throw Error("Q`mousemove");},Fv=function(a){var b=xv();b.update(a);return b.Si()};zv=!!yv&&typeof yv.getRandomValues=="function";
zv||(Cv=(screen.width*screen.width+screen.height)*1E6,Dv=Fv(document.cookie+"|"+document.location+"|"+(new Date).getTime()+"|"+Math.random()),dv(window,"mousemove",Ev));iv=iv||{};iv.g4="ssIFrame_";
_.Gv=function(a,b,c){c=c===void 0?!1:c;this.Bb=a;if(!this.Bb)throw Error("Z");a=_.mv(a,"idpIFrameUrl");if(!a)throw Error("$");this.fV=a;if(!b)throw Error("aa");this.Um=b;a=this.fV;b=document.createElement("a");b.setAttribute("href",a);a=[b.protocol,"//",b.hostname];b.protocol=="http:"&&b.port!=""&&b.port!="0"&&b.port!="80"?(a.push(":"),a.push(b.port)):b.protocol=="https:"&&b.port!=""&&b.port!="0"&&b.port!="443"&&(a.push(":"),a.push(b.port));this.TH=a.join("");this.pfa=[location.protocol,"//",location.host].join("");
this.Yw=this.SH=this.yo=!1;this.bV=null;this.EB=[];this.Mr=[];this.fk={};this.zo=void 0;this.Fs=c};_.g=_.Gv.prototype;_.g.show=function(){var a=this.zo;a.style.position="fixed";a.style.width="100%";a.style.height="100%";a.style.left="0px";a.style.top="0px";a.style.right="0px";a.style.bottom="0px";a.style.display="block";a.style.zIndex="9999999";a.style.overflow="hidden";a.setAttribute("aria-hidden","false")};_.g.hide=function(){pv(this.zo)};
_.g.jB=function(a){if(this.yo)a&&a(this);else{if(!this.zo){var b=iv.g4+this.Bb;var c=this.Bb;var d=location.hostname;var e,f=document.cookie.match("(^|;) ?G_ENABLED_IDPS=([^;]*)(;|$)");f&&f.length>2&&(e=f[2]);(f=e&&Zu(e.split("|"),c)>=0)?wv(e,d):wv(e?e+"|"+c:c,d);c=!f;var h=this.fV,k=this.pfa;d=this.Um;e=this.Fs;e=e===void 0?!1:e;f=document.createElement("iframe");f.setAttribute("id",b);b=f.setAttribute;var l="allow-scripts allow-same-origin";document.requestStorageAccess&&av(document.requestStorageAccess)&&
(l+=" allow-storage-access-by-user-activation");b.call(f,"sandbox",l);f.setAttribute("allow","identity-credentials-get");pv(f);f.setAttribute("frame-border","0");b=[h,"#origin=",encodeURIComponent(k)];b.push("&rpcToken=");b.push(encodeURIComponent(d));c&&b.push("&clearCache=1");_.Wu&&b.push("&debug=1");e&&b.push("&supportBlocked3PCookies=1");document.body.appendChild(f);f.setAttribute("src",b.join(""));this.zo=f}a&&this.EB.push(a)}};_.g.iW=function(){return this.yo&&this.Yw};_.g.Wn=function(){return this.bV};
Hv=function(a){for(var b=0;b<a.EB.length;b++)a.EB[b](a);a.EB=[]};_.Jv=function(a,b,c,d){if(a.yo){if(a.yo&&a.SH)throw a="Failed to communicate with IDP IFrame due to unitialization error: "+a.Wn(),Xu(a),Error(a);Iv(a,{method:b,params:c},d)}else a.Mr.push({kp:{method:b,params:c},callback:d}),a.jB()};Iv=function(a,b,c){if(c){for(var d=b.id;!d||a.fk[d];)d=(new Date).getMilliseconds()+"-"+(Math.random()*1E6+1);b.id=d;a.fk[d]=c}b.rpcToken=a.Um;a.zo.contentWindow.postMessage(_.gv.stringify(b),a.TH)};
Kv=function(a){if(a&&a.indexOf("::")>=0)throw Error("ba");};_.Gv.prototype.zj=function(a,b,c,d,e,f,h,k,l){l=l===void 0?!1:l;Kv(f);b=_.bv(b);_.Jv(this,"getTokenResponse",{clientId:a,loginHint:c,request:b,sessionSelector:d,forceRefresh:h,skipCache:k,id:f,userInteracted:l},e)};_.Gv.prototype.hB=function(a,b,c,d,e){b=_.bv(b);_.Jv(this,"listIdpSessions",{clientId:a,request:b,sessionSelector:c,forceRefresh:e},d)};Lv=function(a,b,c){Kv(b.identifier);_.Jv(a,"getSessionSelector",b,c)};
_.Mv=function(a,b,c,d,e){Kv(b.identifier);_.Jv(a,"setSessionSelector",{domain:b.domain,crossSubDomains:b.crossSubDomains,policy:b.policy,id:b.id,hint:d,disabled:!!c},e)};Nv=function(a,b,c,d,e,f,h){b={clientId:b};c&&(b.pluginName=c);d&&(b.ackExtensionDate=d);b.useFedCm=e;f&&(b.fedCmEnabled=f);_.Jv(a,"monitorClient",b,h)};_.Gv.prototype.revoke=_.jb(8);_.Gv.prototype.wt=_.jb(10);iv.EA={};iv.PG=function(a){return iv.EA[a]};
iv.jB=function(a,b,c){c=c===void 0?!1:c;var d=iv.PG(a);if(!d){d=String;if(zv){var e=new window.Uint32Array(1);yv.getRandomValues(e);e=Number("0."+e[0])}else e=Bv,e+=parseInt(Dv.substr(0,20),16),Dv=Fv(Dv),e/=Cv+1.2089258196146292E24;d=new _.Gv(a,d(2147483647*e),c);iv.EA[a]=d}d.jB(b)};iv.W9=function(a){for(var b in iv.EA){var c=iv.PG(b);if(c&&c.zo&&c.zo.contentWindow==a.source&&c.TH==a.origin)return c}};iv.y$=function(a){for(var b in iv.EA){var c=iv.PG(b);if(c&&c.TH==a)return c}};iv=iv||{};
var Pv=function(){var a=[],b;for(b in _.Ov)a.push(_.Ov[b]);hv.call(this,a);this.um={};Xu("EventBus is ready.")};$u(Pv,hv);_.Ov={Q5:"sessionSelectorChanged",uE:"sessionStateChanged",Rs:"authResult",a3:"displayIFrame"};Rv=function(a,b){var c=Qv;a&&b&&(c.um[a]||(c.um[a]=[]),Zu(c.um[a],b)<0&&c.um[a].push(b))};Sv=function(a){var b=Qv;a&&(b.um[a]||(b.um[a]=[]))};Tv=function(a,b,c){return b&&a.um[b]&&Zu(a.um[b],c)>=0};_.g=Pv.prototype;
_.g.vea=function(a){var b,c=!!a.source&&(a.source===_.ov||a.source.opener===window);if(b=c?iv.y$(a.origin):iv.W9(a)){try{var d=_.gv.parse(a.data)}catch(e){Xu("Bad event, an error happened when parsing data.");return}if(!c){if(!d||!d.rpcToken||d.rpcToken!=b.Um){Xu("Bad event, no RPC token.");return}if(d.id&&!d.method){c=d;if(a=b.fk[c.id])delete b.fk[c.id],a(c.result,c.error);return}}d.method!="fireIdpEvent"?Xu("Bad IDP event, method unknown."):(a=d.params)&&a.type&&this.eV[a.type]?(d=this.eV[a.type],
c&&!d.Z6?Xu("Bad IDP event. Source window cannot be a popup."):d.Ms&&!d.Ms.call(this,b,a)?Xu("Bad IDP event."):d.Jf.call(this,b,a)):Xu("Bad IDP event.")}else Xu("Bad event, no corresponding Idp Stub.")};_.g.Vfa=function(a,b){return Tv(this,a.Bb,b.clientId)};_.g.Ufa=function(a,b){a=a.Bb;b=b.clientId;return!b||Tv(this,a,b)};_.g.l7=function(a,b){return Tv(this,a.Bb,b.clientId)};
_.g.Eda=function(a,b){a.yo=!0;a.Yw=!!b.cookieDisabled;Hv(a);for(b=0;b<a.Mr.length;b++)Iv(a,a.Mr[b].kp,a.Mr[b].callback);a.Mr=[]};_.g.Dda=function(a,b){b={error:b.error};a.yo=!0;a.SH=!0;a.bV=b;a.Mr=[];Hv(a)};_.g.eC=function(a,b){b.originIdp=a.Bb;this.dispatchEvent(b)};var Qv=new Pv,Uv=Qv,Vv={};Vv.idpReady={Jf:Uv.Eda};Vv.idpError={Jf:Uv.Dda};Vv.sessionStateChanged={Jf:Uv.eC,Ms:Uv.Vfa};Vv.sessionSelectorChanged={Jf:Uv.eC,Ms:Uv.Ufa};Vv.authResult={Jf:Uv.eC,Ms:Uv.l7,Z6:!0};Vv.displayIFrame={Jf:Uv.eC};
Qv.eV=Vv||{};dv(window,"message",function(a){Qv.vea.call(Qv,a)});
_.Wv=function(a,b){this.Oe=!1;if(!a)throw Error("ca");var c=[],d;for(d in a)c.push(a[d]);hv.call(this,c);this.Cd=[location.protocol,"//",location.host].join("");this.Xd=b.crossSubDomains?b.domain||this.Cd:this.Cd;if(!b)throw Error("da");if(!b.idpId)throw Error("ea");if(!_.mv(b.idpId,"authServerUrl")||!_.mv(b.idpId,"idpIFrameUrl"))throw Error("fa`"+b.idpId);this.Bb=b.idpId;this.Ob=void 0;this.u8=!!b.disableTokenRefresh;this.s9=!!b.forceTokenRefresh;this.Rga=!!b.skipTokenCache;this.Fs=!!b.supportBlocked3PCookies;
b.pluginName&&(this.kea=b.pluginName);b.ackExtensionDate&&(this.Q6=b.ackExtensionDate);this.I1=b.useFedCm;this.c9=this.Fs&&_.kv();this.setOptions(b);this.Kt=[];this.Yw=this.Ck=this.UV=!1;this.qj=void 0;this.kZ();this.Od=void 0;var e=this,f=function(){Xu("Token Manager is ready.");if(e.Kt.length)for(var h=0;h<e.Kt.length;h++)e.Kt[h].call(e);e.UV=!0;e.Kt=[]};iv.jB(this.Bb,function(h){e.Od=h;h.yo&&h.SH?(e.Ck=!0,e.qj=h.Wn(),e.Dr(e.qj)):(e.Yw=h.iW(),e.Ob?Nv(e.Od,e.Ob,e.kea,e.Q6,e.I1,e.c9,function(k){var l=
!!k.validOrigin,m=!!k.blocked,n=!!k.suppressed;k.invalidExtension?(e.qj={error:"Invalid value for ack_extension_date. Please refer to [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."},e.Ck=!0,e.Dr(e.qj)):l?m?n?(Yu("You have created a new client application that uses libraries for user authentication or authorization that are deprecated. New clients must use the new libraries instead. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."),
Rv(e.Bb,e.Ob),f()):(e.qj={error:"You have created a new client application that uses libraries for user authentication or authorization that are deprecated. New clients must use the new libraries instead. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."},e.Ck=!0,e.Dr(e.qj)):(Yu("Your client application uses libraries for user authentication or authorization that are deprecated. See the [Migration Guide](https://developers.google.com/identity/gsi/web/guides/gis-migration) for more information."),
Rv(e.Bb,e.Ob),f()):(e.qj={error:"Not a valid origin for the client: "+e.Cd+" has not been registered for client ID "+e.Ob+". Please go to https://console.developers.google.com/ and register this origin for your project's client ID."},e.Ck=!0,e.Dr(e.qj))}):(Sv(e.Bb),f()))},this.Fs)};$u(_.Wv,hv);_.g=_.Wv.prototype;_.g.setOptions=function(){};_.g.kZ=function(){};_.g.Dr=function(){};_.g.iW=function(){return this.Yw};_.g.Wn=function(){return this.qj};Xv=function(a,b,c){return function(){b.apply(a,c)}};
_.Yv=function(a,b,c){if(a.UV)b.apply(a,c);else{if(a.Ck)throw a.qj;a.Kt.push(Xv(a,b,c))}};_.Wv.prototype.fQ=_.jb(11);_.Wv.prototype.wt=_.jb(9);_.$v=function(a,b){_.Wv.call(this,a,b);this.BY=new qv;this.Mk=this.Uo=null;Zv(this)};$u(_.$v,_.Wv);_.$v.prototype.setOptions=function(){};
var aw=function(a,b){a.Le={crossSubDomains:!!b.crossSubDomains,id:b.sessionSelectorId,domain:a.Xd};b.crossSubDomains&&(a.Le.policy=b.policy)},bw=function(a,b){if(!b.authParameters)throw Error("ga");if(!b.authParameters.scope)throw Error("ha");if(!b.authParameters.response_type)throw Error("ia");a.rn=b.authParameters;a.rn.redirect_uri||(a.rn.redirect_uri=[location.protocol,"//",location.host,location.pathname].join(""));a.Ij=_.bv(b.rpcAuthParameters||a.rn);if(!a.Ij.scope)throw Error("ja");if(!a.Ij.response_type)throw Error("ka");
a:{var c=a.Ij.response_type.split(" ");for(var d=0,e=c.length;d<e;d++)if(c[d]&&!ev[c[d]]){c=!0;break a}c=!1}if(c)throw Error("la");if(b.enableSerialConsent||b.enableGranularConsent)a.rn.enable_granular_consent=!0,a.Ij.enable_serial_consent=!0;b.authResultIdentifier&&(a.m7=b.authResultIdentifier);b.spec_compliant&&(a.Ij.spec_compliant=b.spec_compliant)};
_.$v.prototype.kZ=function(){var a=this;Qv.addEventListener(_.Ov.Q5,function(b){a.Oe&&a.Le&&b.originIdp==a.Bb&&!b.crossSubDomains==!a.Le.crossSubDomains&&b.domain==a.Le.domain&&b.id==a.Le.id&&a.SX(b)});Qv.addEventListener(_.Ov.uE,function(b){a.Oe&&b.originIdp==a.Bb&&b.clientId==a.Ob&&a.TX(b)});Qv.addEventListener(_.Ov.Rs,function(b){_.ov=void 0;a.Oe&&b.originIdp==a.Bb&&b.clientId==a.Ob&&b.id==a.Jk&&(a.Uo&&(window.clearTimeout(a.Uo),a.Uo=null),a.Jk=void 0,a.Po(b))});Qv.addEventListener(_.Ov.a3,function(b){a.Oe&&
b.originIdp==a.Bb&&(b.hide?a.Od.hide():a.Od.show())})};_.$v.prototype.SX=function(){};_.$v.prototype.TX=function(){};_.$v.prototype.Po=function(){};var dw=function(a,b){cw(a);a.u8||(a.Mk=jv.k8(function(){a.zj(!0)},b-3E5),navigator.onLine&&a.Mk.start())},cw=function(a){a.Mk&&(a.Mk.clear(),a.Mk=null)},Zv=function(a){var b=window;fv()&&(b=document.body);dv(b,"online",function(){a.Mk&&a.Mk.start()});dv(b,"offline",function(){a.Mk&&a.Mk.clear()})};_.$v.prototype.zj=function(){};_.$v.prototype.tX=_.jb(12);
_.$v.prototype.Aca=function(a,b){if(!this.Ob)throw Error("pa");this.Od.hB(this.Ob,this.Ij,this.Le,a,b)};_.$v.prototype.hB=function(a,b){_.Yv(this,this.Aca,[a,b])};_.fw=function(a){this.Fe=void 0;this.Mh=!1;this.Vr=void 0;_.$v.call(this,ew,a)};$u(_.fw,_.$v);var ew={uO:"noSessionBound",ct:"userLoggedOut",m2:"activeSessionChanged",uE:"sessionStateChanged",s6:"tokenReady",r6:"tokenFailed",Rs:"authResult",ERROR:"error"};
_.fw.prototype.setOptions=function(a){if(!a.clientId)throw Error("qa");this.Ob=a.clientId;this.Da=a.id;aw(this,a);bw(this,a)};_.fw.prototype.Dr=function(a){this.dispatchEvent({type:ew.ERROR,error:"idpiframe_initialization_failed",details:a.error,idpId:this.Bb})};var gw=function(a){cw(a);a.Vr=void 0;a.HI=void 0};_.g=_.fw.prototype;
_.g.SX=function(a){var b=a.newValue||{};if(this.Fe!=b.hint||this.Mh!=!!b.disabled){a=this.Fe;var c=!this.Fe||this.Mh;gw(this);this.Fe=b.hint;this.Mh=!!b.disabled;(b=!this.Fe||this.Mh)&&!c?this.dispatchEvent({type:ew.ct,idpId:this.Bb}):b||(a!=this.Fe&&this.dispatchEvent({type:ew.m2,idpId:this.Bb}),this.Fe&&this.zj())}};
_.g.TX=function(a){this.Mh||(this.Fe?a.user||this.Vr?a.user==this.Fe&&(this.Vr?a.sessionState?this.Vr=a.sessionState:(gw(this),this.dispatchEvent({type:ew.ct,idpId:this.Bb})):a.sessionState&&(this.Vr=a.sessionState,this.zj())):this.zj():this.dispatchEvent({type:ew.uE,idpId:this.Bb}))};_.g.Po=function(a){this.dispatchEvent({type:ew.Rs,authResult:a.authResult})};_.g.yu=_.jb(14);_.g.ru=function(a){_.Yv(this,this.DG,[a])};_.g.DG=function(a){Lv(this.Od,this.Le,a)};
_.g.tD=function(a,b,c,d){d=d===void 0?!1:d;if(!a)throw Error("ra");gw(this);this.Fe=a;this.Mh=!1;b&&_.Mv(this.Od,this.Le,!1,this.Fe);this.Oe=!0;this.zj(c,!0,d)};_.g.start=function(){_.Yv(this,this.Pw,[])};
_.g.Pw=function(){var a=this.Ob==cv("client_id")?cv("login_hint"):void 0;var b=this.Ob==cv("client_id")?cv("state"):void 0;this.rJ=b;if(a)window.history.replaceState?window.history.replaceState(null,document.title,window.location.href.split("#")[0]):window.location.href.hash="",this.tD(a,!0,!0,!0);else{var c=this;this.ru(function(d){c.Oe=!0;d&&d.hint?(gw(c),c.Fe=d.hint,c.Mh=!!d.disabled,c.Mh?c.dispatchEvent({type:ew.ct,idpId:c.Bb}):c.tD(d.hint)):(gw(c),c.Fe=void 0,c.Mh=!(!d||!d.disabled),c.dispatchEvent({type:ew.uO,
autoOpenAuthUrl:!c.Mh,idpId:c.Bb}))})}};_.g.o9=function(){var a=this;this.ru(function(b){b&&b.hint?b.disabled?a.dispatchEvent({type:ew.ct,idpId:a.Bb}):a.zj(!0):a.dispatchEvent({type:ew.uO,idpId:a.Bb})})};_.g.TS=function(){_.Yv(this,this.o9,[])};
_.g.zj=function(a,b,c){var d=this;this.Od.zj(this.Ob,this.Ij,this.Fe,this.Le,function(e,f){(f=f||e.error)?f=="user_logged_out"?(gw(d),d.dispatchEvent({type:ew.ct,idpId:d.Bb})):(d.HI=null,d.dispatchEvent({type:ew.r6,idpId:d.Bb,error:f})):(d.HI=e,d.Vr=e.session_state,dw(d,e.expires_at),e.idpId=d.Bb,b&&d.rJ&&(e.state=d.rJ,d.rJ=void 0),d.dispatchEvent({type:ew.s6,idpId:d.Bb,response:e}))},this.Da,a,!1,c===void 0?!1:c)};_.g.revoke=_.jb(7);_.g.GZ=_.jb(15);
_.hw=function(a){this.tn=null;_.$v.call(this,{},a);this.Oe=!0};$u(_.hw,_.$v);_.g=_.hw.prototype;_.g.setOptions=function(a){if(!a.clientId)throw Error("qa");this.Ob=a.clientId;this.Da=a.id;aw(this,a);bw(this,a)};_.g.Dr=function(a){this.tn&&(this.tn({authResult:{error:"idpiframe_initialization_failed",details:a.error}}),this.tn=null)};_.g.Po=function(a){if(this.tn){var b=this.tn;this.tn=null;b(a)}};_.g.yu=_.jb(13);_.g.ru=function(a){this.Ck?a(this.Wn()):_.Yv(this,this.DG,[a])};
_.g.DG=function(a){Lv(this.Od,this.Le,a)};_.iw=function(a,b,c){a.Ck?c(a.Wn()):_.Yv(a,a.Qda,[b,c])};_.hw.prototype.Qda=function(a,b){this.Od.zj(this.Ob,this.Ij,a,this.Le,function(c,d){d?b({error:d}):b(c)},this.Da,this.s9,this.Rga)};_.hw.prototype.HW=_.jb(16);
var jw=function(a){var b=window.location;a=_.sc(a);a!==void 0&&b.assign(a)},kw=function(a){return Array.prototype.concat.apply([],arguments)},lw=function(){try{var a=Array.from((window.crypto||window.msCrypto).getRandomValues(new Uint8Array(64)))}catch(c){a=[];for(var b=0;b<64;b++)a[b]=Math.floor(Math.random()*256)}return _.Nh(a,3).substring(0,64)},mw=function(a){var b=[],c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];if(d===null||d===void 0)d="";b.push(encodeURIComponent(c)+"="+encodeURIComponent(d))}return b.join("&")},
nw=function(a,b){(b===void 0?0:b)||window.addEventListener("hashchange",function(){location.hash.includes("client_id")&&window.location.reload()});jw(a)},ow=function(a,b,c){if(!a.Oe)throw Error("ma");b?_.Mv(a.Od,a.Le,!0,void 0,c):_.Mv(a.Od,a.Le,!0,a.Fe,c)},pw=function(a){if(!a.Oe)throw Error("ma");return a.HI},qw,rw,sw,tw,uw,vw,ww,xw,yw,zw,Aw,Bw,Cw,Dw,Gw,Jw,Kw;
_.hw.prototype.HW=_.pb(16,function(a,b){var c=this.Od,d=this.Ob,e=this.Le,f=_.bv(this.Ij);delete f.response_type;_.Jv(c,"getOnlineCode",{clientId:d,loginHint:a,request:f,sessionSelector:e},b)});_.fw.prototype.GZ=_.pb(15,function(a){pw(this)&&pw(this).access_token&&(this.Od.revoke(this.Ob,pw(this).access_token,a),ow(this,!0))});
_.fw.prototype.yu=_.pb(14,function(){var a=this;return function(b){b&&b.authResult&&b.authResult.login_hint&&(a.aC?(b.authResult.client_id=a.Ob,nw(a.aC+"#"+mw(b.authResult))):a.tD(b.authResult.login_hint,a.Mh||b.authResult.login_hint!=a.Fe,!0,!0))}});
_.hw.prototype.yu=_.pb(13,function(a){var b=this;return function(c){c&&c.authResult&&c.authResult.login_hint?b.ru(function(d){_.Mv(b.Od,b.Le,d&&d.disabled,c.authResult.login_hint,function(){_.iw(b,c.authResult.login_hint,a)})}):a(c&&c.authResult&&c.authResult.error?c.authResult:c&&c.authResult&&!c.authResult.login_hint?{error:"wrong_response_type"}:{error:"unknown_error"})}});_.$v.prototype.tX=_.pb(12,function(){this.Ob&&_.Jv(this.Od,"startPolling",{clientId:this.Ob,origin:this.Cd,id:this.Jk})});
_.Gv.prototype.revoke=_.pb(8,function(a,b,c){_.Jv(this,"revoke",{clientId:a,token:b},c)});_.fw.prototype.revoke=_.pb(7,function(a){_.Yv(this,this.GZ,[a])});qw="openid email profile https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/plus.me https://www.googleapis.com/auth/plus.login".split(" ");
rw=function(){var a=navigator.userAgent,b;if(b=!!a&&a.indexOf("CriOS")!=-1)b=-1,(a=a.match(/CriOS\/(\d+)/))&&a[1]&&(b=parseInt(a[1],10)||-1),b=b<48;return b};
sw=function(){var a=navigator.userAgent.toLowerCase();if(!(a.indexOf("safari/")>-1&&a.indexOf("chrome/")<0&&a.indexOf("crios/")<0&&a.indexOf("android")<0))return!1;var b=RegExp("version/(\\d+)\\.(\\d+)[\\.0-9]*").exec(navigator.userAgent.toLowerCase());if(!b||b.length<3)return!1;a=parseInt(b[1],10);b=parseInt(b[2],10);return a>12||a==12&&b>=1};tw=function(a){return a.length>0&&a.every(function(b){return qw.includes(b)})};
uw=function(a,b,c,d,e,f,h){var k=_.mv(a,"authServerUrl");if(!k)throw Error("X`"+a);a=_.bv(d);a.response_type=h||"permission";a.client_id=c;a.ss_domain=b;if(f&&f.extraQueryParams)for(var l in f.extraQueryParams)a[l]=f.extraQueryParams[l];(b=e)&&!(b=sw())&&(b=navigator.userAgent.toLowerCase(),b.indexOf("ipad;")>-1||b.indexOf("iphone;")>-1?(b=RegExp("os (\\d+)_\\d+(_\\d+)? like mac os x").exec(navigator.userAgent.toLowerCase()),b=!b||b.length<2?!1:parseInt(b[1],10)>=14):b=!1);b&&!a.prompt&&(a.prompt=
"select_account");return k+(k.indexOf("?")<0?"?":"&")+mw(a)};vw=function(a,b,c,d){if(!a.Ob)throw Error("na");a.Jk=c||a.m7||"auth"+Math.floor(Math.random()*1E6+1);b=b||{};b.extraQueryParams=b.extraQueryParams||{};if(!b.extraQueryParams.redirect_uri){var e=a.Cd.split("//");c=b.extraQueryParams;var f=e[0],h=e[1];e=a.Jk;var k=f.indexOf(":");k>0&&(f=f.substring(0,k));f=["storagerelay://",f,"/",h,"?"];f.push("id="+e);c.redirect_uri=f.join("")}return uw(a.Bb,a.Xd,a.Ob,a.rn,!0,b,d)};
ww=function(a,b,c){if(!a.Ob)throw Error("na");return uw(a.Bb,a.Xd,a.Ob,a.rn,!1,b,c)};xw=function(a,b){a.Uo&&window.clearTimeout(a.Uo);a.Uo=window.setTimeout(function(){a.Jk==b&&(_.ov=void 0,a.Uo=null,a.Jk=void 0,a.Po({authResult:{error:"popup_closed_by_user"}}))},1E3)};
yw=function(a,b,c){if(!a.Ob)throw Error("oa");c=c||{};c=vw(a,c.sessionMeta,c.oneTimeId,c.responseType);(Object.hasOwnProperty.call(window,"ActiveXObject")&&!window.ActiveXObject||rw())&&_.Yv(a,a.tX,[]);var d=a.Jk;a.BY.open(c,b,function(){a.Jk==d&&xw(a,d)},function(){a.Jk=void 0;a.Po({authResult:{error:"popup_blocked_by_browser"}})})};
zw=function(a,b){var c=b||{};b=_.bv(a.rn);if(c.sessionMeta&&c.sessionMeta.extraQueryParams)for(var d in c.sessionMeta.extraQueryParams)b[d]=c.sessionMeta.extraQueryParams[d];var e;c.sessionMeta.extraQueryParams.scope&&(e=c.sessionMeta.extraQueryParams.scope.split(" "));!e&&b.scope&&(e=b.scope.split(" "));delete b.redirect_uri;delete b.origin;delete b.client_id;delete b.scope;b.prompt=="select_account"&&delete b.prompt;b.gsiwebsdk="fedcm";b.ss_domain=a.Xd;d=_.mv(a.Bb,"fedcmConfigUrl");c=c.responseType;
b.response_type=c;b.scope=e.join(" ");!b.nonce&&c.includes("id_token")&&(b.nonce="notprovided");c=navigator.userActivation.isActive?"active":"passive";e=tw(e)?["name","email","picture"]:[];return{identity:{providers:[{configURL:d,clientId:a.Ob,fields:e,params:b}],mode:c},mediation:"required"}};
Aw=function(a,b,c){if(!a.Ob)throw Error("oa");b=zw(a,b);navigator.credentials.get(b).then(function(d){d=JSON.parse(d.token);var e={client_id:d.client_id,login_hint:d.login_hint,expires_in:3600,scope:d.scope};d.code&&(e.code=d.code);d.id_token&&(e.id_token=d.id_token);a.Po({type:_.Ov.Rs,idpId:a.Bb,authResult:e})},function(d){d.message.indexOf("identity-credentials-get")>=0||d.message.indexOf("Content Security Policy")>=0?c():a.Po({type:_.Ov.Rs,idpId:a.Bb,authResult:{error:d}})})};
Bw=function(a,b,c){a.Fs&&_.kv()?Aw(a,c,function(){return yw(a,b,c)}):yw(a,b,c)};Cw=function(a,b){b=b||{};var c=ww(a,b.sessionMeta,b.responseType);a.Fs&&_.kv()&&a.I1?(a.aC=b.sessionMeta.extraQueryParams.redirect_uri,Aw(a,b,function(){return nw(c,!0)})):nw(c,!0)};Dw=function(a,b,c){a.Ck?c(a.Wn()):_.Yv(a,a.HW,[b,c])};_.Ew=function(a){_.Be(_.Me,"le",[]).push(a)};
_.Fw=function(a){for(var b=[],c=0,d=0;c<a.length;){var e=a[c++];if(e<128)b[d++]=String.fromCharCode(e);else if(e>191&&e<224){var f=a[c++];b[d++]=String.fromCharCode((e&31)<<6|f&63)}else if(e>239&&e<365){f=a[c++];var h=a[c++],k=a[c++];e=((e&7)<<18|(f&63)<<12|(h&63)<<6|k&63)-65536;b[d++]=String.fromCharCode(55296+(e>>10));b[d++]=String.fromCharCode(56320+(e&1023))}else f=a[c++],h=a[c++],b[d++]=String.fromCharCode((e&15)<<12|(f&63)<<6|h&63)}return b.join("")};
Gw=function(a){var b=[];_.Oh(a,function(c){b.push(c)});return b};_.Hw=function(a,b){_.ti[b||"token"]=a};_.Iw=function(a){delete _.ti[a||"token"]};Kw=function(){if(typeof MessageChannel!=="undefined"){var a=new MessageChannel,b={},c=b;a.port1.onmessage=function(){if(b.next!==void 0){b=b.next;var d=b.cb;b.cb=null;d()}};return function(d){c.next={cb:d};c=c.next;a.port2.postMessage(0)}}return function(d){_.Xa.setTimeout(d,0)}};_.gv={parse:function(a){a=_.Qf("["+String(a)+"]");if(a===!1||a.length!==1)throw new SyntaxError("JSON parsing failed.");return a[0]},stringify:function(a){return _.Rf(a)}};_.hw.prototype.pG=function(a,b){_.Yv(this,this.e9,[a,b])};_.hw.prototype.e9=function(a,b){this.Od.pG(this.Ob,a,this.Ij,this.Le,b)};_.Gv.prototype.pG=function(a,b,c,d,e){c=_.bv(c);_.Jv(this,"gsi:fetchLoginHint",{clientId:a,loginHint:b,request:c,sessionSelector:d},e)};var Lw,Mw=["client_id","cookie_policy","scope"],Nw="client_id cookie_policy fetch_basic_profile hosted_domain scope openid_realm disable_token_refresh login_hint ux_mode redirect_uri state prompt oidc_spec_compliant nonce enable_serial_consent enable_granular_consent include_granted_scopes response_type session_selection plugin_name ack_extension_date use_fedcm gsiwebsdk".split(" "),Ow=["authuser","after_redirect","access_type","hl"],Pw=["login_hint","prompt"],Qw={clientid:"client_id",cookiepolicy:"cookie_policy"},
Rw=["approval_prompt","authuser","login_hint","prompt","hd"],Sw=["login_hint","g-oauth-window","status"],Tw=Math.min(_.Xe("oauth-flow/authWindowWidth",599),screen.width-20),Uw=Math.min(_.Xe("oauth-flow/authWindowHeight",600),screen.height-30);var Vw=function(a){_.lb.call(this,a)};_.y(Vw,_.lb);Vw.prototype.name="gapi.auth2.ExternallyVisibleError";var Ww=function(){};Ww.prototype.select=function(a,b){if(a.sessions&&a.sessions.length==1&&(a=a.sessions[0],a.login_hint)){b(a);return}b()};var Xw=function(){};Xw.prototype.select=function(a,b){if(a.sessions&&a.sessions.length)for(var c=0;c<a.sessions.length;c++){var d=a.sessions[c];if(d.login_hint){b(d);return}}b()};var Yw=function(a){this.n7=a};
Yw.prototype.select=function(a,b){if(a.sessions)for(var c=0;c<a.sessions.length;c++){var d=a.sessions[c];if(d.session_state&&d.session_state.extraQueryParams&&d.session_state.extraQueryParams.authuser==this.n7){d.login_hint?b(d):b();return}}b()};var Zw=function(a){this.ue=a;this.MC=[]};Zw.prototype.select=function(a){var b=0,c=this,d=function(e){if(e)a(e);else{var f=c.MC[b];f?(b++,c.ue.hB(function(h){h?f.select(h,d):d()})):a()}};d()};var $w=function(a){a=new Zw(a);a.MC.push(new Ww);return a},ax=function(a){a=new Zw(a);a.MC.push(new Xw);return a},bx=function(a,b){b===void 0||b===null?b=$w(a):(a=new Zw(a),a.MC.push(new Yw(b)),b=a);return b};var cx=function(a){this.Jf=a;this.isActive=!0};cx.prototype.remove=function(){this.isActive=!1};cx.prototype.trigger=function(){};var dx=function(a){this.remove=function(){a.remove()};this.trigger=function(){a.trigger()}},ex=function(){this.qc=[]};ex.prototype.add=function(a){this.qc.push(a)};ex.prototype.notify=function(a){for(var b=this.qc,c=[],d=0;d<b.length;d++){var e=b[d];e.isActive&&(c.push(e),e=fx(e.Jf,a),e=(0,_.qk)(e),e=(0,_.ok)(e),Jw||(Jw=Kw()),Jw(e))}this.qc=c};var fx=function(a,b){return function(){a(b)}};var ox=function(a){this.La=null;this.Iha=new gx(this);this.qc=new ex;a!=void 0&&this.set(a)};ox.prototype.set=function(a){a!=this.La&&(this.La=a,this.Iha.value=a,this.qc.notify(this.La))};ox.prototype.get=function(){return this.La};ox.prototype.na=function(a){a=new px(this,a);this.qc.add(a);return a};ox.prototype.get=ox.prototype.get;var px=function(a,b){cx.call(this,b);this.Eca=a};_.y(px,cx);px.prototype.trigger=function(){var a=this.Jf;a(this.Eca.get())};
var gx=function(a){this.value=null;this.na=function(b){return new dx(a.na(b))}};var qx={cka:"fetch_basic_profile",dla:"login_hint",Cma:"prompt",Ima:"redirect_uri",ana:"scope",xoa:"ux_mode",Mna:"state"},rx=function(a){this.Ka={};if(a&&!_.Gh(a))if(typeof a.get=="function")this.Ka=a.get();else for(var b in qx){var c=qx[b];c in a&&(this.Ka[c]=a[c])}};rx.prototype.get=function(){return this.Ka};rx.prototype.J_=function(a){this.Ka.scope=a;return this};rx.prototype.Iu=function(){return this.Ka.scope};
var sx=function(a,b){var c=a.Ka.scope;b=kw(b.split(" "),c?c.split(" "):[]);_.Fh(b);a.Ka.scope=b.join(" ")};_.g=rx.prototype;_.g.vga=function(a){this.Ka.prompt=a;return this};_.g.A$=function(){return this.Ka.prompt};_.g.Yfa=function(){_.Vf.warn("Property app_package_name no longer supported and was not set");return this};_.g.D9=function(){_.Vf.warn("Property app_package_name no longer supported")};_.g.uf=function(a){this.Ka.state=a};_.g.getState=function(){return this.Ka.state};var tx=function(){return["toolbar=no","location="+(window.opera?"no":"yes"),"directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,copyhistory=no","width="+Tw,"height="+Uw,"top="+(screen.height-Uw)/2,"left="+(screen.width-Tw)/2].join()},ux=function(a){a=a&&a.id_token;if(!a||!a.split(".")[1])return null;var b=(a.split(".")[1]+"...").replace(/^((....)+).?.?.?$/,"$1");a=JSON;var c=a.parse;b=Gw(b);return c.call(a,_.Fw(b))},vx=function(){Lw=_.Xe("auth2/idpValue","google");var a=_.Xe("oauth-flow/authUrl",
"https://accounts.google.com/o/oauth2/auth"),b=_.Xe("oauth-flow/idpIframeUrl","https://accounts.google.com/o/oauth2/iframe");a={fedcmConfigUrl:_.Xe("oauth-flow/fedcmConfigUrl","https://accounts.google.com/o/fedcm/config.json"),authServerUrl:a,idpIFrameUrl:b};_.nv(Lw,a)},wx=function(a,b,c){for(var d=0;d<b.length;d++){var e=b[d];if(d===b.length-1){a[e]=c;break}_.vb(a[e])||(a[e]={});a=a[e]}},xx=function(){var a=window.location.origin;a||(a=window.location.protocol+"//"+window.location.host);return a},
zx=function(){var a=yx();a.storage_path&&window.sessionStorage.setItem(a.storage_path,xx()+window.location.pathname);if(a.status.toLowerCase()=="enforced")throw new Vw("gapi.auth2 is disabled on this website, but it is still used on page "+window.location.href);a.status.toLowerCase()=="informational"&&_.Vf.warn("gapi.auth2 is disabled on this website, but it is still used on page "+window.location.href)},Ax=function(a){return _.li.get("GSI_ALLOW_3PCD")==="1"?!0:a===!1?!1:a===!0||(_.Me.le||[]).includes("fedcm_migration_mod")?
!0:!1};var Bx=function(a){var b=a?(b=ux(a))?b.sub:null:null;this.Da=b;this.Ic=a?_.fk(a):null};_.g=Bx.prototype;_.g.getId=function(){return this.Da};_.g.MG=function(){var a=ux(this.Ic);return a?a.hd:null};_.g.xg=function(){return!!this.Ic};_.g.Tl=function(a){if(a)return this.Ic;a=Cx;var b=_.fk(this.Ic);!a.PA||a.MH||a.dba||(delete b.access_token,delete b.scope);return b};_.g.rK=function(){return Cx.rK()};_.g.Zk=function(){this.Ic=null};_.g.e$=function(){return this.Ic?this.Ic.scope:null};
_.g.update=function(a){this.Da=a.Da;this.Ic=a.Ic;this.Ic.id_token?this.ly=new Dx(this.Ic):this.ly&&(this.ly=null)};var Ex=function(a){return a.Ic&&typeof a.Ic.session_state=="object"?_.fk(a.Ic.session_state.extraQueryParams||{}):{}};_.g=Bx.prototype;_.g.BG=function(){var a=Ex(this);return a&&a.authuser!==void 0&&a.authuser!==null?a.authuser:null};
_.g.Yk=function(a){var b=Cx,c=new rx(a);b.MH=c.Iu()?!0:!1;Cx.PA&&sx(c,"openid profile email");return new _.xk(function(d,e){var f=Ex(this);f.login_hint=this.getId();f.scope=c.Iu();Fx(b,d,e,f)},this)};_.g.Pu=function(a){return new _.xk(function(b,c){var d=a||{},e=Cx;d.login_hint=this.getId();e.Pu(d).then(b,c)},this)};_.g.Q$=function(a){return this.Yk(a)};_.g.disconnect=function(){return Cx.disconnect()};_.g.G9=function(){return this.ly};
_.g.xA=function(a){if(!this.xg())return!1;var b=this.Ic&&this.Ic.scope?this.Ic.scope.split(" "):"";return _.Nb(a?a.split(" "):[],function(c){return _.tb(b,c)})};var Dx=function(a){a=ux(a);this.x9=a.sub;this.dh=a.name;this.M$=a.given_name;this.b9=a.family_name;this.jV=a.picture;this.gz=a.email};_.g=Dx.prototype;_.g.getId=function(){return this.x9};_.g.getName=function(){return this.dh};_.g.c$=function(){return this.M$};_.g.Y9=function(){return this.b9};_.g.k$=function(){return this.jV};_.g.Qn=function(){return this.gz};var yx,Gx;yx=function(){var a=_.li.get("G_AUTH2_MIGRATION");if(!a)return{status:"none"};a=/(enforced|informational)(?::(.*))?/i.exec(a);return a?{status:a[1].toLowerCase(),storage_path:a[2]}:(_.Vf.warn("The G_AUTH2_MIGRATION cookie value is not valid."),{status:"none"})};Gx=function(a){var b=location;if(a&&a!="none")return a=="single_host_origin"?b.protocol+"//"+b.host:a};
_.Hx=function(a){if(!a)throw new Vw("No cookiePolicy");var b=window.location.hostname;a=="single_host_origin"&&(a=window.location.protocol+"//"+b);if(a=="none")return null;var c=/^(https?:\/\/)([0-9.\-_A-Za-z]+)(?::(\d+))?$/.exec(a);if(!c)throw new Vw("Invalid cookiePolicy");a=c[2];c=c[1];var d={};d.dotValue=a.split(".").length;d.isSecure=c.indexOf("https")!=-1;d.domain=a;if(!_.Mj(b,"."+a)&&!_.Mj(b,a))throw new Vw("Invalid cookiePolicy domain");return d};var Jx=function(a){var b=a||{},c=Ix();_.Bb(Nw,function(d){typeof b[d]==="undefined"&&typeof c[d]!=="undefined"&&(b[d]=c[d])});return b},Ix=function(){for(var a={},b=document.getElementsByTagName("meta"),c=0;c<b.length;++c)if(b[c].name){var d=b[c].name;if(d.indexOf("google-signin-")==0){d=d.substring(14);var e=b[c].content;Qw[d]&&(d=Qw[d]);_.tb(Nw,d)&&e&&(a[d]=e=="true"?!0:e=="false"?!1:e)}}return a},Kx=function(a){return String(a).replace(/_([a-z])/g,function(b,c){return c.toUpperCase()})},Lx=function(a){_.Bb(Nw,
function(b){var c=Kx(b);typeof a[c]!=="undefined"&&typeof a[b]==="undefined"&&(a[b]=a[c],delete a[c])})},Mx=function(a){a=Jx(a);Lx(a);a.cookie_policy||(a.cookie_policy="single_host_origin");var b=Nw+Ow,c;for(c in a)b.indexOf(c)<0&&delete a[c];return a},Nx=function(a,b){if(!a)throw new Vw("Empty initial options.");for(var c=0;c<Mw.length;++c)if(!(b&&Mw[c]=="scope"||a[Mw[c]]))throw new Vw("Missing required parameter '"+Mw[c]+"'");_.Hx(a.cookie_policy)},Px=function(a){var b={authParameters:{redirect_uri:void 0,
response_type:"token id_token",scope:a.scope,"openid.realm":a.openid_realm,include_granted_scopes:!0},clientId:a.client_id,crossSubDomains:!0,domain:Gx(a.cookie_policy),disableTokenRefresh:!!a.disable_token_refresh,idpId:Lw};Ox(b,a);_.Bb(Pw,function(d){a[d]&&(b.authParameters[d]=a[d])});typeof a.enable_serial_consent=="boolean"&&(b.enableSerialConsent=a.enable_serial_consent);typeof a.enable_granular_consent=="boolean"&&(b.enableGranularConsent=a.enable_granular_consent);if(a.plugin_name)b.pluginName=
a.plugin_name;else{var c=_.Xe("auth2/pluginName");c&&(b.pluginName=c)}a.ack_extension_date&&(b.authParameters.ack_extension_date=a.ack_extension_date,b.ackExtensionDate=a.ack_extension_date);typeof a.use_fedcm==="boolean"&&(b.useFedCm=a.use_fedcm);return b},Ox=function(a,b){var c=b.oidc_spec_compliant;b=b.nonce;c&&(a.spec_compliant=c,b=b||lw());b&&(a.authParameters.nonce=b,a.forceTokenRefresh=!0,a.skipTokenCache=!0)},Ux=function(a){var b=a.client_id,c=a.cookie_policy,d=a.scope,e=a.openid_realm,f=
a.hosted_domain,h=a.oidc_spec_compliant,k=a.nonce,l=Qx(a),m={authParameters:{response_type:l,scope:d,"openid.realm":e},rpcAuthParameters:{response_type:l,scope:d,"openid.realm":e},clientId:b,crossSubDomains:!0,domain:Gx(c),idpId:Lw};f&&(m.authParameters.hd=f,m.rpcAuthParameters.hd=f);h&&(m.rpcAuthParameters.spec_compliant=h,k=k||lw());k&&(m.authParameters.nonce=k,m.rpcAuthParameters.nonce=k,m.forceTokenRefresh=!0,m.skipTokenCache=!0);_.Bb(Pw.concat(Ow),function(n){a[n]&&(m.authParameters[n]=a[n])});
a.authuser!==void 0&&a.authuser!==null&&(m.authParameters.authuser=a.authuser);typeof a.include_granted_scopes=="boolean"&&(b=new Rx(a.response_type||"token"),Sx(b)&&(m.authParameters.include_granted_scopes=a.include_granted_scopes),Tx(b)&&(m.rpcAuthParameters.include_granted_scopes=a.include_granted_scopes,a.include_granted_scopes===!1&&(m.forceTokenRefresh=!0,m.skipTokenCache=!0)));typeof a.enable_serial_consent=="boolean"&&(m.enableSerialConsent=a.enable_serial_consent);typeof a.enable_granular_consent==
"boolean"&&(m.enableGranularConsent=a.enable_granular_consent);a.plugin_name?m.pluginName=a.plugin_name:(b=_.Xe("auth2/pluginName"))&&(m.pluginName=b);a.ack_extension_date&&(m.authParameters.ack_extension_date=a.ack_extension_date,m.rpcAuthParameters.ack_extension_date=a.ack_extension_date,m.ackExtensionDate=a.ack_extension_date);typeof a.use_fedcm==="boolean"&&(m.useFedCm=a.use_fedcm);return m},Qx=function(a){a=new Rx(a.response_type||"token");var b=[];Tx(a)&&b.push("token");Vx(a,"id_token")&&b.push("id_token");
b.length==0&&(b=["token","id_token"]);return b.join(" ")},Wx=["permission","id_token"],Xx=/(^|[^_])token/,Rx=function(a){this.Pr=[];this.hI(a)};Rx.prototype.hI=function(a){a?((a.indexOf("permission")>=0||a.match(Xx))&&this.Pr.push("permission"),a.indexOf("id_token")>=0&&this.Pr.push("id_token"),a.indexOf("code")>=0&&this.Pr.push("code")):this.Pr=Wx};var Sx=function(a){return Vx(a,"code")},Tx=function(a){return Vx(a,"permission")};Rx.prototype.toString=function(){return this.Pr.join(" ")};
var Vx=function(a,b){var c=!1;_.Bb(a.Pr,function(d){d==b&&(c=!0)});return c};var Zx=function(a,b,c){this.tJ=b;this.sda=a;for(var d in a)a.hasOwnProperty(d)&&Yx(this,d);if(c&&c.length)for(a=0;a<c.length;a++)this[c[a]]=this.tJ[c[a]]},Yx=function(a,b){a[b]=function(){return a.sda[b].apply(a.tJ,arguments)}};Zx.prototype.then=function(a,b,c){var d=this;return _.Bk().then(function(){return $x(d.tJ,a,b,c)})};_.mk(Zx);var Cx,ay,cy;Cx=null;_.by=function(){return Cx?ay():null};ay=function(){return new Zx(cy.prototype,Cx,["currentUser","isSignedIn"])};cy=function(a){delete a.include_granted_scopes;this.Ka=Px(a);this.i8=a.cookie_policy;this.dba=!!a.scope;(this.PA=a.fetch_basic_profile!==!1)&&(this.Ka.authParameters.scope=dy(this,"openid profile email"));this.Ka.supportBlocked3PCookies=Ax(a.use_fedcm);this.gv=a.hosted_domain;this.Gha=a.ux_mode||"popup";this.aC=a.redirect_uri||null;this.eI()};
cy.prototype.eI=function(){this.currentUser=new ox(new Bx(null));this.isSignedIn=new ox(!1);this.ue=new _.fw(this.Ka);this.UA=this.ir=null;this.oca=new _.xk(function(a,b){this.ir=a;this.UA=b},this);this.DB={};this.vv=!0;ey(this);this.ue.start()};
var ey=function(a){a.ue.addEventListener("error",function(b){a.vv&&a.ir&&(a.vv=!1,a.UA({error:b.error,details:b.details}),a.ir=null,a.UA=null)});a.ue.addEventListener("authResult",function(b){b&&b.authResult&&a.Cf(b);a.ue.yu()(b)});a.ue.addEventListener("tokenReady",function(b){var c=new Bx(b.response);if(a.gv&&a.gv!=c.MG())a.Cf({type:"tokenFailed",reason:"Account domain does not match hosted_domain specified by gapi.auth2.init.",accountDomain:c.MG(),expectedDomain:a.gv});else{a.currentUser.get().update(c);
var d=a.currentUser;d.qc.notify(d.La);a.isSignedIn.set(!0);c=c.BG();(d=_.Hx(a.i8))&&c&&_.li.set(["G_AUTHUSER_",window.location.protocol==="https:"&&d.ff?"S":"H",d.Ti].join(""),c,{domain:d.domain,secure:d.isSecure});_.Hw(b.response);a.Cf(b)}});a.ue.addEventListener("noSessionBound",function(b){a.vv&&b.autoOpenAuthUrl?(a.vv=!1,$w(a.ue).select(function(c){if(c&&c.login_hint){var d=a.ue;_.Yv(d,d.tD,[c.login_hint,!0])}else a.currentUser.set(new Bx(null)),a.isSignedIn.set(!1),_.Iw(),a.Cf(b)})):(a.currentUser.set(new Bx(null)),
a.isSignedIn.set(!1),_.Iw(),a.Cf(b))});a.ue.addEventListener("tokenFailed",function(b){a.Cf(b)});a.ue.addEventListener("userLoggedOut",function(b){a.currentUser.get().Zk();var c=a.currentUser;c.qc.notify(c.La);a.isSignedIn.set(!1);_.Iw();a.Cf(b)})},$x=function(a,b,c,d){return a.oca.then(function(e){if(b)return b(e.O$)},c,d)};cy.prototype.Cf=function(a){if(a){this.vv=!1;var b=a.type||"";if(this.DB[b])this.DB[b](a);this.ir&&(this.ir({O$:this}),this.UA=this.ir=null)}};
var fy=function(a,b){_.Zb(b,function(c,d){a.DB[d]=function(e){a.DB={};c(e)}})},Fx=function(a,b,c,d){d=_.fk(d);a.gv&&(d.hd=a.gv);var e=d.ux_mode||a.Gha;delete d.ux_mode;delete d.app_package_name;var f={sessionMeta:{extraQueryParams:d},responseType:"permission id_token"};e=="redirect"?(d.redirect_uri||(d.redirect_uri=a.aC||xx()+window.location.pathname),gy(a,f)):(delete d.redirect_uri,hy(a,f),fy(a,{authResult:function(h){h.authResult&&h.authResult.error?c(h.authResult):fy(a,{tokenReady:function(){b(a.currentUser.get())},
tokenFailed:c})}}))};cy.prototype.Yk=function(a){return new _.xk(function(b,c){var d=new rx(a);this.MH=d.Iu()?!0:!1;this.PA?(d.Ka.fetch_basic_profile=!0,sx(d,"email profile openid")):d.Ka.fetch_basic_profile=!1;var e=dy(this,d.Iu());d.J_(e);Fx(this,b,c,d.get())},this)};
cy.prototype.Pu=function(a){var b=a||{};this.MH=!!b.scope;a=dy(this,b.scope);if(a=="")return _.Ck({error:"Missing required parameter: scope"});var c={scope:a,access_type:"offline",include_granted_scopes:!0};_.Bb(Rw,function(d){b[d]!=null&&(c[d]=b[d])});c.hasOwnProperty("prompt")||c.hasOwnProperty("approval_prompt")||(c.prompt="consent");b.redirect_uri=="postmessage"||b.redirect_uri==void 0?a=iy(this,c):(c.redirect_uri=b.redirect_uri,gy(this,{sessionMeta:{extraQueryParams:c},responseType:"code id_token"}),
a=_.Bk({message:"Redirecting to IDP."}));return a};
var iy=function(a,b){b.origin=xx();delete b.redirect_uri;hy(a,{sessionMeta:{extraQueryParams:b},responseType:"code permission id_token"});return new _.xk(function(c,d){fy(this,{authResult:function(e){(e=e&&e.authResult)&&e.code?c({code:e.code}):d(e&&e.error?e:{error:"unknown_error"})}})},a)},hy=function(a,b){wx(b,["sessionMeta","extraQueryParams","gsiwebsdk"],"2");Bw(a.ue,tx(),b)},gy=function(a,b){wx(b,["sessionMeta","extraQueryParams","gsiwebsdk"],"2");Cw(a.ue,b)};
cy.prototype.Zk=function(a){var b=a||!1;return new _.xk(function(c){ow(this.ue,b,function(){c()})},this)};cy.prototype.ET=function(){return this.Ka.authParameters.scope};var dy=function(a,b){a=a.ET();b=kw(b?b.split(" "):[],a?a.split(" "):[]);_.Fh(b);return b.join(" ")};cy.prototype.rK=function(){var a=this;return new _.xk(function(b,c){fy(a,{noSessionBound:c,tokenFailed:c,userLoggedOut:c,tokenReady:function(d){b(d.response)}});a.ue.TS()})};
cy.prototype.BP=function(a,b,c,d){if(a=typeof a==="string"?document.getElementById(a):a){var e=this;_.Dj(a,"click",function(){var f=b;typeof b=="function"&&(f=b());e.Yk(f).then(function(h){c&&c(h)},function(h){d&&d(h)})})}else d&&d({error:"Could not attach click handler to the element. Reason: element not found."})};cy.prototype.disconnect=function(){return new _.xk(function(a){this.ue.revoke(function(){a()})},this)};cy.prototype.attachClickHandler=cy.prototype.BP;var jy;_.xk.prototype["catch"]=_.xk.prototype.zD;jy=null;_.ky=function(a){zx();a=Mx(a);if(Cx){if(_.Vu(a,jy||{}))return ay();throw new Vw("gapi.auth2 has been initialized with different options. Consider calling gapi.auth2.getAuthInstance() instead of gapi.auth2.init().");}Nx(a,a.fetch_basic_profile!==!1);vx();jy=a;Cx=new cy(a);_.Me.ga=1;return ay()};var my,oy,ly,qy,py,ry;
_.ny=function(a,b){zx();vx();a=Mx(a);Nx(a);var c=Ux(a);c.supportBlocked3PCookies=Ax(a.use_fedcm);var d=new _.hw(c);a.prompt=="none"?ly(d,a,function(e){e.status=e.error?{signed_in:!1,method:null,google_logged_in:!1}:{signed_in:!0,method:"AUTO",google_logged_in:!0};b(e)}):my(d,a,function(e){if(e.error)e.status={signed_in:!1,method:null,google_logged_in:!1};else{var f=e.access_token||e.id_token;e.status={signed_in:!!f,method:"PROMPT",google_logged_in:!!f}}e["g-oauth-window"]=d.BY.xi;b(e)})};
my=function(a,b,c){var d=new Rx(b.response_type);c=oy(a,d,c);var e={responseType:d.toString()};wx(e,["sessionMeta","extraQueryParams","gsiwebsdk"],b.gsiwebsdk||"2");Sx(d)&&wx(e,["sessionMeta","extraQueryParams","access_type"],b.access_type||"offline");b.redirect_uri&&wx(e,["sessionMeta","extraQueryParams","redirect_uri"],b.redirect_uri);b.state&&wx(e,["sessionMeta","extraQueryParams","state"],b.state);b=tx();a.Ck?c({authResult:{error:"idpiframe_initialization_failed",details:a.Wn().error}}):(a.tn=
c,Bw(a,b,e))};oy=function(a,b,c){if(Tx(b)){var d=py(c);return function(e){e&&e.authResult&&!e.authResult.error?a.yu(function(f){f&&!f.error?(f=_.fk(f),Sx(b)&&(f.code=e.authResult.code),d(f)):d(f?f:{error:"unknown_error"})})(e):d(e&&e.authResult?e.authResult:{error:"unknown_error"})}}return function(e){e&&e.authResult&&!e.authResult.error?c(_.fk(e.authResult)):c(e&&e.authResult?e.authResult:{error:"unknown_error"})}};
ly=function(a,b,c){if(Sx(new Rx(b.response_type))&&b.access_type=="offline")c({error:"immediate_failed",error_subtype:"access_denied"});else{var d=py(c);b.login_hint?a.pG(b.login_hint,function(e){e?qy(a,b,e,d):c({error:"immediate_failed",error_subtype:"access_denied"})}):b.authuser!==void 0&&b.authuser!==null?bx(a,b.authuser).select(function(e){e&&e.login_hint?qy(a,b,e.login_hint,d):d({error:"immediate_failed",error_subtype:"access_denied"})}):a.ru(function(e){e&&e.hint?qy(a,b,e.hint,d):e&&e.disabled?
d({error:"immediate_failed",error_subtype:"no_user_bound"}):(b.session_selection=="first_valid"?ax(a):$w(a)).select(function(f){f&&f.login_hint?qy(a,b,f.login_hint,d):d({error:"immediate_failed",error_subtype:"no_user_bound"})})})}};qy=function(a,b,c,d){b=new Rx(b.response_type);var e=0,f={},h=function(k){!k||k.error?d(k):(e--,_.ij(f,k),e==0&&d(f))};(Tx(b)||Vx(b,"id_token"))&&e++;Sx(b)&&e++;(Tx(b)||Vx(b,"id_token"))&&_.iw(a,c,h);Sx(b)&&Dw(a,c,h)};
py=function(a){return function(b){if(!b||b.error)_.Iw(),b?a(b):a({error:"unknown_error"});else{if(b.access_token){var c=_.fk(b);ry(c);delete c.id_token;delete c.code;_.Hw(c)}a(b)}}};ry=function(a){_.Bb(Sw,function(b){delete a[b]})};_.t("gapi.auth2.init",_.ky);_.t("gapi.auth2.authorize",function(a,b){if(Cx!=null)throw new Vw("gapi.auth2.authorize cannot be called after GoogleAuth has been initialized (i.e. with a call to gapi.auth2.init, or gapi.client.init when given a 'clientId' and a 'scope' parameters).");_.ny(a,function(c){ry(c);b(c)})});_.t("gapi.auth2._gt",function(){return _.ui()});_.t("gapi.auth2.enableDebugLogs",function(a){a=a!==!1;_.Wu=a!="0"&&!!a});_.t("gapi.auth2.getAuthInstance",_.by);
_.t("gapi.auth2.BasicProfile",Dx);_.t("gapi.auth2.BasicProfile.prototype.getId",Dx.prototype.getId);_.t("gapi.auth2.BasicProfile.prototype.getName",Dx.prototype.getName);_.t("gapi.auth2.BasicProfile.prototype.getGivenName",Dx.prototype.c$);_.t("gapi.auth2.BasicProfile.prototype.getFamilyName",Dx.prototype.Y9);_.t("gapi.auth2.BasicProfile.prototype.getImageUrl",Dx.prototype.k$);_.t("gapi.auth2.BasicProfile.prototype.getEmail",Dx.prototype.Qn);_.t("gapi.auth2.GoogleAuth",cy);
_.t("gapi.auth2.GoogleAuth.prototype.attachClickHandler",cy.prototype.BP);_.t("gapi.auth2.GoogleAuth.prototype.disconnect",cy.prototype.disconnect);_.t("gapi.auth2.GoogleAuth.prototype.grantOfflineAccess",cy.prototype.Pu);_.t("gapi.auth2.GoogleAuth.prototype.signIn",cy.prototype.Yk);_.t("gapi.auth2.GoogleAuth.prototype.signOut",cy.prototype.Zk);_.t("gapi.auth2.GoogleAuth.prototype.getInitialScopes",cy.prototype.ET);_.t("gapi.auth2.GoogleUser",Bx);_.t("gapi.auth2.GoogleUser.prototype.grant",Bx.prototype.Q$);
_.t("gapi.auth2.GoogleUser.prototype.getId",Bx.prototype.getId);_.t("gapi.auth2.GoogleUser.prototype.isSignedIn",Bx.prototype.xg);_.t("gapi.auth2.GoogleUser.prototype.getAuthResponse",Bx.prototype.Tl);_.t("gapi.auth2.GoogleUser.prototype.getBasicProfile",Bx.prototype.G9);_.t("gapi.auth2.GoogleUser.prototype.getGrantedScopes",Bx.prototype.e$);_.t("gapi.auth2.GoogleUser.prototype.getHostedDomain",Bx.prototype.MG);_.t("gapi.auth2.GoogleUser.prototype.grantOfflineAccess",Bx.prototype.Pu);
_.t("gapi.auth2.GoogleUser.prototype.hasGrantedScopes",Bx.prototype.xA);_.t("gapi.auth2.GoogleUser.prototype.reloadAuthResponse",Bx.prototype.rK);_.t("gapi.auth2.LiveValue",ox);_.t("gapi.auth2.LiveValue.prototype.listen",ox.prototype.na);_.t("gapi.auth2.LiveValue.prototype.get",ox.prototype.get);_.t("gapi.auth2.SigninOptionsBuilder",rx);_.t("gapi.auth2.SigninOptionsBuilder.prototype.getAppPackageName",rx.prototype.D9);_.t("gapi.auth2.SigninOptionsBuilder.prototype.setAppPackageName",rx.prototype.Yfa);
_.t("gapi.auth2.SigninOptionsBuilder.prototype.getScope",rx.prototype.Iu);_.t("gapi.auth2.SigninOptionsBuilder.prototype.setScope",rx.prototype.J_);_.t("gapi.auth2.SigninOptionsBuilder.prototype.getPrompt",rx.prototype.A$);_.t("gapi.auth2.SigninOptionsBuilder.prototype.setPrompt",rx.prototype.vga);_.t("gapi.auth2.SigninOptionsBuilder.prototype.get",rx.prototype.get);
_.af=_.af||{};
(function(){function a(b){var c="";if(b.nodeType==3||b.nodeType==4)c=b.nodeValue;else if(b.innerText)c=b.innerText;else if(b.innerHTML)c=b.innerHTML;else if(b.firstChild){c=[];for(b=b.firstChild;b;b=b.nextSibling)c.push(a(b));c=c.join("")}return c}_.af.createElement=function(b){if(!document.body||document.body.namespaceURI)try{var c=document.createElementNS("http://www.w3.org/1999/xhtml",b)}catch(d){}return c||document.createElement(b)};_.af.CQ=function(b){var c=_.af.createElement("iframe");try{var d=
["<","iframe"],e=b||{},f;for(f in e)e.hasOwnProperty(f)&&(d.push(" "),d.push(f),d.push('="'),d.push(_.af.aG(e[f])),d.push('"'));d.push("></");d.push("iframe");d.push(">");var h=_.af.createElement(d.join(""));h&&(!c||h.tagName==c.tagName&&h.namespaceURI==c.namespaceURI)&&(c=h)}catch(l){}d=c;b=b||{};for(var k in b)b.hasOwnProperty(k)&&(d[k]=b[k]);return c};_.af.gT=function(){if(document.body)return document.body;try{var b=document.getElementsByTagNameNS("http://www.w3.org/1999/xhtml","body");if(b&&
b.length==1)return b[0]}catch(c){}return document.documentElement||document};_.af.Vqa=function(b){return a(b)}})();
_.Gg=window.gapi&&window.gapi.util||{};
_.Gg=_.Gg={};_.Gg.getOrigin=function(a){return _.Ig(a)};
_.Ny=function(a){if(a.indexOf("GCSC")!==0)return null;var b={xj:!1};a=a.substr(4);if(!a)return b;var c=a.charAt(0);a=a.substr(1);var d=a.lastIndexOf("_");if(d==-1)return b;var e=_.Ly(a.substr(d+1));if(e==null)return b;a=a.substring(0,d);if(a.charAt(0)!=="_")return b;d=c==="E"&&e.ff;return!d&&(c!=="U"||e.ff)||d&&!_.My?b:{xj:!0,ff:d,S7:a.substr(1),domain:e.domain,Ti:e.Ti}};_.Oy=function(a,b){this.dh=a;a=b||{};this.Wca=Number(a.maxAge)||0;this.Xd=a.domain;this.Mm=a.path;this.Efa=!!a.secure};_.Oy.prototype.read=function(){for(var a=this.dh+"=",b=document.cookie.split(/;\s*/),c=0;c<b.length;++c){var d=b[c];if(d.indexOf(a)==0)return d.substr(a.length)}};
_.Oy.prototype.write=function(a,b){if(!Py.test(this.dh))throw"Invalid cookie name";if(!Qy.test(a))throw"Invalid cookie value";a=this.dh+"="+a;this.Xd&&(a+=";domain="+this.Xd);this.Mm&&(a+=";path="+this.Mm);b=typeof b==="number"?b:this.Wca;if(b>=0){var c=new Date;c.setSeconds(c.getSeconds()+b);a+=";expires="+c.toUTCString()}this.Efa&&(a+=";secure");document.cookie=a;return!0};_.Oy.prototype.clear=function(){this.write("",0)};var Qy=/^[-+/_=.:|%&a-zA-Z0-9@]*$/,Py=/^[A-Z_][A-Z0-9_]{0,63}$/;
_.Oy.iterate=function(a){for(var b=document.cookie.split(/;\s*/),c=0;c<b.length;++c){var d=b[c].split("="),e=d.shift();a(e,d.join("="))}};_.Ry=function(a){this.Nf=a};_.Ry.prototype.read=function(){if(Sy.hasOwnProperty(this.Nf))return Sy[this.Nf]};_.Ry.prototype.write=function(a){Sy[this.Nf]=a;return!0};_.Ry.prototype.clear=function(){delete Sy[this.Nf]};var Sy={};_.Ry.iterate=function(a){for(var b in Sy)Sy.hasOwnProperty(b)&&a(b,Sy[b])};var Ty=function(){this.La=null;this.key=function(){return null};this.getItem=function(){return this.La};this.setItem=function(a,b){this.La=b;this.length=1};this.removeItem=function(){this.clear()};this.clear=function(){this.La=null;this.length=0};this.length=0},Uy=function(a){try{var b=a||window.sessionStorage;if(!b)return!1;b.setItem("gapi.sessionStorageTest","gapi.sessionStorageTest"+b.length);b.removeItem("gapi.sessionStorageTest");return!0}catch(c){return!1}},Vy=function(a,b){this.dh=a;this.rN=
Uy(b)?b||window.sessionStorage:new Ty};Vy.prototype.read=function(){return this.rN.getItem(this.dh)};Vy.prototype.write=function(a){try{this.rN.setItem(this.dh,a)}catch(b){return!1}return!0};Vy.prototype.clear=function(){this.rN.removeItem(this.dh)};Vy.iterate=function(a){if(Uy())for(var b=window.sessionStorage.length,c=0;c<b;++c){var d=window.sessionStorage.key(c);a(d,window.sessionStorage[d])}};_.My=window.location.protocol==="https:";_.Wy=_.My||window.location.protocol==="http:"?_.Oy:_.Ry;_.Ly=function(a){var b=a.substr(1),c="",d=window.location.hostname;if(b!==""){c=parseInt(b,10);if(isNaN(c))return null;b=d.split(".");if(b.length<c-1)return null;b.length==c-1&&(d="."+d)}else d="";return{ff:a.charAt(0)=="S",domain:d,Ti:c}};var Xy,Yy,az,bz;Xy=_.Ce();Yy=_.Ce();_.Zy=_.Ce();_.$y=_.Ce();az="state code cookie_policy g_user_cookie_policy authuser prompt g-oauth-window status".split(" ");bz=function(a){this.vY=a;this.aJ=null};
bz.prototype.write=function(a){var b=_.Ce(),c=_.Ce(),d=window.decodeURIComponent?decodeURIComponent:unescape,e;for(e in a)if(_.De(a,e)){var f=a[e];f=f.replace(/\+/g," ");c[e]=d(f);b[e]=a[e]}d=az.length;for(e=0;e<d;++e)delete c[az[e]];a=String(a.authuser||0);d=_.Ce();d[a]=c;c=_.Rf(d);this.vY.write(c);this.aJ=b};bz.prototype.read=function(){return this.aJ};bz.prototype.clear=function(){this.vY.clear();this.aJ=_.Ce()};_.cz=function(a){return a?{domain:a.domain,path:"/",secure:a.ff}:null};
Vy.iterate(function(a){var b=_.Ny(a);b&&b.xj&&(Xy[a]=new bz(new Vy(a)))});_.Wy.iterate(function(a){Xy[a]&&(Yy[a]=new _.Wy(a,_.cz(_.Ny(a))))});
_.mi=function(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;n=m=0}function b(p){for(var q=h,r=0;r<64;r+=4)q[r/4]=p[r]<<24|p[r+1]<<16|p[r+2]<<8|p[r+3];for(r=16;r<80;r++)p=q[r-3]^q[r-8]^q[r-14]^q[r-16],q[r]=(p<<1|p>>>31)&4294967295;p=e[0];var w=e[1],u=e[2],x=e[3],A=e[4];for(r=0;r<80;r++){if(r<40)if(r<20){var D=x^w&(u^x);var E=1518500249}else D=w^u^x,E=1859775393;else r<60?(D=w&u|x&(w|u),E=2400959708):(D=w^u^x,E=3395469782);D=((p<<5|p>>>27)&4294967295)+
D+A+E+q[r]&4294967295;A=x;x=u;u=(w<<30|w>>>2)&4294967295;w=p;p=D}e[0]=e[0]+p&4294967295;e[1]=e[1]+w&4294967295;e[2]=e[2]+u&4294967295;e[3]=e[3]+x&4294967295;e[4]=e[4]+A&4294967295}function c(p,q){if(typeof p==="string"){p=unescape(encodeURIComponent(p));for(var r=[],w=0,u=p.length;w<u;++w)r.push(p.charCodeAt(w));p=r}q||(q=p.length);r=0;if(m==0)for(;r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64;for(;r<q;)if(f[m++]=p[r++],n++,m==64)for(m=0,b(f);r+64<q;)b(p.slice(r,r+64)),r+=64,n+=64}function d(){var p=[],
q=n*8;m<56?c(k,56-m):c(k,64-(m-56));for(var r=63;r>=56;r--)f[r]=q&255,q>>>=8;b(f);for(r=q=0;r<5;r++)for(var w=24;w>=0;w-=8)p[q++]=e[r]>>w&255;return p}for(var e=[],f=[],h=[],k=[128],l=1;l<64;++l)k[l]=0;var m,n;a();return{reset:a,update:c,digest:d,Si:function(){for(var p=d(),q="",r=0;r<p.length;r++)q+="0123456789ABCDEF".charAt(Math.floor(p[r]/16))+"0123456789ABCDEF".charAt(p[r]%16);return q}}};var oi=function(a,b,c){var d=String(_.Xa.location.href);return d&&a&&b?[b,ni(_.Ig(d),a,c||null)].join(" "):null},ni=function(a,b,c){var d=[],e=[];if((Array.isArray(c)?2:1)==1)return e=[b,a],_.Bb(d,function(k){e.push(k)}),pi(e.join(" "));var f=[],h=[];_.Bb(c,function(k){h.push(k.key);f.push(k.value)});c=Math.floor((new Date).getTime()/1E3);e=f.length==0?[c,b,a]:[f.join(":"),c,b,a];_.Bb(d,function(k){e.push(k)});a=pi(e.join(" "));a=[c,a];h.length==0||a.push(h.join(""));return a.join("_")},pi=function(a){var b=
_.mi();b.update(a);return b.Si().toLowerCase()};var ri;_.qi=function(){var a=_.Xa.__SAPISID||_.Xa.__APISID||_.Xa.__3PSAPISID||_.Xa.__1PSAPISID||_.Xa.__OVERRIDE_SID;if(a)return!0;typeof document!=="undefined"&&(a=new _.ji(document),a=a.get("SAPISID")||a.get("APISID")||a.get("__Secure-3PAPISID")||a.get("__Secure-1PAPISID"));return!!a};ri=function(a,b,c,d){(a=_.Xa[a])||typeof document==="undefined"||(a=(new _.ji(document)).get(b));return a?oi(a,c,d):null};
_.si=function(a){var b=_.Ig(String(_.Xa.location.href)),c=[];if(_.qi()){b=b.indexOf("https:")==0||b.indexOf("chrome-extension:")==0||b.indexOf("chrome-untrusted://new-tab-page")==0||b.indexOf("moz-extension:")==0;var d=b?_.Xa.__SAPISID:_.Xa.__APISID;d||typeof document==="undefined"||(d=new _.ji(document),d=d.get(b?"SAPISID":"APISID")||d.get("__Secure-3PAPISID"));(d=d?oi(d,b?"SAPISIDHASH":"APISIDHASH",a):null)&&c.push(d);b&&((b=ri("__1PSAPISID","__Secure-1PAPISID","SAPISID1PHASH",a))&&c.push(b),(a=
ri("__3PSAPISID","__Secure-3PAPISID","SAPISID3PHASH",a))&&c.push(a))}return c.length==0?null:c.join(" ")};
var ts,us;_.ls=function(a){if(a instanceof _.gc)return a.PY;throw Error("j");};_.ms=function(a,b,c,d){this.top=a;this.right=b;this.bottom=c;this.left=d};_.ns=function(a,b){return a==b?!0:a&&b?a.x==b.x&&a.y==b.y:!1};_.os=function(a,b){this.x=a!==void 0?a:0;this.y=b!==void 0?b:0};_.g=_.os.prototype;_.g.clone=function(){return new _.os(this.x,this.y)};_.g.equals=function(a){return a instanceof _.os&&_.ns(this,a)};_.g.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};
_.g.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};_.g.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};_.g.translate=function(a,b){a instanceof _.os?(this.x+=a.x,this.y+=a.y):(this.x+=Number(a),typeof b==="number"&&(this.y+=b));return this};_.g.scale=function(a,b){this.x*=a;this.y*=typeof b==="number"?b:a;return this};_.ps=function(a){return a.scrollingElement?a.scrollingElement:!_.Cd&&_.he(a)?a.documentElement:a.body||a.documentElement};
_.qs=function(a){var b=_.ps(a);a=a.defaultView;return new _.os(a.pageXOffset||b.scrollLeft,a.pageYOffset||b.scrollTop)};_.rs=function(a,b,c,d){return _.ce(a.Bc,b,c,d)};_.ss=function(a){return _.qs(a.Bc)};ts=function(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})};us=function(a){return a.replace(RegExp("(^|[\\s]+)([a-z])","g"),function(b,c,d){return c+d.toUpperCase()})};_.vs=function(a){return _.be(document,a)};_.g=_.ms.prototype;_.g.Qb=function(){return this.right-this.left};_.g.Nc=function(){return this.bottom-this.top};_.g.clone=function(){return new _.ms(this.top,this.right,this.bottom,this.left)};_.g.contains=function(a){return this&&a?a instanceof _.ms?a.left>=this.left&&a.right<=this.right&&a.top>=this.top&&a.bottom<=this.bottom:a.x>=this.left&&a.x<=this.right&&a.y>=this.top&&a.y<=this.bottom:!1};
_.g.expand=function(a,b,c,d){_.vb(a)?(this.top-=a.top,this.right+=a.right,this.bottom+=a.bottom,this.left-=a.left):(this.top-=a,this.right+=Number(b),this.bottom+=Number(c),this.left-=Number(d));return this};_.g.ceil=function(){this.top=Math.ceil(this.top);this.right=Math.ceil(this.right);this.bottom=Math.ceil(this.bottom);this.left=Math.ceil(this.left);return this};
_.g.floor=function(){this.top=Math.floor(this.top);this.right=Math.floor(this.right);this.bottom=Math.floor(this.bottom);this.left=Math.floor(this.left);return this};_.g.round=function(){this.top=Math.round(this.top);this.right=Math.round(this.right);this.bottom=Math.round(this.bottom);this.left=Math.round(this.left);return this};
_.g.translate=function(a,b){a instanceof _.os?(this.left+=a.x,this.right+=a.x,this.top+=a.y,this.bottom+=a.y):(this.left+=a,this.right+=a,typeof b==="number"&&(this.top+=b,this.bottom+=b));return this};_.g.scale=function(a,b){b=typeof b==="number"?b:a;this.left*=a;this.right*=a;this.top*=b;this.bottom*=b;return this};var ys,ws,Cs,Es;_.xs=function(a,b,c){if(typeof b==="string")(b=ws(a,b))&&(a.style[b]=c);else for(var d in b){c=a;var e=b[d],f=ws(c,d);f&&(c.style[f]=e)}};ys={};ws=function(a,b){var c=ys[b];if(!c){var d=ts(b);c=d;a.style[d]===void 0&&(d=(_.Cd?"Webkit":_.Bd?"Moz":null)+us(d),a.style[d]!==void 0&&(c=d));ys[b]=c}return c};_.zs=function(a,b){var c=a.style[ts(b)];return typeof c!=="undefined"?c:a.style[ws(a,b)]||""};
_.As=function(a,b){var c=_.$d(a);return c.defaultView&&c.defaultView.getComputedStyle&&(a=c.defaultView.getComputedStyle(a,null))?a[b]||a.getPropertyValue(b)||"":""};_.Bs=function(a,b){return _.As(a,b)||(a.currentStyle?a.currentStyle[b]:null)||a.style&&a.style[b]};Cs=function(a){try{return a.getBoundingClientRect()}catch(b){return{left:0,top:0,right:0,bottom:0}}};
_.Fs=function(a,b){b=b||_.ps(document);var c=b||_.ps(document);var d=_.Ds(a),e=_.Ds(c),f=_.As(c,"borderLeftWidth");var h=_.As(c,"borderRightWidth");var k=_.As(c,"borderTopWidth"),l=_.As(c,"borderBottomWidth");h=new _.ms(parseFloat(k),parseFloat(h),parseFloat(l),parseFloat(f));c==_.ps(document)?(f=d.x-c.scrollLeft,d=d.y-c.scrollTop):(f=d.x-e.x-h.left,d=d.y-e.y-h.top);a=Es(a);e=c.clientHeight-a.height;h=c.scrollLeft;k=c.scrollTop;h+=Math.min(f,Math.max(f-(c.clientWidth-a.width),0));k+=Math.min(d,Math.max(d-
e,0));c=new _.os(h,k);b.scrollLeft=c.x;b.scrollTop=c.y};_.Ds=function(a){var b=_.$d(a),c=new _.os(0,0);if(a==(b?_.$d(b):document).documentElement)return c;a=Cs(a);b=_.ss(_.ae(b));c.x=a.left+b.x;c.y=a.top+b.y;return c};_.Hs=function(a,b){var c=new _.os(0,0),d=_.ie(_.$d(a));a:{try{_.Wb(d.parent);var e=!0;break a}catch(f){}e=!1}if(!e)return c;do e=d==b?_.Ds(a):_.Gs(a),c.x+=e.x,c.y+=e.y;while(d&&d!=b&&d!=d.parent&&(a=d.frameElement)&&(d=d.parent));return c};
_.Gs=function(a){a=Cs(a);return new _.os(a.left,a.top)};_.Js=function(a,b,c){if(b instanceof _.rd)c=b.height,b=b.width;else if(c==void 0)throw Error("J");a.style.width=_.Is(b,!0);a.style.height=_.Is(c,!0)};_.Is=function(a,b){typeof a=="number"&&(a=(b?Math.round(a):a)+"px");return a};
_.Ks=function(a){var b=Es;if(_.Bs(a,"display")!="none")return b(a);var c=a.style,d=c.display,e=c.visibility,f=c.position;c.visibility="hidden";c.position="absolute";c.display="inline";a=b(a);c.display=d;c.position=f;c.visibility=e;return a};Es=function(a){var b=a.offsetWidth,c=a.offsetHeight,d=_.Cd&&!b&&!c;return(b===void 0||d)&&a.getBoundingClientRect?(a=Cs(a),new _.rd(a.right-a.left,a.bottom-a.top)):new _.rd(b,c)};_.Ls=function(a,b){a.style.display=b?"":"none"};
_.Ns=function(a){var b=_.ae(void 0),c=_.rs(b,"HEAD")[0];if(!c){var d=_.rs(b,"BODY")[0];c=b.wa("HEAD");d.parentNode.insertBefore(c,d)}d=b.wa("STYLE");var e;(e=_.Gc("style",document))&&d.setAttribute("nonce",e);_.Ms(d,a);b.appendChild(c,d)};_.Ms=function(a,b){b=_.ls(b);_.Xa.trustedTypes?_.ve(a,b):a.innerHTML=b};_.Os=_.Bd?"MozUserSelect":_.Cd||_.zd?"WebkitUserSelect":null;
_.dz=function(a){_.dj.call(this);this.Nf=1;this.UB=[];this.ZB=0;this.Tf=[];this.Rj={};this.h7=!!a};_.eb(_.dz,_.dj);_.g=_.dz.prototype;_.g.subscribe=function(a,b,c){var d=this.Rj[a];d||(d=this.Rj[a]=[]);var e=this.Nf;this.Tf[e]=a;this.Tf[e+1]=b;this.Tf[e+2]=c;this.Nf=e+3;d.push(e);return e};_.g.Sw=_.jb(18);_.g.unsubscribe=function(a,b,c){if(a=this.Rj[a]){var d=this.Tf;if(a=a.find(function(e){return d[e+1]==b&&d[e+2]==c}))return this.ol(a)}return!1};
_.g.ol=function(a){var b=this.Tf[a];if(b){var c=this.Rj[b];this.ZB!=0?(this.UB.push(a),this.Tf[a+1]=function(){}):(c&&_.gj(c,a),delete this.Tf[a],delete this.Tf[a+1],delete this.Tf[a+2])}return!!b};
_.g.bp=function(a,b){var c=this.Rj[a];if(c){var d=Array(arguments.length-1),e=arguments.length,f;for(f=1;f<e;f++)d[f-1]=arguments[f];if(this.h7)for(f=0;f<c.length;f++)e=c[f],ez(this.Tf[e+1],this.Tf[e+2],d);else{this.ZB++;try{for(f=0,e=c.length;f<e&&!this.isDisposed();f++){var h=c[f];this.Tf[h+1].apply(this.Tf[h+2],d)}}finally{if(this.ZB--,this.UB.length>0&&this.ZB==0)for(;c=this.UB.pop();)this.ol(c)}}return f!=0}return!1};var ez=function(a,b,c){_.vk(function(){a.apply(b,c)})};
_.dz.prototype.clear=function(a){if(a){var b=this.Rj[a];b&&(b.forEach(this.ol,this),delete this.Rj[a])}else this.Tf.length=0,this.Rj={}};_.dz.prototype.Zb=function(a){if(a){var b=this.Rj[a];return b?b.length:0}a=0;for(b in this.Rj)a+=this.Zb(b);return a};_.dz.prototype.ua=function(){_.dz.N.ua.call(this);this.clear();this.UB.length=0};
_.fz=function(a){this.pha=a};_.gz=function(a){_.dj.call(this);this.ke=new _.dz(a);_.fj(this,this.ke)};_.fz.prototype.toString=function(){return this.pha};_.eb(_.gz,_.dj);_.g=_.gz.prototype;_.g.subscribe=function(a,b,c){return this.ke.subscribe(a.toString(),b,c)};_.g.Sw=_.jb(17);_.g.unsubscribe=function(a,b,c){return this.ke.unsubscribe(a.toString(),b,c)};_.g.ol=function(a){return this.ke.ol(a)};_.g.bp=function(a,b){return this.ke.bp(a.toString(),b)};_.g.clear=function(a){this.ke.clear(a!==void 0?a.toString():void 0)};_.g.Zb=function(a){return this.ke.Zb(a!==void 0?a.toString():void 0)};
var hz,iz,lz,jz,mz,nz,kz;hz=function(a){var b=_.tc("");return _.ec(a.map(function(c){return _.fc(_.tc(c))}).join(_.fc(b).toString()))};iz=function(a){return hz(a)};lz=function(a){for(var b="",c=Object.keys(a),d=0;d<c.length;d++){var e=c[d],f=a[e];if(!jz.test(e))throw Error("j");if(f!==void 0&&f!==null){if(/^on./i.test(e))throw Error("j");kz.indexOf(e.toLowerCase())!==-1&&(f=_.mc(f)?f.toString():_.rc(String(f))||"about:invalid#zClosurez");f=e+'="'+_.tc(String(f))+'"';b+=" "+f}}return b};
_.oz=function(a,b){if(!jz.test("div"))throw Error("j");if(mz.indexOf("DIV")!==-1)throw Error("j");var c="<div";a&&(c+=lz(a));Array.isArray(b)||(b=b===void 0?[]:[b]);nz.indexOf("DIV")!==-1?c+=">":(a=iz(b.map(function(d){return d instanceof _.dc?d:_.tc(String(d))})),c+=">"+a.toString()+"</div>");return _.ec(c)};jz=/^[a-z][a-z\d-]*$/i;mz="APPLET BASE EMBED IFRAME LINK MATH META OBJECT SCRIPT STYLE SVG TEMPLATE".split(" ");nz="AREA BR COL COMMAND HR IMG INPUT KEYGEN PARAM SOURCE TRACK WBR".split(" ");
kz=["action","formaction","href"];_.pz=function(a,b){Array.isArray(b)||(b=[b]);b=b.map(function(c){return typeof c==="string"?c:c.Zo+" "+c.duration+"s "+c.timing+" "+c.delay+"s"});_.xs(a,"transition",b.join(","))};_.qz=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}}(function(){var a=_.me("DIV"),b=_.Cd?"-webkit":_.Bd?"-moz":null,c="transition:opacity 1s linear;";b&&(c+=b+"-transition:opacity 1s linear;");_.Hc(a,_.oz({style:c}));return _.zs(a.firstChild,"transition")!=""});
_.rz=function(a,b){_.Oj.call(this);this.Cm=a||1;this.gx=b||_.Xa;this.OP=(0,_.z)(this.mha,this);this.xW=_.ld()};_.eb(_.rz,_.Oj);_.g=_.rz.prototype;_.g.enabled=!1;_.g.Hc=null;_.g.setInterval=function(a){this.Cm=a;this.Hc&&this.enabled?(this.stop(),this.start()):this.Hc&&this.stop()};
_.g.mha=function(){if(this.enabled){var a=_.ld()-this.xW;a>0&&a<this.Cm*.8?this.Hc=this.gx.setTimeout(this.OP,this.Cm-a):(this.Hc&&(this.gx.clearTimeout(this.Hc),this.Hc=null),this.dispatchEvent("tick"),this.enabled&&(this.stop(),this.start()))}};_.g.start=function(){this.enabled=!0;this.Hc||(this.Hc=this.gx.setTimeout(this.OP,this.Cm),this.xW=_.ld())};_.g.stop=function(){this.enabled=!1;this.Hc&&(this.gx.clearTimeout(this.Hc),this.Hc=null)};_.g.ua=function(){_.rz.N.ua.call(this);this.stop();delete this.gx};
_.sz=function(a,b,c){if(typeof a==="function")c&&(a=(0,_.z)(a,c));else if(a&&typeof a.handleEvent=="function")a=(0,_.z)(a.handleEvent,a);else throw Error("wa");return Number(b)>2147483647?-1:_.Xa.setTimeout(a,b||0)};_.tz=function(a){_.Xa.clearTimeout(a)};
_.vz=function(){_.uz="oauth2relay"+String(2147483647*(0,_.Qg)()|0)};_.wz=new _.gz;_.xz=new _.fz("oauth");_.vz();_.Xe("oauth-flow/client_id");var yz=String(_.Xe("oauth-flow/redirectUri"));if(yz)yz.replace(/[#][\s\S]*/,"");else{var zz=_.Gg.getOrigin(window.location.href);_.Xe("oauth-flow/callbackUrl");encodeURIComponent(zz)}_.Gg.getOrigin(window.location.href);
var Bz,Cz,Dz,Ez,Fz,Gz,Hz,Iz,Jz,Kz,Lz,Nz,Oz,Pz,Qz,Rz,Sz,Yz,Zz,$z,aA,bA,cA,dA,eA,fA,gA,hA,iA,jA,kA,lA,mA,nA,oA,pA,qA,rA,sA,tA,uA,xA,wA,yA,zA,AA,BA,CA,DA,EA;_.Az=function(a,b){if(_.Lh&&!b)return _.Xa.atob(a);var c="";_.Oh(a,function(d){c+=String.fromCharCode(d)});return c};Bz=function(a){var b=String(a("immediate")||"");a=String(a("prompt")||"");return b==="true"||a==="none"};Cz=function(a){return _.ei("enableMultilogin")&&a("cookie_policy")&&!Bz(a)?!0:!1};
Fz=function(){var a,b=null;_.Wy.iterate(function(c,d){c.indexOf("G_AUTHUSER_")===0&&(c=c.substring(11),c=_.Ly(c),!a||c.ff&&!a.ff||c.ff==a.ff&&c.Ti>a.Ti)&&(a=c,b=d)});return{t7:a,authuser:b}};Gz=[".APPS.GOOGLEUSERCONTENT.COM","@DEVELOPER.GSERVICEACCOUNT.COM"];Hz=function(a){a=a.toUpperCase();for(var b=Gz.length,c=0;c<b;++c){var d=a.split(Gz[c]);d.length==2&&d[1]===""&&(a=d[0])}a=a.replace(/-/g,"_").toUpperCase();a.length>40&&(b=new _.Pg,b.ux(a),a=b.Si().toUpperCase());return a};
Iz=function(a){if(!a)return[];a=a.split("=");return a[1]?a[1].split("|"):[]};Jz=function(a){a=a.split(":");return{clientId:a[0].split("=")[1],Wfa:Iz(a[1]),Lra:Iz(a[2]),Eqa:Iz(a[3])}};Kz=function(a){var b=Fz(),c=b.t7;b=b.authuser;var d=a&&Hz(a);if(b!==null){var e;_.Wy.iterate(function(h,k){(h=_.Ny(h))&&h.xj&&(d&&h.S7!=d||h.ff==c.ff&&h.Ti==c.Ti&&(e=k))});if(e){var f=Jz(e);a=f&&f.Wfa[Number(b)];f=f&&f.clientId;if(a)return{authuser:b,Jsa:a,clientId:f}}}return null};
Lz=function(a,b){a=_.ui(a);if(!a||!b&&a.error)return null;b=Math.floor((new Date).getTime()/1E3);return a.expires_at&&b>a.expires_at?null:a};_.Mz=function(a,b){if(b){var c=b;var d=a}else typeof a==="string"?d=a:c=a;c?_.Hw(c,d):_.Iw(d)};
Nz=function(a){if(!a)return null;a!=="single_host_origin"&&(a=_.Ig(a));var b=window.location.hostname,c=b,d=_.My;if(a!=="single_host_origin"){c=a.split("://");if(c.length==2)d=c.shift()==="https";else return _.Vf.log("WARNING invalid cookie_policy: "+a),null;c=c[0]}if(c.indexOf(":")!==-1)c=b="";else{a="."+c;if(b.lastIndexOf(a)!==b.length-a.length)return _.Vf.log("Invalid cookie_policy domain: "+c),null;c=a;b=c.split(".").length-1}return{domain:c,ff:d,Ti:b}};
Oz=function(a){var b=Nz(a);if(!b)return new _.Ry("G_USERSTATE_");a=["G_USERSTATE_",_.My&&b.ff?"S":"H",b.Ti].join("");var c=_.$y[a];c||(c={YI:63072E3},_.Ee(_.cz(b),c),c=new _.Oy(a,c),_.$y[a]=c,b=c.read(),typeof b!=="undefined"&&b!==null&&(document.cookie=a+"=; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/",c.write(b)));return c};Pz=function(a){var b=Oz(a).read();a=_.Ce();if(b){b=b.split(":");for(var c;c=b.shift();)c=c.split("="),a[c[0]]=c[1]}return a};
Qz=function(a,b,c){var d=Pz(b),e=d[a];d[a]="0";var f=[];_.Qm(d,function(k,l){f.push(l+"="+k)});var h=f.join(":");b=Oz(b);h?b.write(h):b.clear();d[a]!==e&&c&&c()};Rz=function(a,b){b=Pz(b);return b[a]=="0"||b[a]=="X"};Sz=function(a){a=Nz(a.g_user_cookie_policy);if(!a||a.ff&&!_.My)a=null;else{var b=["G_AUTHUSER_",_.My&&a.ff?"S":"H",a.Ti].join(""),c=_.Zy[b];c||(c=new _.Wy(b,_.cz(a)),_.Zy[b]=c);a=c}_.Ye("googleapis.config/sessionIndex",null);a.clear()};Yz=function(a){return Bz(function(b){return a[b]})};
Zz=0;$z=!1;aA=[];bA={};cA={};dA=null;eA=function(a){var b=_.uz;return function(c){if(this.f==b&&this.t==_.$f.Pn(this.f)&&this.origin==_.$f.co(this.f))return a.apply(this,arguments)}};fA=function(a){if(a&&!decodeURIComponent(a).startsWith("m;/_/scs/"))throw Error("ya");};gA=function(a){var b=_.af.Rg,c=b(a).jsh;if(c!=null)return fA(c),a;if(b=String(b().jsh||_.Me.h||""))fA(b),c=(a+"#").indexOf("#"),a=a.substr(0,c)+(a.substr(0,c).indexOf("?")!==-1?"&":"?")+"jsh="+encodeURIComponent(b)+a.substr(c);return a};
hA=function(){return!!_.Xe("oauth-flow/usegapi")};iA=function(a,b){hA()?dA.unregister(a):_.$f.unregister(a+":"+b)};jA=function(a,b,c){hA()?dA.register(a,c,_.dn):_.$f.register(a+":"+b,eA(c))};kA=function(){Dz.parentNode.removeChild(Dz)};
lA=function(a){var b=Dz;_.pz(b,[{Zo:"-webkit-transform",duration:1,timing:"ease",delay:0}]);_.pz(b,[{Zo:"transform",duration:1,timing:"ease",delay:0}]);_.sz(function(){b.style.webkitTransform="translate3d(0px,"+a+"px,0px)";b.style.transform="translate3d(0px,"+a+"px,0px)"},0)};mA=function(){var a=Ez+88;lA(a);Ez=a};nA=function(){var a=Ez-88;lA(a);Ez=a};
oA=function(a){var b=a?mA:nA,c=a?nA:mA;a=a?"-":"";Ez=parseInt(a+88,10);Dz.style.webkitTransform="translate3d(0px,"+a+88+"px,0px)";Dz.style.transform="translate3d(0px,"+a+88+"px,0px)";Dz.style.display="";Dz.style.visibility="visible";b();_.sz(c,4E3);_.sz(kA,5E3)};
pA=function(a){var b=_.Xe("oauth-flow/toast/position");b!=="top"&&(b="bottom");var c=document.createElement("div");Dz=c;c.style.cssText="position:fixed;left:0px;z-index:1000;width:100%;";_.xs(c,"visibility","hidden");_.xs(c,b,"-40px");_.xs(c,"height","128px");var d=c;if(_.Kr()){d=document.createElement("div");d.style.cssText="float:left;position:relative;left:50%;";c.appendChild(d);var e=document.createElement("div");e.style.cssText="float:left;position:relative;left:-50%";d.appendChild(e);d=e}e=
b=="top"?"-":"";Ez=parseInt(e+88,10);Dz.style.webkitTransform="translate3d(0px,"+e+88+"px,0px)";Dz.style.transform="translate3d(0px,"+e+88+"px,0px)";e=window;try{for(;e.parent!=e&&e.parent.document;)e=e.parent}catch(f){}e=e.document.body;try{e.insertBefore(c,e.firstChild)}catch(f){}_.an.openChild({url:":socialhost:/:session_prefix:_/widget/oauthflow/toast",queryParams:{clientId:a.client_id,idToken:a.id_token},where:d,onRestyle:function(){b==="top"?oA(!0):oA(!1)}})};
qA=function(a){var b=_.Po(),c=b&&b.scope;b=a&&a.scope;b=typeof b==="string"?b.split(" "):b||[];if(c){c=c.split(" ");for(var d=0;d<c.length;++d){var e=c[d];_.Om.call(b,e)==-1&&b.push(e)}b.length>0&&(a.scope=b.join(" "))}return a};
rA=function(a,b){var c=null;a&&b&&(c=b.client_id=b.client_id||a.client_id,b.scope=b.scope||a.scope,b.g_user_cookie_policy=a.cookie_policy,b.cookie_policy=b.cookie_policy||a.cookie_policy,b.response_type=b.response_type||a.response_type);if(b){b.issued_at||(b.issued_at=String(Math.floor((new Date).getTime()/1E3)));var d=parseInt(b.expires_in,10)||86400;b.error&&(d=_.Xe("oauth-flow/errorMaxAge")||86400);b.expires_in=String(d);b.expires_at||(b.expires_at=String(Math.floor((new Date).getTime()/1E3)+d));
b._aa||b.error||Kz(c)!=null||!Yz(a)||(b._aa="1");a=b.status={};a.google_logged_in=!!b.session_state;c=a.signed_in=!!b.access_token;a.method=c?b["g-oauth-window"]?"PROMPT":"AUTO":null}return b};sA=function(a){a=a&&a.id_token;if(!a||!a.split(".")[1])return null;a=(a.split(".")[1]+"...").replace(/^((....)+)\.?\.?\.?$/,"$1");a=_.Qf(_.Az(a,!0));if(a===!1)throw Error("za");return a};tA=function(a){return(a=sA(a))?a.sub:null};
uA=function(a){a&&aA.push(a);a=_.uz;var b=document.getElementById(a),c=(new Date).getTime();if(b){if(Zz&&c-Zz<6E4)return;var d=_.$f.Pn(a);d&&(iA("oauth2relayReady",d),iA("oauth2callback",d));b.parentNode.removeChild(b);if(/Firefox/.test(navigator.userAgent))try{window.frames[a]=void 0}catch(f){}_.vz();a=_.uz}Zz=c;var e=String(2147483647*(0,_.Qg)()|0);b=_.Xe("oauth-flow/proxyUrl")||_.Xe("oauth-flow/relayUrl");hA()?dA=_.an.openChild({where:_.af.gT(),url:b,id:a,attributes:{style:{width:"1px",height:"1px",
position:"absolute",top:"-100px",display:"none"},"aria-hidden":"true"},dontclear:!0}):(b=[b,"?parent=",encodeURIComponent(_.Gg.getOrigin(window.location.href)),"#rpctoken=",e,"&forcesecure=1"].join(""),c=_.af.gT(),d=_.af.CQ({name:a,id:a}),d.src=gA(b),d.style.width="1px",d.style.height="1px",d.style.position="absolute",d.style.top="-100px",d.tabIndex=-1,d.setAttribute("aria-hidden","true"),c.appendChild(d),_.$f.Iw(a));jA("oauth2relayReady",e,function(){iA("oauth2relayReady",e);var f=aA;if(f!==null){aA=
null;for(var h=f.length,k=0;k<h;++k)f[k]()}});jA("oauth2callback",e,function(f){var h=_.af.Rg;h=h(f);var k=h.state;f=k;f=f.replace(/\|.*$/,"");f={}.hasOwnProperty.call(cA,f)?cA[f]:null;h.state=f;if(h.state!=null){f=bA[k];delete bA[k];k=f&&f.key||"token";var l=h=rA(f&&f.params,h);var m=(m=tA(l))?Rz(m,l.cookie_policy):!1;!m&&l&&(" "+(l.scope||"")+" ").indexOf(" https://www.googleapis.com/auth/plus.login ")>=0&&_.Xe("isLoggedIn")&&(l&&l._aa)==="1"&&(l._aa="0",$z||($z=!0,pA(l)));_.Mz(k,h);h=Lz(k);if(f){k=
f.popup;l=f.after_redirect;if(k&&"keep_open"!=l)try{k.close()}catch(n){}f.callback&&(f.callback(h),f.callback=null)}}})};_.vA=function(a){aA!==null?uA(a):a&&a()};xA=function(a,b){var c=wA,d=tA(a);d&&(Sz(a),Qz(d,b,function(){if(c){var e={error:"user_signed_out"};e.client_id=a.client_id;e.g_user_cookie_policy=a.g_user_cookie_policy;e.scope=a.scope;e.response_type=a.response_type;e.session_state=a.session_state;e=rA(null,e);c(e)}}))};
wA=function(a){a||(a=Lz(void 0,!0));a&&typeof a==="object"||(a={error:"invalid_request",error_description:"no callback data"});var b=a.error_description;b&&window.console&&(window.console.error(a.error),window.console.error(b));a.error||(_.Me.drw=null);_.Mz(a);if(b=a.authuser)_.Xe("googleapis.config/sessionIndex"),_.Ye("googleapis.config/sessionIndex",b);_.wz.bp(_.xz,a);return a};yA=["client_id","cookie_policy","response_type"];zA="client_id response_type login_hint authuser prompt include_granted_scopes after_redirect access_type hl state".split(" ");
AA=function(a){var b=_.fk(a);b.session_state&&b.session_state.extraQueryParams&&(b.authuser=b.session_state.extraQueryParams.authuser);b.session_state=null;a.expires_at&&(b.expires_at=parseInt(a.expires_at/1E3).toString());a.expires_in&&(b.expires_in=a.expires_in.toString());a.first_issued_at&&(b.issued_at=parseInt(a.first_issued_at/1E3).toString(),delete b.first_issued_at);_.Hw(b);return b};
BA=function(a){if(a.include_granted_scopes===void 0){var b=_.Xe("include_granted_scopes");a.include_granted_scopes=!!b}};CA=function(a){window.console&&(typeof window.console.warn==="function"?window.console.warn(a):typeof window.console.log==="function"&&window.console.log(a))};
DA=function(a){var b=a||{},c={};_.Bb(zA,function(d){b[d]!=null&&(c[d]=b[d])});a=_.Xe("googleapis/overrideClientId");a!=null&&(c.client_id=a);BA(c);typeof b.scope==="string"?c.scope=b.scope:Array.isArray(b.scope)&&(c.scope=b.scope.join(" "));b["openid.realm"]!=null&&(c.openid_realm=b["openid.realm"]);b.cookie_policy!=null?c.cookie_policy=b.cookie_policy:b.cookiepolicy!=null&&(c.cookie_policy=b.cookiepolicy);c.login_hint==null&&b.user_id!=null&&(c.login_hint=b.user_id);try{_.Hx(c.cookie_policy)}catch(d){c.cookie_policy&&
CA("The cookie_policy configuration: '"+c.cookie_policy+"' is illegal, and thus ignored."),delete c.cookie_policy}b.hd!=null&&(c.hosted_domain=b.hd);c.prompt==null&&(b.immediate==1||b.immediate=="true"?c.prompt="none":b.approval_prompt=="force"&&(c.prompt="consent"));c.prompt=="none"&&(c.session_selection="first_valid");c.prompt=="none"&&c.access_type=="offline"&&delete c.access_type;typeof c.authuser==="undefined"&&(a=_.ii(),a!=null&&(c.authuser=a));a=b.redirect_uri||_.Xe("oauth-flow/redirectUri");
a!=null&&a!="postmessage"&&(c.redirect_uri=a);c.gsiwebsdk="shim";return c};
EA=function(a,b){var c=DA(a),d=new _.xk(function(e,f){_.ny(c,function(h){var k=h||{};_.Bb(yA,function(l){k[l]==null&&(k[l]=c[l])});!c.include_granted_scopes&&a&&a.scope&&(k.scope=a.scope);a&&a.state!=null&&(k.state=a.state);k.error?(c.prompt=="none"&&k.error=="user_logged_out"&&(k.error="immediate_failed_user_logged_out"),f(k)):(h=AA(k),h.authuser!=null&&_.Ye("googleapis.config/sessionIndex",h.authuser),e(h))})});b&&d.then(b,b);return d};var FA,HA;FA=null;_.IA=function(a,b){if(a.approvalprompt!=="force"){a=_.GA(a);a.prompt="none";delete a.redirect_uri;delete a.approval_prompt;delete a.immediate;if(b=!b)FA?(a.client_id!==FA.client_id&&window.console&&window.console.log&&window.console.log("Ignoring mismatched page-level auth param client_id="+a.client_id),b=!0):(FA=a,b=!1);b||HA(a)}};
_.GA=function(a){var b=a.redirecturi||"postmessage",c=_.zc((a.scope||"").replace(/[\s\xa0]+/g," "));b={client_id:a.clientid,redirect_uri:b,response_type:"code token id_token gsession",scope:c};a.approvalprompt&&(b.approval_prompt=a.approvalprompt);a.state&&(b.state=a.state);a.openidrealm&&(b["openid.realm"]=a.openidrealm);c=a.accesstype=="offline"?!0:(c=a.redirecturi)&&c!="postmessage";c&&(b.access_type="offline");a.requestvisibleactions&&(b.request_visible_actions=_.zc(a.requestvisibleactions.replace(/[\s\xa0]+/g,
" ")));a.after_redirect&&(b.after_redirect=a.after_redirect);a.cookiepolicy&&a.cookiepolicy!=="none"&&(b.cookie_policy=a.cookiepolicy);typeof a.includegrantedscopes!="undefined"&&(b.include_granted_scopes=a.includegrantedscopes);a.e&&(b.e=a.e);(a=a.authuser||_.Xe("googleapis.config/sessionIndex"))&&(b.authuser=a);(a=_.Xe("useoriginassocialhost"))&&(b.use_origin_as_socialhost=a);return b};HA=function(a){_.Bp("waaf0","signin","0");EA(a,function(b){_.Bp("waaf1","signin","0");wA(b)})};
_.JA=function(a){a=_.GA(a);_.Ye("oauth-flow/authWindowWidth",445);_.Ye("oauth-flow/authWindowHeight",615);HA(a)};_.KA=function(a){_.wz.unsubscribe(_.xz,a);_.wz.subscribe(_.xz,a)};var RA,UA;_.MA=function(a){return a.cookiepolicy?!0:(_.LA("cookiepolicy is a required field.  See https://developers.google.com/+/web/signin/#button_attr_cookiepolicy for more information."),!1)};_.LA=function(a){window.console&&(window.console.error?window.console.error(a):window.console.log&&window.console.log(a))};_.QA=function(a,b){var c=_.Po();_.Ee(a,c);c=qA(c);if(_.MA(c)){var d=_.NA();_.OA(c);b?_.Le(b,"click",function(){_.PA(c,d)}):_.PA(c,d)}};
_.NA=function(){var a=new RA;_.KA(function(b){a.ZI&&b&&(b.access_token&&_.Ye("isPlusUser",!0),b["g-oauth-window"]&&(a.ZI=!1,_.Vf.warn("OTA app install is no longer supported.")))});return a};RA=function(){this.ZI=!1};_.OA=function(a){a=_.SA(a);_.TA(a.callback);_.vA(function(){_.IA(a)})};_.SA=function(a){UA(a);a.redirecturi&&delete a.redirecturi;Cz(function(b){return a[b]})||(a.authuser=0);return a};UA=function(a){/^\s*$/.test(a.scope||"")&&(a.scope="https://www.googleapis.com/auth/plus.login")};
_.TA=function(a){if(typeof a==="string")if(window[a])a=window[a];else{_.LA('Callback function named "'+a+'" not found');return}a&&_.KA(a)};_.PA=function(a,b){b.ZI=!0;a=_.SA(a);_.JA(a)};_.t("gapi.auth.authorize",EA);_.t("gapi.auth.checkSessionState",function(a,b){var c=_.Ce();c.client_id=a.client_id;c.session_state=a.session_state;_.vA(function(){hA()?dA.send("check_session_state",c,function(d){b.call(null,d[0])},_.dn):_.$f.call(_.uz,"check_session_state",eA(function(d){b.call(null,d)}),c.session_state,c.client_id)})});_.t("gapi.auth.getAuthHeaderValueForFirstParty",function(a,b){_.Hi(_.Gi(),51).rb();return _.si(a,b)});_.t("gapi.auth.getToken",Lz);
_.t("gapi.auth.getVersionInfo",function(a,b){_.vA(function(){var c=_.si()||"",d=null,e=null;c&&(e=c.split(" "),e.length==2&&(d=e[1]));d?hA()?dA.send("get_versioninfo",{xapisidHash:d,sessionIndex:b},function(f){a(f[0])},_.dn):_.$f.call(_.uz,"get_versioninfo",eA(function(f){a(f)}),d,b):a()})});_.t("gapi.auth.init",_.vA);_.t("gapi.auth.setToken",_.Mz);_.t("gapi.auth.signIn",function(a){_.QA(a)});_.t("gapi.auth.signOut",function(){var a=Lz();a&&xA(a,a.cookie_policy)});
_.t("gapi.auth.unsafeUnpackIdToken",sA);_.t("gapi.auth._pimf",_.IA);_.t("gapi.auth._oart",pA);_.t("gapi.auth._guss",function(a){return Oz(a).read()});
var VA=_.Po();VA.clientid&&VA.scope&&VA.callback&&!_.Xe("disableRealtimeCallback")&&_.OA(VA);
var sy=function(){};var uy;uy=function(){};_.eb(uy,sy);uy.prototype.Oy=function(){return new XMLHttpRequest};_.ty=new uy;
_.Ph=function(a){var b=[],c=0,d;for(d in a)b[c++]=d;return b};_.Qh=function(a){return a==null?"":String(a)};_.Rh=function(a,b,c,d,e,f,h){var k="";a&&(k+=a+":");c&&(k+="//",b&&(k+=b+"@"),k+=c,d&&(k+=":"+d));e&&(k+=e);f&&(k+="?"+f);h&&(k+="#"+h);return k};_.Sh=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");
_.Th=function(a,b){if(!b)return a;var c=a.indexOf("#");c<0&&(c=a.length);var d=a.indexOf("?");if(d<0||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]};_.Uh=function(a,b,c){if(Array.isArray(b))for(var d=0;d<b.length;d++)_.Uh(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+encodeURIComponent(String(b))))};_.Vh=function(a){var b=[],c;for(c in a)_.Uh(c,a[c],b);return b.join("&")};
_.Wh=function(a,b){b=_.Vh(b);return _.Th(a,b)};
var Ui,Vi;_.Ti=function(a){var b={SAPISIDHASH:!0,SAPISID3PHASH:!0,SAPISID1PHASH:!0,APISIDHASH:!0};return a&&(a.OriginToken||a.Authorization&&b[String(a.Authorization).split(" ")[0]])?!0:!1};Ui={HU:_.Ti,lca:_.qi,HT:function(){var a=null;_.qi()&&(a=window.__PVT,a==null&&(a=(new _.ji(document)).get("BEAT")));return a},E9:_.si};
Vi=function(a,b){a=_.af.CQ({id:a,name:a});a.style.width="1px";a.style.height="1px";a.style.position="absolute";a.style.top="-100px";a.style.display="none";if(window.navigator){var c=window.navigator.userAgent||"";var d=window.navigator.product||"";c=c.indexOf("Opera")!=0&&c.indexOf("WebKit")==-1&&d=="Gecko"&&c.indexOf("rv:1.")>0}else c=!1;a.src=c?"about:blank":b;a.tabIndex=-1;typeof a.setAttribute==="function"?a.setAttribute("aria-hidden","true"):a["aria-hidden"]="true";document.body.appendChild(a);
c&&(a.src=b);return a};Ui={HU:_.Ti,lca:_.qi,HT:function(){var a=null;_.qi()&&(a=window.__PVT,a==null&&(a=(new _.ji(document)).get("BEAT")));return a},E9:_.si};var Xi,Wi;Xi=function(){return!!Wi("auth/useFirstPartyAuthV2")};Wi=function(a){return _.Xe("googleapis.config/"+a)};
_.Yi=function(a,b,c){a=a===void 0?{}:a;b=b===void 0?window.location.href:b;c=c===void 0?"auto":c;if(c=="none")return a;var d=a.Authorization,e=a.OriginToken;if(!d&&!e){(e=_.ui())&&e.access_token&&(c=="oauth2"||c=="auto")&&(d=String(e.token_type||"Bearer")+" "+e.access_token);if(e=!d)e=(!!Wi("auth/useFirstPartyAuth")||c=="1p")&&c!="oauth2";if(e&&_.qi()){if(Xi()){d=Wi("primaryEmail");c=Wi("appDomain");e=Wi("fogId");var f=[];d&&f.push({key:"e",value:d});c&&f.push({key:"a",value:c});e&&f.push({key:"u",
value:e});d=_.si(f)}else d=_.si();d&&(c=a["X-Goog-AuthUser"],b=_.ii(b),b=c||b,_.xc(_.Qh(b))&&(!Xi()||Xi()&&_.xc(_.Qh(Wi("primaryEmail")))&&_.xc(_.Qh(Wi("appDomain")))&&_.xc(_.Qh(Wi("fogId"))))&&(b="0"),_.xc(_.Qh(b))||(a["X-Goog-AuthUser"]=b))}d?a.Authorization=d:Wi("auth/useOriginToken")!==!1&&(e=Ui.HT())&&(a.OriginToken=e)}return a};_.Zi=function(){function a(n,p,q,r,w){var u=f("proxy");if(r||!u){u=f("root");var x=f("root-1p")||u;u=u||"https://content.googleapis.com";x=x||"https://clients6.google.com";var A=f("xd3")||"/static/proxy.html";u=(r||String(p?x:u))+A}u=String(u);q&&(u+=(u.indexOf("?")>=0?"&":"?")+"usegapi=1");(p=_.af.Rg().jsh||_.Me.h)&&(u+=(u.indexOf("?")>=0?"&":"?")+"jsh="+encodeURIComponent(p));u+="#parent="+encodeURIComponent(w!=null?String(w):_.Gg.getOrigin(document.location.href));return u+("&rpctoken="+n)}function b(n,
p,q,r,w){var u=d(q,r,w);k[u]||(q=Vi(u,p),_.$f.register("ready:"+n,function(){_.$f.unregister("ready:"+n);if(!l[u]){l[u]=!0;var x=m[u];m[u]=[];for(var A=0,D=x.length;A<D;++A){var E=x[A];e(E.kp,E.Vea,E.callback)}}}),_.$f.Iw(u,p),k[u]=q)}function c(n,p,q){var r=String(2147483647*_.Pi()|0),w=a(r,n,p,q);_.Xf(function(){b(r,w,n,p,q)})}function d(n,p,q){n=a("",n,p,q,"");q=h[n+p];if(!q){q=new _.Pg;q.ux(n);q=q.Si().toLowerCase();var r=_.Pi();q+=r;h[n+p]=q}return"apiproxy"+q}function e(n,p,q){var r=void 0,
w=!1;if(n!=="makeHttpRequests")throw'only "makeHttpRequests" RPCs are implemented';var u=function(N){if(N){if(typeof r!="undefined"&&typeof N.root!="undefined"&&r!=N.root)throw"all requests in a batch must have the same root URL";r=N.root||r;w=Ui.HU(N.headers)}};if(p)for(var x=0,A=p.length;x<A;++x){var D=p[x];D&&u(D.params)}u=!!f("useGapiForXd3");var E=d(w,u,r);k[E]||c(w,u,r);l[E]?_.$f.call(E,n,function(N){if(this.f==E&&this.t==_.$f.Pn(this.f)&&this.origin==_.$f.co(this.f)){var H=_.Qf(N);q(H,N)}},
p):(m[E]||(m[E]=[]),m[E].push({kp:n,Vea:p,callback:q}))}function f(n){return _.Xe("googleapis.config/"+n)}var h={},k={},l={},m={};return{Spa:function(n,p,q){return _.Yi(n,p,q)},Xm:e}}();
var Xg={iia:"Authorization",P2:"Content-ID",Hia:"Content-Transfer-Encoding",Iia:"Content-Type",oja:"Date",fma:"OriginToken",Cka:"hotrod-board-name",Dka:"hotrod-chrome-cpu-model",Eka:"hotrod-chrome-processors",woa:"User-Agent",Roa:"WWW-Authenticate",Toa:"X-Ad-Manager-Impersonation",Soa:"X-Ad-Manager-Debug-Info",Voa:"X-ClientDetails",Woa:"X-Cloudaicompanion-Trace-Id",Xoa:"X-Compass-Routing-Destination",apa:"X-Goog-AuthUser",fpa:"X-Goog-Encode-Response-If-Executable",Yoa:"X-Google-Consent",Zoa:"X-Google-EOM",
hpa:"X-Goog-Meeting-ABR",ipa:"X-Goog-Meeting-Botguardid",jpa:"X-Goog-Meeting-Bot-Info",kpa:"X-Goog-Meeting-ClientInfo",lpa:"X-Goog-Meeting-ClientVersion",mpa:"X-Goog-Meeting-Debugid",npa:"X-Goog-Meeting-Identifier",opa:"X-Goog-Meeting-Interop-Cohorts",ppa:"X-Goog-Meeting-Interop-Type",qpa:"X-Goog-Meeting-OidcIdToken",rpa:"X-Goog-Meeting-RtcClient",spa:"X-Goog-Meeting-StartSource",tpa:"X-Goog-Meeting-Token",upa:"X-Goog-Meeting-Viewer-Token",vpa:"X-Goog-PageId",xpa:"X-Goog-Safety-Content-Type",ypa:"X-Goog-Safety-Encoding",
cpa:"X-Goog-Drive-Client-Version",dpa:"X-Goog-Drive-Resource-Keys",zpa:"X-HTTP-Method-Override",Apa:"X-JavaScript-User-Agent",Bpa:"X-Origin",Cpa:"X-Referer",Dpa:"X-Requested-With",Gpa:"X-Use-HTTP-Status-Code-Override",Epa:"X-Server-Timeout",gpa:"X-Goog-First-Party-Reauth",Fpa:"X-Server-Token",bpa:"x-goog-chat-space-id",wpa:"x-goog-pan-request-context",Uoa:"X-AppInt-Credentials",epa:"X-Goog-Earth-Gcp-Project"},Yg="Accept Accept-Language Authorization Cache-Control cast-device-capabilities Content-Disposition Content-Encoding Content-Language Content-Length Content-MD5 Content-Range Content-Transfer-Encoding Content-Type Date developer-token EES-S7E-MODE financial-institution-id GData-Version google-cloud-resource-prefix hotrod-board-name hotrod-chrome-cpu-model hotrod-chrome-processors Host If-Match If-Modified-Since If-None-Match If-Unmodified-Since linked-customer-id login-customer-id MIME-Version Origin OriginToken Pragma Range request-id Slug Transfer-Encoding User-Agent Want-Digest X-Ad-Manager-Impersonation X-Ad-Manager-Debug-Info x-alkali-account-key x-alkali-application-key x-alkali-auth-apps-namespace x-alkali-auth-entities-namespace x-alkali-auth-entity x-alkali-client-locale x-chrome-connected x-framework-xsrf-token X-Client-Data X-Client-Pctx X-ClientDetails X-Client-Version x-debug-settings-metadata X-Firebase-Locale X-GData-Client X-GData-Key X-Goog-AuthUser X-Goog-PageId X-Goog-Encode-Response-If-Executable X-GoogApps-Allowed-Domains X-Goog-AdX-Buyer-Impersonation X-Goog-Api-Client X-Goog-Api-Key X-Google-EOM X-Goog-Visibilities X-Goog-Correlation-Id X-Goog-Request-Info X-Goog-Request-Reason X-Goog-Request-Time X-Goog-Experiments x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-jspb x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-jspb x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-223261916-bin x-goog-ext-*********-bin x-goog-ext-233818517-bin x-goog-ext-*********-bin x-goog-ext-*********-bin x-goog-ext-*********-jspb x-goog-ext-*********-bin X-Goog-Firebase-Installations-Auth x-goog-greenenergyuserappservice-metadata X-Firebase-Client X-Firebase-Client-Log-Type X-Firebase-GMPID X-Firebase-Auth-Token X-Firebase-AppCheck X-Firebase-Token X-Goog-Drive-Client-Version X-Goog-Drive-Resource-Keys x-goog-iam-authority-selector x-goog-iam-authorization-token x-goog-request-params x-goog-sherlog-context X-Goog-Sn-Metadata X-Goog-Sn-PatientId X-Goog-Spatula X-Goog-Travel-Bgr X-Goog-Travel-Settings X-Goog-Upload-Command X-Goog-Upload-Content-Disposition X-Goog-Upload-Content-Length X-Goog-Upload-Content-Type X-Goog-Upload-File-Name X-Goog-Upload-Header-Content-Encoding X-Goog-Upload-Header-Content-Length X-Goog-Upload-Header-Content-Type X-Goog-Upload-Header-Transfer-Encoding X-Goog-Upload-Offset X-Goog-Upload-Protocol X-Goog-User-Project X-Goog-Visitor-Id X-Goog-FieldMask X-Google-Project-Override x-goog-maps-api-salt x-goog-maps-api-signature x-goog-maps-client-id x-goog-maps-channel-id x-goog-maps-solution-id x-goog-maps-session-id x-goog-maps-traffic-policy x-goog-gmp-client-signals x-goog-spanner-database-role X-HTTP-Method-Override X-JavaScript-User-Agent X-Pan-Versionid X-Proxied-User-IP X-Origin X-Referer X-Requested-With X-Stadia-Client-Context X-Upload-Content-Length X-Upload-Content-Type X-Use-Alt-Service X-Use-HTTP-Status-Code-Override X-Ios-Bundle-Identifier X-Places-Ios-Sdk X-Android-Package X-Android-Cert X-Places-Android-Sdk X-Goog-Maps-Ios-Uuid X-Goog-Maps-Android-Uuid X-Ariane-Xsrf-Token X-Earth-Engine-App-ID-Token X-Earth-Engine-Computation-Profile X-Earth-Engine-Computation-Profiling X-Play-Console-Experiments-Override X-Play-Console-Session-Id X-YouTube-Bootstrap-Logged-In X-Youtube-Client-Version X-Youtube-Lava-Device-Context X-YouTube-VVT X-YouTube-Page-CL X-YouTube-Page-Label X-YouTube-Page-Timestamp X-Compass-Routing-Destination X-Goog-Meeting-ABR X-Goog-Meeting-Botguardid X-Goog-Meeting-Bot-Info X-Goog-Meeting-ClientInfo X-Goog-Meeting-ClientVersion X-Goog-Meeting-Debugid X-Goog-Meeting-Identifier X-Goog-Meeting-Interop-Cohorts X-Goog-Meeting-Interop-Type X-Goog-Meeting-OidcIdToken X-Goog-Meeting-RtcClient X-Goog-Meeting-StartSource X-Goog-Meeting-Token X-Goog-Meeting-Viewer-Token x-sdm-id-token X-Sfdc-Authorization X-Server-Timeout x-foyer-client-environment X-Goog-First-Party-Reauth X-Server-Token x-rfui-request-context x-goog-chat-space-id x-goog-nest-jwt X-Cloud-Trace-Context traceparent x-goog-pan-request-context X-AppInt-Credentials X-Goog-Earth-Gcp-Project".split(" "),
Zg="Digest Cache-Control Content-Disposition Content-Encoding Content-Language Content-Length Content-MD5 Content-Range Content-Transfer-Encoding Content-Type Date ETag Expires Last-Modified Location Pragma Range Server Transfer-Encoding WWW-Authenticate Vary Unzipped-Content-MD5 X-Correlation-ID X-Debug-Tracking-Id X-Google-Consent X-Google-EOM X-Goog-Generation X-Goog-Metageneration X-Goog-Safety-Content-Type X-Goog-Safety-Encoding X-Google-Trace X-Goog-Upload-Chunk-Granularity X-Goog-Upload-Control-URL X-Goog-Upload-Size-Received X-Goog-Upload-Status X-Goog-Upload-URL X-Goog-Diff-Download-Range X-Goog-Hash X-Goog-Updated-Authorization X-Server-Object-Version X-Guploader-Customer X-Guploader-Upload-Result X-Guploader-Uploadid X-Google-Gfe-Backend-Request-Cost X-Earth-Engine-Computation-Profile X-Cloudaicompanion-Trace-Id X-Goog-Meeting-ABR X-Goog-Meeting-Botguardid X-Goog-Meeting-Bot-Info X-Goog-Meeting-ClientInfo X-Goog-Meeting-ClientVersion X-Goog-Meeting-Debugid X-Goog-Meeting-Identifier X-Goog-Meeting-RtcClient X-Goog-Meeting-Token X-Goog-Meeting-Viewer-Token X-Compass-Routing-Destination x-goog-ext-*********-bin x-goog-ext-*********-bin".split(" ");var $g,ah,bh,ch,eh,fh,gh,hh,ih,jh,kh,lh;$g=null;ah=null;bh=null;ch=function(a,b){var c=a.length;if(c!=b.length)return!1;for(var d=0;d<c;++d){var e=a.charCodeAt(d),f=b.charCodeAt(d);e>=65&&e<=90&&(e+=32);f>=65&&f<=90&&(f+=32);if(e!=f)return!1}return!0};
_.dh=function(a){a=String(a||"").split("\x00").join("");for(var b=[],c=!0,d=a.length,e=0;e<d;++e){var f=a.charAt(e),h=a.charCodeAt(e);if(h>=55296&&h<=56319&&e+1<d){var k=a.charAt(e+1),l=a.charCodeAt(e+1);l>=56320&&l<=57343&&(f+=k,h=65536+(h-55296<<10)+(l-56320),++e)}if(!(h>=0&&h<=1114109)||h>=55296&&h<=57343||h>=64976&&h<=65007||(h&65534)==65534)h=65533,f=String.fromCharCode(h);k=!(h>=32&&h<=126)||f==" "||c&&f==":"||f=="\\";!c||f!="/"&&f!="?"||(c=!1);f=="%"&&(e+2>=d?k=!0:(l=16*parseInt(a.charAt(e+
1),16)+parseInt(a.charAt(e+2),16),l>=0&&l<=255?(h=l,f=h==0?"":"%"+(256+l).toString(16).toUpperCase().substr(1),e+=2):k=!0));k&&(f=encodeURIComponent(f),f.length<=1&&(h>=0&&h<=127?f="%"+(256+h).toString(16).toUpperCase().substr(1):(h=65533,f=encodeURIComponent(String.fromCharCode(h)))));b.push(f)}a=b.join("");a=a.split("#")[0];a=a.split("?");b=a[0].split("/");c=[];d=b.length;for(e=0;e<d;++e)f=b[e],h=f.split("%2E").join("."),h=h.split(encodeURIComponent("\uff0e")).join("."),h=="."?e+1==d&&c.push(""):
h==".."?(c.length>0&&c.pop(),e+1==d&&c.push("")):c.push(f);a[0]=c.join("/");for(a=a.join("?");a&&a.charAt(0)=="/";)a=a.substr(1);return"/"+a};eh={"access-control-allow-origin":!0,"access-control-allow-credentials":!0,"access-control-expose-headers":!0,"access-control-max-age":!0,"access-control-allow-headers":!0,"access-control-allow-methods":!0,p3p:!0,"proxy-authenticate":!0,"set-cookie":!0,"set-cookie2":!0,status:!0,tsv:!0,"":!0};
fh={"accept-charset":!0,"accept-encoding":!0,"access-control-request-headers":!0,"access-control-request-method":!0,"client-ip":!0,clientip:!0,connection:!0,"content-length":!0,cookie:!0,cookie2:!0,date:!0,dnt:!0,expect:!0,forwarded:!0,"forwarded-for":!0,"front-end-https":!0,host:!0,"keep-alive":!0,"max-forwards":!0,method:!0,origin:!0,"raw-post-data":!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,url:!0,"user-agent":!0,version:!0,via:!0,"x-att-deviceid":!0,"x-chrome-connected":!0,
"x-client-data":!0,"x-client-ip":!0,"x-do-not-track":!0,"x-forwarded-by":!0,"x-forwarded-for":!0,"x-forwarded-host":!0,"x-forwarded-proto":!0,"x-geo":!0,"x-googapps-allowed-domains":!0,"x-origin":!0,"x-proxyuser-ip":!0,"x-real-ip":!0,"x-referer":!0,"x-uidh":!0,"x-user-ip":!0,"x-wap-profile":!0,"":!0};
gh=function(a){if(!_.kd(a))return null;for(var b={},c=0;c<a.length;c++){var d=a[c];if(typeof d==="string"&&d){var e=d.toLowerCase();ch(d,e)&&(b[e]=d)}}for(var f in Xg)Object.prototype.hasOwnProperty.call(Xg,f)&&(a=Xg[f],c=a.toLowerCase(),ch(a,c)&&Object.prototype.hasOwnProperty.call(b,c)&&(b[c]=a));return b};hh=new RegExp("("+/[\t -~\u00A0-\u2027\u202A-\uD7FF\uE000-\uFFFF]/.source+"|"+/[\uD800-\uDBFF][\uDC00-\uDFFF]/.source+"){1,100}","g");ih=/[ \t]*(\r?\n[ \t]+)+/g;jh=/^[ \t]+|[ \t]+$/g;
kh=function(a,b){if(!b&&typeof a==="object"&&a&&typeof a.length==="number"){b=a;a="";for(var c=b.length,d=0;d<c;++d){var e=kh(b[d],!0);e&&(a&&(e=a+", "+e),a=e)}}if(typeof a==="string"&&(a=a.replace(ih," "),a=a.replace(jh,""),a.replace(hh,"")==""&&a))return a};lh=/^[-0-9A-Za-z!#\$%&'\*\+\.\^_`\|~]+$/g;
_.mh=function(a){if(typeof a!=="string"||!a||!a.match(lh))return null;a=a.toLowerCase();if(bh==null){var b=[],c=_.Xe("googleapis/headers/response");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Xe("client/headers/response"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Zg);(c=_.Xe("googleapis/headers/request"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Xe("client/headers/request"))&&
typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Yg);for(var d in Xg)Object.prototype.hasOwnProperty.call(Xg,d)&&b.push(Xg[d]);bh=gh(b)}return bh!=null&&bh.hasOwnProperty(a)?bh[a]:a};
_.nh=function(a,b){if(!_.mh(a)||!kh(b))return null;a=a.toLowerCase();if(a.match(/^x-google|^x-gfe|^proxy-|^sec-/i)||fh[a])return null;if($g==null){b=[];var c=_.Xe("googleapis/headers/request");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Xe("client/headers/request"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Yg);$g=gh(b)}return $g!=null&&$g.hasOwnProperty(a)?$g[a]:null};
_.oh=function(a,b){if(!_.mh(a)||!kh(b))return null;a=a.toLowerCase();if(eh[a])return null;if(ah==null){b=[];var c=_.Xe("googleapis/headers/response");c&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));(c=_.Xe("client/headers/response"))&&typeof c==="object"&&typeof c.length==="number"||(c=null);c!=null&&(b=b.concat(c));b=b.concat(Zg);ah=gh(b)}return ah!=null&&ah.hasOwnProperty(a)?a:null};
_.ph=function(a,b){if(_.mh(b)&&a!=null&&typeof a==="object"){var c=void 0,d;for(d in a)if(Object.prototype.hasOwnProperty.call(a,d)&&ch(d,b)){var e=kh(a[d]);e&&(c!==void 0&&(e=c+", "+e),c=e)}return c}};_.qh=function(a,b,c,d){var e=_.mh(b);if(e){c&&(c=kh(c));b=b.toLowerCase();for(var f in a)Object.prototype.hasOwnProperty.call(a,f)&&ch(f,b)&&delete a[f];c&&(d||(b=e),a[b]=c)}};
_.rh=function(a,b){var c={};if(!a)return c;a=a.split("\r\n");for(var d=a.length,e=0;e<d;++e){var f=a[e];if(!f)break;var h=f.indexOf(":");if(!(h<=0)){var k=f.substring(0,h);if(k=_.mh(k)){for(f=f.substring(h+1);e+1<d&&a[e+1].match(/^[ \t]/);)f+="\r\n"+a[e+1],++e;if(f=kh(f))if(k=_.oh(k,f)||(b?void 0:k))k=k.toLowerCase(),h=_.ph(c,k),h!==void 0&&(f=h+", "+f),_.qh(c,k,f,!0)}}}return c};
/\uffff/.test("\uffff");
var wy;_.vy=function(a){var b=0,c;for(c in a)b++;return b};wy=function(a,b){var c=[];for(b=b||0;b<a.length;b+=2)_.Uh(a[b],a[b+1],c);return c.join("&")};_.xy=function(a,b){var c=arguments.length==2?wy(arguments[1],0):wy(arguments,1);return _.Th(a,c)};_.yy=function(a,b){_.Mj(a,"/")&&(a=a.slice(0,-1));_.wc(b,"/")&&(b=b.slice(1));return a+"/"+b};_.zy=function(a){switch(a){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:return!0;default:return!1}};var By,Cy,Dy;_.Ay=function(a){_.Oj.call(this);this.headers=new Map;this.h2=a||null;this.Xf=!1;this.Va=null;this.gB="";this.kr=0;this.Bo=this.aI=this.HA=this.ZF=!1;this.Gs=0;this.Qc=null;this.Sm="";this.xh=!1;this.LE=this.CN=null};_.eb(_.Ay,_.Oj);_.Ay.prototype.Ab=null;By=/^https?$/i;Cy=["POST","PUT"];Dy=[];_.Ey=function(a,b,c,d,e,f,h){var k=new _.Ay;Dy.push(k);b&&k.na("complete",b);k.rr("ready",k.N7);f&&k.lD(f);h&&(k.xh=h);k.send(a,c,d,e)};_.g=_.Ay.prototype;
_.g.N7=function(){this.dispose();_.gj(Dy,this)};_.g.lD=function(a){this.Gs=Math.max(0,a)};_.g.setTrustToken=function(a){this.CN=a};_.g.setAttributionReporting=function(a){this.LE=a};
_.g.send=function(a,b,c,d){if(this.Va)throw Error("ta`"+this.gB+"`"+a);b=b?b.toUpperCase():"GET";this.gB=a;this.kr=0;this.ZF=!1;this.Xf=!0;this.Va=this.h2?this.h2.Oy():_.ty.Oy();this.Va.onreadystatechange=(0,_.ok)((0,_.z)(this.OX,this));try{this.aI=!0,this.Va.open(b,String(a),!0),this.aI=!1}catch(h){this.lz(5,h);return}a=c||"";c=new Map(this.headers);if(d)if(Object.getPrototypeOf(d)===Object.prototype)for(var e in d)c.set(e,d[e]);else if(typeof d.keys==="function"&&typeof d.get==="function"){e=_.Aa(d.keys());
for(var f=e.next();!f.done;f=e.next())f=f.value,c.set(f,d.get(f))}else throw Error("ua`"+String(d));d=Array.from(c.keys()).find(function(h){return"content-type"==h.toLowerCase()});e=_.Xa.FormData&&a instanceof _.Xa.FormData;!_.tb(Cy,b)||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=_.Aa(c);for(d=b.next();!d.done;d=b.next())c=_.Aa(d.value),d=c.next().value,c=c.next().value,this.Va.setRequestHeader(d,c);this.Sm&&(this.Va.responseType=this.Sm);"withCredentials"in this.Va&&
this.Va.withCredentials!==this.xh&&(this.Va.withCredentials=this.xh);if("setTrustToken"in this.Va&&this.CN)try{this.Va.setTrustToken(this.CN)}catch(h){}if("setAttributionReporting"in this.Va&&this.LE)try{this.Va.setAttributionReporting(this.LE)}catch(h){}try{this.Qc&&(clearTimeout(this.Qc),this.Qc=null),this.Gs>0&&(this.Qc=setTimeout(this.Ii.bind(this),this.Gs)),this.HA=!0,this.Va.send(a),this.HA=!1}catch(h){this.lz(5,h)}};
_.g.Ii=function(){typeof _.Va!="undefined"&&this.Va&&(this.kr=8,this.dispatchEvent("timeout"),this.abort(8))};_.g.lz=function(a){this.Xf=!1;this.Va&&(this.Bo=!0,this.Va.abort(),this.Bo=!1);this.kr=a;Fy(this);Gy(this)};var Fy=function(a){a.ZF||(a.ZF=!0,a.dispatchEvent("complete"),a.dispatchEvent("error"))};_.Ay.prototype.abort=function(a){this.Va&&this.Xf&&(this.Xf=!1,this.Bo=!0,this.Va.abort(),this.Bo=!1,this.kr=a||7,this.dispatchEvent("complete"),this.dispatchEvent("abort"),Gy(this))};
_.Ay.prototype.ua=function(){this.Va&&(this.Xf&&(this.Xf=!1,this.Bo=!0,this.Va.abort(),this.Bo=!1),Gy(this,!0));_.Ay.N.ua.call(this)};_.Ay.prototype.OX=function(){this.isDisposed()||(this.aI||this.HA||this.Bo?Hy(this):this.DJ())};_.Ay.prototype.DJ=function(){Hy(this)};
var Hy=function(a){if(a.Xf&&typeof _.Va!="undefined")if(a.HA&&_.Iy(a)==4)setTimeout(a.OX.bind(a),0);else if(a.dispatchEvent("readystatechange"),_.Iy(a)==4){a.Xf=!1;try{a.jr()?(a.dispatchEvent("complete"),a.dispatchEvent("success")):(a.kr=6,a.getStatus(),Fy(a))}finally{Gy(a)}}},Gy=function(a,b){if(a.Va){a.Qc&&(clearTimeout(a.Qc),a.Qc=null);var c=a.Va;a.Va=null;b||a.dispatchEvent("ready");try{c.onreadystatechange=null}catch(d){}}};_.Ay.prototype.isActive=function(){return!!this.Va};
_.Ay.prototype.jr=function(){var a=this.getStatus(),b;if(!(b=_.zy(a))){if(a=a===0)a=String(this.gB).match(_.Sh)[1]||null,!a&&_.Xa.self&&_.Xa.self.location&&(a=_.Xa.self.location.protocol.slice(0,-1)),a=!By.test(a?a.toLowerCase():"");b=a}return b};_.Iy=function(a){return a.Va?a.Va.readyState:0};_.Ay.prototype.getStatus=function(){try{return _.Iy(this)>2?this.Va.status:-1}catch(a){return-1}};_.Jy=function(a){try{return a.Va?a.Va.responseText:""}catch(b){return""}};
_.Ky=function(a){try{if(!a.Va)return null;if("response"in a.Va)return a.Va.response;switch(a.Sm){case "":case "text":return a.Va.responseText;case "arraybuffer":if("mozResponseArrayBuffer"in a.Va)return a.Va.mozResponseArrayBuffer}return null}catch(b){return null}};_.Ay.prototype.getResponseHeader=function(a){if(this.Va&&_.Iy(this)==4)return a=this.Va.getResponseHeader(a),a===null?void 0:a};
_.Ay.prototype.getAllResponseHeaders=function(){return this.Va&&_.Iy(this)>=2?this.Va.getAllResponseHeaders()||"":""};_.mj(function(a){_.Ay.prototype.DJ=a(_.Ay.prototype.DJ)});
var vu,Au;_.ru=function(a,b){var c=_.kd(b),d=c?b:arguments;for(c=c?0:1;c<d.length;c++){if(a==null)return;a=a[d[c]]}return a};
_.su=function(a){if(!a||typeof a!=="object")return a;if(typeof a.clone==="function")return a.clone();if(typeof Map!=="undefined"&&a instanceof Map)return new Map(a);if(typeof Set!=="undefined"&&a instanceof Set)return new Set(a);if(a instanceof Date)return new Date(a.getTime());var b=Array.isArray(a)?[]:typeof ArrayBuffer!=="function"||typeof ArrayBuffer.isView!=="function"||!ArrayBuffer.isView(a)||a instanceof DataView?{}:new a.constructor(a.length),c;for(c in a)b[c]=_.su(a[c]);return b};
_.tu=function(){return Math.floor(Math.random()*2147483648).toString(36)+Math.abs(Math.floor(Math.random()*2147483648)^_.ld()).toString(36)};_.uu=function(a,b,c){return _.le(document,arguments)};vu=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(d>=0){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};
_.wu=function(a,b,c){for(var d=0,e=b.length;(d=a.indexOf(b,d))>=0&&d<c;){var f=a.charCodeAt(d-1);if(f==38||f==63)if(f=a.charCodeAt(d+e),!f||f==61||f==38||f==35)return d;d+=e+1}return-1};_.xu=/#|$/;_.yu=function(a){if(a.Ye&&typeof a.Ye=="function")return a.Ye();if(typeof Map!=="undefined"&&a instanceof Map||typeof Set!=="undefined"&&a instanceof Set)return Array.from(a.values());if(typeof a==="string")return a.split("");if(_.kd(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}return _.ub(a)};
_.zu=function(a){if(a.lg&&typeof a.lg=="function")return a.lg();if(!a.Ye||typeof a.Ye!="function"){if(typeof Map!=="undefined"&&a instanceof Map)return Array.from(a.keys());if(!(typeof Set!=="undefined"&&a instanceof Set)){if(_.kd(a)||typeof a==="string"){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}return _.Ph(a)}}};
Au=function(a,b,c){if(a.forEach&&typeof a.forEach=="function")a.forEach(b,c);else if(_.kd(a)||typeof a==="string")Array.prototype.forEach.call(a,b,c);else for(var d=_.zu(a),e=_.yu(a),f=e.length,h=0;h<f;h++)b.call(c,e[h],d&&d[h],a)};var Ou,Iu,Su,Ku,Ju,Mu,Lu,Pu,Nu,Tu;
_.Bu=function(a,b){this.Xd=this.wh=this.Ci="";this.Bg=null;this.uG=this.Mm="";this.Tg=!1;var c;a instanceof _.Bu?(this.Tg=b!==void 0?b:a.Tg,_.Cu(this,a.Ci),_.Du(this,a.wh),_.Eu(this,a.Ng()),_.Fu(this,a.Bg),this.setPath(a.getPath()),_.Gu(this,a.Qd.clone()),this.Uk(a.Mz())):a&&(c=String(a).match(_.Sh))?(this.Tg=!!b,_.Cu(this,c[1]||"",!0),_.Du(this,c[2]||"",!0),_.Eu(this,c[3]||"",!0),_.Fu(this,c[4]),this.setPath(c[5]||"",!0),_.Gu(this,c[6]||"",!0),this.Uk(c[7]||"",!0)):(this.Tg=!!b,this.Qd=new _.Hu(null,
this.Tg))};_.Bu.prototype.toString=function(){var a=[],b=this.Ci;b&&a.push(Iu(b,Ju,!0),":");var c=this.Ng();if(c||b=="file")a.push("//"),(b=this.wh)&&a.push(Iu(b,Ju,!0),"@"),a.push(Ku(encodeURIComponent(String(c)))),c=this.Bg,c!=null&&a.push(":",String(c));if(c=this.getPath())this.Xd&&c.charAt(0)!="/"&&a.push("/"),a.push(Iu(c,c.charAt(0)=="/"?Lu:Mu,!0));(c=this.Qd.toString())&&a.push("?",c);(c=this.Mz())&&a.push("#",Iu(c,Nu));return a.join("")};
_.Bu.prototype.resolve=function(a){var b=this.clone(),c=!!a.Ci;c?_.Cu(b,a.Ci):c=!!a.wh;c?_.Du(b,a.wh):c=!!a.Xd;c?_.Eu(b,a.Ng()):c=a.Bg!=null;var d=a.getPath();if(c)_.Fu(b,a.Bg);else if(c=!!a.Mm){if(d.charAt(0)!="/")if(this.Xd&&!this.Mm)d="/"+d;else{var e=b.getPath().lastIndexOf("/");e!=-1&&(d=b.getPath().slice(0,e+1)+d)}e=d;if(e==".."||e==".")d="";else if(_.yc(e,"./")||_.yc(e,"/.")){d=_.wc(e,"/");e=e.split("/");for(var f=[],h=0;h<e.length;){var k=e[h++];k=="."?d&&h==e.length&&f.push(""):k==".."?((f.length>
1||f.length==1&&f[0]!="")&&f.pop(),d&&h==e.length&&f.push("")):(f.push(k),d=!0)}d=f.join("/")}else d=e}c?b.setPath(d):c=a.Uq();c?_.Gu(b,a.Qd.clone()):c=!!a.uG;c&&b.Uk(a.Mz());return b};_.Bu.prototype.clone=function(){return new _.Bu(this)};_.Cu=function(a,b,c){a.Ci=c?Ou(b,!0):b;a.Ci&&(a.Ci=a.Ci.replace(/:$/,""));return a};_.Du=function(a,b,c){a.wh=c?Ou(b):b;return a};_.Bu.prototype.Ng=function(){return this.Xd};_.Eu=function(a,b,c){a.Xd=c?Ou(b,!0):b;return a};
_.Fu=function(a,b){if(b){b=Number(b);if(isNaN(b)||b<0)throw Error("L`"+b);a.Bg=b}else a.Bg=null;return a};_.Bu.prototype.getPath=function(){return this.Mm};_.Bu.prototype.setPath=function(a,b){this.Mm=b?Ou(a,!0):a;return this};_.Bu.prototype.Uq=function(){return this.Qd.toString()!==""};_.Gu=function(a,b,c){b instanceof _.Hu?(a.Qd=b,a.Qd.LL(a.Tg)):(c||(b=Iu(b,Pu)),a.Qd=new _.Hu(b,a.Tg));return a};_.Bu.prototype.hb=function(a,b){return _.Gu(this,a,b)};_.Bu.prototype.getQuery=function(){return this.Qd.toString()};
_.Qu=function(a,b,c){a.Qd.set(b,c);return a};_.g=_.Bu.prototype;_.g.Pg=function(a){return this.Qd.get(a)};_.g.Mz=function(){return this.uG};_.g.Uk=function(a,b){this.uG=b?Ou(a):a;return this};_.g.removeParameter=function(a){this.Qd.remove(a);return this};_.g.LL=function(a){this.Tg=a;this.Qd&&this.Qd.LL(a)};_.Ru=function(a,b){return a instanceof _.Bu?a.clone():new _.Bu(a,b)};Ou=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""};
Iu=function(a,b,c){return typeof a==="string"?(a=encodeURI(a).replace(b,Su),c&&(a=Ku(a)),a):null};Su=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)};Ku=function(a){return a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")};Ju=/[#\/\?@]/g;Mu=/[#\?:]/g;Lu=/[#\?]/g;Pu=/[#\?@]/g;Nu=/#/g;_.Hu=function(a,b){this.Ae=this.Lc=null;this.jg=a||null;this.Tg=!!b};Tu=function(a){a.Lc||(a.Lc=new Map,a.Ae=0,a.jg&&vu(a.jg,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))};
_.g=_.Hu.prototype;_.g.Zb=function(){Tu(this);return this.Ae};_.g.add=function(a,b){Tu(this);this.jg=null;a=Uu(this,a);var c=this.Lc.get(a);c||this.Lc.set(a,c=[]);c.push(b);this.Ae+=1;return this};_.g.remove=function(a){Tu(this);a=Uu(this,a);return this.Lc.has(a)?(this.jg=null,this.Ae-=this.Lc.get(a).length,this.Lc.delete(a)):!1};_.g.clear=function(){this.Lc=this.jg=null;this.Ae=0};_.g.isEmpty=function(){Tu(this);return this.Ae==0};_.g.Fl=function(a){Tu(this);a=Uu(this,a);return this.Lc.has(a)};
_.g.forEach=function(a,b){Tu(this);this.Lc.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};_.g.lg=function(){Tu(this);for(var a=Array.from(this.Lc.values()),b=Array.from(this.Lc.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};_.g.Ye=function(a){Tu(this);var b=[];if(typeof a==="string")this.Fl(a)&&(b=b.concat(this.Lc.get(Uu(this,a))));else{a=Array.from(this.Lc.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};
_.g.set=function(a,b){Tu(this);this.jg=null;a=Uu(this,a);this.Fl(a)&&(this.Ae-=this.Lc.get(a).length);this.Lc.set(a,[b]);this.Ae+=1;return this};_.g.get=function(a,b){if(!a)return b;a=this.Ye(a);return a.length>0?String(a[0]):b};_.g.setValues=function(a,b){this.remove(a);b.length>0&&(this.jg=null,this.Lc.set(Uu(this,a),_.Yb(b)),this.Ae+=b.length)};
_.g.toString=function(){if(this.jg)return this.jg;if(!this.Lc)return"";for(var a=[],b=Array.from(this.Lc.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.Ye(d);for(var f=0;f<d.length;f++){var h=e;d[f]!==""&&(h+="="+encodeURIComponent(String(d[f])));a.push(h)}}return this.jg=a.join("&")};_.g.clone=function(){var a=new _.Hu;a.jg=this.jg;this.Lc&&(a.Lc=new Map(this.Lc),a.Ae=this.Ae);return a};var Uu=function(a,b){b=String(b);a.Tg&&(b=b.toLowerCase());return b};
_.Hu.prototype.LL=function(a){a&&!this.Tg&&(Tu(this),this.jg=null,this.Lc.forEach(function(b,c){var d=c.toLowerCase();c!=d&&(this.remove(c),this.setValues(d,b))},this));this.Tg=a};_.Hu.prototype.extend=function(a){for(var b=0;b<arguments.length;b++)Au(arguments[b],function(c,d){this.add(d,c)},this)};
var XA=function(a){if(!a||typeof a!=="function")throw new WA("Must provide a function.");this.Cg=null;this.X8=a},YA=!1,jB,kB,lB,mB,nB,oB,pB,qB,rB,sB,tB,uB,vB,wB,xB;YA=!1;
var ZA=function(a){return new _.xk(function(b){var c=a.length,d=[];if(c)for(var e=function(k,l,m){c--;d[k]=l?{xz:!0,value:m}:{xz:!1,reason:m};c==0&&b(d)},f,h=0;h<a.length;h++)f=a[h],_.Ek(f,_.bb(e,h,!0),_.bb(e,h,!1));else b(d)})},$A,aB,bB,cB={JP:function(a){$A=a;try{delete cB.JP}catch(b){}},KP:function(a){aB=a;try{delete cB.KP}catch(b){}},LP:function(a){bB=a;try{delete cB.LP}catch(b){}}},dB=function(a){return _.zy(a.status)},eB=function(){var a=!0,b=_.ty.Oy();b&&b.withCredentials!==void 0||(a=!1);
return a},fB=function(a,b){if(b==null)return b;b=String(b);b.match(/^\/\/.*/)&&(b=(window.location.protocol=="http:"?"http:":"https:")+b);b.match(/^\/([^\/].*)?$/)&&window.location.host&&String(window.location.protocol).match(/^https?:$/)&&(b=window.location.protocol+"//"+window.location.host+b);var c=b.match(/^(https?:)(\/\/)?(\/([^\/].*)?)?$/i);c&&window.location.host&&String(window.location.protocol).match(/^https?:$/)&&(b=c[1]+"//"+window.location.host+(c[3]||""));b=b.replace(/^(https?:\/\/[^\/?#@]*)\/$/i,
"$1");b=b.replace(/^(http:\/\/[-_a-z0-9.]+):0*80([\/?#].*)?$/i,"$1$2");b=b.replace(/^(https:\/\/[-_a-z0-9.]+):0*443([\/?#].*)?$/i,"$1$2");b.match(/^https?:\/\/[-_a-z0-9.]*[-_a-z][-_a-z0-9.]*$/i)&&(b=b.toLowerCase());c=_.Xe("client/rewrite");_.vb(c)&&Object.prototype.hasOwnProperty.call(c,b)?b=String(c[b]||b):(b=b.replace(/^(https?):\/\/www\.googleapis\.com$/,"$1://content.googleapis.com"),b=b.replace(/^(https?):\/\/www-(googleapis-[-_a-z0-9]+\.[-_a-z0-9]+\.google\.com)$/,"$1://content-$2"),b.match(/^https?:\/\/content(-[-_a-z0-9.]+)?\.googleapis\.com$/)||
(b=b.replace(/^(https?):\/\/([-_a-z0-9]+(\.[-_a-z0-9]+)?\.googleapis\.com)$/,"$1://content-$2")));a&&(a=_.Xe("client/firstPartyRewrite"),_.vb(a)&&Object.prototype.hasOwnProperty.call(a,b)?b=String(a[b]||b):(b=b.replace(/^(https?):\/\/content\.googleapis\.com$/,"$1://clients6.google.com"),b=b.replace(/^(https?):\/\/content-([-a-z0-9]+)\.([-a-z0-9]+)\.googleapis\.com$/,"$1://$2-googleapis.$3.google.com"),b=b.replace(/^(https?):\/\/content-([-a-z0-9]+)\.googleapis\.com$/,"$1://$2.clients6.google.com"),
b=b.replace(/^(https?):\/\/([-a-z0-9]+)-www-googleapis\.([-a-z0-9]+).google.com$/,"$1://content-googleapis-$2.$3.google.com")));return b},WA=function(a){_.lb.call(this,a)};_.y(WA,_.lb);WA.prototype.name="gapix.client.GapiClientError";XA.prototype.then=function(a,b,c){this.Cg||(this.Cg=this.X8());return this.Cg.then(a,b,c)};XA.prototype.cD=function(a){this.Cg||(this.Cg=a)};
var gB=function(a){var b={},c;for(c in a)if(Object.prototype.hasOwnProperty.call(a,c)){var d=_.ph(a,c);d&&(c=_.oh(c,d))&&_.qh(b,c,d,!0)}return b},hB={error:{code:-1,message:"A network error occurred and the request could not be completed."}},iB=function(a,b,c,d){_.Ay.call(this);this.Hd=a;this.dJ=b;this.Kd=c;a={};if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(b=_.ph(d,e),b!==void 0&&(e=_.nh(e,b))&&_.qh(a,e,b));d={};for(var f in a)Object.prototype.hasOwnProperty.call(a,f)&&(d[unescape(encodeURIComponent(f))]=
unescape(encodeURIComponent(a[f])));this.Xu=d;this.Cg=null};_.y(iB,_.Ay);
iB.prototype.then=function(a){this.Cg||(this.Cg=(new _.xk(function(b,c){this.na("error",(0,_.z)(function(){c(jB(this))},this));this.na("success",(0,_.z)(function(){b(jB(this))},this));this.send(this.Hd,this.dJ,this.Kd,this.Xu)},this)).then(function(b){b.headers=gB(b.headers);return b},function(b){return b.status?(b.headers=gB(b.headers),_.Ck(b)):_.Ck({result:hB,body:'{"error":{"code":-1,"message":"A network error occurred and the request could not be completed."}}',headers:null,status:null,statusText:null})}));
return this.Cg.then.apply(this.Cg,arguments)};jB=function(a){var b=a.getStatus(),c=_.Jy(a);var d=b==204?!1:a.Sm==""?_.Qf(c):_.Ky(a);var e=a.getAllResponseHeaders();e=_.rh(e,!1);try{var f=_.Iy(a)>2?a.Va.statusText:""}catch(h){f=""}return{result:d,body:c,headers:e,status:b,statusText:f}};kB=/;\s*charset\s*=\s*("utf-?8"|utf-?8)\s*(;|$)/i;lB=/^(text\/[^\s;\/""]+|application\/(json(\+[^\s;\/""]*)?|([^\s;\/""]*\+)?xml))\s*(;|$)/i;mB=/;\s*charset\s*=/i;nB=/(([\r\n]{0,2}[A-Za-z0-9+\/]){4,4}){0,1024}([\r\n]{0,2}[A-Za-z0-9+\/][\r\n]{0,2}[AQgw]([\r\n]{0,2}=){2,2}|([\r\n]{0,2}[A-Za-z0-9+\/]){2,2}[\r\n]{0,2}[AEIMQUYcgkosw048][\r\n]{0,2}=|([\r\n]{0,2}[A-Za-z0-9+\/]){4,4})[\r\n]{0,2}/g;
oB=function(a){var b=[];a=a.replace(nB,function(c){b.push(_.Az(c));return""});if(a.length)throw Error("va");return b.join("")};pB=function(a){var b=a.headers;if(b&&_.ph(b,"X-Goog-Safety-Encoding")==="base64"){var c=oB(a.body),d=_.ph(b,"X-Goog-Safety-Content-Type");b["Content-Type"]=d;if(d.match(kB)||d.match(lB)&&!d.match(mB))c=_.Ih(c),c=_.Fw(c);_.qh(b,"X-Goog-Safety-Encoding");_.qh(b,"X-Goog-Safety-Content-Type");a.body=c}};
qB=function(a,b,c){c||((c=_.Xe("googleapis.config/proxy"))&&(c=String(c).replace(/\/static\/proxy\.html$/,"")||"/"),c=String(c||""));c||(c=_.Xe("googleapis.config/root"),b&&(c=_.Xe("googleapis.config/root-1p")||c),c=String(c||""));c=String(fB(b,c)||c);return a=_.yy(c,a)};
rB=function(a,b){var c=a.params||_.Ce();c.url=c.path;var d=c.root;d=qB("/",_.Ti(c.headers),d);d.match(/^(.*[^\/])?\/$/)&&(d=d.substr(0,d.length-1));c.root=d;a.params=c;_.Zi.Xm("makeHttpRequests",[a],function(e,f){e&&e.gapiRequest?(e.gapiRequest.data?pB(e.gapiRequest.data):pB(e),b(e,_.Rf(e))):b(e,f)})};
sB=function(a){var b=_.ru(a,"params","headers");b&&typeof b==="object"||(b={});a={};for(var c in b)if(Object.prototype.hasOwnProperty.call(b,c)){var d=_.ph(b,c);d&&(_.nh(c,d),_.qh(a,c,d))}c=(window.location.href.match(_.Sh)[1]||null)=="chrome-extension";a=_.Ti(a);return!(c&&a)&&eB()};
tB=function(a){return new _.xk(function(b,c){var d=function(e){e&&e.gapiRequest?e=e.gapiRequest.data||e:c(e);e={result:e.status!=204&&_.Qf(e.body),body:e.body,headers:e.headers||null,status:e.status||null,statusText:e.statusText||null};dB(e)?b(e):c(e)};try{rB(a,d)}catch(e){c(e)}})};uB=function(a){var b=!_.Xe("client/cors")||!!_.Xe("client/xd4"),c={};_.Qm(a,function(d,e){(d=_.nh(e,d))||b||(d=_.mh(e));d&&(e=_.ph(a,d))&&_.qh(c,d,e)});return c};
vB=function(a){var b=a.params||_.Ce();a=_.fk(b.headers||{});var c=b.httpMethod||"GET",d=String(b.url||""),e=encodeURIComponent("$unique");if(!(c==="POST"||_.wu(d,"$unique",d.search(_.xu))>=0||_.wu(d,e,d.search(_.xu))>=0)){var f=[];for(h in a)Object.prototype.hasOwnProperty.call(a,h)&&f.push(h.toLowerCase());f.sort();f.push(_.Ig(location.href));var h=f.join(":");f=_.mi();f.update(h);h=f.Si().toLowerCase().substr(0,7);h=String(parseInt(h,16)%1E3+1E3).substr(1);d=_.xy(d,e,"gc"+h)}e=b.body||null;h=b.responseType||
null;b=_.Ti(a)||b.authType=="1p";f=!!_.Xe("googleapis.config/auth/useUberProxyAuth")||!!_.Xe("client/withCredentials");_.qh(a,"X-Referer");a=uB(a);var k=new iB(d,c,e,a);k.xh=b||f;h&&(k.Sm=h);return new _.xk(function(l,m){k.then(function(n){pB(n);l(n)},function(n){m(n)})})};wB=function(a,b){var c=function(d){d=_.fk(d);delete d.result;d={gapiRequest:{data:d}};b&&b(d,_.Rf(d))};vB(a).then(c,c)};
xB=function(a,b){(_.Xe("client/cors")||_.Xe("client/xd4"))&&sB(a)?(_.Hi(_.Gi(),12).rb(),wB(a,b)):(_.Hi(_.Gi(),11).rb(),rB(a,b))};_.yB={};var zB=function(a){this.kw=a;this.Xf=!1;this.promise={then:(0,_.z)(function(b,c,d){this.Xf||(this.Xf=!0);this.jw&&!this.hw?this.kw.resolve(this.jw):this.hw&&!this.jw&&this.kw.reject(this.hw);return this.kw.promise.then(b,c,d)},this)}};zB.prototype.resolve=function(a){this.Xf?this.kw.resolve(a):this.jw||this.hw||(this.jw=a)};zB.prototype.reject=function(a){this.Xf?this.kw.reject(a):this.jw||this.hw||(this.hw=a)};var AB=function(a){a=_.su(a.error);return{code:a.code,data:a.errors,message:a.message}},BB=function(a){throw Error("Aa`"+a);};var CB=function(a){XA.call(this,CB.prototype.Yo);if(!a||typeof a!="object"&&typeof a!="string")throw new WA("Missing required parameters");if(typeof a==="string"){var b={};b.path=a}else b=a;if(!b.path)throw new WA('Missing required parameter: "path"');this.jh={};this.jh.path=b.path;this.jh.method=b.method||"GET";this.jh.params=b.params||{};this.jh.headers=b.headers||{};this.jh.body=b.body;this.jh.root=b.root;this.jh.responseType=b.responseType;this.jh.apiId=b.apiId;this.un=b.authType||"auto";this.vca=
!!b.isXd4;this.RV=!1;this.Jj(this.un);this.uZ=!1};_.y(CB,XA);CB.prototype.Gf=function(){return this.jh};CB.prototype.Jj=function(a){this.un=a;this.RV=this.un==="1p"};CB.prototype.Fq=function(){return this.RV};
CB.prototype.Ej=function(){if(!this.uZ){this.uZ=!0;var a=this.jh,b=a.headers=a.headers||{},c=[],d=[];for(h in b)if(Object.prototype.hasOwnProperty.call(b,h)){c.push(h);var e=h,f=_.ph(b,e);f&&(e=_.nh(e,f)||_.mh(e))&&d.push([e,f])}var h=0;for(e=c.length;h<e;++h)delete b[c[h]];c=0;for(h=d.length;c<h;++c)_.qh(b,d[c][0],d[c][1]);if(this.vca)d=this.un=="1p";else{d=b;c=String(_.Xe("client/version","1.1.0"));h=String(_.Xe("client/name","google-api-javascript-client"));h=DB[h]===!0?h:"google-api-javascript-client";
e=String(_.Xe("client/appName",""));f=[];e&&(f.push(e),f.push(" "));f.push(h);c&&(f.push("/"),f.push(c));_.qh(d,"X-JavaScript-User-Agent",f.join(""));_.qh(b,"X-Requested-With","XMLHttpRequest");d=_.ph(b,"Content-Type");a.body&&!d&&_.qh(b,"Content-Type","application/json");_.Xe("client/allowExecutableResponse")||_.qh(b,"X-Goog-Encode-Response-If-Executable","base64");(d=_.ph(b,"Content-Type"))&&d.toLowerCase()=="application/json"&&!a.params.alt&&(a.params.alt="json");(d=a.body||null)&&_.vb(d)&&(a.body=
_.Rf(d));a.key=a.id;b=_.Yi(b,void 0,this.un);d=_.Ti(b);if((c=b)&&window.navigator){h=[];for(e=0;e<EB.length;e++)(f=window.navigator[EB[e]])&&h.push(encodeURIComponent(EB[e])+"="+encodeURIComponent(f));_.qh(c,"X-ClientDetails",h.join("&"))}(c=_.Xe("client/apiKey"))&&a.params.key===void 0&&(a.params.key=c);(c=_.Xe("client/trace"))&&!a.params.trace&&(a.params.trace=c)}this.un=="auto"&&(d?this.Jj("1p"):(b=_.ph(b,"Authorization"))&&String(b).match(/^(Bearer|MAC)[ \t]/i)?this.Jj("oauth2"):this.Jj("none"));
if((b=String(a.path||"").match(/^(https?:\/\/[^\/?#]+)([\/?#].*)?$/i))&&!a.root)if(a.root=String(b[1]),a.path=String(b[2]||"/"),a.path.match(/^\/_ah\/api(\/.*)?$/))a.root+="/_ah/api",a.path=a.path.substr(8);else{b=_.Xe("googleapis.config/root");d&&(b=_.Xe("googleapis.config/root-1p")||b);b=String(b||"");c=a.root+a.path;if(h=b&&c.substr(0,b.length)===b)h=_.Ru(b),e=_.Ru(c),h=(!h.Xd&&!e.Xd||h.Ng()==e.Ng())&&(h.Bg==null&&e.Bg==null||h.Bg==e.Bg);h&&(a.path=c.substr(b.length),a.root=b)}b=a.params;c=_.dh(a.path);
h=String(_.Xe("googleapis.config/xd3")||"");h.length>=18&&h.substring(h.length-18)=="/static/proxy.html"&&(h=h.substring(0,h.length-18));h||(h="/");e=_.dh(h);if(h!=e)throw Error("x");h.charAt(h.length-1)!="/"&&(h+="/");c=_.yy(h,c);_.Mj(c,"/")&&(c=c.substring(0,c.length-1));h=_.Ce();for(var k in b)Object.prototype.hasOwnProperty.call(b,k)&&(e=encodeURIComponent(k),h[e]=b[k]);c=_.Wh(c,h);a.path=c;a.root=fB(!!d,a.root);a.url=qB(a.path,!!d,a.root)}};
var FB=function(a){a.Ej();var b=a.jh;return{key:"gapiRequest",params:{id:b.id,key:b.key,url:b.url,path:b.path,httpMethod:b.method,body:b.body||"",headers:b.headers||{},urlParams:{},root:b.root,authType:a.un}}};_.g=CB.prototype;_.g.execute=function(a){var b=FB(this);xB(b,function(c,d){var e=c;c.gapiRequest&&(e=c.gapiRequest);e&&e.data&&(e=e.data);c=e;c=c instanceof Array?c[0]:c;if(c.status!=204&&c.body)try{var f=_.Qf(c.body)}catch(h){}a&&a(f,d)})};
_.g.Yo=function(){var a=FB(this);(_.Xe("client/cors")||_.Xe("client/xd4"))&&sB(a)?(_.Hi(_.Gi(),15).rb(),a=vB(a)):(_.Hi(_.Gi(),14).rb(),a=tB(a));return a};_.g.fj=function(){return this.Yo()};_.g.Ce=function(){return this.jh.root};_.g.Iv=function(){console.log("makeJsonRpc is not supported for this request.");return{}};_.g.getFormat=function(){return 0};var EB=["appVersion","platform","userAgent"],DB={"google-api-gwt-client":!0,"google-api-javascript-client":!0};CB.prototype.execute=CB.prototype.execute;
CB.prototype.then=CB.prototype.then;CB.prototype.getPromise=CB.prototype.fj;var GB=function(a){if(!a||typeof a!="object")throw new WA("Missing rpc parameters");if(!a.method)throw new WA("Missing rpc method");this.tC=a};_.g=GB.prototype;_.g.Ce=function(){var a=this.tC.transport;return a?a.root||null:null};_.g.execute=function(a){var b=aB();b.add(this,{id:"gapiRpc",callback:this.Gv(a)});b.execute()};
_.g.Iv=function(a){var b=this.tC.method,c=String,d;(d=this.tC.apiVersion)||(d=String(b).split(".")[0],d=_.Xe("googleapis.config/versions/"+b)||_.Xe("googleapis.config/versions/"+d)||"v1",d=String(d));a={jsonrpc:"2.0",id:a,method:b,apiVersion:c(d)};(b=this.tC.rpcParams)&&(a.params=b);return a};
_.g.Gv=function(a){return function(b,c){if(b)if(b.error){var d=b.error;d.error==null&&(d.error=_.fk(b.error))}else d=b.result||b.data,_.vb(d)&&d.result==null&&(d.result=_.fk(b.result||b.data));else d=!1;a(d,c)}};_.g.then=function(){throw BB('The "then" method is not available on this object.');};_.g.cD=function(){};_.g.Gf=function(){};_.g.Ej=function(){};_.g.Jj=function(){};_.g.Fq=function(){};_.g.fj=function(){};GB.prototype.execute=GB.prototype.execute;var IB=function(a,b){this.Xe=b||0;this.Xe==2?(b=null,a!=null&&_.vb(a)&&(b={},b.method=a.method,b.rpcParams=a.rpcParams,b.transport=a.transport,b.root=a.root,b.apiVersion=a.apiVersion,b.authType=a.authType),this.Rb=new GB(b)):(this.Xe==0&&(b=a&&a.callback)&&(a.callback=HB(b)),b=null,a!=null&&(_.vb(a)?(b={},b.path=a.path,b.method=a.method,b.params=a.params,b.headers=a.headers,b.body=a.body,b.root=a.root,b.responseType=a.responseType,b.authType=a.authType,b.apiId=a.apiId):typeof a==="string"&&(b=a)),
this.Rb=new CB(b))},HB=function(a){return function(b){if(b!=null&&_.vb(b)&&b.error){var c=AB(b);b=_.Rf([{id:"gapiRpc",error:c}]);c.error=_.su(c)}else b==null&&(b={}),c=_.su(b),c.result=_.su(b),b=_.Rf([{id:"gapiRpc",result:b}]);a(c,b)}};_.g=IB.prototype;_.g.getFormat=function(){return this.Xe};_.g.execute=function(a){this.Rb.execute(a&&this.Xe==1?HB(a):a)};_.g.then=function(a,b,c){return this.Rb.then(a,b,c)};_.g.cD=function(a){this.Rb.cD(a)};_.g.Gf=function(){return this.Rb.Gf()};_.g.Ej=function(){this.Rb.Ej()};
_.g.Ce=function(){return this.Rb.Ce()};_.g.Iv=function(a){if(this.Rb.Iv)return this.Rb.Iv(a)};_.g.Jj=function(a){this.Rb.Jj(a)};_.g.Fq=function(){return!!this.Rb.Fq()};_.g.fj=function(){return this.Rb.fj()};IB.prototype.execute=IB.prototype.execute;IB.prototype.then=IB.prototype.then;IB.prototype.getPromise=IB.prototype.fj;var JB=/<response-(.*)>/,KB=/^application\/http(;.+$|$)/,LB=["clients6.google.com","content.googleapis.com","www.googleapis.com"],MB=function(a,b){a=_.ph(a,b);if(!a)throw new WA("Unable to retrieve header.");return a},NB=function(a){var b=void 0;a=_.Aa(a);for(var c=a.next();!c.done;c=a.next()){c=c.value.Gf().apiId;if(typeof c!=="string")return"batch";if(b===void 0)b=c;else if(b!=c)return"batch"}b=_.Xe("client/batchPath/"+b)||"batch/"+b.split(":").join("/");return String(b)},OB=function(a){a=a.map(function(b){return b.request});
return NB(a)},PB=function(a,b){var c=[];a=a.Gf();var d=function(f,h){_.Qm(f,function(k,l){h.push(l+": "+k)})},e={"Content-Type":"application/http","Content-Transfer-Encoding":"binary"};e["Content-ID"]="<"+b+">";d(e,c);c.push("");c.push(a.method+" "+a.path);d(a.headers,c);c.push("");a.body&&c.push(a.body);return c.join("\r\n")},SB=function(a,b){a=QB(a,b);var c={};_.Zb(a,function(d,e){c[e]=RB(d,e)});return c},RB=function(a,b){return{result:a.result||a.body,rawResult:_.Rf({id:b,result:a.result||a.body}),
id:b}},QB=function(a,b){a=_.zc(a);_.Mj(a,"--")&&(a=a.substring(0,a.length-2));a=a.split(b);b=_.Ce();for(var c=0;c<a.length;c++)if(a[c]){var d;if(d=a[c]){_.Mj(d,"\r\n")&&(d=d.substring(0,d.length-2));if(d){d=d.split("\r\n");for(var e=0,f={headers:{},body:""};e<d.length&&d[e]=="";)e++;for(f.outerHeaders=TB(d,e);e<d.length&&d[e]!="";)e++;e++;var h=d[e++].split(" ");f.status=Number(h[1]);f.statusText=h.slice(2).join(" ");for(f.headers=TB(d,e);e<d.length&&d[e]!="";)e++;e++;f.body=d.slice(e).join("\r\n");
pB(f);d=f}else d=null;e=_.Ce();f=MB(d.outerHeaders,"Content-Type");if(KB.exec(f)==null)throw new WA("Unexpected Content-Type <"+f+">");f=MB(d.outerHeaders,"Content-ID");f=JB.exec(f);if(!f)throw new WA("Unable to recognize Content-Id.");e.id=decodeURIComponent(f[1].split("@")[0].replace(/^.*[+]/,""));e.response={status:d.status,statusText:d.statusText,headers:d.headers};d.status!=204&&(e.response.body=d.body,e.response.result=_.Qf(d.body));d=e}else d=null;d&&d.id&&(b[d.id]=d.response)}return b},TB=
function(a,b){for(var c=[];b<a.length&&a[b];b++)c.push(a[b]);return _.rh(c.join("\r\n"),!1)},UB=function(a,b,c){a=a||b;(b=!a)||(b=_.Ru(a).Ci!=="https");if(b&&(a=c?_.Xe("googleapis.config/root-1p"):_.Xe("googleapis.config/root"),!a))return!1;a=fB(c,String(a))||a;return LB.includes(_.Ru(a).Ng())};var VB=function(a){XA.call(this,VB.prototype.Yo);this.qk={};this.my={};this.Qm=[];this.Sd=a;this.Vca=!!a;this.JU=this.wA=!1};_.y(VB,XA);var WB=function(a,b){a=_.Aa(Object.values(a.qk));for(var c=a.next();!c.done;c=a.next())if(c.value.map(function(d){return d.id}).includes(b))return!0;return!1};VB.prototype.Yp=function(a){(function(b){setTimeout(function(){throw b;})})(a)};
VB.prototype.add=function(a,b){var c=b||_.Ce();b=_.Ce();if(!a)throw new WA("Batch entry "+(_.De(c,"id")?'"'+c.id+'" ':"")+"is missing a request method");a.Ej();b.request=a;var d=_.Hk();d=new zB(d);b.mC=d;a.cD(b.mC.promise);d=a.Gf().headers;_.Ti(d)&&(this.wA=!0);(d=String((d||{}).Authorization||"")||null)&&d.match(/^Bearer|MAC[ \t]/i)&&(this.JU=!0);d=a.Gf().root;if(!this.Vca){if(d&&this.Sd&&d!=this.Sd)throw new WA('The "root" provided in this request is not consistent with that of existing requests in the batch.');
this.Sd=d||this.Sd}if(_.De(c,"id")){d=c.id;if(WB(this,d))throw new WA('Batch ID "'+d+'" already in use, please use another.');b.id=d}else{do b.id=String(Math.round(2147483647*_.Pi()));while(WB(this,b.id))}b.callback=c.callback;c="batch";UB(this.Sd,a.Gf().path,this.wA)&&(c=OB([b]));this.qk[c]=this.qk[c]||[];this.qk[c].push(b);this.my[b.id]=b;return b.id};
var XB=function(a){var b=[],c=UB(a.Sd,void 0,a.wA);Object.entries(a.qk).length>1&&_.Vf.warn("Heterogeneous batch requests are deprecated. See https://developers.googleblog.com/2018/03/discontinuing-support-for-json-rpc-and.html");for(var d=_.Aa(Object.entries(a.qk)),e=d.next();!e.done;e=d.next()){e=_.Aa(e.value);var f=e.next().value;e=e.next().value;for(var h=!0,k=_.Aa(e),l=k.next();!l.done;l=k.next())l=l.value,l.request.Ej(),f==="batch"&&c&&(h=!1,l.mca=!0,l.request.Gf.root=a.Sd,b.push(l.request),
a.Qm.push([l]));if(h){var m=e;f=a.Sd;h=a.wA;k=a.JU;l="batch"+String(Math.round(2147483647*_.Pi()))+String(Math.round(2147483647*_.Pi()));var n="--"+l;l="multipart/mixed; boundary="+l;for(var p={path:OB(m),method:"POST"},q=[],r=0;r<m.length;r++)q.push(PB(m[r].request,[n.substr(n.indexOf("--")+2),"+",encodeURIComponent(m[r].id).split("(").join("%28").split(")").join("%29").split(".").join("%2E"),"@googleapis.com"].join("")));p.body=[n,q.join("\r\n"+n+"\r\n"),n+"--"].join("\r\n")+"\r\n";p.root=f||null;
_.Xe("client/xd4")&&eB()?(p.isXd4=!0,p.params={$ct:l},p.headers={},_.qh(p.headers,"Content-Type","text/plain; charset=UTF-8"),h?p.authType="1p":k&&(p.authType="oauth2"),f=new CB(p)):(p.headers={},_.qh(p.headers,"Content-Type",l),f=bB(p));b.push(f);a.Qm.push(e)}}return b};
VB.prototype.execute=function(a){if(!(Object.keys(this.qk).length<1)){var b=this.Gv(a);a=XB(this);var c=[],d=a.map(function(e){return new _.xk(function(f){try{e.execute(function(h,k){return f({FP:h,Bea:k})})}catch(h){c.push(h),f({FP:{xz:!1,reason:h}})}})});if(c.length>0&&c.length===a.length)throw c[0];_.Fk(d).then(function(e){var f=e.map(function(h){return h.Bea});e=e.map(function(h){return h.FP});b(e,f)})}};
VB.prototype.Yo=function(){var a=this;if(Object.keys(this.qk).length<1)return _.Bk({});var b=XB(this).map(function(c){return new _.xk(function(d,e){return c.fj().then(d,e)})});return ZA(b).then(function(c){c=c.map(function(d){return d.xz?d.value:d});return YB(a,c,!0)})};
VB.prototype.nY=function(a,b,c,d){var e={};if(c){e=b?QB:SB;b=MB(a.headers,"Content-Type").split("boundary=")[1];if(!b)throw new WA("Boundary not indicated in response.");e=e(a.body,"--"+b)}else b?(a.result=_.Qf(a.body),e[d]=a):e[d]=RB(a,d);a={};e=_.Aa(Object.entries(e));for(b=e.next();!b.done;b=e.next())if(c=_.Aa(b.value),b=c.next().value,c=c.next().value,a[b]=c,!this.my[b])throw new WA("Could not find batch entry for id "+b+".");return a};
var YB=function(a,b,c,d,e){for(var f=!1,h={},k,l=0,m=0;m<b.length;m++){var n=b[m];if(n&&Object.keys(n).includes("fulfilled")&&n.xz===!1){l++;b[m]=n.reason;n=ZB([b[m]]);for(var p=_.Aa(a.Qm[m]),q=p.next();!q.done;q=p.next())h[q.value.id]=n}else{if(a.Qm[m].length<1)throw new WA("Error processing batch responses.");try{var r=!(a.Qm[m].length===1&&a.Qm[m][0].mca),w=a.Qm[m][0].id;if(!c){p=n;q=r;var u=d[m],x=p;if(u&&(!x||!q)){var A=_.Qf(u);A&&(x=A.gapiRequest?A.gapiRequest.data:A,!q&&p&&(x.body=p))}if(!x)throw new WA("The batch response is missing.");
n=x}p=void 0;if(q=n){var D=q.headers;if(D){var E=_.Ce();for(p in D)if(Object.prototype.hasOwnProperty.call(D,p)){var N=_.ph(D,p);_.qh(E,p,N,!0)}q.headers=E}}if(r&&MB(n.headers,"Content-Type").indexOf("multipart/mixed")!=0)throw new WA("The response's Content-Type is not multipart/mixed.");k=k||_.su(n);var H=dB(n);H&&!dB(k)&&(k.status=n.status,k.statusText=n.statusText);if(H||c||!r)f=!0,h=Object.assign(h,a.nY(n,c,r,w))}catch(R){for(l++,b[m]=R,n=ZB([R]),p=_.Aa(a.Qm[m]),q=p.next();!q.done;q=p.next())h[q.value.id]=
n}}}if(l===b.length){d=ZB(b);h=_.Rf(d);k=0;a=Array.from(Object.values(a.qk)).flat();f=_.Aa(a);for(l=f.next();!l.done;l=f.next())if(l=l.value,c)l.mC.reject(d);else if(l.callback)try{k++,l.callback(d,h)}catch(R){VB.prototype.Yp(R)}if(e)try{e(d,h)}catch(R){VB.prototype.Yp(R)}else if(k!==a.length)throw b.length===1?b[0]:d;}else{if(f)for(f=_.Aa(Object.entries(h)),l=f.next();!l.done;l=f.next())if(l=_.Aa(l.value),m=l.next().value,l=l.next().value,c)m=a.my[m],l&&dB(l)?m.mC.resolve(l):m.mC.reject(l);else if(m=
a.my[m],m.callback){if(l&&l.rawResult)try{delete l.rawResult}catch(R){}try{m.callback(l||!1,_.Rf(l))}catch(R){VB.prototype.Yp(R)}}k.result=h||{};k.body=b.length===1?k.body:"";if(e)try{e(h||null,d.length===1?d[0]:null)}catch(R){VB.prototype.Yp(R)}return k}},ZB=function(a){var b={error:{code:0,message:"The batch request could not be fulfilled.  "}};a=_.Aa(a);for(var c=a.next();!c.done;c=a.next())(c=c.value)&&c.message||c instanceof Error&&c.message?b.error.message+=(c.message||c instanceof Error&&c.message)+
"  ":c&&c.error&&c.error.message&&(b.error.message+=c.error.message+"  ",b.error.code=c.error.code||b.error.code||0);b.error.message=b.error.message.trim();return{result:b,body:_.Rf(b),headers:null,status:null,statusText:null}};VB.prototype.Gv=function(a){var b=this;return function(c,d){b.QE(c,d,a)}};VB.prototype.QE=function(a,b,c){YB(this,a,!1,b,c)};VB.prototype.add=VB.prototype.add;VB.prototype.execute=VB.prototype.execute;VB.prototype.then=VB.prototype.then;var $B=function(){this.Nl=[];this.Sd=this.nf=null};
$B.prototype.add=function(a,b){b=b||{};var c={},d=Object.prototype.hasOwnProperty;if(a)c.kp=a;else throw new WA("Batch entry "+(d.call(b,"id")?'"'+b.id+'" ':"")+"is missing a request method");if(d.call(b,"id")){a=b.id;for(d=0;d<this.Nl.length;d++)if(this.Nl[d].id==a)throw new WA('Batch ID "'+a+'" already in use, please use another.');c.id=a}else{do c.id=String(2147483647*_.Pi()|0);while(d.call(this.Nl,c.id))}c.callback=b.callback;this.Nl.push(c);return c.id};
var aC=function(a){return function(b){var c=b.body;if(b=b.result){for(var d={},e=b.length,f=0;f<e;++f)d[b[f].id]=b[f];a(d,c)}else a(b,c)}};
$B.prototype.execute=function(a){this.nf=[];for(var b,c,d=0;d<this.Nl.length;d++)b=this.Nl[d],c=b.kp,this.nf.push(c.Iv(b.id)),this.Sd=c.Ce()||this.Sd;c=this.Gv(a);a={requests:this.nf,root:this.Sd};b={};d=a.headers||{};for(var e in d){var f=e;if(Object.prototype.hasOwnProperty.call(d,f)){var h=_.ph(d,f);h&&(f=_.nh(f,h)||_.mh(f))&&_.qh(b,f,h)}}_.qh(b,"Content-Type","application/json");e=aC(c);bB({method:"POST",root:a.root||void 0,path:"/rpc",params:a.urlParams,headers:b,body:a.requests||[]}).then(e,
e)};$B.prototype.Gv=function(a){var b=this;return function(c,d){b.QE(c,d,a)}};$B.prototype.QE=function(a,b,c){a||(a={});for(var d=0;d<this.Nl.length;d++){var e=this.Nl[d];e.callback&&e.callback(a[e.id]||!1,b)}c&&c(a,b)};cB.KP(function(){return new $B});$B.prototype.add=$B.prototype.add;$B.prototype.execute=$B.prototype.execute;var bC=function(a,b){this.Vda=a;this.Xe=b||null;this.Af=null};bC.prototype.hI=function(a){this.Xe=a;this.Af=this.Xe==2?new $B:new VB(this.Vda)};bC.prototype.add=function(a,b){if(!a)throw a=b||_.Ce(),new WA("Batch entry "+(_.De(a,"id")?'"'+a.id+'" ':"")+"is missing a request method");this.Xe===null&&this.hI(a.getFormat());this.Xe!==a.getFormat()&&BB("Unable to add item to batch.");var c=b&&b.callback;this.Xe==1&&c&&(b.callback=function(d){d=cC(d);var e=_.Rf([d]);c(d,e)});return this.Af.add(a,b)};
bC.prototype.execute=function(a){var b=a&&this.Xe==1?function(c){var d=[];_.Qm(c,function(f,h){f=cC(f);c[h]=f;d.push(f)});var e=_.Rf(d);a(c,e)}:a;this.Af&&this.Af.execute(b)};var cC=function(a){var b=a?_.ru(a,"result"):null;_.vb(b)&&b.error!=null&&(b=AB(b),a={id:a.id,error:b});return a};bC.prototype.then=function(a,b,c){this.Xe==2&&BB('The "then" method is not available on this object.');return this.Af.then(a,b,c)};bC.prototype.add=bC.prototype.add;bC.prototype.execute=bC.prototype.execute;
bC.prototype.then=bC.prototype.then;var dC=function(a){XA.call(this,dC.prototype.Yo);this.Rb=a;this.aQ=!1};_.y(dC,XA);var eC=function(a){a.Rb.Ej();var b=a.Rb,c=b.Gf();return!(UB(c.root,c.path,a.Rb.Fq())?NB([b])!=="batch":1)};_.g=dC.prototype;
_.g.execute=function(a){var b=this;this.aQ=!0;if(eC(this))this.Rb.execute(a);else{_.Hi(_.Gi(),13).rb();var c=function(d){if(typeof a==="function"){var e={gapiRequest:{data:{status:d&&d.status,statusText:d&&d.statusText,headers:d&&d.headers,body:d&&d.body}}};if(b.getFormat()===1){a=HB(a);var f={}}var h=d?d.result:!1;d&&d.status==204&&(h=f,delete e.gapiRequest.data.body);a(h,_.Rf(e))}};this.fj().then(c,c)}};
_.g.Yo=function(){if(eC(this))return this.Rb.fj();this.aQ||_.Hi(_.Gi(),16).rb();return new _.xk(function(a,b){var c=$A(),d=c.add(this.Rb,{id:"gapiRequest"});c.then(function(e){var f=e.result;if(f&&(f=f[d])){Object.prototype.hasOwnProperty.call(f,"result")||(f.result=!1);Object.prototype.hasOwnProperty.call(f,"body")||(f.body="");dB(f)?a(f):b(f);return}b(e)},b)},this)};_.g.Gf=function(){if(this.Rb.Gf)return this.Rb.Gf()};_.g.Ej=function(){this.Rb.Ej&&this.Rb.Ej()};_.g.Ce=function(){if(this.Rb.Ce)return this.Rb.Ce()};
_.g.Jj=function(a){this.Rb.Jj&&this.Rb.Jj(a)};_.g.Fq=function(){return this.Rb.Fq()};_.g.getFormat=function(){return this.Rb.getFormat?this.Rb.getFormat():0};_.g.fj=function(){return this.Yo()};dC.prototype.execute=dC.prototype.execute;dC.prototype.then=dC.prototype.then;dC.prototype.getPromise=dC.prototype.fj;var fC="/rest?fields="+encodeURIComponent("kind,name,version,rootUrl,servicePath,resources,parameters,methods,batchPath,id")+"&pp=0",gC=function(a,b){return"/discovery/v1/apis/"+(encodeURIComponent(a)+"/"+encodeURIComponent(b)+fC)},iC=function(a,b,c,d){if(_.vb(a)){var e=a;var f=a.name;a=a.version||"v1"}else f=a,a=b;if(!f||!a)throw new WA("Missing required parameters.");var h=c||function(){},k=_.vb(d)?d:{};c=function(l){var m=l&&l.result;if(!m||m.error||!m.name||!l||l.error||l.message||l.message)h(m&&
m.error?m:l&&(l.error||l.message||l.message)?l:new WA("API discovery response missing required fields."));else{l=k.root;l=m.rootUrl!=null?String(m.rootUrl):l;l=typeof l==="string"?l.replace(/([^\/])\/$/,"$1"):void 0;k.root=l;m.name&&m.version&&!m.id&&(m.id=[m.name,m.version].join(":"));m.id&&(k.apiId=m.id,l="client/batchPath/"+m.id,m.batchPath&&!_.Xe(l)&&_.Ye(l,m.batchPath));var n=m.servicePath,p=m.parameters,q=function(w){_.Qm(w,function(u){if(!(u&&u.id&&u.path&&u.httpMethod))throw new WA("Missing required parameters");
var x=u.id.split("."),A=window.gapi.client,D;for(D=0;D<x.length-1;D++){var E=x[D];A[E]=A[E]||{};A=A[E]}var N,H;k&&(k.hasOwnProperty("root")&&(N=k.root),k.hasOwnProperty("apiId")&&(H=k.apiId));E=window.gapi.client[x[0]];E.ZN||(E.ZN={servicePath:n||"",parameters:p,apiId:H});x=x[D];A[x]||(A[x]=_.bb(hC,{path:typeof u.path==="string"?u.path:null,httpMethod:typeof u.httpMethod==="string"?u.httpMethod:null,parameters:u.parameters,parameterName:(u.request||{}).parameterName||"",request:u.request,root:N},
E.ZN))})},r=function(w){_.Qm(w,function(u){q(u.methods);r(u.resources)})};r(m.resources);q(m.methods);h.call()}};e?c({result:e}):f.indexOf("://")>0?bB({path:f,params:{pp:0,fields:("/"+f).indexOf("/discovery/v1/apis/")>=0?"kind,name,version,rootUrl,servicePath,resources,parameters,methods,batchPath,id":'fields["kind"],fields["name"],fields["version"],fields["rootUrl"],fields["servicePath"],fields["resources"],fields["parameters"],fields["methods"],fields["batchPath"],fields["id"]'}}).then(c,c):bB({path:gC(f,
a),root:d&&d.root}).then(c,c)},hC=function(a,b,c,d,e){e=e===void 0?{}:e;var f=b.servicePath||"";_.wc(f,"/")||(f="/"+f);var h=jC(a.path,[a.parameters,b.parameters],c||{});c=h.Ed;var k=h.xha;f=_.yy(f,h.path);h=k.root;delete k.root;var l=a.parameterName;!l&&_.vy(k)==1&&k.hasOwnProperty("resource")&&(l="resource");if(l){var m=k[l];delete k[l]}m==null&&(m=d);m==null&&a.request&&(_.Gh(k)&&(k=void 0),m=k);e=e||{};l=a.httpMethod;l=="GET"&&m!==void 0&&String(m)!=""&&(_.qh(e,"X-HTTP-Method-Override",l),l="POST");
if((m==null||d!=null)&&k)for(var n in k)typeof k[n]==="string"&&(c[n]=k[n]);return bB({path:f,method:l,params:c,headers:e,body:m,root:h||a.root,apiId:b.apiId},1)},jC=function(a,b,c){c=_.fk(c);var d={};_.Pm(b,function(e){_.Qm(e,function(f,h){var k=f.required;if(f.location=="path")if(Object.prototype.hasOwnProperty.call(c,h))_.yc(a,"{"+h+"}")?(f=encodeURIComponent(String(c[h])),a=a.replace("{"+h+"}",f)):_.yc(a,"{+"+h+"}")&&(f=encodeURI(String(c[h])),a=a.replace("{+"+h+"}",f)),delete c[h];else{if(k)throw new WA("Required path parameter "+
h+" is missing.");}else f.location=="query"&&Object.prototype.hasOwnProperty.call(c,h)&&(d[h]=c[h],delete c[h])})});if(b=c.trace)d.trace=b,delete c.trace;return{path:a,Ed:d,xha:c}};var kC=function(a,b,c,d){var e=b||"v1",f=_.vb(d)?d:{root:d};if(c)iC(a,e,function(h){if(h)if(h.error)c(h);else{var k="API discovery was unsuccessful.";if(h.message||h.message)k=h.message||h.message;c({error:k,code:0})}else c()},f);else return new _.xk(function(h,k){var l=function(m){m?k(m):h()};try{iC(a,e,l,f)}catch(m){k(m)}})},lC=new RegExp(/^((([Hh][Tt][Tt][Pp][Ss]?:)?\/\/[^\/?#]*)?\/)?/.source+/(_ah\/api\/)?(batch|rpc)(\/|\?|#|$)/.source),mC=function(a,b){if(!a)throw new WA("Missing required parameters");
var c=typeof a==="object"?a:{path:a};a=c.callback;delete c.callback;b=new IB(c,b);if(c=!!_.Xe("client/xd4")&&eB()){var d=b.Gf();c=d.path;(d=d.root)&&d.charAt(d.length-1)!=="/"&&(d+="/");d&&c&&c.substr(0,d.length)===d&&(c=c.substr(d.length));c=!c.match(lC)}c&&(b=new dC(b));return a?(b.execute(a),null):b};cB.LP(function(a){return mC.apply(null,arguments)});
var nC=function(a,b){if(!a)throw new WA("Missing required parameters");for(var c=a.split("."),d=window.gapi.client,e=0;e<c.length-1;e++){var f=c[e];d[f]=d[f]||{};d=d[f]}c=c[c.length-1];if(!d[c]){var h=b||{};d[c]=function(k){var l=typeof h=="string"?h:h.root;k&&k.root&&(l=k.root);return new IB({method:a,apiVersion:h.apiVersion,rpcParams:k,transport:{name:"googleapis",root:l}},2)}}},oC=function(a){return new bC(a)};cB.JP(function(a){return oC.apply(null,arguments)});
var pC=function(a){if(_.yB.JSONRPC_ERROR_MOD)throw new WA(a+" is discontinued. See https://developers.googleblog.com/2018/03/discontinuing-support-for-json-rpc-and.html");_.Vf.log(a+" is deprecated. See https://developers.google.com/api-client-library/javascript/reference/referencedocs")};_.t("gapi.client.init",function(a){a.apiKey&&_.Ye("client/apiKey",a.apiKey);var b=_.Ib(a.discoveryDocs||[],function(d){return kC(d)});if((a.clientId||a.client_id)&&a.scope){var c=new _.xk(function(d,e){var f=function(){_.Xa.gapi.auth2.init.call(_.Xa.gapi.auth2,a).then(function(){d()},e)};YA?f():_.Xa.gapi.load("auth2",{callback:function(){f()},onerror:function(h){e(h||Error("Ba"))}})});b.push(c)}else(a.clientId||a.client_id||a.scope)&&_.Vf.log("client_id and scope must both be provided to initialize OAuth.");
return _.Fk(b).then(function(){})});_.t("gapi.client.load",kC);_.t("gapi.client.newBatch",oC);_.t("gapi.client.newRpcBatch",function(){pC("gapi.client.newRpcBatch");return oC()});_.t("gapi.client.newHttpBatch",function(a){pC("gapi.client.newHttpBatch");return new bC(a,0)});_.t("gapi.client.register",function(a,b){pC("gapi.client.register");var c;b&&(c={apiVersion:b.apiVersion,root:b.root});nC(a,c)});_.t("gapi.client.request",mC);
_.t("gapi.client.rpcRequest",function(a,b,c){pC("gapi.client.rpcRequest");if(!a)throw new WA('Missing required parameter "method".');return new IB({method:a,apiVersion:b,rpcParams:c,transport:{name:"googleapis",root:c&&c.root||""}},2)});_.t("gapi.client.setApiKey",function(a){_.Ye("client/apiKey",a);_.Ye("googleapis.config/developerKey",a)});_.t("gapi.client.setApiVersions",function(a){pC("gapi.client.setApiVersions");_.Ye("googleapis.config/versions",a)});_.t("gapi.client.getToken",function(a){return _.ui(a)});
_.t("gapi.client.setToken",function(a,b){a?_.Hw(a,b):_.Iw(b)});_.t("gapi.client.AuthType",{jia:"auto",NONE:"none",Yla:"oauth2",fka:"1p"});_.t("gapi.client.AuthType.AUTO","auto");_.t("gapi.client.AuthType.NONE","none");_.t("gapi.client.AuthType.OAUTH2","oauth2");_.t("gapi.client.AuthType.FIRST_PARTY","1p");
});
// Google Inc.
