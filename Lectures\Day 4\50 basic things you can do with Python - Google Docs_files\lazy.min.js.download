(function(){var m,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");
},u=ca(this),v=function(a,b){if(b)a:{var c=u;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&ba(c,a,{configurable:!0,writable:!0,value:b})}};
v("Symbol",function(a){if(a)return a;var b=function(f,g){this.$jscomp$symbol$id_=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.$jscomp$symbol$id_};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});
v("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=u[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return da(aa(this))}})}return a});
var da=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a},ea=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},fa;if(typeof Object.setPrototypeOf=="function")fa=Object.setPrototypeOf;else{var ha;a:{var ia={a:!0},ja={};try{ja.__proto__=ia;ha=ja.a;break a}catch(a){}ha=!1}fa=ha?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var ka=fa,w=function(a,b){a.prototype=ea(b.prototype);a.prototype.constructor=a;if(ka)ka(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.superClass_=b.prototype},x=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},la=function(a){if(!(a instanceof
Array)){a=x(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a},y=function(a){return na(a,a)},na=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},z=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},oa=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)z(d,e)&&(a[e]=d[e])}return a};v("Object.assign",function(a){return a||oa});
var pa=function(){this.isRunning_=!1;this.yieldAllIterator_=null;this.yieldResult=void 0;this.nextAddress=1;this.finallyAddress_=this.catchAddress_=0;this.abruptCompletion_=null},qa=function(a){if(a.isRunning_)throw new TypeError("Generator is already running");a.isRunning_=!0};pa.prototype.next_=function(a){this.yieldResult=a};pa.prototype.throw_=function(a){this.abruptCompletion_={exception:a,isException:!0};this.nextAddress=this.catchAddress_||this.finallyAddress_};
pa.prototype.return=function(a){this.abruptCompletion_={return:a};this.nextAddress=this.finallyAddress_};var ra=function(a,b,c){a.nextAddress=c;return{value:b}},sa=function(a){this.context_=new pa;this.program_=a};sa.prototype.next_=function(a){qa(this.context_);if(this.context_.yieldAllIterator_)return ta(this,this.context_.yieldAllIterator_.next,a,this.context_.next_);this.context_.next_(a);return ua(this)};
var va=function(a,b){qa(a.context_);var c=a.context_.yieldAllIterator_;if(c)return ta(a,"return"in c?c["return"]:function(d){return{value:d,done:!0}},b,a.context_.return);a.context_.return(b);return ua(a)};sa.prototype.throw_=function(a){qa(this.context_);if(this.context_.yieldAllIterator_)return ta(this,this.context_.yieldAllIterator_["throw"],a,this.context_.next_);this.context_.throw_(a);return ua(this)};
var ta=function(a,b,c,d){try{var e=b.call(a.context_.yieldAllIterator_,c);if(!(e instanceof Object))throw new TypeError("Iterator result "+e+" is not an object");if(!e.done)return a.context_.isRunning_=!1,e;var f=e.value}catch(g){return a.context_.yieldAllIterator_=null,a.context_.throw_(g),ua(a)}a.context_.yieldAllIterator_=null;d.call(a.context_,f);return ua(a)},ua=function(a){for(;a.context_.nextAddress;)try{var b=a.program_(a.context_);if(b)return a.context_.isRunning_=!1,{value:b.value,done:!1}}catch(c){a.context_.yieldResult=
void 0,a.context_.throw_(c)}a.context_.isRunning_=!1;if(a.context_.abruptCompletion_){b=a.context_.abruptCompletion_;a.context_.abruptCompletion_=null;if(b.isException)throw b.exception;return{value:b.return,done:!0}}return{value:void 0,done:!0}},wa=function(a){this.next=function(b){return a.next_(b)};this.throw=function(b){return a.throw_(b)};this.return=function(b){return va(a,b)};this[Symbol.iterator]=function(){return this}},xa=function(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}
return new Promise(function(d,e){function f(g){g.done?d(g.value):Promise.resolve(g.value).then(b,c).then(f,e)}f(a.next())})},ya=function(a){return xa(new wa(new sa(a)))},za=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};v("globalThis",function(a){return a||u});
v("Promise",function(a){function b(){this.batch_=null}function c(g){return g instanceof e?g:new e(function(h){h(g)})}if(a)return a;b.prototype.asyncExecute=function(g){if(this.batch_==null){this.batch_=[];var h=this;this.asyncExecuteFunction(function(){h.executeBatch_()})}this.batch_.push(g)};var d=u.setTimeout;b.prototype.asyncExecuteFunction=function(g){d(g,0)};b.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var g=this.batch_;this.batch_=[];for(var h=0;h<g.length;++h){var k=
g[h];g[h]=null;try{k()}catch(l){this.asyncThrow_(l)}}}this.batch_=null};b.prototype.asyncThrow_=function(g){this.asyncExecuteFunction(function(){throw g;})};var e=function(g){this.state_=0;this.result_=void 0;this.onSettledCallbacks_=[];this.isRejectionHandled_=!1;var h=this.createResolveAndReject_();try{g(h.resolve,h.reject)}catch(k){h.reject(k)}};e.prototype.createResolveAndReject_=function(){function g(l){return function(n){k||(k=!0,l.call(h,n))}}var h=this,k=!1;return{resolve:g(this.resolveTo_),
reject:g(this.reject_)}};e.prototype.resolveTo_=function(g){if(g===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof e)this.settleSameAsPromise_(g);else{a:switch(typeof g){case "object":var h=g!=null;break a;case "function":h=!0;break a;default:h=!1}h?this.resolveToNonPromiseObj_(g):this.fulfill_(g)}};e.prototype.resolveToNonPromiseObj_=function(g){var h=void 0;try{h=g.then}catch(k){this.reject_(k);return}typeof h=="function"?this.settleSameAsThenable_(h,
g):this.fulfill_(g)};e.prototype.reject_=function(g){this.settle_(2,g)};e.prototype.fulfill_=function(g){this.settle_(1,g)};e.prototype.settle_=function(g,h){if(this.state_!=0)throw Error("Cannot settle("+g+", "+h+"): Promise already settled in state"+this.state_);this.state_=g;this.result_=h;this.state_===2&&this.scheduleUnhandledRejectionCheck_();this.executeOnSettledCallbacks_()};e.prototype.scheduleUnhandledRejectionCheck_=function(){var g=this;d(function(){if(g.notifyUnhandledRejection_()){var h=
u.console;typeof h!=="undefined"&&h.error(g.result_)}},1)};e.prototype.notifyUnhandledRejection_=function(){if(this.isRejectionHandled_)return!1;var g=u.CustomEvent,h=u.Event,k=u.dispatchEvent;if(typeof k==="undefined")return!0;typeof g==="function"?g=new g("unhandledrejection",{cancelable:!0}):typeof h==="function"?g=new h("unhandledrejection",{cancelable:!0}):(g=u.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.result_;return k(g)};
e.prototype.executeOnSettledCallbacks_=function(){if(this.onSettledCallbacks_!=null){for(var g=0;g<this.onSettledCallbacks_.length;++g)f.asyncExecute(this.onSettledCallbacks_[g]);this.onSettledCallbacks_=null}};var f=new b;e.prototype.settleSameAsPromise_=function(g){var h=this.createResolveAndReject_();g.callWhenSettled_(h.resolve,h.reject)};e.prototype.settleSameAsThenable_=function(g,h){var k=this.createResolveAndReject_();try{g.call(h,k.resolve,k.reject)}catch(l){k.reject(l)}};e.prototype.then=
function(g,h){function k(r,q){return typeof r=="function"?function(t){try{l(r(t))}catch(A){n(A)}}:q}var l,n,p=new e(function(r,q){l=r;n=q});this.callWhenSettled_(k(g,l),k(h,n));return p};e.prototype.catch=function(g){return this.then(void 0,g)};e.prototype.callWhenSettled_=function(g,h){function k(){switch(l.state_){case 1:g(l.result_);break;case 2:h(l.result_);break;default:throw Error("Unexpected state: "+l.state_);}}var l=this;this.onSettledCallbacks_==null?f.asyncExecute(k):this.onSettledCallbacks_.push(k);
this.isRejectionHandled_=!0};e.resolve=c;e.reject=function(g){return new e(function(h,k){k(g)})};e.race=function(g){return new e(function(h,k){for(var l=x(g),n=l.next();!n.done;n=l.next())c(n.value).callWhenSettled_(h,k)})};e.all=function(g){var h=x(g),k=h.next();return k.done?c([]):new e(function(l,n){function p(t){return function(A){r[t]=A;q--;q==0&&l(r)}}var r=[],q=0;do r.push(void 0),q++,c(k.value).callWhenSettled_(p(r.length-1),n),k=h.next();while(!k.done)})};return e});
v("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});v("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}});
v("WeakMap",function(a){function b(){}function c(k){var l=typeof k;return l==="object"&&k!==null||l==="function"}function d(k){if(!z(k,f)){var l=new b;ba(k,f,{value:l})}}function e(k){var l=Object[k];l&&(Object[k]=function(n){if(n instanceof b)return n;Object.isExtensible(n)&&d(n);return l(n)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),l=Object.seal({}),n=new a([[k,2],[l,3]]);if(n.get(k)!=2||n.get(l)!=3)return!1;n.delete(k);n.set(l,4);return!n.has(k)&&n.get(l)==4}catch(p){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var g=0,h=function(k){this.id_=(g+=Math.random()+1).toString();if(k){k=x(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};h.prototype.set=function(k,l){if(!c(k))throw Error("Invalid WeakMap key");d(k);if(!z(k,f))throw Error("WeakMap key fail: "+k);k[f][this.id_]=l;return this};h.prototype.get=function(k){return c(k)&&z(k,f)?k[f][this.id_]:void 0};h.prototype.has=function(k){return c(k)&&z(k,f)&&z(k[f],
this.id_)};h.prototype.delete=function(k){return c(k)&&z(k,f)&&z(k[f],this.id_)?delete k[f][this.id_]:!1};return h});
v("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var h=Object.seal({x:4}),k=new a(x([[h,"s"]]));if(k.get(h)!="s"||k.size!=1||k.get({x:4})||k.set({x:4},"t")!=k||k.size!=2)return!1;var l=k.entries(),n=l.next();if(n.done||n.value[0]!=h||n.value[1]!="s")return!1;n=l.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!l.next().done?!1:!0}catch(p){return!1}}())return a;var b=new WeakMap,c=function(h){this[0]={};this[1]=
f();this.size=0;if(h){h=x(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}};c.prototype.set=function(h,k){h=h===0?0:h;var l=d(this,h);l.list||(l.list=this[0][l.id]=[]);l.entry?l.entry.value=k:(l.entry={next:this[1],previous:this[1].previous,head:this[1],key:h,value:k},l.list.push(l.entry),this[1].previous.next=l.entry,this[1].previous=l.entry,this.size++);return this};c.prototype.delete=function(h){h=d(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],
h.entry.previous.next=h.entry.next,h.entry.next.previous=h.entry.previous,h.entry.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].previous=f();this.size=0};c.prototype.has=function(h){return!!d(this,h).entry};c.prototype.get=function(h){return(h=d(this,h).entry)&&h.value};c.prototype.entries=function(){return e(this,function(h){return[h.key,h.value]})};c.prototype.keys=function(){return e(this,function(h){return h.key})};c.prototype.values=function(){return e(this,
function(h){return h.value})};c.prototype.forEach=function(h,k){for(var l=this.entries(),n;!(n=l.next()).done;)n=n.value,h.call(k,n[1],n[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(h,k){var l=k&&typeof k;l=="object"||l=="function"?b.has(k)?l=b.get(k):(l=""+ ++g,b.set(k,l)):l="p_"+k;var n=h[0][l];if(n&&z(h[0],l))for(h=0;h<n.length;h++){var p=n[h];if(k!==k&&p.key!==p.key||k===p.key)return{id:l,list:n,index:h,entry:p}}return{id:l,list:n,index:-1,entry:void 0}},e=function(h,
k){var l=h[1];return da(function(){if(l){for(;l.head!=h[1];)l=l.previous;for(;l.next!=l.head;)return l=l.next,{done:!1,value:k(l)};l=null}return{done:!0,value:void 0}})},f=function(){var h={};return h.previous=h.next=h.head=h},g=0;return c});v("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)z(b,d)&&c.push(b[d]);return c}});v("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});
v("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});var Aa=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};
v("String.prototype.includes",function(a){return a?a:function(b,c){return Aa(this,b,"includes").indexOf(b,c||0)!==-1}});v("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(h){return h};var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});
v("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)z(b,d)&&c.push([d,b[d]]);return c}});v("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});v("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});v("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});v("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});
v("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});v("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=Aa(this,b,"startsWith");b+="";var e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var g=0;g<f&&c<e;)if(d[c++]!=b[g++])return!1;return g>=f}});
var Ba=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};v("Array.prototype.entries",function(a){return a?a:function(){return Ba(this,function(b,c){return[b,c]})}});v("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});
v("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});v("Array.prototype.keys",function(a){return a?a:function(){return Ba(this,function(b){return b})}});v("Array.prototype.values",function(a){return a?a:function(){return Ba(this,function(b,c){return c})}});
v("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("String.prototype.replaceAll called with a non-global RegExp argument.");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}});/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var Ca=Ca||{},B=this||self,Da=function(a,b,c){a=a.split(".");c=c||B;for(var d;a.length&&(d=a.shift());)a.length||b===void 0?c=c[d]&&c[d]!==Object.prototype[d]?c[d]:c[d]={}:c[d]=b},C=function(a,b){a=a.split(".");b=b||B;for(var c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b},Ea=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"},Fa=function(a){var b=Ea(a);return b=="array"||b=="object"&&typeof a.length=="number"},Ha=function(a){var b=typeof a;return b==
"object"&&a!=null||b=="function"},Ia=function(a,b,c){return a.call.apply(a.bind,arguments)},Ja=function(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}},Ka=function(a,b,c){Ka=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?Ia:Ja;return Ka.apply(null,
arguments)},La=function(a){return a},Ma=function(a,b){function c(){}c.prototype=b.prototype;a.superClass_=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.base=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Na=globalThis.trustedTypes,Oa;function Pa(){var a=null;if(!Na)return a;try{var b=function(c){return c};a=Na.createPolicy("uf-la#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a};var Qa=function(a){this.privateDoNotAccessOrElseWrappedResourceUrl=a};Qa.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedResourceUrl+""};function Ra(a){var b;Oa===void 0&&(Oa=Pa());a=(b=Oa)?b.createScriptURL(a):a;return new Qa(a)}function Sa(a){if(a instanceof Qa)return a.privateDoNotAccessOrElseWrappedResourceUrl;throw Error("");};var Ta=function(a){this.privateDoNotAccessOrElseWrappedUrl=a};Ta.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedUrl};var Ua=new Ta("about:invalid#zClosurez");function Va(a){if(a instanceof Ta)return a.privateDoNotAccessOrElseWrappedUrl;throw Error("");};function D(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,D);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b)}Ma(D,Error);D.prototype.name="CustomError";var Wa;function Xa(a){B.setTimeout(function(){throw a;},0)};var Ya=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};function Za(){var a=B.navigator;return a&&(a=a.userAgent)?a:""};var $a=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1},ab=Array.prototype.forEach?function(a,b){Array.prototype.forEach.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=typeof a==="string"?a.split(""):a,e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)},bb=Array.prototype.some?function(a,b){return Array.prototype.some.call(a,
b,void 0)}:function(a,b){for(var c=a.length,d=typeof a==="string"?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1};function cb(a,b){b=$a(a,b);var c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c}function db(a){var b=a.length;if(b>0){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};var eb=Za().toLowerCase().indexOf("webkit")!=-1&&Za().indexOf("Edge")==-1,fb=eb&&Za().indexOf("Mobile")!=-1;var gb={},hb=null,ib=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}a=2;a===void 0&&(a=0);if(!hb)for(hb={},c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),d=["+/=","+/","-_=","-_.","-_"],e=0;e<5;e++){var f=c.concat(d[e].split(""));gb[e]=f;for(var g=0;g<f.length;g++){var h=f[g];hb[h]===void 0&&(hb[h]=g)}}a=gb[a];c=Array(Math.floor(b.length/3));d=a[64]||"";for(e=f=0;f<b.length-2;f+=3){var k=b[f],l=b[f+1];h=b[f+
2];g=a[k>>2];k=a[(k&3)<<4|l>>4];l=a[(l&15)<<2|h>>6];h=a[h&63];c[e++]=""+g+k+l+h}g=0;h=d;switch(b.length-f){case 2:g=b[f+1],h=a[(g&15)<<2]||d;case 1:b=b[f],c[e]=""+a[b>>2]+a[(b&3)<<4|g>>4]+h+d}return c.join("")};var jb=typeof Symbol==="function"&&typeof Symbol()==="symbol";function kb(a,b,c){return typeof Symbol==="function"&&typeof Symbol()==="symbol"?(c===void 0?0:c)&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol():b}var lb=kb("jas",void 0,!0),mb=kb(void 0,Symbol()),nb=kb(void 0,"0ub"),ob=kb(void 0,"0ubs"),qb=kb(void 0,"0actk"),rb=kb("m_m","messagePrototypeMarker",!0),sb=kb();Math.max.apply(Math,la(Object.values({IS_REPEATED_FIELD:1,IS_IMMUTABLE_ARRAY:2,IS_API_FORMATTED:4,ONLY_MUTABLE_VALUES:8,UNFROZEN_SHARED:16,MUTABLE_REFERENCES_ARE_OWNED:32,CONSTRUCTED:64,HAS_MESSAGE_ID:128,FROZEN_ARRAY:256,STRING_FORMATTED:512,GBIGINT_FORMATTED:1024,HAS_WRAPPER:2048,MUTABLE_SUBSTRUCTURES:4096})));var tb={internalArrayState:{value:0,configurable:!0,writable:!0,enumerable:!1}},ub=Object.defineProperties,E=jb?lb:"internalArrayState",vb,wb=[];F(wb,7);vb=Object.freeze(wb);
function xb(a,b){jb||E in a||ub(a,tb);a[E]|=b}function F(a,b){jb||E in a||ub(a,tb);a[E]=b};function yb(){return typeof BigInt==="function"};var zb={};function Ab(a,b){return b===void 0?a.copyOnWrite!==Bb&&!!(2&(a.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows[E]|0)):!!(2&b)&&a.copyOnWrite!==Bb}var Bb={},Cb=Object.freeze({});function Db(a){return a};function Eb(a){a.isGuard_doNotManuallySetPrettyPlease=!0;return a};var Fb=Eb(function(a){return typeof a==="number"}),Gb=Eb(function(a){return typeof a==="string"}),Hb=Eb(function(a){return typeof a==="boolean"}),Ib=Eb(function(a){return typeof a==="bigint"});var Jb=typeof B.BigInt==="function"&&typeof B.BigInt(0)==="bigint";function G(a){var b=a;if(Gb(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if(Fb(b)&&!Number.isSafeInteger(b))throw Error(String(b));return Jb?BigInt(a):a=Hb(a)?a?"1":"0":Gb(a)?a.trim()||"0":String(a)}
var Kb=Eb(function(a){return Jb?Ib(a):Gb(a)&&/^(?:-?[1-9]\d*|0)$/.test(a)}),Qb=Eb(function(a){return Jb?a>=Lb&&a<=Mb:a[0]==="-"?Nb(a,Ob):Nb(a,Pb)}),Ob=Number.MIN_SAFE_INTEGER.toString(),Lb=Jb?BigInt(Number.MIN_SAFE_INTEGER):void 0,Pb=Number.MAX_SAFE_INTEGER.toString(),Mb=Jb?BigInt(Number.MAX_SAFE_INTEGER):void 0;function Nb(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};var H=0,I=0;function Rb(a){var b=a>>>0;H=b;I=(a-b)/4294967296>>>0}function Sb(a){if(a<0){Rb(0-a);var b=x(Tb(H,I));a=b.next().value;b=b.next().value;H=a>>>0;I=b>>>0}else Rb(a)}function Ub(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else yb()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+Vb(c)+Vb(a));return c}
function Vb(a){a=String(a);return"0000000".slice(a.length)+a}function Wb(){var a=H,b=I;b&2147483648?yb()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=x(Tb(a,b)),a=b.next().value,b=b.next().value,a="-"+Ub(a,b)):a=Ub(a,b);return a}function Tb(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};function Xb(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};var Yb=void 0;function Zb(a){a=Error(a);Xb(a,"warning");return a}function $b(a,b){if(a!=null){var c;var d=(c=Yb)!=null?c:Yb={};c=d[a]||0;c>=b||(d[a]=c+1,a=Error(),Xb(a,"incident"),Xa(a))}};function ac(a){return Array.prototype.slice.call(a)};var bc=typeof BigInt==="function"?BigInt.asIntN:void 0,cc=Number.isSafeInteger,dc=Number.isFinite,ec=Math.trunc;function fc(a){return a.displayName||a.name||"unknown type name"}var hc=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function ic(a){switch(typeof a){case "bigint":return!0;case "number":return dc(a);case "string":return hc.test(a);default:return!1}}function jc(a){if(!dc(a))throw Zb("enum");return a|0}function kc(a){return a==null?a:dc(a)?a|0:void 0}
function lc(a){if(typeof a!=="number")throw Zb("int32");if(!dc(a))throw Zb("int32");return a|0}
function mc(a){var b=0;b=b===void 0?0:b;if(!ic(a))throw Zb("int64");var c=typeof a;switch(b){case 512:switch(c){case "string":return nc(a);case "bigint":return String(bc(64,a));default:return oc(a)}case 1024:switch(c){case "string":return pc(a);case "bigint":return G(bc(64,a));default:return qc(a)}case 0:switch(c){case "string":return nc(a);case "bigint":return G(bc(64,a));default:return rc(a)}default:throw Error("Unknown format requested type for int64");}}
function sc(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337}
function tc(a){if(sc(a))return a;if(a.length<16)Sb(Number(a));else if(yb())a=BigInt(a),H=Number(a&BigInt(4294967295))>>>0,I=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");I=H=0;for(var c=a.length,d=0+b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),I*=1E6,H=H*1E6+d,H>=4294967296&&(I+=Math.trunc(H/4294967296),I>>>=0,H>>>=0);b&&(b=x(Tb(H,I)),a=b.next().value,b=b.next().value,H=a,I=b)}return Wb()}
function rc(a){ic(a);a=ec(a);if(!cc(a)){Sb(a);var b=H,c=I;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);var d=c*4294967296+(b>>>0);b=Number.isSafeInteger(d)?d:Ub(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function oc(a){ic(a);a=ec(a);if(cc(a))a=String(a);else{var b=String(a);sc(b)?a=b:(Sb(a),a=Wb())}return a}function nc(a){ic(a);var b=ec(Number(a));if(cc(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return tc(a)}
function pc(a){var b=ec(Number(a));if(cc(b))return G(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return yb()?G(bc(64,BigInt(a))):G(tc(a))}function qc(a){return cc(a)?G(rc(a)):G(oc(a))}function uc(a){if(typeof a!=="string")throw Error();return a}function vc(a){if(a!=null&&typeof a!=="string")throw Error();return a}function wc(a){return a==null||typeof a==="string"?a:void 0};function xc(a,b){var c=La(mb),d;jb&&c&&((d=a[c])==null?void 0:d[b])!=null&&$b(nb,3)}var yc={reviveIntoImmutable:!0};function zc(a,b,c){var d=d===void 0?!1:d;if(La(sb)&&La(mb)&&c===sb){c=a.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows;var e=c[mb];if(!e)return;if(e=e.reviveUnknownFields)try{e(c,b,yc);return}catch(f){Xa(f)}}d&&(a=a.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows,(d=La(mb))&&d in a&&(a=a[d])&&delete a[b])};function Ac(a,b,c,d){var e=d!==void 0;d=!!d;var f=La(mb),g;!e&&jb&&f&&(g=a[f])&&g.forEachUnknownField(Bc);f=[];var h=a.length;g=4294967295;var k=!1,l=!!(b&64),n=l?b&128?0:-1:void 0;if(!(b&1)){var p=h&&a[h-1];p!=null&&typeof p==="object"&&p.constructor===Object?(h--,g=h):p=void 0;if(l&&!(b&128)&&!e){k=!0;var r;g=((r=Cc)!=null?r:Db)(g-n,n,a,p)+n}}b=void 0;for(e=0;e<h;e++)if(r=a[e],r!=null&&(r=c(r,d))!=null)if(l&&e>=g){var q=e-n,t=void 0;((t=b)!=null?t:b={})[q]=r}else f[e]=r;if(p)for(var A in p)a=p[A],
a!=null&&(a=c(a,d))!=null&&(h=+A,e=void 0,l&&!Number.isNaN(h)&&(e=h+n)<g?f[e]=a:(h=void 0,((h=b)!=null?h:b={})[A]=a));b&&(k?f.push(b):f[g]=b);return f}function Dc(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return Qb(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[E]|0;return a.length===0&&b&1?void 0:Ac(a,b,Dc)}if(a!=null&&a[rb]===zb)return Ec(a);return}return a}var Cc;
function Ec(a){a=a.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows;return Ac(a,a[E]|0,Dc)}function Bc(a,b){b<500||$b(ob,1)};function J(a,b,c){var d=d===void 0?0:d;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-8380417|(b&1023)<<13)}else{if(!Array.isArray(a))throw Error("narr");e=a[E]|0;2048&e&&!(2&e)&&Fc();if(e&256)throw Error("farr");if(e&64)return d!==0||e&2048||F(a,e|2048),a;if(c&&(e|=128,c!==a[0]))throw Error("mid");a:{c=a;e|=64;var f=c.length;if(f){var g=f-1,h=c[g];if(h!=null&&typeof h==="object"&&h.constructor===Object){b=e&128?0:-1;g-=b;if(g>=1024)throw Error("pvtlmt");for(var k in h)f=+k,f<g&&(c[f+b]=h[k],
delete h[k]);e=e&-8380417|(g&1023)<<13;break a}}if(b){k=Math.max(b,f-(e&128?0:-1));if(k>1024)throw Error("spvt");e=e&-8380417|(k&1023)<<13}}}e|=64;d===0&&(e|=2048);F(a,e);return a}function Fc(){$b(qb,5)};function Gc(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[E]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=Hc(a,c,!1,b&&!(c&16)):(xb(a,34),c&4&&Object.freeze(a)));return a}if(a!=null&&a[rb]===zb)return b=a.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows,c=b[E]|0,Ab(a,c)?a:Hc(b,c)}function Hc(a,b,c,d){d!=null||(d=!!(34&b));a=Ac(a,b,Gc,d);d=32;c&&(d|=2);b=b&8380609|d;F(a,b);return a}
function Ic(a){if(a.copyOnWrite!==Bb)return!1;var b=a.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows;b=Hc(b,b[E]|0);xb(b,2048);a.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=b;a.copyOnWrite=void 0;a.noLegacyNull=void 0;return!0}function Jc(a){if(!Ic(a)&&Ab(a,a.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows[E]|0))throw Error();};var Kc=G(0),Lc={},Nc=function(a,b,c,d){Object.isExtensible(a);b=Mc(a.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows,b,c);if(b!==null||d&&a.noLegacyNull!==Bb)return b},Mc=function(a,b,c,d){if(b===-1)return null;var e=b+(c?0:-1),f=a.length-1;if(!(f<1+(c?0:-1))){if(e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object){c=g[b];var h=!0}else if(e===f)c=g;else return}else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return h?g[b]=d:a[e]=d,d}return c}},Pc=
function(a,b,c){Jc(a);a=a.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows;Oc(a,a[E]|0,b,c)};function Oc(a,b,c,d){var e=c+-1,f=a.length-1;if(f>=0&&e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object)return g[c]=d,b}if(e<=f)return a[e]=d,b;if(d!==void 0){var h;f=((h=b)!=null?h:b=a[E]|0)>>13&1023||536870912;c>=f?d!=null&&(e={},a[f+-1]=(e[c]=d,e)):a[e]=d}return b}
function Qc(a,b,c,d,e){var f=a.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows,g=f[E]|0;d=Ab(a,g)?1:d;e=!!e||d===3;d===2&&Ic(a)&&(f=a.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows,g=f[E]|0);a=Mc(f,b);var h=Array.isArray(a)?a:vb;var k=h===vb?7:h[E]|0;a=k;2&g&&(a|=2);var l=a|1;a=4&l?!1:!0;if(a){4&l&&(h=ac(h),k=0,l=Rc(l,g),g=Oc(f,g,b,h));for(var n=0,p=0;n<h.length;n++){var r=c(h[n]);r!=null&&(h[p++]=r)}p<n&&(h.length=p);c=(l|4)&-513;l=c&=-1025;l&=-4097}l!==k&&(F(h,l),2&l&&Object.freeze(h));
c=h;k=h=l;d===1||(d!==4?0:2&h||!(16&h)&&32&g)?Sc(h)||(h|=!c.length||a&&!(4096&h)||32&g&&!(4096&h||16&h)?2:256,h!==k&&F(c,h),Object.freeze(c)):(d===2&&Sc(h)&&(c=ac(c),k=0,h=Rc(h,g),Oc(f,g,b,c)),Sc(h)||(e||(h|=16),h!==k&&F(c,h)));return c}function Sc(a){return!!(2&a)&&!!(4&a)||!!(256&a)}
function Tc(a,b,c,d){Jc(a);var e=a.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows,f=e[E]|0;if(c==null)return Oc(e,f,b),a;if(!Array.isArray(c))throw Zb();var g=c===vb?7:c[E]|0,h=g,k=Sc(g),l=k||Object.isFrozen(c);k||(g=0);l||(c=ac(c),h=0,g=Rc(g,f),l=!1);g|=5;k=4&g?512&g?512:1024&g?1024:0:void 0;k=k!=null?k:0;for(var n=0;n<c.length;n++){var p=c[n],r=d(p,k);Object.is(p,r)||(l&&(c=ac(c),h=0,g=Rc(g,f),l=!1),c[n]=r)}g!==h&&(l&&(c=ac(c),g=Rc(g,f)),F(c,g));Oc(e,f,b,c);return a}
function K(a,b,c,d){Jc(a);var e=a.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows;Oc(e,e[E]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a}function Uc(a,b,c,d,e){a=Mc(a,d,e,function(f){if(f==null||f[rb]!==zb)if(Array.isArray(f)){var g=f[E]|0;var h=g|b&32;h|=b&2;h!==g&&F(f,h);f=new c(f)}else f=void 0;return f});if(a!=null)return a}
var L=function(a,b,c){var d=a.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows,e=d[E]|0;b=Uc(d,e,b,c);if(b==null)return b;e=d[E]|0;if(!Ab(a,e)){var f=b;var g=f.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows,h=g[E]|0;f=Ab(f,h)?new f.constructor(Hc(g,h,!1)):f;f!==b&&(Ic(a)&&(d=a.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows,e=d[E]|0),b=f,Oc(d,e,c,b))}return b},M=function(a,b,c,d){if(d!=null){if(!(d instanceof b))throw Error("Expected instanceof "+fc(b)+" but got "+(d&&fc(d.constructor)));
}else d=void 0;Pc(a,c,d);return a};function Rc(a,b){return a=(2&b?a|2:a&-3)&-273}
var N=function(a,b){var c=c===void 0?!1:c;a=Nc(a,b);a=a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0;return a!=null?a:c},Vc=function(a,b){var c=c===void 0?"":c;var d;return(d=wc(Nc(a,b)))!=null?d:c},Wc=function(a,b,c){a=Qc(a,b,wc,3,!0);if(typeof c!=="number"||c<0||c>=a.length)throw Error();return a[c]},Xc=function(a){return Qc(a,4,kc,void 0===Cb?2:4)},P=function(a,b,c){if(c!=null&&typeof c!=="boolean")throw Error("Expected boolean but got "+Ea(c)+": "+c);return K(a,b,c,!1)};var Q=function(a,b,c){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a,b,c)};Q.prototype.toJSON=function(){return Ec(this)};var Yc=function(a,b){if(b==null||b=="")return new a;b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");xb(b,32);return new a(b)};
Q.prototype.getExtension=function(a){xc(this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows,a.fieldIndex);zc(this,a.fieldIndex,a.lazyParse);return a.ctor?a.isRepeated?a.getExtensionFn(this,a.ctor,a.fieldIndex,void 0===Cb?2:4,a.hasMessageId):a.getExtensionFn(this,a.ctor,a.fieldIndex,a.hasMessageId):a.isRepeated?a.getExtensionFn(this,a.fieldIndex,void 0===Cb?2:4,a.hasMessageId):a.getExtensionFn(this,a.fieldIndex,a.defaultValue,a.hasMessageId)};
Q.prototype.hasExtension=function(a){xc(this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows,a.fieldIndex);zc(this,a.fieldIndex,a.lazyParse);if(a.ctor){var b=this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows;a=Uc(b,b[E]|0,a.ctor,a.fieldIndex,a.hasMessageId)!==void 0}else xc(this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows,a.fieldIndex),zc(this,a.fieldIndex,a.lazyParse),a=a.ctor?a.getExtensionFn(this,a.ctor,a.fieldIndex,a.hasMessageId):a.getExtensionFn(this,a.fieldIndex,
null,a.hasMessageId),a=(a===null?void 0:a)!==void 0;return a};Q.prototype.clone=function(){var a=this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows;return new this.constructor(Hc(a,a[E]|0,!1))};Q.prototype[rb]=zb;Q.prototype.toString=function(){return this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows.toString()};function Zc(a,b){for(var c in a)b.call(void 0,a[c],c,a)}function $c(a,b){for(var c in a)if(b.call(void 0,a[c],c,a))return!0;return!1}var ad="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function bd(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<ad.length;f++)c=ad[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};var ed=function(a,b){this.stringConstValueWithSecurityContract__googStringSecurityPrivate_=a===cd&&b||"";this.STRING_CONST_TYPE_MARKER__GOOG_STRING_SECURITY_PRIVATE_=dd};ed.prototype.toString=function(){return this.stringConstValueWithSecurityContract__googStringSecurityPrivate_};
var fd=function(a){return a instanceof ed&&a.constructor===ed&&a.STRING_CONST_TYPE_MARKER__GOOG_STRING_SECURITY_PRIVATE_===dd?a.stringConstValueWithSecurityContract__googStringSecurityPrivate_:"type_error:Const"},R=function(a){return new ed(cd,a)},dd={},cd={};var gd=function(a){this.isValid=a};function hd(a){return new gd(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var id=[hd("data"),hd("http"),hd("https"),hd("mailto"),hd("ftp"),new gd(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function jd(a){var b=b===void 0?id:b;a:if(b=b===void 0?id:b,!(a instanceof Ta)){for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof gd&&d.isValid(a)){a=new Ta(a);break a}}a=void 0}return a||Ua}var kd=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function ld(a){a=a instanceof Ta?Va(a):kd.test(a)?a:void 0;return a};function md(a,b){b=ld(b);b!==void 0&&a.open(b,void 0,void 0)}function nd(a){a=a===void 0?document:a;var b,c;a=(c=(b=a).querySelector)==null?void 0:c.call(b,"script[nonce]");return a==null?"":a.nonce||a.getAttribute("nonce")||""};function od(a,b){a.src=Sa(b);(b=nd(a.ownerDocument))&&a.setAttribute("nonce",b)};var pd="alternate author bookmark canonical cite help icon license modulepreload next prefetch dns-prefetch prerender preconnect preload prev search subresource".split(" ");function S(a){var b=za.apply(1,arguments);if(b.length===0)return Ra(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return Ra(c)};var sd=function(a){return a?new qd(rd(a)):Wa||(Wa=new qd)},ud=function(a,b){Zc(b,function(c,d){d=="style"?a.style.cssText=c:d=="class"?a.className=c:d=="for"?a.htmlFor=c:td.hasOwnProperty(d)?a.setAttribute(td[d],c):d.lastIndexOf("aria-",0)==0||d.lastIndexOf("data-",0)==0?a.setAttribute(d,c):a[d]=c})},td={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",
valign:"vAlign",width:"width"},vd=function(a,b,c){function d(h){h&&b.appendChild(typeof h==="string"?a.createTextNode(h):h)}for(var e=1;e<c.length;e++){var f=c[e];if(!Fa(f)||Ha(f)&&f.nodeType>0)d(f);else{a:{if(f&&typeof f.length=="number"){if(Ha(f)){var g=typeof f.item=="function"||typeof f.item=="string";break a}if(typeof f==="function"){g=typeof f.item=="function";break a}}g=!1}ab(g?db(f):f,d)}}},wd=function(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)},
xd=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null},rd=function(a){return a.nodeType==9?a:a.ownerDocument||a.document},qd=function(a){this.document_=a||B.document||document};m=qd.prototype;m.getElementsByTagName=function(a,b){return(b||this.document_).getElementsByTagName(String(a))};m.createElement=function(a){return wd(this.document_,a)};m.createTextNode=function(a){return this.document_.createTextNode(String(a))};m.getWindow=function(){return this.document_.defaultView};
m.appendChild=function(a,b){a.appendChild(b)};m.append=function(a,b){vd(rd(a),a,arguments)};m.canHaveChildren=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
m.removeNode=xd;m.contains=function(a,b){if(!a||!b)return!1;if(a.contains&&b.nodeType==1)return a==b||a.contains(b);if(typeof a.compareDocumentPosition!="undefined")return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};var T=typeof AsyncContext!=="undefined"&&typeof AsyncContext.Snapshot==="function"?function(a){return a&&AsyncContext.Snapshot.wrap(a)}:function(a){return a};var yd=function(a,b){this.limit_=100;this.create_=a;this.reset_=b;this.occupants_=0;this.head_=null};yd.prototype.get=function(){if(this.occupants_>0){this.occupants_--;var a=this.head_;this.head_=a.next;a.next=null}else a=this.create_();return a};yd.prototype.put=function(a){this.reset_(a);this.occupants_<this.limit_&&(this.occupants_++,a.next=this.head_,this.head_=a)};var Bd=function(){this.workTail_=this.workHead_=null};Bd.prototype.add=function(a,b){var c=Cd.get();c.set(a,b);this.workTail_?this.workTail_.next=c:this.workHead_=c;this.workTail_=c};Bd.prototype.remove=function(){var a=null;this.workHead_&&(a=this.workHead_,this.workHead_=this.workHead_.next,this.workHead_||(this.workTail_=null),a.next=null);return a};var Cd=new yd(function(){return new Dd},function(a){return a.reset()}),Dd=function(){this.next=this.scope=this.fn=null};
Dd.prototype.set=function(a,b){this.fn=a;this.scope=b;this.next=null};Dd.prototype.reset=function(){this.next=this.scope=this.fn=null};var Ed,Fd=!1,Gd=new Bd,Id=function(a,b){Ed||Hd();Fd||(Ed(),Fd=!0);Gd.add(a,b)},Hd=function(){var a=Promise.resolve(void 0);Ed=function(){a.then(Jd)}};function Jd(){for(var a;a=Gd.remove();){try{a.fn.call(a.scope)}catch(b){Xa(b)}Cd.put(a)}Fd=!1};var Kd=function(){return!0},Ld=function(){};var Md=function(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};var U=function(a){this.state_=0;this.result_=void 0;this.callbackEntriesTail_=this.callbackEntries_=this.parent_=null;this.hadUnhandledRejection_=this.executing_=!1;if(a!=Ld)try{var b=this;a.call(void 0,function(c){Nd(b,2,c)},function(c){Nd(b,3,c)})}catch(c){Nd(this,3,c)}},Od=function(){this.next=this.context=this.onRejected=this.onFulfilled=this.child=null;this.always=!1};Od.prototype.reset=function(){this.context=this.onRejected=this.onFulfilled=this.child=null;this.always=!1};
var Pd=new yd(function(){return new Od},function(a){a.reset()}),Qd=function(a,b,c){var d=Pd.get();d.onFulfilled=a;d.onRejected=b;d.context=c;return d},Rd=function(a){return new U(function(b,c){c(a)})},Td=function(){var a,b,c=new U(function(d,e){a=d;b=e});return new Sd(c,a,b)};U.prototype.then=function(a,b,c){return Ud(this,T(typeof a==="function"?a:null),T(typeof b==="function"?b:null),c)};U.prototype.$goog_Thenable=!0;var Wd=function(a,b,c,d){Vd(a,Qd(b||Ld,c||null,d))};
U.prototype.finally=function(a){var b=this;a=T(a);return new Promise(function(c,d){Wd(b,function(e){a();c(e)},function(e){a();d(e)})})};U.prototype.thenCatch=function(a,b){return Ud(this,null,T(a),b)};U.prototype.catch=U.prototype.thenCatch;U.prototype.cancel=function(a){if(this.state_==0){var b=new Xd(a);Id(function(){Yd(this,b)},this)}};
var Yd=function(a,b){if(a.state_==0)if(a.parent_){var c=a.parent_;if(c.callbackEntries_){for(var d=0,e=null,f=null,g=c.callbackEntries_;g&&(g.always||(d++,g.child==a&&(e=g),!(e&&d>1)));g=g.next)e||(f=g);e&&(c.state_==0&&d==1?Yd(c,b):(f?(d=f,d.next==c.callbackEntriesTail_&&(c.callbackEntriesTail_=d),d.next=d.next.next):Zd(c),$d(c,e,3,b)))}a.parent_=null}else Nd(a,3,b)},Vd=function(a,b){a.callbackEntries_||a.state_!=2&&a.state_!=3||ae(a);a.callbackEntriesTail_?a.callbackEntriesTail_.next=b:a.callbackEntries_=
b;a.callbackEntriesTail_=b},Ud=function(a,b,c,d){var e=Qd(null,null,null);e.child=new U(function(f,g){e.onFulfilled=b?function(h){try{var k=b.call(d,h);f(k)}catch(l){g(l)}}:f;e.onRejected=c?function(h){try{var k=c.call(d,h);k===void 0&&h instanceof Xd?g(h):f(k)}catch(l){g(l)}}:g});e.child.parent_=a;Vd(a,e);return e.child};U.prototype.unblockAndFulfill_=function(a){this.state_=0;Nd(this,2,a)};U.prototype.unblockAndReject_=function(a){this.state_=0;Nd(this,3,a)};
var Nd=function(a,b,c){if(a.state_==0){a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself"));a.state_=1;a:{var d=c,e=a.unblockAndFulfill_,f=a.unblockAndReject_;if(d instanceof U){Wd(d,e,f,a);var g=!0}else if(Md(d))d.then(e,f,a),g=!0;else{if(Ha(d))try{var h=d.then;if(typeof h==="function"){be(d,h,e,f,a);g=!0;break a}}catch(k){f.call(a,k);g=!0;break a}g=!1}}g||(a.result_=c,a.state_=b,a.parent_=null,ae(a),b!=3||c instanceof Xd||ce(a,c))}},be=function(a,b,c,d,e){var f=!1,g=function(k){f||(f=
!0,c.call(e,k))},h=function(k){f||(f=!0,d.call(e,k))};try{b.call(a,g,h)}catch(k){h(k)}},ae=function(a){a.executing_||(a.executing_=!0,Id(a.executeCallbacks_,a))},Zd=function(a){var b=null;a.callbackEntries_&&(b=a.callbackEntries_,a.callbackEntries_=b.next,b.next=null);a.callbackEntries_||(a.callbackEntriesTail_=null);return b};U.prototype.executeCallbacks_=function(){for(var a;a=Zd(this);)$d(this,a,this.state_,this.result_);this.executing_=!1};
var $d=function(a,b,c,d){if(c==3&&b.onRejected&&!b.always)for(;a&&a.hadUnhandledRejection_;a=a.parent_)a.hadUnhandledRejection_=!1;if(b.child)b.child.parent_=null,de(b,c,d);else try{b.always?b.onFulfilled.call(b.context):de(b,c,d)}catch(e){ee.call(null,e)}Pd.put(b)},de=function(a,b,c){b==2?a.onFulfilled.call(a.context,c):a.onRejected&&a.onRejected.call(a.context,c)},ce=function(a,b){a.hadUnhandledRejection_=!0;Id(function(){a.hadUnhandledRejection_&&ee.call(null,b)})},ee=Xa,Xd=function(a){D.call(this,
a)};Ma(Xd,D);Xd.prototype.name="cancel";var Sd=function(a,b,c){this.promise=a;this.resolve=b;this.reject=c};/*

 Copyright 2005, 2007 Bob Ippolito. All Rights Reserved.
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: MIT
*/
var V=function(a){var b=fe;this.sequence_=[];this.onCancelFunction_=b;this.defaultScope_=a||null;this.hadError_=this.fired_=!1;this.result_=void 0;this.silentlyCanceled_=this.blocking_=this.blocked_=!1;this.unhandledErrorId_=0;this.parent_=null;this.branches_=0};
V.prototype.cancel=function(a){if(this.fired_)this.result_ instanceof V&&this.result_.cancel();else{if(this.parent_){var b=this.parent_;delete this.parent_;a?b.cancel(a):(b.branches_--,b.branches_<=0&&b.cancel())}this.onCancelFunction_?this.onCancelFunction_.call(this.defaultScope_,this):this.silentlyCanceled_=!0;this.fired_||(a=new ge(this),he(this),ie(this,!1,a))}};V.prototype.continue_=function(a,b){this.blocked_=!1;ie(this,a,b)};
var ie=function(a,b,c){a.fired_=!0;a.result_=c;a.hadError_=!b;je(a)},he=function(a){if(a.fired_){if(!a.silentlyCanceled_)throw new ke(a);a.silentlyCanceled_=!1}};V.prototype.callback=function(a){he(this);ie(this,!0,a)};V.prototype.addCallback=function(a,b){return le(this,a,null,b)};V.prototype.finally=function(a){var b=this;return new Promise(function(c,d){le(b,function(e){a();c(e)},function(e){a();d(e)})})};
var le=function(a,b,c,d){var e=a.fired_;e||(b===c?b=c=T(b):(b=T(b),c=T(c)));a.sequence_.push([b,c,d]);e&&je(a);return a};V.prototype.then=function(a,b,c){var d,e,f=new U(function(g,h){e=g;d=h});le(this,e,function(g){g instanceof ge?f.cancel():d(g);return me},this);return f.then(a,b,c)};V.prototype.$goog_Thenable=!0;
var ne=function(a){return bb(a.sequence_,function(b){return typeof b[1]==="function"})},me={},je=function(a){if(a.unhandledErrorId_&&a.fired_&&ne(a)){var b=a.unhandledErrorId_,c=oe[b];c&&(B.clearTimeout(c.id_),delete oe[b]);a.unhandledErrorId_=0}a.parent_&&(a.parent_.branches_--,delete a.parent_);b=a.result_;for(var d=c=!1;a.sequence_.length&&!a.blocked_;){var e=a.sequence_.shift(),f=e[0],g=e[1];e=e[2];if(f=a.hadError_?g:f)try{var h=f.call(e||a.defaultScope_,b);h===me&&(h=void 0);h!==void 0&&(a.hadError_=
a.hadError_&&(h==b||h instanceof Error),a.result_=b=h);if(Md(b)||typeof B.Promise==="function"&&b instanceof B.Promise)d=!0,a.blocked_=!0}catch(k){b=k,a.hadError_=!0,ne(a)||(c=!0)}}a.result_=b;d&&(h=Ka(a.continue_,a,!0),d=Ka(a.continue_,a,!1),b instanceof V?(le(b,h,d),b.blocking_=!0):b.then(h,d));c&&(b=new pe(b),oe[b.id_]=b,a.unhandledErrorId_=b.id_)},ke=function(){D.call(this)};Ma(ke,D);ke.prototype.message="Deferred has already fired";ke.prototype.name="AlreadyCalledError";var ge=function(){D.call(this)};
Ma(ge,D);ge.prototype.message="Deferred was canceled";ge.prototype.name="CanceledError";var pe=function(a){this.id_=B.setTimeout(Ka(this.throwError,this),0);this.error_=a};pe.prototype.throwError=function(){delete oe[this.id_];throw this.error_;};var oe={};var te=function(){var a=Ra("https://apis.google.com/js/client.js"),b={},c=b.document||document,d=Sa(a).toString(),e=(new qd(c)).createElement("SCRIPT"),f={script_:e,timeout_:void 0},g=new V(f),h=null,k=b.timeout!=null?b.timeout:5E3;k>0&&(h=window.setTimeout(function(){qe(e,!0);var l=new re(1,"Timeout reached for loading script "+d);he(g);ie(g,!1,l)},k),f.timeout_=h);e.onload=e.onreadystatechange=function(){e.readyState&&e.readyState!="loaded"&&e.readyState!="complete"||(qe(e,b.cleanupWhenDone||!1,
h),g.callback(null))};e.onerror=function(){qe(e,!0,h);var l=new re(0,"Error while loading script "+d);he(g);ie(g,!1,l)};f=b.attributes||{};bd(f,{type:"text/javascript",charset:"UTF-8"});ud(e,f);od(e,a);se(c).appendChild(e);return g},se=function(a){var b;return(b=(a||document).getElementsByTagName("HEAD"))&&b.length!==0?b[0]:a.documentElement},fe=function(){if(this&&this.script_){var a=this.script_;a&&a.tagName=="SCRIPT"&&qe(a,!0,this.timeout_)}},qe=function(a,b,c){c!=null&&B.clearTimeout(c);a.onload=
function(){};a.onerror=function(){};a.onreadystatechange=function(){};b&&window.setTimeout(function(){xd(a)},0)},re=function(a,b){var c="Jsloader error (code #"+a+")";b&&(c+=": "+b);D.call(this,c);this.code=a};Ma(re,D);var ue=function(a){var b=a.serverUrl;var c=a.apiKey;a=a.authUser===void 0?0:a.authUser;this.serverUrl_=b;this.apiKey_=c;this.authUser_=a};ue.prototype.get=function(a,b){return ve(this,"GET",a,null,b)};
var ve=function(a,b,c,d,e){var f,g,h,k,l;return ya(function(n){if(n.nextAddress==1)return ra(n,we(),2);f=C("gapi.client");g=C("gapi.config");h=xe();k=f.getToken();f.setToken(null);g.update("googleapis.config/auth/useFirstPartyAuth",!0);g.update("googleapis.config/auth/useFirstPartyAuthV2",!0);g.update("client/xd4",!1);g.update("client/cors",!1);g.update("client/apiKey",a.apiKey_);l=f.request({root:a.serverUrl_,path:c,method:b,body:d?JSON.stringify(Ec(d)):void 0,headers:{"Content-Type":"application/json+protobuf",
"X-Goog-Api-Key":a.apiKey_,"X-Goog-AuthUser":a.authUser_}}).then(function(p){try{return Yc(e,p.body)}catch(r){}});ye(h);f.setToken(k);return n.return(l)})},xe=function(){var a=C("gapi.config"),b={};b["googleapis.config/auth/useFirstPartyAuth"]=a.get("googleapis.config/auth/useFirstPartyAuth");b["googleapis.config/auth/useFirstPartyAuthV2"]=a.get("googleapis.config/auth/useFirstPartyAuthV2");b["client/xd4"]=a.get("client/xd4");b["client/cors"]=a.get("client/cors");b["client/apiKey"]=a.get("client/apiKey");
return b},ye=function(a){for(var b=C("gapi.config"),c=x(Object.keys(a)),d=c.next();!d.done;d=c.next())d=d.value,b.update(d,a[d])},we=function(){return C("gapi.load")?ze():te().then(function(){return ze()},function(a){return Rd("Failed initializing gapi.\nGapi error: "+a)})},ze=function(){var a=Td(),b=C("gapi.client");if(b)a.resolve(b);else try{C("gapi.load")("client",{callback:function(){a.resolve(C("gapi.client"))}})}catch(c){a.reject("Failed loading gapi library: client")}return a.promise};var W=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(W,Q);W.prototype.getSeconds=function(){var a=a===void 0?Kc:a;var b=Nc(this,1);var c=typeof b;b=b==null?b:c==="bigint"?G(bc(64,b)):ic(b)?c==="string"?pc(b):qc(b):void 0;return b!=null?b:a};W.prototype.setSeconds=function(a){a=a==null?a:mc(a);return K(this,1,a,"0")};var Ae=function(){var a=(new W).setSeconds((new Date).getTimezoneOffset()*-60);return K(a,2,lc(0),0)};var Be=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Be,Q);Be.prototype.getTimezoneOffset=function(){return L(this,W,3)};Be.prototype.setTimezoneOffset=function(a){return M(this,W,3,a)};var Ce=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Ce,Q);Ce.prototype.setPlatform=function(a){return K(this,1,a==null?a:jc(a),0)};Ce.prototype.setSupportedCapabilityList=function(a){return Tc(this,3,a,jc)};Ce.prototype.setLibraryVersionInt=function(a){return K(this,4,a==null?a:lc(a),0)};var De=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(De,Q);De.prototype.setDeviceInfo=function(a){return M(this,Be,1,a)};De.prototype.setLibraryInfo=function(a){return M(this,Ce,2,a)};var Ee=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Ee,Q);Ee.prototype.getAllowedCompletionStyleList=function(){return Xc(this)};var Fe=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Fe,Q);var Ge=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Ge,Q);Ge.prototype.getPromptDelay=function(){return L(this,Fe,1)};Ge.prototype.getAllowedPromptStyleList=function(){return Xc(this)};var He=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(He,Q);He.prototype.getLanguage=function(){return Vc(this,8)};He.prototype.getCompletion=function(){return L(this,Ee,2)};He.prototype.getDisplaySettings=function(){return L(this,Ge,3)};var Ie=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Ie,Q);Ie.prototype.setIsScheduledSurvey=function(a){return P(this,1,a)};var Je=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Je,Q);var Ke=function(a){return Vc(a,1)};var Le=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Le,Q);m=Le.prototype;m.setTriggerId=function(a){return K(this,1,vc(a),"")};m.setLanguageList=function(a){return Tc(this,2,a,uc)};m.getLanguage=function(){return Wc(this,2)};m.setTestingMode=function(a){return P(this,3,a)};m.getSurveyId=function(){return Vc(this,4)};m.setSurveyId=function(a){Pc(this,4,vc(a))};var Me=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Me,Q);Me.prototype.setTriggerContext=function(a){return M(this,Le,1,a)};Me.prototype.setClientContext=function(a){return M(this,De,2,a)};Me.prototype.setScheduledSurveyContext=function(a){M(this,Ie,3,a)};var Ne=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Ne,Q);m=Ne.prototype;m.getSession=function(){return L(this,Je,1)};m.getSurveyPayload=function(){return L(this,He,2)};m.getError=function(a){return Wc(this,4,a)};m.getSurveyId=function(){return Vc(this,5)};m.setSurveyId=function(a){K(this,5,vc(a),"")};var Oe=function(){this.actions_={}};Oe.prototype.register=function(a,b,c){this.actions_[a]={callback:b,isApplicable:c||Kd}};Oe.prototype.execute=function(a,b){(a=this.actions_[a])&&a.isApplicable()&&a.callback.apply(null,b||[])};Oe.prototype.isApplicable=function(a){a=this.actions_[a];return!!a&&a.isApplicable()};Oe.prototype.register=Oe.prototype.register;var Pe=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Pe,Q);var Qe=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Qe,Q);Qe.prototype.setDirection=function(a){return K(this,8,vc(a),"")};var Re=function(a){return function(b){return Yc(a,b)}}(Qe);function Se(a){if(!a)return null;a=wc(Nc(a,4,void 0,Lc));return a===null||a===void 0?null:Ra(a)};var Te=y([""]),Ue=y(["https://www.google.com/tools/feedback/help_panel_binary.js"]);
function Ve(a,b,c,d){var e=a.helpCenterPath.startsWith("/")?a.helpCenterPath.substring(1):a.helpCenterPath,f=c.document,g=a.nonce,h=Re(b);h=L(h,Pe,10)?Se(L(h,Pe,10))||S(Te):S(Ue);var k=sd(f).createElement("SCRIPT");g&&k.setAttribute("nonce",g);k.onload=function(){c.startHelpCard({apiKey:"",context:a.helpCenterContext,directToGetHelp:!1,enableSendFeedback:!1,helpApiData:{helpApiConfig:a,productWindow:c},helpcenter:e,helpPanelStartTimeMs:a.helpPanelStartTimeMs,helpPanelTheme:a.helpPanelTheme,locale:a.locale,
nd4cSettingsIsEnabled:!1,onOpenHelpPanelCallback:d,serverData:b})};od(k,h);f.body.appendChild(k)};var We=y(["https://www.google.com/tools/feedback/"]),Xe=y(["http://localhost.corp.google.com/inapp/"]),Ye=y(["http://localhost.proxy.googlers.com/inapp/"]),Ze=y(["https://asx-frontend-autopush.corp.google.com/inapp/"]),$e=y(["https://asx-frontend-autopush.corp.google.com/tools/feedback/"]),af=y(["https://asx-frontend-autopush.corp.google.co.uk/inapp/"]),bf=y(["https://asx-frontend-autopush.corp.google.co.uk/tools/feedback/"]),cf=y(["https://asx-frontend-autopush.corp.google.de/inapp/"]),df=y(["https://asx-frontend-autopush.corp.google.de/tools/feedback/"]),
ef=y(["https://asx-frontend-autopush.corp.youtube.com/tools/feedback/"]),ff=y(["https://asx-frontend-autopush.corp.youtube.com/inapp/"]),gf=y(["https://asx-help-frontend-autopush.corp.youtube.com/tools/feedback/"]),hf=y(["https://asx-help-frontend-autopush.corp.youtube.com/inapp/"]),jf=y(["https://asx-frontend-staging.corp.google.com/inapp/"]),kf=y(["https://asx-frontend-staging.corp.google.com/tools/feedback/"]),lf=y(["https://support.google.com/inapp/"]),mf=y(["https://sandbox.google.com/inapp/"]),
nf=y(["https://sandbox.google.com/tools/feedback/"]),of=y(["https://www.google.cn/tools/feedback/"]),pf=y(["https://help.youtube.com/tools/feedback/"]),qf=y(["https://asx-frontend-staging.corp.google.com/inapp/"]),rf=y(["https://asx-frontend-staging.corp.google.com/tools/feedback/"]),sf=y(["https://localhost.corp.google.com/inapp/"]),tf=y(["https://localhost.proxy.googlers.com/inapp/"]),uf=S(We),vf=[S(Xe),S(Ye)],wf=[S(Ze),S($e),S(af),S(bf),S(cf),S(df),S(ef),S(ff),S(gf),S(hf)],xf=[S(jf),S(kf)],yf=
[uf,S(lf),S(mf),S(nf),S(of),S(pf),S(qf),S(rf),S(sf),S(tf)];la(vf);la(wf);la(xf);la(yf);var zf=function(){this.disposed_=this.disposed_;this.onDisposeCallbacks_=this.onDisposeCallbacks_};zf.prototype.disposed_=!1;zf.prototype.isDisposed=function(){return this.disposed_};zf.prototype.dispose=function(){this.disposed_||(this.disposed_=!0,this.disposeInternal())};zf.prototype[Symbol.dispose]=function(){this.dispose()};zf.prototype.disposeInternal=function(){if(this.onDisposeCallbacks_)for(;this.onDisposeCallbacks_.length;)this.onDisposeCallbacks_.shift()()};var Af=function(a,b){this.type=a;this.currentTarget=this.target=b;this.defaultPrevented=this.propagationStopped_=!1};Af.prototype.stopPropagation=function(){this.propagationStopped_=!0};Af.prototype.preventDefault=function(){this.defaultPrevented=!0};var Bf=function(){if(!B.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};B.addEventListener("test",c,b);B.removeEventListener("test",c,b)}catch(d){}return a}();var Cf=function(a,b){Af.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.timeStamp=0;this.event_=null;a&&this.init(a,b)};Ma(Cf,Af);
Cf.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.offsetX=eb||a.offsetX!==void 0?a.offsetX:a.layerX,this.offsetY=
eb||a.offsetY!==void 0?a.offsetY:a.layerY,this.clientX=a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||(c=="keypress"?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType=a.pointerType;this.state=a.state;this.timeStamp=
a.timeStamp;this.event_=a;a.defaultPrevented&&Cf.superClass_.preventDefault.call(this)};Cf.prototype.stopPropagation=function(){Cf.superClass_.stopPropagation.call(this);this.event_.stopPropagation?this.event_.stopPropagation():this.event_.cancelBubble=!0};Cf.prototype.preventDefault=function(){Cf.superClass_.preventDefault.call(this);var a=this.event_;a.preventDefault?a.preventDefault():a.returnValue=!1};var Hf="closure_listenable_"+(Math.random()*1E6|0);var If=0;var Jf=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.handler=e;this.key=++If;this.removed=this.callOnce=!1},Kf=function(a){a.removed=!0;a.listener=null;a.proxy=null;a.src=null;a.handler=null};function Lf(a){this.src=a;this.listeners={};this.typeCount_=0}Lf.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.listeners[f];a||(a=this.listeners[f]=[],this.typeCount_++);var g=Mf(a,b,d,e);g>-1?(b=a[g],c||(b.callOnce=!1)):(b=new Jf(b,this.src,f,!!d,e),b.callOnce=c,a.push(b));return b};
Lf.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.listeners))return!1;var e=this.listeners[a];b=Mf(e,b,c,d);return b>-1?(Kf(e[b]),Array.prototype.splice.call(e,b,1),e.length==0&&(delete this.listeners[a],this.typeCount_--),!0):!1};var Nf=function(a,b){var c=b.type;c in a.listeners&&cb(a.listeners[c],b)&&(Kf(b),a.listeners[c].length==0&&(delete a.listeners[c],a.typeCount_--))};
Lf.prototype.getListener=function(a,b,c,d){a=this.listeners[a.toString()];var e=-1;a&&(e=Mf(a,b,c,d));return e>-1?a[e]:null};Lf.prototype.hasListener=function(a,b){var c=a!==void 0,d=c?a.toString():"",e=b!==void 0;return $c(this.listeners,function(f){for(var g=0;g<f.length;++g)if(!(c&&f[g].type!=d||e&&f[g].capture!=b))return!0;return!1})};var Mf=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.removed&&f.listener==b&&f.capture==!!c&&f.handler==d)return e}return-1};var Of="closure_lm_"+(Math.random()*1E6|0),Pf={},Qf=0,Sf=function(a,b,c,d,e){if(d&&d.once)Rf(a,b,c,d,e);else if(Array.isArray(b))for(var f=0;f<b.length;f++)Sf(a,b[f],c,d,e);else c=Tf(c),a&&a[Hf]?a.listen(b,c,Ha(d)?!!d.capture:!!d,e):Uf(a,b,c,!1,d,e)},Uf=function(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");var g=Ha(e)?!!e.capture:!!e,h=Vf(a);h||(a[Of]=h=new Lf(a));c=h.add(b,c,d,g,f);if(!c.proxy){d=Wf();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)Bf||(e=g),e===void 0&&(e=!1),a.addEventListener(b.toString(),
d,e);else if(a.attachEvent)a.attachEvent(Xf(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");Qf++}},Wf=function(){var a=Yf,b=function(c){return a.call(b.src,b.listener,c)};return b},Rf=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)Rf(a,b[f],c,d,e);else c=Tf(c),a&&a[Hf]?a.eventTargetListeners_.add(String(b),c,!0,Ha(d)?!!d.capture:!!d,e):Uf(a,b,c,!0,d,e)},Zf=function(a,b,c,d,e){if(Array.isArray(b))for(var f=
0;f<b.length;f++)Zf(a,b[f],c,d,e);else d=Ha(d)?!!d.capture:!!d,c=Tf(c),a&&a[Hf]?a.eventTargetListeners_.remove(String(b),c,d,e):a&&(a=Vf(a))&&(b=a.getListener(b,c,d,e))&&$f(b)},$f=function(a){if(typeof a!=="number"&&a&&!a.removed){var b=a.src;if(b&&b[Hf])Nf(b.eventTargetListeners_,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(Xf(c),d):b.addListener&&b.removeListener&&b.removeListener(d);Qf--;(c=Vf(b))?(Nf(c,a),c.typeCount_==0&&
(c.src=null,b[Of]=null)):Kf(a)}}},Xf=function(a){return a in Pf?Pf[a]:Pf[a]="on"+a},Yf=function(a,b){if(a.removed)a=!0;else{b=new Cf(b,this);var c=a.listener,d=a.handler||a.src;a.callOnce&&$f(a);a=c.call(d,b)}return a},Vf=function(a){a=a[Of];return a instanceof Lf?a:null},ag="__closure_events_fn_"+(Math.random()*1E9>>>0),Tf=function(a){if(typeof a==="function")return a;a[ag]||(a[ag]=function(b){return a.handleEvent(b)});return a[ag]};var bg=function(){zf.call(this);this.eventTargetListeners_=new Lf(this);this.actualEventTarget_=this;this.parentEventTarget_=null};Ma(bg,zf);bg.prototype[Hf]=!0;m=bg.prototype;m.addEventListener=function(a,b,c,d){Sf(this,a,b,c,d)};m.removeEventListener=function(a,b,c,d){Zf(this,a,b,c,d)};
m.dispatchEvent=function(a){var b=this.parentEventTarget_;if(b){var c=[];for(var d=1;b;b=b.parentEventTarget_)c.push(b),++d}b=this.actualEventTarget_;d=a.type||a;if(typeof a==="string")a=new Af(a,b);else if(a instanceof Af)a.target=a.target||b;else{var e=a;a=new Af(d,b);bd(a,e)}e=!0;var f;if(c)for(f=c.length-1;!a.propagationStopped_&&f>=0;f--){var g=a.currentTarget=c[f];e=cg(g,d,!0,a)&&e}a.propagationStopped_||(g=a.currentTarget=b,e=cg(g,d,!0,a)&&e,a.propagationStopped_||(e=cg(g,d,!1,a)&&e));if(c)for(f=
0;!a.propagationStopped_&&f<c.length;f++)g=a.currentTarget=c[f],e=cg(g,d,!1,a)&&e;return e};m.disposeInternal=function(){bg.superClass_.disposeInternal.call(this);if(this.eventTargetListeners_){var a=this.eventTargetListeners_,b=0,c;for(c in a.listeners){for(var d=a.listeners[c],e=0;e<d.length;e++)++b,Kf(d[e]);delete a.listeners[c];a.typeCount_--}}this.parentEventTarget_=null};m.listen=function(a,b,c,d){return this.eventTargetListeners_.add(String(a),b,!1,c,d)};
var cg=function(a,b,c,d){b=a.eventTargetListeners_.listeners[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.removed&&g.capture==c){var h=g.listener,k=g.handler||g.src;g.callOnce&&Nf(a.eventTargetListeners_,g);e=h.call(k,d)!==!1&&e}}return e&&!d.defaultPrevented};bg.prototype.getListener=function(a,b,c,d){return this.eventTargetListeners_.getListener(String(a),b,c,d)};
bg.prototype.hasListener=function(a,b){return this.eventTargetListeners_.hasListener(a!==void 0?String(a):void 0,b)};var dg=function(a){try{return B.JSON.parse(a)}catch(b){}a=String(a);if(/^\s*$/.test(a)?0:/^[\],:{}\s\u2028\u2029]*$/.test(a.replace(/\\["\\\/bfnrtu]/g,"@").replace(/(?:"[^"\\\n\r\u2028\u2029\x00-\x08\x0a-\x1f]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)[\s\u2028\u2029]*(?=:|,|]|}|$)/g,"]").replace(/(?:^|:|,)(?:[\s\u2028\u2029]*\[)+/g,"")))try{return eval("("+a+")")}catch(b){}throw Error("Invalid JSON string: "+a);};var eg=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),fg=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(d>=0){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};var X=function(){bg.call(this);this.headers=new Map;this.active_=!1;this.xhr_=null;this.lastUri_="";this.inAbort_=this.inOpen_=this.inSend_=this.errorDispatched_=!1;this.timeoutInterval_=0;this.timeoutId_=null;this.responseType_="";this.progressEventsEnabled_=this.withCredentials_=!1;this.attributionReportingOptions_=this.trustToken_=null};Ma(X,bg);
var gg=/^https?$/i,hg=["POST","PUT"],ig=[],jg=function(a,b,c,d,e,f){var g=new X;ig.push(g);b&&g.listen("complete",b);g.eventTargetListeners_.add("ready",g.cleanupSend_,!0,void 0,void 0);f&&(g.timeoutInterval_=Math.max(0,f));g.withCredentials_=!0;g.send(a,c,d,e)};m=X.prototype;m.cleanupSend_=function(){this.dispose();cb(ig,this)};m.setTrustToken=function(a){this.trustToken_=a};m.setAttributionReporting=function(a){this.attributionReportingOptions_=a};
m.send=function(a,b,c,d){if(this.xhr_)throw Error("[goog.net.XhrIo] Object is active with another request="+this.lastUri_+"; newUri="+a);b=b?b.toUpperCase():"GET";this.lastUri_=a;this.errorDispatched_=!1;this.active_=!0;this.xhr_=new XMLHttpRequest;this.xhr_.onreadystatechange=T(Ka(this.onReadyStateChange_,this));this.progressEventsEnabled_&&"onprogress"in this.xhr_&&(this.xhr_.onprogress=T(Ka(function(g){this.onProgressHandler_(g,!0)},this)),this.xhr_.upload&&(this.xhr_.upload.onprogress=T(Ka(this.onProgressHandler_,
this))));try{this.inOpen_=!0,this.xhr_.open(b,String(a),!0),this.inOpen_=!1}catch(g){this.error_(5,g);return}a=c||"";c=new Map(this.headers);if(d)if(Object.getPrototypeOf(d)===Object.prototype)for(var e in d)c.set(e,d[e]);else if(typeof d.keys==="function"&&typeof d.get==="function"){e=x(d.keys());for(var f=e.next();!f.done;f=e.next())f=f.value,c.set(f,d.get(f))}else throw Error("Unknown input type for opt_headers: "+String(d));d=Array.from(c.keys()).find(function(g){return"content-type"==g.toLowerCase()});
e=B.FormData&&a instanceof B.FormData;!($a(hg,b)>=0)||d||e||c.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");b=x(c);for(d=b.next();!d.done;d=b.next())c=x(d.value),d=c.next().value,c=c.next().value,this.xhr_.setRequestHeader(d,c);this.responseType_&&(this.xhr_.responseType=this.responseType_);"withCredentials"in this.xhr_&&this.xhr_.withCredentials!==this.withCredentials_&&(this.xhr_.withCredentials=this.withCredentials_);if("setTrustToken"in this.xhr_&&this.trustToken_)try{this.xhr_.setTrustToken(this.trustToken_)}catch(g){}if("setAttributionReporting"in
this.xhr_&&this.attributionReportingOptions_)try{this.xhr_.setAttributionReporting(this.attributionReportingOptions_)}catch(g){}try{this.timeoutId_&&(clearTimeout(this.timeoutId_),this.timeoutId_=null),this.timeoutInterval_>0&&(this.timeoutId_=setTimeout(this.timeout_.bind(this),this.timeoutInterval_)),this.inSend_=!0,this.xhr_.send(a),this.inSend_=!1}catch(g){this.error_(5,g)}};m.timeout_=function(){typeof Ca!="undefined"&&this.xhr_&&(this.dispatchEvent("timeout"),this.abort(8))};
m.error_=function(){this.active_=!1;this.xhr_&&(this.inAbort_=!0,this.xhr_.abort(),this.inAbort_=!1);kg(this);lg(this)};var kg=function(a){a.errorDispatched_||(a.errorDispatched_=!0,a.dispatchEvent("complete"),a.dispatchEvent("error"))};X.prototype.abort=function(){this.xhr_&&this.active_&&(this.active_=!1,this.inAbort_=!0,this.xhr_.abort(),this.inAbort_=!1,this.dispatchEvent("complete"),this.dispatchEvent("abort"),lg(this))};
X.prototype.disposeInternal=function(){this.xhr_&&(this.active_&&(this.active_=!1,this.inAbort_=!0,this.xhr_.abort(),this.inAbort_=!1),lg(this,!0));X.superClass_.disposeInternal.call(this)};X.prototype.onReadyStateChange_=function(){if(!this.isDisposed())if(this.inOpen_||this.inSend_||this.inAbort_)mg(this);else this.onReadyStateChangeEntryPoint_()};X.prototype.onReadyStateChangeEntryPoint_=function(){mg(this)};
var mg=function(a){if(a.active_&&typeof Ca!="undefined")if(a.inSend_&&ng(a)==4)setTimeout(a.onReadyStateChange_.bind(a),0);else if(a.dispatchEvent("readystatechange"),ng(a)==4){a.active_=!1;try{try{var b=ng(a)>2?a.xhr_.status:-1}catch(g){b=-1}a:switch(b){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var c=!0;break a;default:c=!1}var d;if(!(d=c)){var e;if(e=b===0){var f=String(a.lastUri_).match(eg)[1]||null;!f&&B.self&&B.self.location&&(f=B.self.location.protocol.slice(0,-1));e=!gg.test(f?
f.toLowerCase():"")}d=e}d?(a.dispatchEvent("complete"),a.dispatchEvent("success")):kg(a)}finally{lg(a)}}};X.prototype.onProgressHandler_=function(a,b){this.dispatchEvent(og(a,"progress"));this.dispatchEvent(og(a,b?"downloadprogress":"uploadprogress"))};
var og=function(a,b){return{type:b,lengthComputable:a.lengthComputable,loaded:a.loaded,total:a.total}},lg=function(a,b){if(a.xhr_){a.timeoutId_&&(clearTimeout(a.timeoutId_),a.timeoutId_=null);var c=a.xhr_;a.xhr_=null;b||a.dispatchEvent("ready");try{c.onreadystatechange=null}catch(d){}}};X.prototype.isActive=function(){return!!this.xhr_};
var ng=function(a){return a.xhr_?a.xhr_.readyState:0},pg=function(a){if(a.xhr_){a=a.xhr_.responseText;a.indexOf(")]}'\n")==0&&(a=a.substring(5));a:{if(B.JSON)try{var b=B.JSON.parse(a);break a}catch(c){}b=dg(a)}return b}};X.prototype.getResponseHeader=function(a){if(this.xhr_&&ng(this)==4)return a=this.xhr_.getResponseHeader(a),a===null?void 0:a};X.prototype.getAllResponseHeaders=function(){return this.xhr_&&ng(this)>=2?this.xhr_.getAllResponseHeaders()||"":""};var qg=y([""]),rg=y(["https://www.google.com/tools/feedback/help_panel_binary.js"]);function sg(a,b,c,d,e,f){return ya(function(g){return g.return(new Promise(function(h){jg(""+a+"/repeater_help_panel?locale="+b+"&helpContext="+c+"&productId="+d+"&helpcenter="+e+"&openingMode="+f,function(k){k=k.target;var l=null;try{l=Yc(Qe,JSON.stringify(pg(k)))}catch(n){}h(l)})}))})}
function tg(a,b,c,d){var e=a.helpCenterPath.startsWith("/")?a.helpCenterPath.substring(1):a.helpCenterPath,f=Re(b),g=a.helpPanelMode||0,h=a.fixedHelpPanelContainer,k=a.customHelpPanelContainer;h&&g!==1?h=void 0:g!==1||h&&!N(f,5)||(g=0,h=void 0);k&&g!==2?g=2:g!==2||k&&!a.anchor||(g=0,k=void 0);var l=a.minimizeMode;g!==2||l&&l!==0?g===1&&(l=2):l=2;var n=a.openingMode;if(a.directToGetHelp)n=2;else if(a.supportContentUrl||a.defaultHelpArticleId)n=3;var p=c.document,r=a.nonce,q=L(f,Pe,10)?Se(L(f,Pe,10))||
S(qg):S(rg),t=C("document.location.href",c);a.helpCenterContext||a.context||!t||(a.context=t.substring(0,1200));t=!0;if(d){var A=JSON.stringify(d);(t=A.length<=1200)&&(a.psdJson=A)}t||(d={invalidPsd:!0});var O=a.helpPanelTheme;a.helpPanelTheme===2&&(O=c.matchMedia&&c.matchMedia("(prefers-color-scheme: dark)").matches?1:0);t=sd(p).createElement("SCRIPT");r&&t.setAttribute("nonce",r);t.onload=function(){c.startHelpPanel({helpcenter:e,apiKey:"testpage",channel:a.channel,context:a.context||a.helpCenterContext||
c.location.href,defaultHelpArticleFragment:a.defaultHelpArticleFragment,defaultHelpArticleId:a.defaultHelpArticleId,defaultHelpArticleHelpcenterPath:a.defaultHelpArticleHelpcenterPath,directToGetHelp:a.directToGetHelp||!1,openToHelpGuideEntryButtonId:a.openToHelpGuideEntryButtonId,enableHelpGuideMaximize:a.enableHelpGuideMaximize,enableHelpGuideConversationalAi:a.enableHelpGuideConversationalAi,enableHelpGuideHumanChat:a.enableHelpGuideHumanChat,internalHelpCenter:Vc(f,12),enableSendFeedback:a.enableSendFeedback||
!1,helpPanelTheme:O,locale:a.locale,nd4cSettingsIsEnabled:a.nd4cSettingsIsEnabled||!1,nd4cSettingsCountryCode:a.nd4cSettingsCountryCode||"",serverData:b,supportContentUrl:a.supportContentUrl,symptom:a.symptom,helpApiData:{helpApiConfig:a,frdProductData:a.frdProductData,productData:d,productWindow:c},helpPanelMode:g,onPromotedProductLinkClickCallback:a.onPromotedProductLinkClickCallback,fixedHelpPanelContainer:h,customHelpPanelContainer:k,openingMode:n,onMinimizeCallback:a.onMinimizeCallback,onGseEventCallback:a.onGseEventCallback,
minimizeMode:l||0,helpFlowSessionId:a.helpFlowSessionId||a.supportVisitId,helpGuideHelpCenterEmbedEntryPoint:a.helpGuideHelpCenterEmbedEntryPoint,helpGuideCommonEmbedEntryPoint:a.helpGuideCommonEmbedEntryPoint,helpGuideStartingFlow:a.helpGuideStartingFlow,gseSessionOptions:a.gseSessionOptions,helpPanelStartTimeMs:a.helpPanelStartTimeMs,disableEndUserCredentials:a.disableEndUserCredentials,gsePageUrl:a.gsePageUrl,mendelIds:a.mendelIds,productDeepLinkRegex:a.productDeepLinkRegex,onProductDeepLinkClickCallback:a.onProductDeepLinkClickCallback,
supportJourneyId:a.supportJourneyId})};od(t,q);p.body.appendChild(t)};for(var ug={en:["en-us"],ar:["ar-eg"],zh:["zh-cn","zh-hans","zh-hans-cn"],"zh-tw":["zh-hant","zh-hant-tw"],nl:["nl-nl"],"en-gb":[],fr:["fr-fr"],de:["de-de"],it:["it-it"],ja:["ja-jp"],ko:["ko-kr"],pl:["pl-pl"],pt:["pt-br"],ru:["ru-ru"],es:["es-es"],th:["th-th"],tr:["tr-tr"],"es-419":[],bg:["bg-bg"],ca:["ca-es"],hr:["hr-hr"],cs:["cs-cz"],da:["da-dk"],fil:["fil-ph","tl","tl-ph"],fi:["fi-fi"],el:["el-gr"],iw:["he","he-il","iw-il"],hi:["hi-in"],hu:["hu-hu"],id:["id-id","in","in-id"],lv:["lv-lv"],lt:["lt-lt"],
no:["no-no","nb","nb-no"],"pt-pt":[],ro:["ro-ro","mo"],sr:["sr-rs","sr-cyrl-rs"],sk:["sk-sk"],sl:["sl-sl"],sv:["sv-se"],uk:["uk-ua"],vi:["vi-vn"],fa:["fa-ir"],af:["af-za"],bn:["bn-in"],et:["et-ee"],is:["is-is"],ms:["ms-my"],mr:["mr-in"],sw:["sw-tz"],ta:["ta-in"],sq:["sq-al"],hy:["hy-am"],az:["az-az"],my:["my-mm"],ka:["ka-ge"],kk:["kk-kz"],km:["km-kh"],lo:["lo-la"],mk:["mk-mk"],mn:["mn-mn"],ne:["ne-np"],si:["si-lk"],am:["am-et"],gu:["gu-in"],kn:["kn-in"],ml:["ml-in"],te:["te-in"],ur:["ur-pk"],ky:["ky-kg"],
pa:["pa-in"],uz:["uz-uz"],"sr-latn":["sh"],"fr-ca":["fr-ca"]},vg={},wg=x(Object.keys(ug)),xg=wg.next();!xg.done;xg=wg.next()){var yg=xg.value;vg[yg]=yg;for(var zg=x(ug[yg]),Ag=zg.next();!Ag.done;Ag=zg.next())vg[Ag.value]=yg};var Bg=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Bg,Q);Bg.prototype.getTimezoneOffset=function(){return L(this,W,3)};Bg.prototype.setTimezoneOffset=function(a){return M(this,W,3,a)};var Cg=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Cg,Q);Cg.prototype.setPlatform=function(a){return K(this,1,a==null?a:jc(a),0)};Cg.prototype.setSupportedCapabilityList=function(a){return Tc(this,3,a,jc)};Cg.prototype.setLibraryVersionInt=function(a){return K(this,4,a==null?a:lc(a),0)};var Dg=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Dg,Q);Dg.prototype.setDeviceInfo=function(a){return M(this,Bg,1,a)};Dg.prototype.setLibraryInfo=function(a){return M(this,Cg,2,a)};var Eg=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Eg,Q);Eg.prototype.getAllowedCompletionStyleList=function(){return Xc(this)};var Fg=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Fg,Q);var Gg=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Gg,Q);Gg.prototype.getPromptDelay=function(){return L(this,Fg,1)};Gg.prototype.getAllowedPromptStyleList=function(){return Xc(this)};var Hg=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Hg,Q);Hg.prototype.getLanguage=function(){return Vc(this,8)};Hg.prototype.getCompletion=function(){return L(this,Eg,2)};Hg.prototype.getDisplaySettings=function(){return L(this,Gg,3)};var Ig=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Ig,Q);Ig.prototype.setIsScheduledSurvey=function(a){return P(this,1,a)};var Jg=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Jg,Q);var Kg=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Kg,Q);var Lg=function(a){return N(a,1)},Mg=function(a){var b=new Kg;return P(b,1,a)};var Ng=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Ng,Q);m=Ng.prototype;m.setTriggerId=function(a){return K(this,1,vc(a),"")};m.setLanguageList=function(a){return Tc(this,2,a,uc)};m.getLanguage=function(){return Wc(this,2)};m.setTestingMode=function(a){return P(this,3,a)};m.getSurveyId=function(){return Vc(this,4)};m.setSurveyId=function(a){Pc(this,4,vc(a))};var Og=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Og,Q);var Pg=function(a,b){return K(a,1,vc(b),"")};Og.prototype.setApiKey=function(a){return K(this,2,vc(a),"")};Og.prototype.setPlatform=function(a){return K(this,3,a==null?a:jc(a),0)};var Qg=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Qg,Q);var Rg=function(a,b){return M(a,Kg,1,b)};var Sg=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Sg,Q);Sg.prototype.setTriggerContext=function(a){return M(this,Ng,1,a)};Sg.prototype.setClientContext=function(a){return M(this,Dg,2,a)};Sg.prototype.setScheduledSurveyContext=function(a){M(this,Ig,3,a)};var Tg=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(Tg,Q);m=Tg.prototype;m.getSession=function(){return L(this,Jg,1)};m.getSurveyPayload=function(){return L(this,Hg,2)};m.getError=function(a){return Wc(this,4,a)};m.getSurveyId=function(){return Vc(this,5)};m.setSurveyId=function(a){K(this,5,vc(a),"")};var Ug=function(a){if(!a)return"";if(/^about:(?:blank|srcdoc)$/.test(a))return window.origin||"";a.indexOf("blob:")===0&&(a=a.substring(5));a=a.split("#")[0].split("?")[0];a=a.toLowerCase();a.indexOf("//")==0&&(a=window.location.protocol+a);/^[\w\-]*:\/\//.test(a)||(a=window.location.href);var b=a.substring(a.indexOf("://")+3),c=b.indexOf("/");c!=-1&&(b=b.substring(0,c));c=a.substring(0,a.indexOf("://"));if(!c)throw Error("URI is missing protocol: "+a);if(c!=="http"&&c!=="https"&&c!=="chrome-extension"&&
c!=="moz-extension"&&c!=="file"&&c!=="android-app"&&c!=="chrome-search"&&c!=="chrome-untrusted"&&c!=="chrome"&&c!=="app"&&c!=="devtools")throw Error("Invalid URI scheme in origin: "+c);a="";var d=b.indexOf(":");if(d!=-1){var e=b.substring(d+1);b=b.substring(0,d);if(c==="http"&&e!=="80"||c==="https"&&e!=="443")a=":"+e}return c+"://"+b+a};function Vg(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;n=l=0}function b(p){for(var r=g,q=0;q<64;q+=4)r[q/4]=p[q]<<24|p[q+1]<<16|p[q+2]<<8|p[q+3];for(q=16;q<80;q++)p=r[q-3]^r[q-8]^r[q-14]^r[q-16],r[q]=(p<<1|p>>>31)&4294967295;p=e[0];var t=e[1],A=e[2],O=e[3],pb=e[4];for(q=0;q<80;q++){if(q<40)if(q<20){var ma=O^t&(A^O);var Ga=1518500249}else ma=t^A^O,Ga=1859775393;else q<60?(ma=t&A|O&(t|A),Ga=2400959708):(ma=t^A^O,Ga=3395469782);ma=((p<<5|p>>>27)&4294967295)+
ma+pb+Ga+r[q]&4294967295;pb=O;O=A;A=(t<<30|t>>>2)&4294967295;t=p;p=ma}e[0]=e[0]+p&4294967295;e[1]=e[1]+t&4294967295;e[2]=e[2]+A&4294967295;e[3]=e[3]+O&4294967295;e[4]=e[4]+pb&4294967295}function c(p,r){if(typeof p==="string"){p=unescape(encodeURIComponent(p));for(var q=[],t=0,A=p.length;t<A;++t)q.push(p.charCodeAt(t));p=q}r||(r=p.length);q=0;if(l==0)for(;q+64<r;)b(p.slice(q,q+64)),q+=64,n+=64;for(;q<r;)if(f[l++]=p[q++],n++,l==64)for(l=0,b(f);q+64<r;)b(p.slice(q,q+64)),q+=64,n+=64}function d(){var p=
[],r=n*8;l<56?c(h,56-l):c(h,64-(l-56));for(var q=63;q>=56;q--)f[q]=r&255,r>>>=8;b(f);for(q=r=0;q<5;q++)for(var t=24;t>=0;t-=8)p[r++]=e[q]>>t&255;return p}for(var e=[],f=[],g=[],h=[128],k=1;k<64;++k)h[k]=0;var l,n;a();return{reset:a,update:c,digest:d,digestString:function(){for(var p=d(),r="",q=0;q<p.length;q++)r+="0123456789ABCDEF".charAt(Math.floor(p[q]/16))+"0123456789ABCDEF".charAt(p[q]%16);return r}}};var Xg=function(a,b,c){var d=String(B.location.href);return d&&a&&b?[b,Wg(Ug(d),a,c||null)].join(" "):null},Wg=function(a,b,c){var d=[],e=[];if((Array.isArray(c)?2:1)==1)return e=[b,a],ab(d,function(h){e.push(h)}),Yg(e.join(" "));var f=[],g=[];ab(c,function(h){g.push(h.key);f.push(h.value)});c=Math.floor((new Date).getTime()/1E3);e=f.length==0?[c,b,a]:[f.join(":"),c,b,a];ab(d,function(h){e.push(h)});a=Yg(e.join(" "));a=[c,a];g.length==0||a.push(g.join(""));return a.join("_")},Yg=function(a){var b=
Vg();b.update(a);return b.digestString().toLowerCase()};var Zg=function(){this.document_=document||{cookie:""}};m=Zg.prototype;m.isEnabled=function(){if(!B.navigator.cookieEnabled)return!1;if(!this.isEmpty())return!0;this.set("TESTCOOKIESENABLED","1",{maxAge:60});if(this.get("TESTCOOKIESENABLED")!=="1")return!1;this.remove("TESTCOOKIESENABLED");return!0};
m.set=function(a,b,c){var d=!1;if(typeof c==="object"){var e=c.sameSite;d=c.secure||!1;var f=c.domain||void 0;var g=c.path||void 0;var h=c.maxAge}if(/[;=\s]/.test(a))throw Error('Invalid cookie name "'+a+'"');if(/[;\r\n]/.test(b))throw Error('Invalid cookie value "'+b+'"');h===void 0&&(h=-1);this.document_.cookie=a+"="+b+(f?";domain="+f:"")+(g?";path="+g:"")+(h<0?"":h==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+h*1E3)).toUTCString())+(d?";secure":"")+(e!=null?
";samesite="+e:"")};m.get=function(a,b){for(var c=a+"=",d=(this.document_.cookie||"").split(";"),e=0,f;e<d.length;e++){f=Ya(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};m.remove=function(a,b,c){var d=this.containsKey(a);this.set(a,"",{maxAge:0,path:b,domain:c});return d};m.getKeys=function(){return $g(this).keys};m.getValues=function(){return $g(this).values};m.isEmpty=function(){return!this.document_.cookie};m.containsKey=function(a){return this.get(a)!==void 0};
m.clear=function(){for(var a=$g(this).keys,b=a.length-1;b>=0;b--)this.remove(a[b])};var $g=function(a){a=(a.document_.cookie||"").split(";");for(var b=[],c=[],d,e,f=0;f<a.length;f++)e=Ya(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));return{keys:b,values:c}};var ah=function(a,b,c,d){(a=B[a])||typeof document==="undefined"||(a=(new Zg).get(b));return a?Xg(a,c,d):null};function bh(a){Kb(a);Qb(a);return Qb(a)?Number(a):String(a)};var ch=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(ch,Q);m=ch.prototype;m.getEnableSsEngine=function(){return N(this,2)};m.getEnableAwr=function(){return N(this,3)};m.getAlohaAutoGaRollout=function(){return N(this,5)};m.getEnableConfigurator=function(){return N(this,6)};m.getEnableMweb=function(){return N(this,7)};var eh=function(){var a=dh();return P(a,7,!0)};ch.prototype.getEnableCtlConsentCheckbox=function(){return N(this,8)};
ch.prototype.getEnableIframe=function(){return N(this,9)};var dh=function(){var a=new ch;a=P(a,5,!0);a=P(a,2,!0);a=P(a,4,!1);a=P(a,8,!0);return P(a,9,!0)};m=ch.prototype;m.getEnableScreenshotNudge=function(){return N(this,10)};m.getEnableWebStartupConfigEndpoint=function(){return N(this,11)};m.getEnableJunkNudge=function(){return N(this,12)};m.getEnableConfiguratorLocale=function(){return N(this,13)};m.getEnableTinyNoPointer=function(){return N(this,14)};
m.getEnableSupportSessionLogging=function(){return N(this,15)};m.getEnableFileUploadForScreenshot=function(){return N(this,16)};m.getEnableDirectDeflectionForSingleCategory=function(){return N(this,17)};m.getEnableImageSanitization=function(){return N(this,18)};m.getEnableAlohaBinarySplit=function(){return N(this,19)};m.getEnableDbFeedbackIntents=function(){return N(this,20)};m.getEnableMarkMandatoryFieldsWithRequired=function(){return N(this,21)};
m.getEnableFeedbackCategoryCustomUi=function(){return N(this,22)};m.getEnableRealtimeCtl=function(){return N(this,23)};var fh=function(a){this.internalArrayDoNotAccessOrElseMightBeUndefinedWhoKnows=J(a)};w(fh,Q);function gh(a){return hh.some(function(b){return b.test(a)})}var hh=[/https:\/\/sandbox\.google\.com\/tools\/feedback/,/https:\/\/feedback-frontend-qual[a-z0-9.]*\.google\.com\/inapp/,/https:\/\/feedback-frontend-qual[a-z0-9.]*\.google\.com\/tools\/feedback/,/https:\/\/.*\.googleusercontent\.com\/inapp/];var ih="af am ar-EG ar-JO ar-MA ar-SA ar-XB ar az be bg bn bs ca cs cy da de-AT de-CH de el en en-GB en-AU en-CA en-IE en-IN en-NZ en-SG en-XA en-XC en-ZA es es-419 es-AR es-BO es-CL es-CO es-CR es-DO es-EC es-GT es-HN es-MX es-NI es-PA es-PE es-PR es-PY es-SV es-US es-UY es-VE et eu fa fi fil fr-CA fr-CH fr gl gsw gu he hi hr hu hy id in is it iw ja ka kk km kn ko ky ln lo lt lv mk ml mn mo mr ms my nb ne nl no pa pl pt pt-BR pt-PT ro ru si sk sl sq sr-Latn sr sv sw ta te th tl tr uk ur uz vi zh zh-CN zh-HK zh-TW zu".split(" ");var jh=y(["https://www.gstatic.com/uservoice/feedback/client/web/","/main_light_binary.js"]),kh=y(["https://www.gstatic.com/uservoice/feedback/client/web/","/main_binary__",".js"]);
function lh(a,b){var c;var d=(c=a.formContent)==null?void 0:c.locale;c=d==null?void 0:d.split("-")[0];d=d&&ih.includes(d)?d:c&&ih.includes(c)?c:void 0;d=(d!=null?d:"en").replaceAll("-","_").toLowerCase();var e;a=((e=a.initializationData)==null?0:e.useNightlyRelease)?"nightly":"live";var f;return(b==null?0:(f=b.getEnableAlohaBinarySplit)==null?0:f.call(b))?S(jh,a):S(kh,a,d)};var mh,nh;function oh(a,b,c,d){if(mh)return mh;var e=lh(a,d);return mh=b.feedbackV2GlobalObject?Promise.resolve(b.feedbackV2GlobalObject):new Promise(function(f,g){var h=wd(document,"SCRIPT");od(h,e);h.onload=function(){b.feedbackV2GlobalObject?f(b.feedbackV2GlobalObject):g(Error("feedbackV2GlobalObject not found on window."))};h.onerror=function(){g(Error("Feedback binary script tag failed to load: "+e.toString()))};c.body.appendChild(h)})}
function ph(a,b,c,d){if(nh)return nh;var e=lh(a,d);return nh=b.feedbackV2GlobalObject?Promise.resolve(b.feedbackV2GlobalObject):new Promise(function(f,g){var h=wd(document,"SCRIPT");od(h,e);h.onload=function(){b.feedbackV2GlobalObject?f(b.feedbackV2GlobalObject):g(Error("feedbackV2GlobalObject not found on window."))};h.onerror=function(){g(Error("Feedback binary script tag failed to load: "+e.toString()))};c.body.appendChild(h)})}
function qh(a,b,c,d,e){e=e===void 0?!0:e;var f,g,h,k,l;return ya(function(n){switch(n.nextAddress){case 1:return f=Date.now(),ra(n,oh(a,c,d,b),2);case 2:g=n.yieldResult;if(!(e||((k=a.initializationData)==null?0:k.useNightlyRelease)||((l=a.initializationData)==null?0:l.isLocalServer))){h=g.initializeFeedbackClient(a,f,b);n.nextAddress=3;break}return ra(n,g.initializeFeedbackClientAsync(a,f,b),4);case 4:h=n.yieldResult;case 3:return h.initiateAloha(),n.return(h)}})}
function rh(a,b,c,d){var e,f,g;return ya(function(h){if(h.nextAddress==1)return e=Date.now(),ra(h,ph(a,c,d.document,b),2);if(h.nextAddress!=3)return f=h.yieldResult,ra(h,f.initializeFeedbackClientAsync(a,e,b,d),3);g=h.yieldResult;g.initiateAloha();return h.return(g)})}
function sh(a,b,c){var d=!0;d=d===void 0?!0:d;var e,f,g,h,k,l,n,p,r,q;return ya(function(t){e=c||B;if((f=b)==null?0:(h=(g=f).getEnableAlohaBinarySplit)==null?0:h.call(g)){k=e;if(k.isFormOpened)throw l=Error("Form is either loading or already opened"),l.name="DuplicateFormError",l;k.isFormOpened=!0;a.callbacks=a.callbacks||{};n=a.callbacks.onClose||function(){};a.callbacks.onClose=function(A){k.isFormOpened=!1;n(A)};try{return t.return(rh(a,b,k,e))}catch(A){throw k.isFormOpened=!1,A;}}else{p=e;if(p.isFormOpened)throw r=
Error("Form is either loading or already opened"),r.name="DuplicateFormError",r;p.isFormOpened=!0;a.callbacks=a.callbacks||{};q=a.callbacks.onClose||function(){};a.callbacks.onClose=function(A){p.isFormOpened=!1;q(A)};try{return t.return(qh(a,b,p,e.document,d))}catch(A){throw p.isFormOpened=!1,A;}}t.nextAddress=0})};function th(a,b){return ya(function(c){return c.return(new Promise(function(d){var e=uh(b!=null?b:"")+"/aloha_form_properties?productId="+a;jg(e,function(f){f=f.target;var g=null;try{g=Yc(fh,JSON.stringify(pg(f)))}catch(h){f=new fh,g=eh(),g=P(g,10,!0),g=P(g,12,!0),g=P(g,13,!1),g=P(g,14,!0),g=P(g,15,!0),g=P(g,20,!1),g=M(f,ch,1,g)}d(g)},"GET","",{},2E3)}))})}function uh(a){return gh(a)?a:"https://www.google.com/tools/feedback"};var vh=function(a,b,c){a.timeOfStartCall=(new Date).getTime();var d=c||B,e=d.document,f=a.nonce||nd(d.document);f&&!a.nonce&&(a.nonce=f);if(a.flow=="help"){var g=C("document.location.href",d);!a.helpCenterContext&&g&&(a.helpCenterContext=g.substring(0,1200));g=!0;if(b&&JSON&&JSON.stringify){var h=JSON.stringify(b);(g=h.length<=1200)&&(a.psdJson=h)}g||(b={invalidPsd:!0})}b=[a,b,c];d.GOOGLE_FEEDBACK_START_ARGUMENTS=b;c=a.feedbackServerUri||"//www.google.com/tools/feedback";if(g=d.GOOGLE_FEEDBACK_START)g.apply(d,
b);else{d=c+"/load.js?";for(var k in a)b=a[k],b==null||Ha(b)||(d+=encodeURIComponent(k)+"="+encodeURIComponent(b)+"&");a=sd(e).createElement("SCRIPT");f&&a.setAttribute("nonce",f);od(a,Ra(d));e.body.appendChild(a)}},wh=function(a,b,c,d){var e,f;ya(function(g){e=c||B;var h=a.serverEnvironment==="DEV",k=c||B;k=a.nonce||nd(k.document);h={integrationKeys:{productId:a.productId,feedbackBucket:a.bucket,triggerId:a.triggerId},callbacks:{onClose:a.callback,onLoad:a.onLoadCallback},formContent:{locale:a.locale,
disableScreenshot:a.disableScreenshotting,productDisplayName:void 0,announcement:void 0,issueCategories:void 0,includeSeveritySelection:void 0,customImageSrc:void 0,thankYouMessage:void 0,userEmail:void 0,defaultFormInputValues:void 0,defaultFormInputValuesString:void 0,abuseLink:a.abuseLink,additionalDataConsent:a.additionalDataConsent},initializationData:{isLocalServer:h,nonce:k,useNightlyRelease:h,feedbackJsUrl:void 0,feedbackCssUrl:void 0,feedbackJsUrlSerialized:void 0,feedbackCssUrlSerialized:void 0,
submissionServerUri:a.feedbackServerUri,colorScheme:a.colorScheme},extraData:{productVersion:a.productVersion,authUser:a.authuser,configuratorId:a.configuratorId,customZIndex:a.customZIndex,tinyNoPointer:a.tinyNoPointer,allowNonLoggedInFeedback:a.allowNonLoggedInFeedback,enableAnonymousFeedback:a.enableAnonymousFeedback}};b&&(k=new Map(Object.entries(b)),h.extraData.productSpecificData=k);f=h;return ra(g,sh(f,d,e),0)})},xh=function(a,b,c){try{if(a.flow==="help"){var d=a.helpCenterPath.replace(/^\//,
"");md(c||window,"https://support.google.com/"+d)}else a.flow==="submit"?vh(a,b,c):th(a.productId,a.feedbackServerUri).then(function(e){e=L(e,ch,1);var f=!fb||(e==null?void 0:e.getEnableMweb()),g=!a.tinyNoPointer||(e==null?void 0:e.getEnableTinyNoPointer());!e||e.getAlohaAutoGaRollout()&&f&&g?wh(a,b,c,e):vh(a,b,c)},function(e){e&&e.name!=="DuplicateFormError"&&vh(a,b,c)})}catch(e){wh(a,b,c,null)}};Da("userfeedback.api.startFeedback",xh);function yh(a,b){var c=fd(a);if(!zh.test(c))throw Error("Invalid TrustedResourceUrl format: "+c);a=c.replace(Ah,function(d,e){if(!Object.prototype.hasOwnProperty.call(b,e))throw Error('Found marker, "'+e+'", in format string, "'+c+'", but no valid label mapping found in args: '+JSON.stringify(b));d=b[e];return d instanceof ed?fd(d):encodeURIComponent(String(d))});return Ra(a)}var Ah=/%{(\w+)}/g,zh=RegExp("^((https:)?//[0-9a-z.:[\\]-]+/|/[^/\\\\]|[^:/\\\\%]+/|[^:/\\\\%]*[?#]|about:blank#)","i");var Bh=function(){};Bh.prototype.next=function(){return Ch};var Ch={done:!0,value:void 0};Bh.prototype.__iterator__=function(){return this};var Dh=function(a){if(a instanceof Bh)return a;if(typeof a.__iterator__=="function")return a.__iterator__(!1);if(Fa(a)){var b=0,c=new Bh;c.next=function(){for(;;){if(b>=a.length)return Ch;if(b in a)return{value:a[b++],done:!1};b++}};return c}throw Error("Not implemented");};function Eh(a){this.elements_={};if(a)for(var b=0;b<a.length;b++)this.elements_[Fh(a[b])]=null;for(var c in Object.prototype);}var Gh={},Fh=function(a){return a in Gh||String(a).charCodeAt(0)==32?" "+a:a},Hh=function(a){return a.charCodeAt(0)==32?a.slice(1):a};m=Eh.prototype;m.add=function(a){this.elements_[Fh(a)]=null};m.clear=function(){this.elements_={}};m.clone=function(){var a=new Eh,b;for(b in this.elements_)a.elements_[b]=null;return a};m.contains=function(a){return Fh(a)in this.elements_};
m.has=function(a){return this.contains(a)};m.equals=function(a){return Ih(this,a)&&Ih(a,this)};m.forEach=function(a,b){for(var c in this.elements_)a.call(b,Hh(c),void 0,this)};m.values=Object.keys?function(){return Object.keys(this.elements_).map(Hh,this)}:function(){var a=[],b;for(b in this.elements_)a.push(Hh(b));return a};m.getValues=function(){return this.values()};m.isEmpty=function(){for(var a in this.elements_)return!1;return!0};
var Ih=function(a,b){for(var c in a.elements_)if(!(c in b.elements_))return!1;return!0};Eh.prototype.delete=function(a){a=Fh(a);return a in this.elements_?(delete this.elements_[a],!0):!1};Eh.prototype.remove=function(a){return this.delete(a)};Eh.prototype.__iterator__=function(){return Dh(this.getValues())};var Jh=function(a){if(a.getValues&&typeof a.getValues=="function")return a.getValues();if(typeof Map!=="undefined"&&a instanceof Map||typeof Set!=="undefined"&&a instanceof Set)return Array.from(a.values());if(typeof a==="string")return a.split("");if(Fa(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}b=[];c=0;for(d in a)b[c++]=a[d];return b},Kh=function(a){if(a.getKeys&&typeof a.getKeys=="function")return a.getKeys();if(!a.getValues||typeof a.getValues!="function"){if(typeof Map!==
"undefined"&&a instanceof Map)return Array.from(a.keys());if(!(typeof Set!=="undefined"&&a instanceof Set)){if(Fa(a)||typeof a==="string"){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}b=[];c=0;for(var d in a)b[c++]=d;return b}}},Lh=function(a,b,c){if(a.forEach&&typeof a.forEach=="function")a.forEach(b,c);else if(Fa(a)||typeof a==="string")Array.prototype.forEach.call(a,b,c);else for(var d=Kh(a),e=Jh(a),f=e.length,g=0;g<f;g++)b.call(c,e[g],d&&d[g],a)};var Mh=function(a){this.domain_=this.userInfo_=this.scheme_="";this.port_=null;this.fragment_=this.path_="";this.ignoreCase_=this.isReadOnly_=!1;if(a instanceof Mh){this.ignoreCase_=a.ignoreCase_;Nh(this,a.scheme_);var b=a.userInfo_;Y(this);this.userInfo_=b;b=a.domain_;Y(this);this.domain_=b;Oh(this,a.port_);b=a.path_;Y(this);this.path_=b;Ph(this,a.queryData_.clone());a=a.fragment_;Y(this);this.fragment_=a}else a&&(b=String(a).match(eg))?(this.ignoreCase_=!1,Nh(this,b[1]||"",!0),a=b[2]||"",Y(this),
this.userInfo_=Qh(a),a=b[3]||"",Y(this),this.domain_=Qh(a,!0),Oh(this,b[4]),a=b[5]||"",Y(this),this.path_=Qh(a,!0),Ph(this,b[6]||"",!0),a=b[7]||"",Y(this),this.fragment_=Qh(a)):(this.ignoreCase_=!1,this.queryData_=new Rh(null,this.ignoreCase_))};
Mh.prototype.toString=function(){var a=[],b=this.scheme_;b&&a.push(Sh(b,Th,!0),":");var c=this.domain_;if(c||b=="file")a.push("//"),(b=this.userInfo_)&&a.push(Sh(b,Th,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.port_,c!=null&&a.push(":",String(c));if(c=this.path_)this.domain_&&c.charAt(0)!="/"&&a.push("/"),a.push(Sh(c,c.charAt(0)=="/"?Uh:Vh,!0));(c=this.queryData_.toString())&&a.push("?",c);(c=this.fragment_)&&a.push("#",Sh(c,Wh));return a.join("")};
Mh.prototype.resolve=function(a){var b=this.clone(),c=!!a.scheme_;c?Nh(b,a.scheme_):c=!!a.userInfo_;if(c){var d=a.userInfo_;Y(b);b.userInfo_=d}else c=!!a.domain_;c?(d=a.domain_,Y(b),b.domain_=d):c=a.port_!=null;d=a.path_;if(c)Oh(b,a.port_);else if(c=!!a.path_){if(d.charAt(0)!="/")if(this.domain_&&!this.path_)d="/"+d;else{var e=b.path_.lastIndexOf("/");e!=-1&&(d=b.path_.slice(0,e+1)+d)}e=d;if(e==".."||e==".")d="";else if(e.indexOf("./")!=-1||e.indexOf("/.")!=-1){d=e.lastIndexOf("/",0)==0;e=e.split("/");
for(var f=[],g=0;g<e.length;){var h=e[g++];h=="."?d&&g==e.length&&f.push(""):h==".."?((f.length>1||f.length==1&&f[0]!="")&&f.pop(),d&&g==e.length&&f.push("")):(f.push(h),d=!0)}d=f.join("/")}else d=e}c?(Y(b),b.path_=d):c=a.queryData_.toString()!=="";c?Ph(b,a.queryData_.clone()):c=!!a.fragment_;c&&(a=a.fragment_,Y(b),b.fragment_=a);return b};Mh.prototype.clone=function(){return new Mh(this)};
var Nh=function(a,b,c){Y(a);a.scheme_=c?Qh(b,!0):b;a.scheme_&&(a.scheme_=a.scheme_.replace(/:$/,""));return a},Oh=function(a,b){Y(a);if(b){b=Number(b);if(isNaN(b)||b<0)throw Error("Bad port number "+b);a.port_=b}else a.port_=null},Ph=function(a,b,c){Y(a);b instanceof Rh?(a.queryData_=b,a.queryData_.setIgnoreCase(a.ignoreCase_)):(c||(b=Sh(b,Xh)),a.queryData_=new Rh(b,a.ignoreCase_))};Mh.prototype.getQuery=function(){return this.queryData_.toString()};
Mh.prototype.removeParameter=function(a){Y(this);this.queryData_.remove(a);return this};var Y=function(a){if(a.isReadOnly_)throw Error("Tried to modify a read-only Uri");};Mh.prototype.setIgnoreCase=function(a){this.ignoreCase_=a;this.queryData_&&this.queryData_.setIgnoreCase(a)};
var Qh=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""},Sh=function(a,b,c){return typeof a==="string"?(a=encodeURI(a).replace(b,Yh),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null},Yh=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)},Th=/[#\/\?@]/g,Vh=/[#\?:]/g,Uh=/[#\?]/g,Xh=/[#\?@]/g,Wh=/#/g,Rh=function(a,b){this.count_=this.keyMap_=null;this.encodedQuery_=a||null;this.ignoreCase_=!!b},Zh=function(a){a.keyMap_||(a.keyMap_=
new Map,a.count_=0,a.encodedQuery_&&fg(a.encodedQuery_,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))};m=Rh.prototype;m.add=function(a,b){Zh(this);this.encodedQuery_=null;a=$h(this,a);var c=this.keyMap_.get(a);c||this.keyMap_.set(a,c=[]);c.push(b);this.count_+=1;return this};m.remove=function(a){Zh(this);a=$h(this,a);return this.keyMap_.has(a)?(this.encodedQuery_=null,this.count_-=this.keyMap_.get(a).length,this.keyMap_.delete(a)):!1};
m.clear=function(){this.keyMap_=this.encodedQuery_=null;this.count_=0};m.isEmpty=function(){Zh(this);return this.count_==0};m.containsKey=function(a){Zh(this);a=$h(this,a);return this.keyMap_.has(a)};m.forEach=function(a,b){Zh(this);this.keyMap_.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};
m.getKeys=function(){Zh(this);for(var a=Array.from(this.keyMap_.values()),b=Array.from(this.keyMap_.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};m.getValues=function(a){Zh(this);var b=[];if(typeof a==="string")this.containsKey(a)&&(b=b.concat(this.keyMap_.get($h(this,a))));else{a=Array.from(this.keyMap_.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};
m.set=function(a,b){Zh(this);this.encodedQuery_=null;a=$h(this,a);this.containsKey(a)&&(this.count_-=this.keyMap_.get(a).length);this.keyMap_.set(a,[b]);this.count_+=1;return this};m.get=function(a,b){if(!a)return b;a=this.getValues(a);return a.length>0?String(a[0]):b};
m.toString=function(){if(this.encodedQuery_)return this.encodedQuery_;if(!this.keyMap_)return"";for(var a=[],b=Array.from(this.keyMap_.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.getValues(d);for(var f=0;f<d.length;f++){var g=e;d[f]!==""&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.encodedQuery_=a.join("&")};
m.clone=function(){var a=new Rh;a.encodedQuery_=this.encodedQuery_;this.keyMap_&&(a.keyMap_=new Map(this.keyMap_),a.count_=this.count_);return a};var $h=function(a,b){b=String(b);a.ignoreCase_&&(b=b.toLowerCase());return b};
Rh.prototype.setIgnoreCase=function(a){a&&!this.ignoreCase_&&(Zh(this),this.encodedQuery_=null,this.keyMap_.forEach(function(b,c){var d=c.toLowerCase();c!=d&&(this.remove(c),this.remove(d),b.length>0&&(this.encodedQuery_=null,this.keyMap_.set($h(this,d),db(b)),this.count_+=b.length))},this));this.ignoreCase_=a};Rh.prototype.extend=function(a){for(var b=0;b<arguments.length;b++)Lh(arguments[b],function(c,d){this.add(d,c)},this)};new Eh("head HEAD link LINK style STYLE meta META defs DEFS script SCRIPT html HTML base BASE colgroup COLGROUP col COL wbr WBR content CONTENT slot SLOT".split(" "));new Eh("svg SVG polygon POLYGON g G br BR".split(" "));function ai(a,b){a=new Mh(a);return b===void 0||b?Nh(a,a.scheme_||location.protocol).toString():Nh(a,"http").toString()};var bi=y(["https://feedback.googleusercontent.com/resources/annotator.css"]),ci=y(["https://feedback.googleusercontent.com/resources/render_frame2.html"]);S(bi);S(ci);var di=function(a){var b=a||{};a=b.serverUri;var c=a+"/%{resource}",d={resource:R("chat_load.js")};b=b.https;var e=[R("//www.google.com/tools/feedback/%{resource}"),R("https://www.google.com/tools/feedback/%{resource}"),R("https://support.google.com/inapp/%{resource}"),R("https://sandbox.google.com/inapp/%{resource}"),R("https://feedback2-test.corp.google.com/inapp/%{resource}"),R("https://feedback2-test.corp.googleusercontent.com/inapp/%{resource}"),R("https://sandbox.google.com/tools/feedback/%{resource}"),
R("https://feedback2-test.corp.google.com/tools/feedback/%{resource}"),R("https://feedback2-test.corp.googleusercontent.com/tools/feedback/%{resource}"),R("https://www.google.cn/tools/feedback/%{resource}")].filter(function(f){return fd(f)==c})[0];if(e)return yh(e,d);a=ai(a,b===void 0||!!b);a=Va(jd(a));return yh(R("//www.google.com/tools/feedback/%{resource}"),d)};Da("userfeedback.api.help.startHelpWithChatSupport",function(a,b){a.flow="help";xh(a,b)});var ei=function(a,b){var c=a.serverUri||"//www.google.com/tools/feedback";B.GOOGLE_HELP_CHAT_ARGUMENTS=arguments;var d=wd(document,"SCRIPT");c=di({serverUri:c});od(d,c);window.document.body.appendChild(d)};Da("userfeedback.api.help.loadChatSupport",ei);var fi=y(["https://www.gstatic.com/uservoice/surveys/resources/","/js/survey/survey_binary__",".js"]),gi=y(["https://gstatic.com/uservoice/surveys/resources/","/js/survey/survey_","_",".css"]),hi=Date.now(),ii=/uservoice\/surveys\/resources\/(non)?prod\/js\/survey\/survey_(dark|light)_(ltr|rtl)/gi,ji=/uservoice\/surveys\/resources\/(non)?prod\/js\/survey\/survey_binary__/gi,Z=function(a,b){this.productId_=a;this.receiverUri_=b.receiverUri;this.locale_=b.locale||b.locale||"en".replace(/-/g,"_");this.window_=
b.window||b.window||top;this.productData_=b.productData||b.productData||{};a:{if(a=b.frdProductDataSerializedJspb||b.frdProductDataSerializedJspb)try{var c=ib(a);break a}catch(e){c=void 0;break a}c=(c=b.frdProductData||b.frdProductData)?ki(c):void 0}this.frdProductDataBase64EncodedString_=c;this.helpCenterPath_=b.helpCenterPath||b.helpCenterPath||"";this.helpcenter=this.helpCenterPath_.startsWith("/")?this.helpCenterPath_.substring(1):this.helpCenterPath_;this.apiKey_=b.apiKey||b.apiKey||"";this.renderApiUri_=
b.renderApiUri||b.renderApiUri||"";this.asxUiUri_=b.asxUiUri||b.asxUiUri||"";var d;(b=b.nonce||b.nonce)||(b=(d=this.window_)==null?void 0:d.document,b=nd(b));this.nonce_=b;this.surveyStartupConfig_=Rg(new Qg,Mg(!1));this.thirdPartyDomainSupportEnabled_=!1};m=Z.prototype;m.startFeedback=function(a){var b=window.GOOGLE_FEEDBACK_DESTROY_FUNCTION;b&&b();xh(li(this,a),this.productData_,this.window_)};m.updateProductData=function(a){this.productData_=Object.assign({},this.productData_,a)};
m.updateContext=function(a){var b=C("gapi.rpc");b&&document.getElementById("help_panel_main_frame")!==null&&(a||(a=C("document.location.href",window).substring(0,1200)),b.setup(""),b.sendHandshake("help_panel_main_frame/help_panel_content_frame",""),b.call("help_panel_main_frame/help_panel_content_frame","adaptContext",null,a))};
m.startHelp=function(a){var b=this,c=document.getElementById("help_panel_main_frame");if(c&&c.style.visibility==="hidden"){if(c.style.visibility="visible",a.onRestoreCallback)a.onRestoreCallback()}else{c=(new Date).getTime();var d=a?mi(this,a,c):{};a=d.openingMode||0;try{sg(this.receiverUri_||"https://www.google.com/tools/feedback",this.locale_,d.helpCenterContext,this.productId_,this.helpcenter,a).then(function(e){var f=d.fixedHelpPanelContainer;if(f){var g=f.style.width;f.style.width="0";f.style.display=
"none";f.style.width=g!=null?g:"360px";f.replaceChildren()}else xd(document.getElementById("help_panel_main_frame"));tg(d,JSON.stringify(Ec(e)),b.window_,b.productData_)})}catch(e){md(window,"https://support.google.com/"+this.helpcenter)}}};
m.startHelpCard=function(a,b){var c=this,d=(new Date).getTime(),e=a?mi(this,a,d):{};a=e.openingMode||0;try{sg(this.receiverUri_||"https://www.google.com/tools/feedback",this.locale_,e.helpCenterContext,this.productId_,this.helpcenter,a).then(function(f){var g;(g=document.getElementById("help_card_main_frame"))==null||g.remove();Ve(e,JSON.stringify(Ec(f)),c.window_,b||void 0)})}catch(f){md(window,"https://support.google.com/"+this.helpcenter)}};
var li=function(a,b){b=b||{};return{bucket:b.bucket||b.bucket,locale:a.locale_,callback:b.onend||b.onend||function(){},onLoadCallback:b.onLoadCallback||b.onLoadCallback,serverUri:b.serverUri||b.serverUri||a.receiverUri_,productId:a.productId_,productVersion:b.productVersion||b.productVersion,authuser:b.authuser||b.authuser,abuseLink:b.abuseLink||b.abuseLink,customZIndex:b.customZIndex||b.customZIndex,flow:b.flow||b.flow||"wizard",enableAnonymousFeedback:b.enableAnonymousFeedback||b.enableAnonymousFeedback,
allowNonLoggedInFeedback:b.allowNonLoggedInFeedback||b.allowNonLoggedInFeedback,tinyNoPointer:b.tinyNoPointer||b.tinyNoPointer,disableScreenshotAtStartup:b.disableScreenshotAtStartup||b.disableScreenshotAtStartup,disableScreenshotting:b.disableScreenshotting||b.disableScreenshotting,feedbackServerUri:b.feedbackServerUri||b.feedbackServerUri,colorScheme:b.colorScheme||b.colorScheme,triggerId:b.triggerId||b.triggerId,serverEnvironment:b.serverEnvironment||b.serverEnvironment}},mi=function(a,b,c){var d=
b||{};b=li(a,b);var e,f,g,h,k,l,n,p=d.anchor,r=d.channel,q=d.context,t=a.helpCenterPath_,A=d.helpFlowSessionId,O=d.enableSendFeedback||!1,pb=d.defaultHelpArticleId,ma=d.supportContentUrl,Ga=d.helpPanelTheme,ti=d.nd4cSettings?d.nd4cSettings.isEnabled:!1,ui=d.nd4cSettings?d.nd4cSettings.countryCode:"",vi=d.userIp?d.userIp:"",wi=d.defaultHelpArticleFragment,xi=d.suggestHost,yi=a.renderApiUri_,zi=d.symptom,Ai=d.timezone,Bi=d.directToGetHelp||!1,Ci=(e=d.helpGuideOptions)==null?void 0:e.openToHelpGuideEntryButtonId;
e=(f=d.helpGuideOptions)==null?void 0:f.enableHelpGuideMaximize;f=((g=d.helpGuideOptions)==null?void 0:g.enableHelpGuideConversationalAi)===!1?!1:!0;g=(h=d.helpGuideOptions)==null?void 0:h.enableHelpGuideHumanChat;h=a.window_.location.protocol+"//"+a.window_.location.host;var Di=d.helpPanelMode,Ei=d.fixedHelpPanelContainer,Fi=d.customHelpPanelContainer,Gi=a.frdProductDataBase64EncodedString_,Hi=d.onCloseCallback,Ii=d.onMinimizeCallback,Ji=d.onLoadCallback,Ki=d.onPromotedProductLinkClickCallback,Li=
d.onGseEventCallback,Mi=d.openingMode,Ni=d.minimizeMode,Oi=((k=d.helpGuideOptions)==null?0:k.helpGuideHelpCenterEmbedEntryPoint)?ki(d.helpGuideOptions.helpGuideHelpCenterEmbedEntryPoint):void 0;k=((l=d.helpGuideOptions)==null?0:l.helpGuideCommonEmbedEntryPoint)?ki(d.helpGuideOptions.helpGuideCommonEmbedEntryPoint):void 0;a:{var Df;if((Df=d.helpGuideOptions)==null?0:Df.helpGuideStartingFlowSerializedJspb)try{var zd=ib(d.helpGuideOptions.helpGuideStartingFlowSerializedJspb);break a}catch(Pi){zd=void 0;
break a}var Ef;zd=((Ef=d.helpGuideOptions)==null?0:Ef.helpGuideStartingFlow)?ki(d.helpGuideOptions.helpGuideStartingFlow):void 0}a:{var Ff;if((Ff=d.helpGuideOptions)==null?0:Ff.gseSessionOptionsSerializedJspb)try{var Ad=ib(d.helpGuideOptions.gseSessionOptionsSerializedJspb);break a}catch(Pi){Ad=void 0;break a}var Gf;Ad=((Gf=d.helpGuideOptions)==null?0:Gf.gseSessionOptions)?ki(d.helpGuideOptions.gseSessionOptions):void 0}a={anchor:p,channel:r,flow:"help",helpCenterContext:q,helpCenterPath:t,helpFlowSessionId:A,
enableSendFeedback:O,defaultHelpArticleId:pb,supportContentUrl:ma,helpPanelTheme:Ga,nd4cSettingsIsEnabled:ti,nd4cSettingsCountryCode:ui,userIp:vi,defaultHelpArticleFragment:wi,newApi:!0,suggestHost:xi,renderApiUri:yi,symptom:zi,timezone:Ai,directToGetHelp:Bi,openToHelpGuideEntryButtonId:Ci,enableHelpGuideMaximize:e,enableHelpGuideConversationalAi:f,enableHelpGuideHumanChat:g,startedFromHelpApi:!0,domain:h,helpPanelMode:Di,fixedHelpPanelContainer:Ei,customHelpPanelContainer:Fi,frdProductData:Gi,onCloseCallback:Hi,
onMinimizeCallback:Ii,onLoadCallback:Ji,onPromotedProductLinkClickCallback:Ki,onGseEventCallback:Li,openingMode:Mi,minimizeMode:Ni,helpGuideHelpCenterEmbedEntryPoint:Oi,helpGuideCommonEmbedEntryPoint:k,helpGuideStartingFlow:zd,gseSessionOptions:Ad,helpPanelStartTimeMs:c,asxUiUri:a.asxUiUri_,disableEndUserCredentials:d.disableEndUserCredentials,gsePageUrl:(n=d.helpGuideOptions)==null?void 0:n.pageUrl};bd(b,a);return b};m=Z.prototype;
m.loadChatSupport=function(a){var b=a||{};a=a?mi(this,a):{};bd(a,{escalationJSONString:b.escalationJSONString});ei(a,this.productData_)};
m.requestSurvey=function(a){if(!ni(a.triggerId))throw Error("Invalid triggerId");var b=Date.now();oi(this,a,!1).then(function(c,d){var e=d.getSurveyPayload();if(e){var f=pi(e),g;d.getSession()&&Ke(d.getSession())&&(g=Ke(d.getSession()));var h={surveyData:{surveyData:JSON.stringify(Ec(d)),triggerRequestTime:b,apiKey:this.apiKey_,nonProd:a.nonProd,language:e.getLanguage(),libraryVersion:*********,surveyMetadata:{triggerId:a.triggerId,sessionId:g,surveyId:d.getSurveyId()},feedback1pEnabled:Lg(L(this.surveyStartupConfig_,
Kg,1)),thirdPartyDomainSupportEnabled:this.thirdPartyDomainSupportEnabled_},triggerId:a.triggerId,surveyError:null};setTimeout(function(){return c(h)},f*1E3)}else h={surveyData:null,triggerId:a.triggerId,surveyId:a.surveyIdForTestingMode,surveyError:{reason:"No eligible surveys."}},c(h)}.bind(this,a.callback),function(c){var d="";try{d=JSON.stringify(c)}catch(e){d="message: "+c.message+", stack: "+c.stack}a.callback({surveyData:null,triggerId:a.triggerId,surveyError:{reason:"Failed to trigger survey: "+
d}})})};
m.presentSurvey=function(a){if(a.surveyData){var b=a.surveyData&&a.surveyData.surveyData&&a.surveyData.surveyData.surveyData?a.surveyData.surveyData:a.surveyData;switch(a.defaultStyle){case 1:var c;a.promptStyle=(c=a.promptStyle)!=null?c:2;var d;a.completionStyle=(d=a.completionStyle)!=null?d:2;break;default:var e;a.promptStyle=(e=a.promptStyle)!=null?e:1;var f;a.completionStyle=(f=a.completionStyle)!=null?f:1}c=Yc(Ne,b.surveyData).getSurveyPayload();a:if(d=a.promptStyle,e=c.getDisplaySettings().getAllowedPromptStyleList(),d){switch(d){case 1:d=
e.includes(1);break a;case 2:d=e.includes(2);break a}d=!1}else d=!0;if(d){a:if(d=a.completionStyle,c=c.getCompletion().getAllowedCompletionStyleList(),d){switch(d){case 1:c=c.includes(1);break a;case 2:c=c.includes(2);break a}c=!1}else c=!0;if(c)if(a.parentDomElementId!=void 0&&a.parentDomElementId!=""&&document.getElementById(a.parentDomElementId)==null)a.listener&&a.listener.surveyPrompted&&a.listener.surveyPrompted(a.surveyData,{reason:"Invalid parent dom element id"});else{c=b.nonProd?"nonprod":
"prod";d=a.colorScheme===2?"dark":"light";e=document.body;a:{f=rd(e);if(f.defaultView&&f.defaultView.getComputedStyle&&(f=f.defaultView.getComputedStyle(e,null))){f=f.direction||f.getPropertyValue("direction")||"";break a}f=""}f="rtl"==(f||(e.currentStyle?e.currentStyle.direction:null)||e.style&&e.style.direction)?"rtl":"ltr";if(a.completionStyle===2&&!qi("https://gstatic.com/uservoice/surveys/resources/"+c+"/js/survey/survey_"+d+"_"+f+".css")){ri();e=document.createElement("link");c=S(gi,c,d,f);
if(c instanceof Qa)e.href=Sa(c).toString(),e.rel="stylesheet";else{if(pd.indexOf("stylesheet")===-1)throw Error('TrustedResourceUrl href attribute required with rel="stylesheet"');c=ld(c);c!==void 0&&(e.href=c,e.rel="stylesheet")}document.head.appendChild(e)}si(this,a,b)}else a.listener&&a.listener.surveyPrompted&&a.listener.surveyPrompted(a.surveyData,{reason:"Invalid completion style"})}else a.listener&&a.listener.surveyPrompted&&a.listener.surveyPrompted(a.surveyData,{reason:"Invalid prompt style"})}};
m.dismissSurvey=function(a){window.hatsNextGlobalObject&&window.hatsNextGlobalObject.dismissSurvey&&window.hatsNextGlobalObject.dismissSurvey(a.surveyMetadata)};
m.scheduleSurvey=function(a){if(!ni(a.triggerId))throw Error("Invalid triggerId");oi(this,a,!0).then(function(b){b({surveyData:null,triggerId:a.triggerId,surveyId:a.surveyIdForTestingMode,surveyError:{reason:"Survey scheduled for later."}})}.bind(this,a.callback),function(b){var c="";try{c=JSON.stringify(b)}catch(d){c="message: "+b.message+", stack: "+b.stack}a.callback({surveyData:null,triggerId:a.triggerId,surveyError:{reason:"Failed to trigger survey: "+c}})})};
m.registerHelpAction=function(a,b,c){Qi().register(a,b,c)};m.executeHelpAction=function(a,b){Qi().execute(a,b)};m.isHelpActionApplicable=function(a){return Qi().isApplicable(a)};
var Ri=function(a,b){if(b.enableFeedback1pEndpoint&&b.enableTestingMode)return a.surveyStartupConfig_=Rg(new Qg,Mg(!0)),Promise.resolve(a.surveyStartupConfig_);b=new ue({serverUrl:b.nonProd?"https://stagingqual-feedback-pa-googleapis.sandbox.google.com":"https://feedback-pa.clients6.google.com",apiKey:"AIzaSyCB6OnnfuitFnaYWu4BvtGKaoLFk4cm-GE",authUser:b.authuser});var c=Pg(new Og,(a.productId_||"").toString()).setPlatform(1);return ve(b,"POST","v1/survey/startup_config",c,Qg).then(function(d){return a.surveyStartupConfig_=
d})},oi=function(a,b,c){return Ri(a,b).then(function(d){if(Lg(L(d,Kg,1))){d=new ue({serverUrl:b.nonProd?"https://stagingqual-feedback-pa-googleapis.sandbox.google.com":"https://feedback-pa.clients6.google.com",apiKey:a.apiKey_,authUser:b.authuser});var e=Si(a,b),f=Ae();f=(new Bg).setTimezoneOffset(f);var g=(new Cg).setPlatform(1).setLibraryVersionInt(*********).setSupportedCapabilityList([1,2]),h=b.preferredSurveyLanguageList&&b.preferredSurveyLanguageList.length>0?b.preferredSurveyLanguageList:[a.locale_];
h=(new Ng).setTriggerId(b.triggerId).setLanguageList(h).setTestingMode(!!b.enableTestingMode);b.surveyIdForTestingMode!=""&&b.surveyIdForTestingMode!=void 0&&b.enableTestingMode==1&&h.setSurveyId(b.surveyIdForTestingMode);f=(new Sg).setTriggerContext(h).setClientContext((new Dg).setDeviceInfo(f).setLibraryInfo(g));c!=""&&c!=void 0&&c==1&&f.setScheduledSurveyContext((new Ig).setIsScheduledSurvey(c));d=ve(d,"POST",e,f,Tg)}else d=Ti(a,b,c);return d},function(){return Ti(a,b,c)})},Si=function(a,b){var c=
[],d=Ug(String(B.location.href));var e=[];var f;(f=B.__SAPISID||B.__APISID||B.__3PSAPISID||B.__1PSAPISID||B.__OVERRIDE_SID)?f=!0:(typeof document!=="undefined"&&(f=new Zg,f=f.get("SAPISID")||f.get("APISID")||f.get("__Secure-3PAPISID")||f.get("__Secure-1PAPISID")),f=!!f);f&&(f=(d=d.indexOf("https:")==0||d.indexOf("chrome-extension:")==0||d.indexOf("chrome-untrusted://new-tab-page")==0||d.indexOf("moz-extension:")==0)?B.__SAPISID:B.__APISID,f||typeof document==="undefined"||(f=new Zg,f=f.get(d?"SAPISID":
"APISID")||f.get("__Secure-3PAPISID")),(f=f?Xg(f,d?"SAPISIDHASH":"APISIDHASH",c):null)&&e.push(f),d&&((d=ah("__1PSAPISID","__Secure-1PAPISID","SAPISID1PHASH",c))&&e.push(d),(c=ah("__3PSAPISID","__Secure-3PAPISID","SAPISID3PHASH",c))&&e.push(c)));e=e.length==0?null:e.join(" ");b.thirdPartyDomainSupportEnabled!=void 0&&b.thirdPartyDomainSupportEnabled&&(a.thirdPartyDomainSupportEnabled_=!0,e=!1);return e?"v1/survey/trigger":"v1/survey/trigger/trigger_anonymous"},Ti=function(a,b,c){var d=new ue({serverUrl:b.nonProd?
"https://test-scone-pa-googleapis.sandbox.google.com":"https://scone-pa.clients6.google.com",apiKey:a.apiKey_,authUser:b.authuser}),e=Si(a,b),f=Ae();f=(new Be).setTimezoneOffset(f);var g=(new Ce).setPlatform(1).setLibraryVersionInt(*********).setSupportedCapabilityList([1,2]);a=b.preferredSurveyLanguageList&&b.preferredSurveyLanguageList.length>0?b.preferredSurveyLanguageList:[a.locale_];a=(new Le).setTriggerId(b.triggerId).setLanguageList(a).setTestingMode(!!b.enableTestingMode);b.surveyIdForTestingMode!=
""&&b.surveyIdForTestingMode!=void 0&&b.enableTestingMode==1&&a.setSurveyId(b.surveyIdForTestingMode);b=(new Me).setTriggerContext(a).setClientContext((new De).setDeviceInfo(f).setLibraryInfo(g));c!=""&&c!=void 0&&c==1&&b.setScheduledSurveyContext((new Ie).setIsScheduledSurvey(c));return ve(d,"POST",e,b,Ne)},ni=function(a){return typeof a=="string"&&!!a.match(/^[A-Za-z0-9]+$/)},pi=function(a){try{var b=parseInt,c=a.getDisplaySettings().getPromptDelay();var d=L(c,W,1);var e=b(bh(d.getSeconds()),10);
isNaN(e)&&(e=0)}catch(k){e=0}try{b=parseInt;var f=a.getDisplaySettings().getPromptDelay();var g=L(f,W,2);var h=b(bh(g.getSeconds()),10);isNaN(h)&&(h=0)}catch(k){h=0}return Math.floor(Math.random()*(h-e+1))+e},si=function(a,b,c){var d=c.nonProd?"nonprod":"prod",e=c.language&&vg[c.language.toLowerCase()]?vg[c.language.toLowerCase()]:vg[a.locale_.toLowerCase()];e=e&&e.replace("-","_");e==="fa"&&(e="en");var f=e?encodeURI(e):"en";b.enableReloadScriptWhenLanguageChanges&&!Ui("https://www.gstatic.com/uservoice/surveys/resources/"+
d+"/js/survey/survey_binary__"+f+".js")&&Vi();window.hatsNextGlobalObject?Wi(b,c):(e=document.createElement("script"),d=S(fi,d,f),od(e,d),e.type="text/javascript",e.onload=function(){return Wi(b,c)},e.onerror=function(){b.listener&&b.listener.surveyPrompted&&b.listener.surveyPrompted(c,{reason:"Failed to load survey binary"})},e.setAttribute("data-survey-binary",""),a.nonce_&&e.setAttribute("nonce",a.nonce_),document.querySelector("[data-survey-binary]")||document.body.appendChild(e))},Wi=function(a,
b){a:{var c="triggerCutoffTime";a.parentDomElementId!=null&&a.parentDomElementId!=""&&(c+="_"+a.parentDomElementId);if(window.hatsNextGlobalObject&&window.hatsNextGlobalObject[c]){if(window.hatsNextGlobalObject[c]>b.triggerRequestTime){a.listener&&a.listener.surveyPrompted&&a.listener.surveyPrompted(b,{reason:"Survey was triggered before the most recent survey event. Please re-trigger the survey."});c=!1;break a}}else if(hi>b.triggerRequestTime){a.listener&&a.listener.surveyPrompted&&a.listener.surveyPrompted(b,
{reason:"Survey must be triggered after initializing the help API."});c=!1;break a}c=Date.now()-b.triggerRequestTime;c>864E5?(a.listener&&a.listener.surveyPrompted&&a.listener.surveyPrompted(b,{reason:"Survey must be triggered within the last 24 hours. Survey was triggered "+(c+" ms ago.")}),c=!1):c=!0}if(c){var d,e,f;window.hatsNextGlobalObject.initSurvey({surveyTriggerResponse:b.surveyData,nonprod:b.nonProd,darkMode:a.colorScheme==2,seamlessMode:a.seamlessMode,zIndex:a.customZIndex,triggerRequestTime:b.triggerRequestTime,
authuser:a.authuser,apiKey:b.apiKey,locale:b.language,customLogoAltText:a.customLogoAltText,customLogoUrl:a.customLogoUrl,productData:a.productData,listener:a.listener,surveyData:b,surveyMetadata:b.surveyMetadata,promptStyle:(d=a.promptStyle)!=null?d:1,completionStyle:(e=a.completionStyle)!=null?e:1,defaultStyle:(f=a.defaultStyle)!=null?f:0,parentDomElementId:a.parentDomElementId,persistCompletionCard:a.persistCompletionCard,hidePrivacyBanner:a.hidePrivacyBanner,hideInlineSurveyBorder:a.hideInlineSurveyBorder,
hideInlineSurveyBackground:a.hideInlineSurveyBackground,feedback1pEnabled:b.feedback1pEnabled,thirdPartyDomainSupportEnabled:b.thirdPartyDomainSupportEnabled})}},Qi=function(){var a=C("help.globals.actions",top);a||(a=new Oe,Da("help.globals.actions",a,top));return a},Ui=function(a){return[].concat(la(document.getElementsByTagName("script"))).find(function(b){return b&&b.getAttribute("src")===a})},Vi=function(){[].concat(la(document.getElementsByTagName("script"))).forEach(function(a){var b;if(a==
null?0:(b=a.getAttribute("src"))==null?0:b.match(ji))a.parentNode.removeChild(a),window.hatsNextGlobalObject=null})},ri=function(){document.querySelectorAll("link[rel=stylesheet]").forEach(function(a){var b;(a==null?0:(b=a.getAttribute("href"))==null?0:b.match(ii))&&a.parentNode.removeChild(a)})},ki=function(a){if(!a)return"";try{return ib(JSON.stringify(Ec(a)))}catch(b){return console.log("Failed to serialize and encode proto: ",b),""}},qi=function(a){return[].concat(la(document.querySelectorAll("link[rel=stylesheet]"))).find(function(b){return b&&
b.getAttribute("href")===a})};Z.prototype.isHelpActionApplicable=Z.prototype.isHelpActionApplicable;Z.prototype.executeHelpAction=Z.prototype.executeHelpAction;Z.prototype.registerHelpAction=Z.prototype.registerHelpAction;Z.prototype.scheduleSurvey=Z.prototype.scheduleSurvey;Z.prototype.dismissSurvey=Z.prototype.dismissSurvey;Z.prototype.presentSurvey=Z.prototype.presentSurvey;Z.prototype.requestSurvey=Z.prototype.requestSurvey;Z.prototype.loadChatSupport=Z.prototype.loadChatSupport;
Z.prototype.startHelpCard=Z.prototype.startHelpCard;Z.prototype.startHelp=Z.prototype.startHelp;Z.prototype.updateContext=Z.prototype.updateContext;Z.prototype.updateProductData=Z.prototype.updateProductData;Z.prototype.startFeedback=Z.prototype.startFeedback;Da("help.service.Lazy",Z);Da("help.service.Lazy.create",function(a,b){return new Z(a,b)});}).call(this);
