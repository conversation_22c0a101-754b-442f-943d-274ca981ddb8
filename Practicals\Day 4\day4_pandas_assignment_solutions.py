# Day 4: Pandas Basics – Assignment Solutions (40 Marks)
# Author: <PERSON><PERSON>

"""
Section A: Fill in the Blanks (8 × 1 = 8 Marks)
------------------------------------------------
1. pd.DataFrame(...) creates a DataFrame (pandas object that stores table data).
2. df.head(n) shows the first n rows of the DataFrame.
3. df.shape returns a tuple (rows, columns) representing rows and columns.
4. df.columns lists the column names.
5. df.dtypes shows each column’s datatype.
6. To compute summary stats like sum or mean on your table, you use df.agg().
7. df.groupby('col') first groups the rows by the values in col.
8. After grouping, you call .agg({'col':'sum'}) to aggregate the values in that column.
"""

"""
Section B: Match the Columns (6 × 1 = 6 Marks)
----------------------------------------------
9.  df.head()           d. Shows the first few rows
10. df.dropna()        a. <PERSON>moves rows with missing values
11. df.columns         b. Shows column names
12. df.agg()           c. Computes summary statistics like sum, mean, max
13. df.groupby('col')  f. Splits the DataFrame into groups based on one column
14. df.dtypes          e. Shows data type of each column
"""

"""
Section C: True or False (6 × 1 = 6 Marks)
------------------------------------------
15. df.shape returns (rows, columns).                                 T
16. df.tail() shows the first 5 rows by default.                      F
17. After df.groupby('city'), you can call .agg() to summarize each city’s data.  T
18. df.fillna(0) removes all rows with missing values.                F
19. df.describe() gives count, mean, min, max for numeric columns.    T
20. df.info() shows row count, column names, and data types.          T
"""

"""
Section D: Short-Answer Theory (5 × 2 = 10 Marks)
-------------------------------------------------
21. What is a DataFrame?
    A DataFrame is a 2-dimensional, labeled data structure in pandas that stores data in rows and columns, similar to a spreadsheet or SQL table.

22. Why do we use df.head() before any analysis?
    df.head() lets us quickly preview the first few rows of the data to check its structure and contents before deeper analysis.

23. Explain why checking df.dtypes is important.
    Checking df.dtypes ensures each column has the correct data type, which is crucial for accurate calculations and analysis.

24. How does df.groupby() help when you have categories?
    df.groupby() allows you to split data into groups based on category values, making it easy to perform calculations for each group separately.

25. When would you use df.agg() instead of individual .sum() or .mean() calls?
    df.agg() lets you compute multiple summary statistics (like sum, mean, min, max) at once for different columns, making your code concise and efficient.
"""

# --------------------------------------------------
# Section E: Practical Coding (Real-Life Scenarios)
# --------------------------------------------------

import pandas as pd

# E1: Weekly Milk Sales (5 Marks)
days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
litres = [10, 12, 9, 11, 13, 8, 14]
milk_df = pd.DataFrame({'day': days, 'litres': litres})

print('E1: Weekly Milk Sales DataFrame:')
print(milk_df)
print('\nFirst 3 rows:')
print(milk_df.head(3))
print('\nShape:', milk_df.shape)
print('Columns:', milk_df.columns.tolist())
print('Data types:\n', milk_df.dtypes)

# E2: Monthly Expenses Summary (5 Marks)
data = {
    'category': ['rent', 'food', 'utilities', 'transport'],
    'amount': [15000, 8000, 2000, 3000]
}
expenses_df = pd.DataFrame(data)

print('\nE2: Monthly Expenses DataFrame:')
print(expenses_df)

summary = expenses_df['amount'].agg(['sum', 'mean', 'max', 'min'])
print('\nExpense Summary (total, average, max, min):')
print(summary)