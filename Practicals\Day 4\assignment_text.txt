Day 4: Pandas Basics – 
Assignment (40 Marks)
Youʼve learned ho w to create tables Dat aFrames), inspect them, check their  
structur e, and comput e summar ies with aggr egation. This assignment t ests your 
grasp with dif ferent question t ypes—fill-in-the-blank, mat ching, true/f alse, shor t 
answ ers, and hands-on coding b ased on r eal-lif e scenar ios. Sho w all st eps and  
answ ers cle arly.
S e c t i o n  A :  F i l l  i n  t h e  B l a n k s  ( 8  ×  1  =  8  M a r k s )
 p d . D a t a F r a m e ( … ) creates a ________ (p andas object that st ores table dat a).
 d f . h e a d ( n ) sho ws the first ___ r ows of the Dat aFrame.
 d f . s h a p e retur ns a tuple  ( _ _ _ _ ,  _ _ _ _ ) representing r ows and columns.
 d f . c o l u m n s lists the ________ names.
 d f . d t y p e s sho ws e ach column ʼs ________.
 To comput e summar y stats like sum or me an on y our t able, y ou use  d f . _ _ _ _ ( ).
 d f . g r o u p b y ( ' c o l ' ) first ________ the r ows b y the v alues in  c o l.
 After gr ouping, y ou c all  . a g g ( { ' c o l ' : ' s u m ' } ) to ________ the v alues in that column.
S e c t i o n  B :  M a t c h  t h e  C o l u m n s  ( 6  ×  1  =  6  M a r k s )
Day 4 Pandas Basics  Assignment 40 Mar ks)
1Match Column A  with the best descr iption in  Column B . Write the let ter (a–f) ne xt 
to each number 914.
Column A Column B
9.  d f . h e a d ( ) a. Remo ves rows with missing v alues
10.  d f . d r o p n a ( ) b. Sho ws column names
11.  d f . c o l u m n s c. Comput es summar y statistics lik e sum, me an, max
12.  d f . a g g ( ) d. Sho ws the first f ew rows
13.  d f . g r o u p b y ( ' c o l ' )e. Sho ws dat a type of e ach column
14.  d f . d t y p e s f. Split s the Dat aFrame int o groups b ased on one column
S e c t i o n  C :  T r u e  o r  F a l s e  ( 6  ×  1  =  6  M a r k s )
Write T for true,  F for false:
15.  d f . s h a p e retur ns  ( r o w s ,  c o l u m n s ).
16.  d f . t a i l ( ) sho ws the first 5 r ows b y def ault.
17. After  d f . g r o u p b y ( ' c i t y ' ), you c an c all  . a g g ( ) to summar ize each cit yʼs dat a.
18.  d f . f i l l n a ( 0 ) remo ves all r ows with missing v alues.
19.  d f . d e s c r i b e ( ) gives count, me an, min, max f or numer ic columns.
20.  d f . i n f o ( ) sho ws r ow count, column names, and dat a types.
S e c t i o n  D :  S h o r t - A n s w e r  T h e o r y  ( 5  ×  2  =  1 0  M a r k s )
Answ er in 12 sent ences e ach.
21. What is a Dat aFrame?
22. Why do w e use  d f . h e a d ( ) before an y anal ysis?
23. Explain wh y checking  d f . d t y p e s is impor tant.
24. How does  d f . g r o u p b y ( ) help when y ou ha ve categor ies?
25. When w ould y ou use  d f . a g g ( ) inst ead of individual  . s u m ( ) or  . m e a n ( ) calls?
Day 4 Pandas Basics  Assignment 40 Mar ks)
2S e c t i o n  E :  P r a c t i c a l  C o d i n g  ( R e a l - L i f e  S c e n a r i o s )  ( 2  ×  5  =  1 0  
M a r k s )
E 1 :  W e e k l y  M i l k  S a l e s  ( 5  M a r k s )
A dair y shop r ecor ds litr es of milk sold e ach da y:
cshar p
Cop y
10, 12, 9 , 11, 13 , 8, 14
Tasks:
 Create a Dat aFrame with columns  d a y ( M o n,  T u e, …,  S u n) and  l i t r e s.
 Displa y the first 3 r ows.
 Print the Dat aFrame ʼs shape, columns, and d types.
Sho w y our code and it s output.
E 2 :  M o n t h l y  E x p e n s e s  S u m m a r y  ( 5  M a r k s )
You track y our Januar y expenses:
categor y amount ₹
rent 15 000
food 8 000
utilities 2 000
transpor t 3 000
Tasks:
 Create a Dat aFrame with this dat a.
 Use a single p andas command t o comput e total, average, maximum , 
and  minimum  expense.
 Print the r esul ts cle arly.
 
Day 4 Pandas Basics  Assignment 40 Mar ks)
3The 90Minut e Coding Rule
This rule helps y ou po wer thr ough comple x coding t asks (lik e your p andas  
assignment) in one solid, deep-w ork session. Her eʼs ho w it w orks:
1 .  S e t  a  C l e a r  I n t e n t i o n  ( 5  m i n u t e s )
Pick one go al: “Finish Section AD of the p andas assignment. ˮ
Write it do wn on p aper or a sticky not e.
Remo ve distr actions : silence phone, close email t abs, cle ar desk.
2 .  D e e p - W o r k  B l o c k  ( 8 0  m i n u t e s )
Your brain does best in 90-minut e rhythms. W eʼll use 80 minut es of f ocused  
coding, divided int o mini-phases:
Phase Duration What t o Do
A. Plan & W arm-Up 5 min Read instructions; open y our Py thon edit or
B. Section A 10 min Fill in the blanks 8 Qʼ s)
C. Section B 10 min Match the columns 6 Qʼ s)
D. Section C 10 min True/F alse 6 Qʼ s)
E. Section D 15 min Shor t theor y answ ers 5 Qʼ s)
F. Section E1 20 min Code “W eekl y Milk Salesˮ 5 mar ks)
G. Section E2 20 min Code “Monthl y Expenses Summar yˮ 5 mar ks)
3 .  R e f l e c t i o n  &  W r a p - U p  ( 5  m i n u t e s )
Review your answ ers and code.
Fix any typos or logic er rors.
Day 4 Pandas Basics  Assignment 40 Mar ks)
4Save and  submit  your w ork.
4 .  T a k e  a  B r e a k  ( a t  9 0  m i n u t e s )
Walk, str etch, get wat er.
Youʼve earned it!
W h y  T h i s  W o r k s
 Biologic al Peak: Our brains c ycle about e very 90 min—fr om aler t to tired. 
Use the first 80 min when y ouʼre shar p.
 Single- Task F ocus:  By sticking t o one assignment, y ou a void “ cont ext 
swit chingˮ ( jumping bet ween t asks), which kills pr oductivit y.
 Micr o-Phase Planning:  Breaking 80 min int o 7 cle ar st eps k eeps y ou 
motiv ated—e ach mini-go al feels achie vable.
 Built-In R eview: The final 5 min r eflection c atches small mist akes bef ore 
they count against y ou.
 Ener gy Management:  A pr oper br eak pr events bur nout and r eset s your 
focus f or the ne xt task.
D e t a i l e d  R o a d m a p  E x a m p l e
Time Task Tip
05 minPrepare Read assignment,
open edit orKeep y our int ention not e visible.
515 minSection A  Fill in the blanks 8
Qʼs)Work quickl y—y ou kno w these
definitions w ell.
1525 minSection B  Mat ch columns 6
Qʼs)Trust y our first instinct on p airs.
2535 min Section C  True/F alse 6 Qʼ s) Answ er dir ectly—no o verthinking.
3550 min Section D  Theor y 5 Qʼ s) One or t wo cle ar sent ences e ach.
507 0 minSection E1  Weekl y Milk Sales
(5 m)Write code st ep-b y-step; t est af ter
each line.
Day 4 Pandas Basics  Assignment 40 Mar ks)
57090 minSection E2  Monthl y Expenses
Summar yUse  . a g g ( ) once f or all st ats; pr int
clearly.
90 min Break!Step awa y and celebrat e your
progress.
Follow this 90Minut e Coding Rule, and y ouʼll finish the assignment in one  
focused burst—no str ess, no distractions, just cle ar pr ogress. Good luck,  
Sagar!
Day 4 Pandas Basics  Assignment 40 Mar ks)
6