
import java.util.*;

/**
 * LeetCode 3373: Maximize the Number of Target Nodes After Connecting Trees II
 *
 * Problem: Given two undirected trees, find the maximum number of target nodes
 * for each node in the first tree when connecting one node from tree1 to tree2.
 * A node u is target to node v if the path between them has even number of
 * edges.
 *
 * Key Insight: answer[i] = even_distances_tree1[i] + max(odd_distances_tree2)
 *
 * Time Complexity: O(n + m) where n, m are the sizes of the trees Space
 * Complexity: O(n + m) for adjacency lists and BFS queues
 */
class Solution {

    public int[] maxTargetNodes(int[][] edges1, int[][] edges2) {
        int n = edges1.length + 1; // Number of nodes in tree1
        int m = edges2.length + 1; // Number of nodes in tree2

        // Build adjacency lists for both trees
        List<List<Integer>> tree1 = buildAdjacencyList(edges1, n);
        List<List<Integer>> tree2 = buildAdjacencyList(edges2, m);

        // Calculate even-distance counts for each node in tree1
        int[] evenCountsTree1 = new int[n];
        for (int i = 0; i < n; i++) {
            evenCountsTree1[i] = countNodesAtEvenDistance(tree1, i, n);
        }

        // Calculate odd-distance counts for each node in tree2
        int[] oddCountsTree2 = new int[m];
        for (int i = 0; i < m; i++) {
            oddCountsTree2[i] = countNodesAtOddDistance(tree2, i, m);
        }

        // Find maximum odd count in tree2
        int maxOddTree2 = 0;
        for (int oddCount : oddCountsTree2) {
            maxOddTree2 = Math.max(maxOddTree2, oddCount);
        }

        // Build result array using the key formula
        int[] result = new int[n];
        for (int i = 0; i < n; i++) {
            result[i] = evenCountsTree1[i] + maxOddTree2;
        }

        return result;
    }

    /**
     * Builds adjacency list representation of a tree from edge array
     */
    private List<List<Integer>> buildAdjacencyList(int[][] edges, int nodeCount) {
        List<List<Integer>> adj = new ArrayList<>();
        for (int i = 0; i < nodeCount; i++) {
            adj.add(new ArrayList<>());
        }

        for (int[] edge : edges) {
            int u = edge[0];
            int v = edge[1];
            adj.get(u).add(v);
            adj.get(v).add(u);
        }

        return adj;
    }

    /**
     * Counts nodes at even distance from the given start node using BFS
     */
    private int countNodesAtEvenDistance(List<List<Integer>> tree, int start, int nodeCount) {
        boolean[] visited = new boolean[nodeCount];
        Queue<Integer> queue = new LinkedList<>();
        Queue<Integer> distanceQueue = new LinkedList<>();

        queue.offer(start);
        distanceQueue.offer(0);
        visited[start] = true;

        int evenCount = 0;

        while (!queue.isEmpty()) {
            int node = queue.poll();
            int distance = distanceQueue.poll();

            // Count nodes at even distance (including the start node itself)
            if (distance % 2 == 0) {
                evenCount++;
            }

            // Explore neighbors
            for (int neighbor : tree.get(node)) {
                if (!visited[neighbor]) {
                    visited[neighbor] = true;
                    queue.offer(neighbor);
                    distanceQueue.offer(distance + 1);
                }
            }
        }

        return evenCount;
    }

    /**
     * Counts nodes at odd distance from the given start node using BFS
     */
    private int countNodesAtOddDistance(List<List<Integer>> tree, int start, int nodeCount) {
        boolean[] visited = new boolean[nodeCount];
        Queue<Integer> queue = new LinkedList<>();
        Queue<Integer> distanceQueue = new LinkedList<>();

        queue.offer(start);
        distanceQueue.offer(0);
        visited[start] = true;

        int oddCount = 0;

        while (!queue.isEmpty()) {
            int node = queue.poll();
            int distance = distanceQueue.poll();

            // Count nodes at odd distance
            if (distance % 2 == 1) {
                oddCount++;
            }

            // Explore neighbors
            for (int neighbor : tree.get(node)) {
                if (!visited[neighbor]) {
                    visited[neighbor] = true;
                    queue.offer(neighbor);
                    distanceQueue.offer(distance + 1);
                }
            }
        }

        return oddCount;
    }
}
