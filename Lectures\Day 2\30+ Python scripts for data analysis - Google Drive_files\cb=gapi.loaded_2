gapi.loaded_2(function(_){var window=this;
_.Ps=function(a){return"rtl"==_.Bs(a,"direction")};_.Qs=function(a,b,c,d){this.left=a;this.top=b;this.width=c;this.height=d};_.g=_.Qs.prototype;_.g.clone=function(){return new _.Qs(this.left,this.top,this.width,this.height)};_.g.intersects=function(a){return this.left<=a.left+a.width&&a.left<=this.left+this.width&&this.top<=a.top+a.height&&a.top<=this.top+this.height};
_.g.contains=function(a){return a instanceof _.os?a.x>=this.left&&a.x<=this.left+this.width&&a.y>=this.top&&a.y<=this.top+this.height:this.left<=a.left&&this.left+this.width>=a.left+a.width&&this.top<=a.top&&this.top+this.height>=a.top+a.height};_.g.distance=function(a){var b=a.x<this.left?this.left-a.x:Math.max(a.x-(this.left+this.width),0);a=a.y<this.top?this.top-a.y:Math.max(a.y-(this.top+this.height),0);return Math.sqrt(b*b+a*a)};_.g.getSize=function(){return new _.od(this.width,this.height)};
_.g.getCenter=function(){return new _.os(this.left+this.width/2,this.top+this.height/2)};_.g.ceil=function(){this.left=Math.ceil(this.left);this.top=Math.ceil(this.top);this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};_.g.floor=function(){this.left=Math.floor(this.left);this.top=Math.floor(this.top);this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};
_.g.round=function(){this.left=Math.round(this.left);this.top=Math.round(this.top);this.width=Math.round(this.width);this.height=Math.round(this.height);return this};_.g.translate=function(a,b){a instanceof _.os?(this.left+=a.x,this.top+=a.y):(this.left+=a,typeof b==="number"&&(this.top+=b));return this};_.g.scale=function(a,b){b=typeof b==="number"?b:a;this.left*=a;this.width*=a;this.top*=b;this.height*=b;return this};_.Rs=function(a){return _.Bs(a,"position")};
_.Ss=function(a,b,c){if(b instanceof _.os){var d=b.x;b=b.y}else d=b,b=c;a.style.left=_.Is(d,!1);a.style.top=_.Is(b,!1)};_.Ts=function(a,b){a=a.style;"opacity"in a?a.opacity=b:"MozOpacity"in a?a.MozOpacity=b:"filter"in a&&(a.filter=b===""?"":"alpha(opacity="+Number(b)*100+")")};_.Us=function(){if(_.Cd){var a=/Windows NT ([0-9.]+)/;return(a=a.exec(_.Gc()))?a[1]:"0"}return _.Bd?(a=/1[0|1][_.][0-9_.]+/,(a=a.exec(_.Gc()))?a[0].replace(/_/g,"."):"10"):_.Ed?(a=/Android\s+([^\);]+)(\)|;)/,(a=a.exec(_.Gc()))?a[1]:""):_.Fd||_.Hd||_.Id?(a=/(?:iPhone|CPU)\s+OS\s+(\S+)/,(a=a.exec(_.Gc()))?a[1].replace(/_/g,"."):""):""}();var Vs;Vs=function(a){return(a=a.exec(_.Gc()))?a[1]:""};_.Ws=function(){if(_.vh)return Vs(/Firefox\/([0-9.]+)/);if(_.vd||_.wd||_.td)return _.Sd;if(_.Ah){if(_.Zc()||_.$c()){var a=Vs(/CriOS\/([0-9.]+)/);if(a)return a}return Vs(/Chrome\/([0-9.]+)/)}if(_.Bh&&!_.Zc())return Vs(/Version\/([0-9.]+)/);if(_.wh||_.xh){if(a=/Version\/(\S+).*Mobile\/(\S+)/.exec(_.Gc()))return a[1]+"."+a[2]}else if(_.zh)return(a=Vs(/Android\s+([0-9.]+)/))?a:Vs(/Version\/([0-9.]+)/);return""}();
var Xs,Ys,at;_.Wd.prototype.pH=_.pb(2,function(){return _.de(this.getWindow())});Xs=function(a,b){return new _.os(a.x-b.x,a.y-b.y)};Ys=function(a){var b=_.Xd(a),c=_.Bs(a,"position"),d=c=="fixed"||c=="absolute";for(a=a.parentNode;a&&a!=b;a=a.parentNode)if(a.nodeType==11&&a.host&&(a=a.host),c=_.Bs(a,"position"),d=d&&c=="static"&&a!=b.documentElement&&a!=b.body,!d&&(a.scrollWidth>a.clientWidth||a.scrollHeight>a.clientHeight||c=="fixed"||c=="absolute"||c=="relative"))return a;return null};
_.Zs=function(a){for(var b=new _.ms(0,Infinity,Infinity,0),c=_.Yd(a),d=c.tb().body,e=c.tb().documentElement,f=_.ps(c.Cc);a=Ys(a);)if((!_.zd||a.clientHeight!=0||a!=d)&&a!=d&&a!=e&&_.Bs(a,"overflow")!="visible"){var h=_.Ds(a),k=new _.os(a.clientLeft,a.clientTop);h.x+=k.x;h.y+=k.y;b.top=Math.max(b.top,h.y);b.right=Math.min(b.right,h.x+a.clientWidth);b.bottom=Math.min(b.bottom,h.y+a.clientHeight);b.left=Math.max(b.left,h.x)}d=f.scrollLeft;f=f.scrollTop;b.left=Math.max(b.left,d);b.top=Math.max(b.top,f);
c=c.pH();b.right=Math.min(b.right,d+c.width);b.bottom=Math.min(b.bottom,f+c.height);return b.top>=0&&b.left>=0&&b.bottom>b.top&&b.right>b.left?b:null};_.$s=function(a){var b=_.Ds(a);a=_.Ks(a);return new _.Qs(b.x,b.y,a.width,a.height)};at=function(a,b){return(b&8&&_.Ps(a)?b^4:b)&-9};
_.bt=function(a,b,c,d,e,f,h,k,l){var m;if(m=c.offsetParent){var n=m.tagName=="HTML"||m.tagName=="BODY";if(!n||_.Rs(m)!="static"){var p=_.Ds(m);if(!n){n=_.Ps(m);var q;if(q=n){q=_.Bh&&_.Ac(_.Ws,10)>=0;var r=_.Jd&&_.Ac(_.Us,10)>=0,w=_.Ah&&_.Ac(_.Ws,85)>=0;q=_.yd||q||r||w}n=q?-m.scrollLeft:n&&_.Bs(m,"overflowX")!="visible"?m.scrollWidth-m.clientWidth-m.scrollLeft:m.scrollLeft;p=Xs(p,new _.os(n,m.scrollTop))}}}m=p||new _.os;p=_.$s(a);if(n=_.Zs(a))w=new _.Qs(n.left,n.top,n.right-n.left,n.bottom-n.top),
n=Math.max(p.left,w.left),q=Math.min(p.left+p.width,w.left+w.width),n<=q&&(r=Math.max(p.top,w.top),w=Math.min(p.top+p.height,w.top+w.height),r<=w&&(p.left=n,p.top=r,p.width=q-n,p.height=w-r));q=_.Yd(a);n=_.Yd(c);q.tb()!=n.tb()&&(q=q.tb().body,n=_.Hs(q,n.getWindow()),n=Xs(n,_.Ds(q)),p.left+=n.x,p.top+=n.y);a=at(a,b);b=p.left;a&4?b+=p.width:a&2&&(b+=p.width/2);b=new _.os(b,p.top+(a&1?p.height:0));b=Xs(b,m);e&&(b.x+=(a&4?-1:1)*e.x,b.y+=(a&1?-1:1)*e.y);if(h)if(l)var u=l;else if(u=_.Zs(c))u.top-=m.y,u.right-=
m.x,u.bottom-=m.y,u.left-=m.x;e=u;l=b.clone();u=at(c,d);d=_.Ks(c);a=k?k.clone():d.clone();k=l;l=a;k=k.clone();l=l.clone();a=0;if(f||u!=0)u&4?k.x-=l.width+(f?f.right:0):u&2?k.x-=l.width/2:f&&(k.x+=f.left),u&1?k.y-=l.height+(f?f.bottom:0):f&&(k.y+=f.top);h&&(e?(f=k,u=l,a=0,(h&65)==65&&(f.x<e.left||f.x>=e.right)&&(h&=-2),(h&132)==132&&(f.y<e.top||f.y>=e.bottom)&&(h&=-5),f.x<e.left&&h&1&&(f.x=e.left,a|=1),h&16&&(b=f.x,f.x<e.left&&(f.x=e.left,a|=4),f.x+u.width>e.right&&(u.width=Math.min(e.right-f.x,b+
u.width-e.left),u.width=Math.max(u.width,0),a|=4)),f.x+u.width>e.right&&h&1&&(f.x=Math.max(e.right-u.width,e.left),a|=1),h&2&&(a|=(f.x<e.left?16:0)|(f.x+u.width>e.right?32:0)),f.y<e.top&&h&4&&(f.y=e.top,a|=2),h&32&&(b=f.y,f.y<e.top&&(f.y=e.top,a|=8),f.y+u.height>e.bottom&&(u.height=Math.min(e.bottom-f.y,b+u.height-e.top),u.height=Math.max(u.height,0),a|=8)),f.y+u.height>e.bottom&&h&4&&(f.y=Math.max(e.bottom-u.height,e.top),a|=2),h&8&&(a|=(f.y<e.top?64:0)|(f.y+u.height>e.bottom?128:0)),h=a):h=256,
a=h);f=new _.Qs(0,0,0,0);f.left=k.x;f.top=k.y;f.width=l.width;f.height=l.height;h=a;h&496||(_.Ss(c,new _.os(f.left,f.top)),a=f.getSize(),_.pd(d,a)||(f=a,c=c.style,_.yd?c.MozBoxSizing="border-box":_.zd?c.WebkitBoxSizing="border-box":c.boxSizing="border-box",c.width=Math.max(f.width,0)+"px",c.height=Math.max(f.height,0)+"px"));return h};
_.mt=function(a,b){for(var c in a)if(a[c]==b)return!0;return!1};_.nt=function(a){if(a.nodeType==1)return _.Gs(a);a=a.changedTouches?a.changedTouches[0]:a;return new _.os(a.clientX,a.clientY)};_.ot=function(a){this.qa=a;this.Aa=a.xc()};_.ot.prototype.Iv=function(){pt(this)};
var pt=function(a){var b=a.qa.kd();if(a.Aa.anchorBox&&b&&b.getIframeEl())b=_.Ds(b.getIframeEl()),a.Aa.anchorBox.left+=b.x,a.Aa.anchorBox.top+=b.y;else{b=a.Aa.anchor;if(b!="_default"&&b!="_iframe"){var c=_.vs(b);if(c)a.Aa.anchorBox=_.$s(c);else{_.Sf.error("Anchor not found in DOM: "+b+'. Falling back to "_default".');a.Aa.anchor="_default";return}}b=="_iframe"&&(b=_.de(),a.Aa.anchorBox=new _.Qs(0,0,b.width,b.height))}a.Aa.anchor=""};_.ot.prototype.onBeforeParentOpen=_.ot.prototype.Iv;_.qt=function(a){_.ot.call(this,a)};_.y(_.qt,_.ot);_.g=_.qt.prototype;_.g.open=function(){var a=this.Aa,b=document.createElement("ins");document.getElementById(a.container).appendChild(b);b.style.display="block";_.xs(b,a.containerCss);this.qa.setSiteEl(b);this.qa.hh(b)};_.g.Qd=function(){document.getElementById(this.qa.id).style.height=this.qa.height+"px"};_.g.close=function(){_.oe(this.qa.getSiteEl());_.oe(this.Gh);this.Gh=null};_.g.SK=_.jb(6);
_.g.cj=function(){if(this.Gh)return this.Gh;var a=this.Aa;!a.anchorBox&&a.anchor&&pt(this);var b=this.qa.kd();if(a.anchor=="_default"&&b){var c=b.getSiteEl();c=_.$s(_.vs(c));a.anchorBox=c}if(!a.anchorBox)return _.Sf.error("No anchor box defined."),null;a=new _.os(a.anchorBox.left,a.anchorBox.top);b&&(b=_.Hs(b.getIframeEl(),window),this.To=new _.os,this.To.x=b.x,this.To.y=b.y,a.x+=b.x,a.y+=b.y,_.rt(a));this.BE=a;b=_.st(this,!0);this.Gh=document.createElement("ins");this.Gh.style.cssText=b;document.body.appendChild(this.Gh);
return this.Gh};_.st=function(a,b){var c=a.Aa;return"position: absolute !important;background-color: transparent !important;left: "+a.BE.x+"px !important;top: "+a.BE.y+"px !important;width: "+c.anchorBox.width+"px !important;height: "+c.anchorBox.height+"px !important;z-index: -10000 !important;display: "+(b?"block":"none")+" !important;"};
_.tt=function(a,b){var c=0,d=0;if(b.pageX||b.pageY)c=b.pageX,d=b.pageY;else if(b.clientX||b.clientY)c=b.target?b.target:b.srcElement,d=c.ownerDocument&&c.ownerDocument.parentWindow?c.ownerDocument.parentWindow:window,_.vd?(c=d.document.documentElement.scrollLeft,d=d.document.documentElement.scrollTop):(c=d.pageXOffset,d=d.pageYOffset),c=b.clientX+c,d=b.clientY+d;b=new _.os(c,d);a=_.$s(a);return a=new _.ms(a.top,a.left+a.width,a.top+a.height,a.left),a.contains(b)};
_.rt=function(a){var b=window,c=document.body,d=_.Ds(c);b=c.currentStyle||b.getComputedStyle(c,"");b.position&&b.position!="static"&&(a.x-=d.x,a.y-=d.y)};_.ut=function(a){var b=a.qa.kd()&&a.qa.kd().getSiteEl();b=b&&b.style.zIndex?parseInt(b.style.zIndex,10)+1:0;return Math.min(2147483647,Math.max(2E9,a.Aa.zIndex||b))};var vt,wt,xt;vt={"bottom-center":1,"bottom-end":13,"bottom-left":1,"bottom-right":5,"bottom-start":9,"left-bottom":1,"left-center":0,"left-top":0,"right-bottom":5,"right-center":4,"right-top":4,"top-center":0,"top-end":12,"top-left":0,"top-right":4,"top-start":8};wt={"bottom-center":!0,"top-center":!0};xt={"left-center":!0,"right-center":!0};
_.zt=function(a,b,c,d,e){e=e||{x:0,y:0};if(wt[b]){var f=_.Ks(a).width/2;e.x=d=="top-right"||d=="bottom-right"?e.x+f:e.x-f}wt[d]&&(f=_.Ks(c).width/2,e.x+=f);xt[b]&&(f=_.Ks(a).height/2,e.y=d=="right-bottom"||d=="left-bottom"?e.y+f:e.y-f);xt[d]&&(e.y+=_.Ks(c).height/2);_.bt(c,vt[d],a,vt[b],new _.os(e.x,e.y));d=_.ht(a,function(h){if(h==document)return!1;h=_.Rs(h);return!!h&&h!="static"});c=b=0;d&&(c=_.Ds(d),b=-c.x,c=-c.y);a=a.style;parseInt(a.left,10)<b&&(a.left=b+"px");parseInt(a.top,10)<c&&(a.top=c+
"px")};_.At=function(a){_.ot.call(this,a.qa);this.qh=a;this.gF=[]};_.y(_.At,_.qt);_.g=_.At.prototype;_.g.Iv=function(){this.qh.Iv()};_.g.open=function(){this.qh.open();(this.Aa.closeClickDetection||this.Aa.hideClickDetection)&&Bt(this)};_.g.Qd=function(a){this.qh.Qd(a)};_.g.HX=function(a){this.qh.onRenderStart&&this.qh.onRenderStart(a)};_.g.close=function(){if(this.Aa.closeClickDetection||this.Aa.hideClickDetection)_.Bb(this.gF,function(a){_.Ij(a)}),this.gF=[];this.qh.close()};
var Bt=function(a){_.Bb(["click","touchstart"],(0,_.z)(function(b){this.gF.push(_.Bj(document,b,(0,_.z)(this.q7,this),!0))},a))};_.At.prototype.q7=function(a){_.tt(this.qa.getSiteEl(),a)||(this.Aa.hideClickDetection&&this.qh.show?this.qh.show(!1):this.close())};_.At.prototype.onBeforeParentOpen=_.At.prototype.Iv;_.At.prototype.open=_.At.prototype.open;_.At.prototype.onready=_.At.prototype.Qd;_.At.prototype.onRenderStart=_.At.prototype.HX;_.At.prototype.close=_.At.prototype.close;
var Jda,L7,M7;for(_.K7=function(a){return{xc:function(){return a},kd:function(){return a.openerIframe}}},Jda=function(a){(new _.ot(_.K7(a))).Iv()},L7="bubble circlepicker float hover hover-menu slide-menu".split(" "),M7=0;M7<L7.length;M7++)_.Tn[L7[M7]]=Jda;
_.HG=function(a){_.ot.call(this,a.qa);this.qh=a;this.Kt=null};_.y(_.HG,_.qt);_.HG.prototype.open=function(){this.Aa.targetPos=this.Aa.targetPos||"top-start";this.Aa.anchorPos=this.Aa.anchorPos||"bottom-start";var a=this.cj(),b=this.qa.getSiteEl();b?(b.style.visibility="hidden",b.style.position="absolute",a.parentNode.appendChild(b)):this.qh.open()};
_.HG.prototype.Qd=function(){if(this.Aa.closeClickDetection){var a=this,b=function(e){_.tt(a.qa.getSiteEl(),e)||(a.qa.close(),a.Kt=null)};document.V6?(document.V6("click",b),this.Kt=function(){document.removeEventListener("click",b,!1)}):document.attachEvent&&(document.attachEvent("onclick",b),this.Kt=function(){document.detachEvent("onclick",b)})}var c=document.getElementById(this.qa.id),d=this.qa.getSiteEl();c.style.height=this.qa.height+"px";(c=this.cj())&&_.zt(d,this.Aa.targetPos,c,this.Aa.anchorPos,
{x:this.Aa.leftOffset||0,y:this.Aa.topOffset||0});d.style.visibility="visible"};_.HG.prototype.close=function(){this.qh.close();this.Kt&&this.Kt()};_.HG.prototype.open=_.HG.prototype.open;_.HG.prototype.onready=_.HG.prototype.Qd;_.HG.prototype.close=_.HG.prototype.close;_.IG=function(a){_.ot.call(this,a);this.Na=document.createElement("div")};_.y(_.IG,_.qt);_.g=_.IG.prototype;_.g.create=function(a){var b={position:"absolute",top:"-10000px",zIndex:_.ut(this)};this.Aa.width&&(b.width=this.Aa.width+"px");for(var c in b)this.Na.style[c]=b[c];(a||document.body).appendChild(this.Na)};_.g.open=function(a){this.qa.Qh("updateContainer",(0,_.z)(this.ol,this));this.create(a);this.qa.hh(this.Na);this.qa.setSiteEl(this.Na)};
_.g.Qd=function(){var a=JG(this);if(a){var b=document.getElementById(this.qa.getId());b.style.height=a.height+"px";b.style.width=a.width+"px";this.qa.width=a.width;this.qa.height=a.height;b.style.boxShadow="0 4px 16px rgba(0, 0, 0, 0.3)"}};_.g.close=function(){this.Na.parentNode&&this.Na.parentNode.removeChild(this.Na)};
_.g.ol=function(a,b,c){var d=this.qa.getSiteEl();d&&(a?(this.Bi(b,c),d.style.opacity=0,d.style.display="",window.setTimeout((0,_.z)(function(){KG(d,!0);d.style.opacity=1},this),0)):(d.style.display="none",KG(d,!1),d.style.opacity=0))};var KG=function(a,b){for(var c=0;c<NG.length;c++)a.style[NG[c]]=b?"opacity .13s linear":""};
_.IG.prototype.Bi=function(a,b){var c=this.qa.kd();a+=10;b+=10;c&&(c=_.Hs(c.getSiteEl(),window),a+=c.x,b+=c.y);if(c=JG(this)){var d=_.de(window),e=_.qs(document);if(d.width&&(a=Math.min(a,d.width+e.x-c.width-8),b+c.height>d.height+e.y-8)){b-=20+c.height;var f=window,h=f.document;d=0;if(h){d=h.body;var k=h.documentElement;if(k&&d)if(f=_.ce(f).height,_.ee(h)&&k.scrollHeight)d=k.scrollHeight!=f?k.scrollHeight:k.offsetHeight;else{h=k.scrollHeight;var l=k.offsetHeight;k.clientHeight!=l&&(h=d.scrollHeight,
l=d.offsetHeight);d=h>f?h>l?h:l:h<l?h:l}else d=0}b=Math.max(b,Math.min(e.y+1,d-c.height))}}c=this.qa.getSiteEl();c.style.left=a+"px";c.style.top=b+"px"};var JG=function(a){return a.qa.width&&a.qa.height?{width:a.qa.width,height:a.qa.height}:(a=a.qa.getIframeEl())&&a.offsetWidth&&a.offsetHeight?{width:a.offsetWidth,height:a.offsetHeight}:null},NG=["transition","WebkitTransition","MozTransition","OTranstion","msTransition"];_.IG.prototype.open=_.IG.prototype.open;_.IG.prototype.onready=_.IG.prototype.Qd;
_.IG.prototype.close=_.IG.prototype.close;_.OG=function(a){_.ot.call(this,a)};_.y(_.OG,_.qt);_.OG.prototype.open=function(){var a=document.createElement("div");_.xs(a,{top:"-10000px",position:"absolute",zIndex:_.ut(this)});this.cj().parentNode.appendChild(a);this.qa.setSiteEl(a);this.qa.hh(a)};
_.OG.prototype.Qd=function(){var a=document.getElementById(this.qa.id);a.style.height=this.qa.height+"px";a.style.width=this.qa.width+"px";a.style.boxShadow="0 4px 16px rgba(0, 0, 0, 0.3)";var b=this.qa.getSiteEl();b.style.lineHeight=0;var c=this.cj(),d=this.Aa.targetPos||"top-start",e=this.Aa.anchorPos||"bottom-start",f=this.Aa.leftOffset||0,h=this.Aa.topOffset||0;_.zt(b,d,c,e,{x:f,y:h});var k=_.de(window),l=_.qs(document),m=b.offsetLeft<l.x||b.offsetLeft+b.offsetWidth>k.width+l.x;k=b.offsetTop<
l.y||b.offsetTop+b.offsetHeight>k.height+l.y;d=PG(d,m,k);e=PG(e,m,k);_.zt(b,d,c,e,{x:f*(m?-1:1),y:h*(k?-1:1)});b.style.visibility="visible";this.qB=_.Bj(document,"mouseover",(0,_.z)(function(n){n.target===a&&this.yn&&(window.clearTimeout(this.yn),this.yn=null)},this));this.eJ=_.Bj(document,"mouseout",(0,_.z)(function(n){n.target===a&&(this.yn=window.setTimeout((0,_.z)(this.qa.close,this.qa),1E3))},this))};
var PG=function(a,b,c){a=a.split("-");for(var d=0;d<2;d++)b&&QG[a[d]]&&(a[d]=QG[a[d]]),c&&RG[a[d]]&&(a[d]=RG[a[d]]);return a.join("-")};_.OG.prototype.close=function(){this.qB&&(_.Ij(this.qB),_.Ij(this.eJ),this.eJ=this.qB=null);this.yn&&(window.clearTimeout(this.yn),this.yn=null);_.qt.prototype.close.call(this)};_.OG.prototype.qB=null;_.OG.prototype.eJ=null;_.OG.prototype.yn=null;var QG={end:"start",left:"right",right:"left",start:"end"},RG={top:"bottom",bottom:"top"};_.OG.prototype.open=_.OG.prototype.open;
_.OG.prototype.onready=_.OG.prototype.Qd;_.OG.prototype.close=_.OG.prototype.close;
_.Sn.hover=function(a){var b=new _.IG(_.K7(a));b.create(a.where);a.where=b.Na;a.onClose=function(){b.close()};a.onRestyle=function(c){if(c.updateContainer){var d=c.updateContainer;b.ol(d.visible,d.x,d.y)}c.width&&(b.qa.width=c.width);c.height&&(b.qa.height=c.height)};a.onCreate=function(c){b.qa=c;c.kd=function(){return a.openerIframe};c.register("_ready",(0,_.z)(b.Qd,b),_.dn);c.updateContainer=function(d,e,f){b.ol(d,e,f)}}};
var Vt;_.Ut={};Vt={};_.Wt=function(a,b){this.t0=a===_.Ut&&b||"";this.Y5=Vt};_.Wt.prototype.toString=function(){return this.t0};_.Xt=function(a){return a instanceof _.Wt&&a.constructor===_.Wt&&a.Y5===Vt?a.t0:"type_error:Const"};
_.FF=function(){};_.FF.prototype.next=function(){return _.GF};_.GF={done:!0,value:void 0};_.HF=function(a){return{value:a,done:!1}};_.FF.prototype.Bh=function(){return this};
_.XH=_.Zj(function(a){return typeof a==="number"});_.YH=_.Zj(function(a){return typeof a==="string"});_.ZH=_.Zj(function(a){return typeof a==="boolean"});
var yK,BK,EK,JK,MK,gL,VK;_.fK=function(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};_.gK=function(a,b,c){return typeof Symbol==="function"&&typeof Symbol()==="symbol"?(c===void 0?0:c)&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol():b};_.lK=function(a,b){_.hK||_.iK in a||jK(a,kK);a[_.iK]|=b};_.mK=function(a,b){_.hK||_.iK in a||jK(a,kK);a[_.iK]=b};_.nK=function(a,b){a[_.iK]&=~b};
_.oK=function(a){if(4&a)return 2048&a?2048:4096&a?4096:0};_.rK=function(a){return a[pK]===qK};_.tK=function(a,b){return b===void 0?a.qF!==sK&&!!(2&(a.V[_.iK]|0)):!!(2&b)&&a.qF!==sK};_.uK=function(a){return a!==null&&typeof a==="object"&&!Array.isArray(a)&&a.constructor===Object};_.vK=function(a,b){if(typeof b!=="number"||b<0||b>=a.length)throw Error();};_.wK=function(a,b){if(typeof b!=="number"||b<0||b>a.length)throw Error();};_.xK=function(){return typeof BigInt==="function"};yK=function(a){return a};
_.AK=function(a){var b=a;if((0,_.YH)(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if((0,_.XH)(b)&&!Number.isSafeInteger(b))throw Error(String(b));return zK?BigInt(a):a=(0,_.ZH)(a)?a?"1":"0":(0,_.YH)(a)?a.trim()||"0":String(a)};BK=function(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};EK=function(a){var b=a>>>0;_.CK=b;_.DK=(a-b)/4294967296>>>0};
_.GK=function(a){if(a<0){EK(-a);var b=_.Ca(_.FK(_.CK,_.DK));a=b.next().value;b=b.next().value;_.CK=a>>>0;_.DK=b>>>0}else EK(a)};_.IK=function(a,b){var c=b*4294967296+(a>>>0);return Number.isSafeInteger(c)?c:_.HK(a,b)};
_.HK=function(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else _.xK()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+JK(c)+JK(a));return c};JK=function(a){a=String(a);return"0000000".slice(a.length)+a};
_.KK=function(a){if(a.length<16)_.GK(Number(a));else if(_.xK())a=BigInt(a),_.CK=Number(a&BigInt(4294967295))>>>0,_.DK=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");_.DK=_.CK=0;for(var c=a.length,d=b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),_.DK*=1E6,_.CK=_.CK*1E6+d,_.CK>=4294967296&&(_.DK+=Math.trunc(_.CK/4294967296),_.DK>>>=0,_.CK>>>=0);b&&(b=_.Ca(_.FK(_.CK,_.DK)),a=b.next().value,b=b.next().value,_.CK=a,_.DK=b)}};_.FK=function(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};
MK=function(a,b){if(a!=null){var c;var d=(c=LK)!=null?c:LK={};c=d[a]||0;c>=b||(d[a]=c+1,a=Error(),_.fK(a,"incident"),_.ph(a))}};_.NK=function(a){return Array.prototype.slice.call(a)};_.OK=function(a){if(a!=null&&typeof a!=="boolean")throw Error("qb`"+_.gd(a)+"`"+a);return a};_.RK=function(a){switch(typeof a){case "bigint":return!0;case "number":return(0,_.PK)(a);case "string":return QK.test(a);default:return!1}};_.SK=function(a){return a==null||typeof a==="string"?a:void 0};
_.UK=function(a,b,c,d){if(a!=null&&typeof a==="object"&&_.rK(a))return a;if(!Array.isArray(a))return c?d&2?((a=b[_.TK])||(a=new b,_.lK(a.V,34),a=b[_.TK]=a),b=a):b=new b:b=void 0,b;var e=c=a[_.iK]|0;e===0&&(e|=d&32);e|=d&2;e!==c&&_.mK(a,e);return new b(a)};_.XK=function(a){var b=VK(WK);return b?a[b]:void 0};_.ZK=function(a,b){var c=VK(WK),d;_.hK&&c&&((d=a[c])==null?void 0:d[b])!=null&&MK(YK,3)};
_.bL=function(a,b,c,d,e){var f=d?!!(b&32):void 0;d=[];var h=a.length,k=!1;if(b&64){if(b&256){h--;var l=a[h];var m=h}else m=4294967295,l=void 0;if(!(e||b&512)){k=!0;var n;var p=((n=$K)!=null?n:yK)(l?m- -1:b>>16&1023||536870912,-1,a,l);m=p+-1}}else m=4294967295,b&1||(l=h&&a[h-1],_.uK(l)?(h--,m=h,p=0):l=void 0);n=void 0;for(var q=0;q<h;q++){var r=a[q];if(r!=null&&(r=c(r,f))!=null)if(q>=m){var w=void 0;((w=n)!=null?w:n={})[q- -1]=r}else d[q]=r}if(l)for(var u in l)h=l[u],h!=null&&(h=c(h,f))!=null&&(q=
+u,q<p?d[q+-1]=h:(q=void 0,((q=n)!=null?q:n={})[u]=h));n&&(k?d.push(n):d[m]=n);e&&(_.mK(d,b&67043905|(n!=null?290:34)),VK(WK)&&(a=_.XK(a))&&"function"==typeof _.aL&&a instanceof _.aL&&(d[WK]=a.H7()));return d};
_.dL=function(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return(0,_.cL)(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[_.iK]|0;return a.length===0&&b&1?void 0:_.bL(a,b,_.dL,!1,!1)}if(_.rK(a))return _.eL(a);if("function"==typeof _.fL&&a instanceof _.fL)return a.GE();return}return a};_.eL=function(a){a=a.V;return _.bL(a,a[_.iK]|0,_.dL,void 0,!1)};
_.hL=function(a,b,c){if(a==null){var d=96;c?(a=[c],d|=512):a=[];b&&(d=d&-67043329|(b&1023)<<16)}else{if(!Array.isArray(a))throw Error("tb");d=a[_.iK]|0;8192&d||!(64&d)||2&d||gL();if(d&1024)throw Error("vb");if(d&64)return d&16384||_.mK(a,d|16384),a;d|=64;if(c&&(d|=512,c!==a[0]))throw Error("wb");a:{c=a;var e=c.length;if(e){var f=e-1,h=c[f];if(_.uK(h)){d|=256;b=d&512?0:-1;f-=b;if(f>=1024)throw Error("yb");for(var k in h)e=+k,e<f&&(c[e+b]=h[k],delete h[k]);d=d&-67043329|(f&1023)<<16;break a}}if(b){k=
Math.max(b,e-(d&512?0:-1));if(k>1024)throw Error("zb");d=d&-67043329|(k&1023)<<16}}}d|=16384;_.mK(a,d);return a};gL=function(){MK(iL,5)};
_.jL=function(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[_.iK]|0;if(a.length===0&&c&1)return;if(c&2)return a;var d;if(d=b)d=c===0||!!(c&32)&&!(c&64||!(c&16));return d?(_.lK(a,34),c&4&&Object.freeze(a),a):_.bL(a,c,_.jL,b!==void 0,!0)}if(_.rK(a))return b=a.V,c=b[_.iK]|0,_.tK(a,c)?a:_.bL(b,c,_.jL,!0,!0);if("function"==typeof _.fL&&a instanceof _.fL)return a};_.kL=function(a){var b=a.V,c=b[_.iK]|0;if(!_.tK(a,c))return a;a=new a.constructor(_.bL(b,c,_.jL,!0,!0));_.nK(a.V,2);return a};
_.lL=function(a){if(a.qF!==sK)return!1;var b=a.V;b=_.bL(b,b[_.iK]|0,_.jL,!0,!0);_.nK(b,2);a.V=b;a.qF=void 0;return!0};_.mL=function(a){if(!_.lL(a)&&_.tK(a,a.V[_.iK]|0))throw Error();};_.nL=function(a,b,c,d,e){var f=c+(e?0:-1),h=a.length-1,k;if(f>=h&&((k=b)!=null?k:b=a[_.iK]|0)&256)return a[h][c]=d,b;if(f<=h)return a[f]=d,b;d!==void 0&&(h=b>>16&1023||536870912,c>=h?d!=null&&(f={},a[h+(e?0:-1)]=(f[c]=d,f),b|=256,_.mK(a,b)):a[f]=d);return b};
_.qL=function(a,b,c,d){a=_.oL(a,b,c,d);return Array.isArray(a)?a:pL};_.sL=function(a,b){a===0&&(a=_.rL(a,b),a|=16);return a|1};_.tL=function(a){return!!(2&a)&&!!(4&a)||!!(1024&a)};_.uL=function(a,b,c,d){_.mL(a);var e=a.V;_.nL(e,e[_.iK]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a};_.vL=function(a,b,c,d,e){var f=_.oL(a,b,d,e);c=_.UK(f,c,!1,b);c!==f&&c!=null&&_.nL(a,b,d,c,e);return c};
_.xL=function(a,b,c,d,e,f,h,k,l){var m=_.tK(a,c);f=m?1:f;k=!!k||f===3;m=l&&!m;(f===2||m)&&_.lL(a)&&(b=a.V,c=b[_.iK]|0);a=_.qL(b,c,e,h);var n=a[_.iK]|0;l=!!(4&n);if(!l){n=_.sL(n,c);var p=a,q=c,r=!!(2&n);r&&(q|=2);for(var w=!r,u=!0,x=0,A=0;x<p.length;x++){var C=_.UK(p[x],d,!1,q);if(C instanceof d){if(!r){var F=_.tK(C);w&&(w=!F);u&&(u=F)}p[A++]=C}}A<x&&(p.length=A);n|=4;n=u?n|16:n&-17;n=w?n|8:n&-9;_.mK(p,n);r&&Object.freeze(p)}if(m&&!(8&n||!a.length&&(f===1||f===4&&32&n))){_.tL(n)&&(a=_.NK(a),n=_.rL(n,
c),c=_.nL(b,c,e,a,h));d=a;m=n;for(p=0;p<d.length;p++)n=d[p],q=_.kL(n),n!==q&&(d[p]=q);m|=8;m=d.length?m&-17:m|16;_.mK(d,m);n=m}f===1||f===4&&32&n?_.tL(n)||(c=n,n|=!a.length||16&n&&(!l||32&n)?2:1024,n!==c&&_.mK(a,n),Object.freeze(a)):(f===2&&_.tL(n)&&(a=_.NK(a),n=_.rL(n,c),n=_.wL(n,c,k),_.mK(a,n),c=_.nL(b,c,e,a,h)),_.tL(n)||(b=n,n=_.wL(n,c,k),n!==b&&_.mK(a,n)));return a};_.rL=function(a,b){2&a&&(a|=16);a=(2&b?a|2:a&-3)|32;return a&=-1025};_.wL=function(a,b,c){32&b&&c||(a&=-33);return a};
_.yL=function(a,b,c,d,e,f,h,k){_.mL(a);var l=a.V;a=_.xL(a,l,l[_.iK]|0,c,b,2,d,!0);h&&k?(f!=null||(f=a.length-1),_.vK(a,f),a.splice(f,h),a.length||_.lK(a,16)):(h?_.wK(a,f):e=e!=null?e:new c,f!=void 0?a.splice(f,h,e):a.push(e),f=c=a[_.iK]|0,_.tK(e)?(c&=-9,a.length===1&&(c|=16)):c&=-17,c!==f&&_.mK(a,c))};VK=function(a){return a};_.zL=typeof Uint8Array!=="undefined";_.AL=!_.vd&&typeof btoa==="function";var BL,WK,YK,iL,pK;_.hK=typeof Symbol==="function"&&typeof Symbol()==="symbol";BL=_.gK("jas",void 0,!0);_.TK=_.gK(void 0,"0di");WK=_.gK(void 0,Symbol());YK=_.gK(void 0,"0ub");iL=_.gK(void 0,"0actk");pK=_.gK("m_m","Dra",!0);var kK,jK,pL,CL;kK={Dba:{value:0,configurable:!0,writable:!0,enumerable:!1}};jK=Object.defineProperties;_.iK=_.hK?BL:"Dba";CL=[];_.mK(CL,55);pL=Object.freeze(CL);var qK,sK;qK={};sK={};_.DL=Object.freeze({});var zK=typeof _.Xa.BigInt==="function"&&typeof _.Xa.BigInt(0)==="bigint";var GL,EL,HL,FL;_.cL=_.Zj(function(a){return zK?a>=EL&&a<=FL:a[0]==="-"?BK(a,GL):BK(a,HL)});GL=Number.MIN_SAFE_INTEGER.toString();EL=zK?BigInt(Number.MIN_SAFE_INTEGER):void 0;HL=Number.MAX_SAFE_INTEGER.toString();FL=zK?BigInt(Number.MAX_SAFE_INTEGER):void 0;_.CK=0;_.DK=0;var LK=void 0;var QK;_.IL=typeof BigInt==="function"?BigInt.asIntN:void 0;_.JL=typeof BigInt==="function"?BigInt.asUintN:void 0;_.KL=Number.isSafeInteger;_.PK=Number.isFinite;_.LL=Math.trunc;QK=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;var $K;_.ML=_.AK(0);_.NL=function(a,b,c){return _.oL(a.V,void 0,b,c)};_.oL=function(a,b,c,d,e){if(c===-1)return null;d=c+(d?0:-1);var f=a.length-1;if(d>=f&&(b!=null?b:a[_.iK]|0)&256){b=a[f][c];var h=!0}else if(d<=f)b=a[d];else return;if(e&&b!=null){e=e(b);if(e==null)return e;if(e!==b)return h?a[f][c]=e:a[d]=e,e}return b};_.OL=function(a,b,c,d){a=a.V;return _.vL(a,a[_.iK]|0,b,c,d)!==void 0};_.PL=function(a,b,c){this.V=_.hL(a,b,c)};_.PL.prototype.toJSON=function(){return _.eL(this)};_.QL=function(a,b){if(b==null||b=="")return new a;b=JSON.parse(b);if(!Array.isArray(b))throw Error("Kb");_.lK(b,32);return new a(b)};_.PL.prototype.getExtension=function(a){_.ZK(this.V,175237375);return a.ctor?a.Ez(this,a.ctor,175237375,a.Su):a.Ez(this,175237375,a.defaultValue,a.Su)};
_.RL=function(a,b){_.ZK(a.V,175237375);a=b.ctor?b.Ez(a,b.ctor,175237375,b.Su):b.Ez(a,175237375,null,b.Su);return a===null?void 0:a};_.PL.prototype.hasExtension=function(a){_.ZK(this.V,175237375);return a.ctor?_.OL(this,a.ctor,175237375,a.Su):_.RL(this,a)!==void 0};_.PL.prototype.clone=function(){var a=this,b=a.V;a=new a.constructor(_.bL(b,b[_.iK]|0,_.jL,!0,!0));_.nK(a.V,2);return a};_.PL.prototype[pK]=qK;_.PL.prototype.toString=function(){return this.V.toString()};
_.cM=function(a,b){return/-[a-z]/.test(b)?!1:_.aM&&a.dataset?b in a.dataset:a.hasAttribute?a.hasAttribute("data-"+_.bM(b)):!!a.getAttribute("data-"+_.bM(b))};_.bM=function(a){return String(a).replace(/([A-Z])/g,"-$1").toLowerCase()};_.aM=!_.vd&&!_.th();
_.dM=function(a,b){this.v7=a[_.Xa.Symbol.iterator]();this.Cca=b};_.dM.prototype[Symbol.iterator]=function(){return this};_.dM.prototype.next=function(){var a=this.v7.next();return{value:a.done?void 0:this.Cca.call(void 0,a.value),done:a.done}};_.eM=function(a){this.rG=a};_.eM.prototype.Bh=function(){return new _.fM(this.rG())};_.eM.prototype[Symbol.iterator]=function(){return new _.gM(this.rG())};_.eM.prototype.Yw=_.jb(44);_.fM=function(a){this.uv=a};_.y(_.fM,_.FF);_.fM.prototype.next=function(){return this.uv.next()};_.fM.prototype[Symbol.iterator]=function(){return new _.gM(this.uv)};_.fM.prototype.Yw=_.jb(43);_.gM=function(a){_.eM.call(this,function(){return a});this.uv=a};_.y(_.gM,_.eM);_.gM.prototype.next=function(){return this.uv.next()};
var hN,kN,pN,sN,vN,wN,xN,yN,CN,DN,ON,RN,SN,TN,VN,WN,lN,mN,jN,ZN,JN;_.gN=function(a){var b=a.V,c=b[_.iK]|0;return _.tK(a,c)?a:new a.constructor(_.bL(b,c,_.jL,!0,!0))};
hN=function(a){var b=_.Gc();if(a==="Internet Explorer")return _.Oc()?_.Vc(b):"";b=_.Lc(b);var c=_.Uc(b);switch(a){case "Opera":if(_.Nc())return c(["Version","Opera"]);if(_.Sc())return c(["OPR"]);break;case "Microsoft Edge":if(_.Pc())return c(["Edge"]);if(_.Qc())return c(["Edg"]);break;case "Chromium":if(_.Tc())return c(["Chrome","CriOS","HeadlessChrome"])}return a==="Firefox"&&_.sh()||a==="Safari"&&_.th()||a==="Android Browser"&&_.uh()||a==="Silk"&&_.Kc("Silk")?(a=b[2])&&a[1]||"":""};
_.iN=function(a){if(_.Mc()&&a!=="Silk"){var b=_.Hc.brands.find(function(c){return c.brand===a});if(!b||!b.version)return NaN;b=b.version.split(".")}else{b=hN(a);if(b==="")return NaN;b=b.split(".")}return b.length===0?NaN:Number(b[0])};kN=function(a){return jN[a]||""};_.nN=function(a){if(!_.AL)return lN(a);mN.test(a)&&(a=a.replace(mN,kN));a=atob(a);for(var b=new Uint8Array(a.length),c=0;c<a.length;c++)b[c]=a.charCodeAt(c);return b};_.oN=function(a){return _.zL&&a!=null&&a instanceof Uint8Array};
pN=function(){var a=_.CK,b=_.DK;b&2147483648?_.xK()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=_.Ca(_.FK(a,b)),a=b.next().value,b=b.next().value,a="-"+_.HK(a,b)):a=_.HK(a,b);return a};_.qN=function(a){a=Error(a);_.fK(a,"warning");return a};_.rN=function(a){if(a!=null&&typeof a!=="number")throw Error("pb`"+typeof a+"`"+a);return a};sN=function(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)};
_.tN=function(a){if(!(0,_.PK)(a))throw _.qN("enum");return a|0};_.uN=function(a){if(typeof a!=="number")throw _.qN("int32");if(!(0,_.PK)(a))throw _.qN("int32");return a|0};vN=function(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return(0,_.PK)(a)?a|0:void 0};wN=function(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337};xN=function(a){if(wN(a))return a;_.KK(a);return pN()};
yN=function(a){var b=(0,_.LL)(Number(a));if((0,_.KL)(b))return _.AK(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return _.xK()?_.AK((0,_.IL)(64,BigInt(a))):_.AK(xN(a))};_.zN=function(a){var b=(0,_.LL)(Number(a));if((0,_.KL)(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return xN(a)};_.AN=function(a){a=(0,_.LL)(a);if(!(0,_.KL)(a)){_.GK(a);var b=_.CK,c=_.DK;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);b=_.IK(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a};
_.BN=function(a){a=(0,_.LL)(a);if((0,_.KL)(a))a=String(a);else{var b=String(a);wN(b)?a=b:(_.GK(a),a=pN())}return a};CN=function(a){return(0,_.KL)(a)?_.AK(_.AN(a)):_.AK(_.BN(a))};
DN=function(a,b){b=b===void 0?0:b;if(!_.RK(a))throw _.qN("int64");var c=typeof a;switch(b){case 2048:switch(c){case "string":return _.zN(a);case "bigint":return String((0,_.IL)(64,a));default:return _.BN(a)}case 4096:switch(c){case "string":return yN(a);case "bigint":return _.AK((0,_.IL)(64,a));default:return CN(a)}case 0:switch(c){case "string":return _.zN(a);case "bigint":return _.AK((0,_.IL)(64,a));default:return _.AN(a)}default:return _.rb(b,"Unknown format requested type for int64")}};
_.EN=function(a,b){return a==null?a:DN(a,b===void 0?0:b)};_.FN=function(a){var b=typeof a;if(a==null)return a;if(b==="bigint")return _.AK((0,_.IL)(64,a));if(_.RK(a))return b==="string"?yN(a):CN(a)};_.GN=function(a){if(typeof a!=="string")throw Error();return a};_.HN=function(a){if(a!=null&&typeof a!=="string")throw Error();return a};
_.IN=function(a,b,c,d,e){_.mL(a);var f=a.V,h=f[_.iK]|0;if(c==null)return _.nL(f,h,b,void 0,e),a;var k=c[_.iK]|0,l=k,m=_.tL(k),n=m||Object.isFrozen(c);m||(k=0);n||(c=_.NK(c),l=0,k=_.rL(k,h),k=_.wL(k,h,!0),n=!1);k|=21;var p;m=(p=_.oK(k))!=null?p:0;for(p=0;p<c.length;p++){var q=c[p],r=d(q,m);Object.is(q,r)||(n&&(c=_.NK(c),l=0,k=_.rL(k,h),k=_.wL(k,h,!0),n=!1),c[p]=r)}k!==l&&(n&&(c=_.NK(c),k=_.rL(k,h),k=_.wL(k,h,!0)),_.mK(c,k));_.nL(f,h,b,c,e);return a};
_.KN=function(a){if(_.hK){var b;return(b=a[JN])!=null?b:a[JN]=new Map}if(JN in a)return a[JN];b=new Map;Object.defineProperty(a,JN,{value:b});return b};_.LN=function(a,b,c,d){var e=a.get(d);if(e!=null)return e;for(var f=e=0;f<d.length;f++){var h=d[f];_.oL(b,c,h,void 0)!=null&&(e!==0&&(c=_.nL(b,c,e,void 0,void 0)),e=h)}a.set(d,e);return e};_.MN=function(a){return a===_.DL?2:4};_.NN=function(a){return JSON.stringify(_.eL(a))};
ON=function(){this.oI=!1;this.mn=null;this.Wj=void 0;this.Pc=1;this.In=this.vn=0;this.xS=this.Ch=null};_.g=ON.prototype;_.g.Kw=function(){if(this.oI)throw new TypeError("Generator is already running");this.oI=!0};_.g.Hp=function(){this.oI=!1};_.g.Gv=function(a){this.Wj=a};_.g.Vw=function(a){this.Ch={ZR:a,LV:!0};this.Pc=this.vn||this.In};_.g.return=function(a){this.Ch={return:a};this.Pc=this.In};_.PN=function(a,b,c){a.Pc=c;return{value:b}};ON.prototype.Xg=function(a){this.Pc=a};
_.QN=function(a){a.vn=0;var b=a.Ch.ZR;a.Ch=null;return b};RN=function(a){this.qb=new ON;this.gea=a};RN.prototype.Gv=function(a){this.qb.Kw();if(this.qb.mn)return SN(this,this.qb.mn.next,a,this.qb.Gv);this.qb.Gv(a);return TN(this)};var UN=function(a,b){a.qb.Kw();var c=a.qb.mn;if(c)return SN(a,"return"in c?c["return"]:function(d){return{value:d,done:!0}},b,a.qb.return);a.qb.return(b);return TN(a)};
RN.prototype.Vw=function(a){this.qb.Kw();if(this.qb.mn)return SN(this,this.qb.mn["throw"],a,this.qb.Gv);this.qb.Vw(a);return TN(this)};SN=function(a,b,c,d){try{var e=b.call(a.qb.mn,c);if(!(e instanceof Object))throw new TypeError("Iterator result "+e+" is not an object");if(!e.done)return a.qb.Hp(),e;var f=e.value}catch(h){return a.qb.mn=null,a.qb.Vw(h),TN(a)}a.qb.mn=null;d.call(a.qb,f);return TN(a)};
TN=function(a){for(;a.qb.Pc;)try{var b=a.gea(a.qb);if(b)return a.qb.Hp(),{value:b.value,done:!1}}catch(c){a.qb.Wj=void 0,a.qb.Vw(c)}a.qb.Hp();if(a.qb.Ch){b=a.qb.Ch;a.qb.Ch=null;if(b.LV)throw b.ZR;return{value:b.return,done:!0}}return{value:void 0,done:!0}};VN=function(a){this.next=function(b){return a.Gv(b)};this.throw=function(b){return a.Vw(b)};this.return=function(b){return UN(a,b)};this[Symbol.iterator]=function(){return this}};
WN=function(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new Promise(function(d,e){function f(h){h.done?d(h.value):Promise.resolve(h.value).then(b,c).then(f,e)}f(a.next())})};_.XN=function(a){return WN(new VN(new RN(a)))};lN=function(a){var b=a.length,c=b*3/4;c%3?c=Math.floor(c):_.xc("=.",a[b-1])&&(c=_.xc("=.",a[b-2])?c-2:c-1);var d=new Uint8Array(c),e=0;_.Mh(a,function(f){d[e++]=f});return e!==c?d.subarray(0,e):d};mN=/[-_.]/g;jN={"-":"+",_:"/",".":"="};_.YN={};
JN=_.gK(void 0,"1oa");_.D={};_.$N=function(a,b,c,d){_.mL(a);var e=a.V;_.nL(e,e[_.iK]|0,b,c,d);return a};_.aO=function(a,b){return _.oL(a.V,void 0,b,void 0,sN)};_.bO=function(a,b){a=a.V;return _.LN(_.KN(a),a,void 0,b)};_.cO=function(a,b,c){return _.bO(a,b)===c?c:-1};_.dO=function(a,b,c,d){var e=a.V,f=e[_.iK]|0;b=_.vL(e,f,b,c,d);if(b==null)return b;f=e[_.iK]|0;if(!_.tK(a,f)){var h=_.kL(b);h!==b&&(_.lL(a)&&(e=a.V,f=e[_.iK]|0),b=h,_.nL(e,f,c,b,d))}return b};
_.eO=function(a,b,c,d,e,f){_.yL(a,b,c,f,e,d,1);return a};_.fO=function(a,b,c,d,e){var f=a.V;return _.xL(a,f,f[_.iK]|0,b,c,d,e,!1,!0)};_.gO=function(a,b,c,d){c==null&&(c=void 0);_.$N(a,b,c,d);return a};
_.hO=function(a,b,c,d){_.mL(a);var e=a.V,f=e[_.iK]|0;if(c==null)return _.nL(e,f,b,void 0,d),a;for(var h=c[_.iK]|0,k=h,l=_.tL(h),m=l||Object.isFrozen(c),n=!0,p=!0,q=0;q<c.length;q++){var r=c[q];l||(r=_.tK(r),n&&(n=!r),p&&(p=r))}l||(h=n?13:5,h=p?h|16:h&-17);m&&h===k||(c=_.NK(c),k=0,h=_.rL(h,f),h=_.wL(h,f,!0));h!==k&&_.mK(c,h);_.nL(e,f,b,c,d);return a};
_.iO=function(a,b,c){a=_.NL(a,b,c);a!=null&&(typeof a==="bigint"?(0,_.cL)(a)?a=Number(a):(a=(0,_.IL)(64,a),a=(0,_.cL)(a)?Number(a):String(a)):a=_.RK(a)?typeof a==="number"?_.AN(a):_.zN(a):void 0);return a};_.G=function(a,b,c){a=_.NL(a,b,c);return a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0};_.jO=function(a,b,c){return vN(_.NL(a,b,c))};_.kO=function(a,b,c){return _.SK(_.NL(a,b,c))};_.lO=function(a,b,c){a=_.NL(a,b,c);return a==null?a:(0,_.PK)(a)?a|0:void 0};
_.mO=function(a,b,c,d){c=c===void 0?!1:c;var e;return(e=_.G(a,b,d))!=null?e:c};_.nO=function(a,b,c){c=c===void 0?0:c;var d;return(d=_.jO(a,b))!=null?d:c};_.oO=function(a,b){var c=c===void 0?"":c;var d;return(d=_.kO(a,b))!=null?d:c};_.pO=function(a,b){var c=c===void 0?0:c;var d;return(d=_.lO(a,b))!=null?d:c};_.qO=function(a,b,c,d){return _.$N(a,b,_.OK(c),d)};_.rO=function(a,b,c,d){return _.$N(a,b,c==null?c:_.uN(c),d)};_.sO=function(a,b,c,d,e){return _.$N(a,b,_.EN(c,d===void 0?0:d),e)};
_.tO=function(a,b,c,d){return _.$N(a,b,_.HN(c),d)};_.uO=function(a,b,c,d){return _.$N(a,b,c==null?c:_.tN(c),d)};_.fL=function(a,b){if(b!==_.YN)throw Error("ob");this.La=a;if(a!=null&&a.length===0)throw Error("nb");};_.vO=function(){return ZN||(ZN=new _.fL(null,_.YN))};
_.fL.prototype.GE=function(){var a=this.La;if(a==null)a="";else if(typeof a!=="string"){if(_.AL){for(var b="",c=0,d=a.length-10240;c<d;)b+=String.fromCharCode.apply(null,a.subarray(c,c+=10240));b+=String.fromCharCode.apply(null,c?a.subarray(c):a);a=btoa(b)}else a=_.Lh(a);a=this.La=a}return a};_.fL.prototype.isEmpty=function(){return this.La==null};_.fL.prototype.Nsa=function(){var a=_.wO(this);return a?a.length:0};
_.wO=function(a){if(_.YN!==_.YN)throw Error("ob");var b=a.La;b==null||_.oN(b)||(typeof b==="string"?b=_.nN(b):(_.gd(b),b=null));return b==null?b:a.La=b};
var T3;T3=function(a,b){this.zd=a;this.Ef=b};_.U3=function(a){this.w=this.Kb=a;this.n=null;this.slf=0;this.ssh=!1;this.sen=!0;this.shl=this.itm=null};T3.prototype.onReady=function(a){this.zd.or(this.Ef,a)};T3.prototype.Kv=function(){this.zd.oh(this.Ef)};T3.prototype.onClose=function(){this.zd.oc(this.Ef)};T3.prototype.onError=function(a,b,c,d,e){this.zd.oe(this.Ef,a,b,c,d,e)};_.U3.prototype.sm=function(a,b){this.Kb.wp(new T3(a,this));this.n=b};_.U3.prototype.sh=function(){this.Kb.show()};_.U3.prototype.hi=function(){this.Kb.hide()};_.U3.prototype.cl=function(){this.Kb.close()};_.U3.prototype.en=function(){this.Kb.enable()};_.U3.prototype.di=function(){this.Kb.disable()};_.U3.prototype.hl=function(a){this.Kb.O9(a)};_.U3.prototype.vr=function(a,b){this.Kb.u$(a,b)};
var T7=function(a,b,c){this.Gj=a||{};this.YH=b||0;this.tba=c||0;a={};b=(0,_.z)(this.nT,this);a.fc=b;b=(0,_.z)(this.kK,this);a.rc=b;b=(0,_.z)(this.dN,this);a.sc=b;b=(0,_.z)(this.Vu,this);a.hc=b;b=(0,_.z)(this.wt,this);a.cc=b;b=(0,_.z)(this.xU,this);a.os=b;b=(0,_.z)(this.wU,this);a.or=b;b=(0,_.z)(this.uU,this);a.oh=b;b=(0,_.z)(this.sU,this);a.oc=b;b=(0,_.z)(this.tU,this);a.oe=b;b=(0,_.z)(this.vU,this);a.oi=b;this.zd=a},U7=function(){T7.call(this)},Sda=function(a){if(window.___jsl.man)a(window.___jsl.man);
else{var b=function(){var d=new U7;window.___jsl.man=d;a(d)};if(window.gbar){var c=function(){if(window.gbar.wg){var d=new V7;window.___jsl.man=d;a(d)}else b()};window.gbar.wg?c():window.gbar.qm?window.gbar.qm(c):b()}else b()}},W7=function(){return window.___jsl.man},Tda=function(a){var b=a,c;return function(){if(b){var d=b;b=void 0;c=d.apply(this,arguments)}return c}},X7=function(a){return document.body==a?"body":a.__cardid||null},Y7=function(a){var b=X7(a);b||(b=a.__cardid=Uda++);return String(b)},
Wda=function(a){var b=a.className||"getAttribute"in a&&a.getAttribute("class");return b&&Vda.test(b)||a.getAttribute&&_.cM(a,"hovercardId")||"getAttribute"in a&&a.getAttribute("oid")&&_.ci("card/p")==36?!0:a.tagName.toUpperCase()=="G:HOVERCARD"},$7=function(a,b){var c={};_.gj(c,Z7,_.ci("iframes/card")||{},_.ci("card")||{});for(var d=[],e=a;e;e=e.parentNode){var f=X7(e);f&&b[f]&&d.push(b[f])}_.Bb(d.reverse(),function(h){_.gj(c,h)});b=a.tagName.toUpperCase()=="G:HOVERCARD"?"":"data-";d=a.attributes;
for(e=0;e<d.length;e++)_.vc(d[e].name,b)&&(c[d[e].name.substring(b.length)]=d[e].value);"getAttribute"in a&&a.getAttribute("oid")&&_.ci("card/p")==36&&(c.ytid=a.getAttribute("oid"));!c.userid&&a.tagName.toUpperCase()=="A"&&a.pathname&&(b=a.pathname.match(/^\/?(\d+)$/),/\.google\.com$/.test(a.hostname)&&b&&(c.userid=b[1]));c.hl||(c.hl=_.ci("lang")||_.Nq().hl||void 0);c.m=c.entity;c.src=c.source;delete c.entity;delete c.source;return c},a8=function(a,b){b=b[a];typeof b!=="number"&&(b=Z7[a]);return b<
0?0:b},Xda=function(a){a.zd.os(a.Ef)},b8=function(a){this.Ef=a;this.kf=0;this.nD=!1;this.SR=!0;this.fn=null},c8=function(a){return a.kf==5||a.kf==4};b8.prototype.isEnabled=function(){return this.SR};b8.prototype.qI=function(){return this.nD};b8.prototype.zc=function(a){this.SR=a};_.g=T7.prototype;_.g.kK=function(a,b,c){try{a+=b!=null?"_"+b:"",c.sm(this.zd,a),this.Gj[a]=new b8(c)}catch(d){return!1}return!0};_.g.nT=function(a,b){return(a=this.Gj[a+(b!=null?"_"+b:"")])?a.Ef:null};
_.g.dN=function(a){var b=d8(this,a);if(b&&(b.kf==2||b.kf==3)&&b.isEnabled()&&!b.qI()){try{a.sh()}catch(c){this.reportError(c,"am","shc")}b.nD=!0}};_.g.Vu=function(a){var b=d8(this,a);if(b&&(b.kf==2||b.kf==3||c8(b))&&b.qI()){try{a.hi()}catch(c){this.reportError(c,"am","hic")}b.nD=!1}};_.g.wt=function(a){var b=d8(this,a);if(b&&b.kf!=5){try{this.Vu(a),a.cl()}catch(c){this.reportError(c,"am","clc")}e8(this,b)}};_.g.xU=function(a){(a=d8(this,a))&&a.kf==0&&(Yda(this,a),a.kf=1)};
var Yda=function(a,b){a.YH?(a=setTimeout((0,_.z)(function(){c8(b)||f8(this,b)},a),a.YH),b.fn=a):f8(a,b)},f8=function(a,b){var c=a.tba-a.YH;c>0&&(a=setTimeout((0,_.z)(function(){c8(b)||(b.kf=4,this.wt(b.Ef))},a),c),b.fn=a)},g8=function(a){a.fn!=null&&(clearTimeout(a.fn),a.fn=null)};_.g=T7.prototype;_.g.wU=function(a){(a=d8(this,a))&&!c8(a)&&a.kf==1&&(g8(a),a.kf=3)};_.g.uU=function(a){(a=d8(this,a))&&!c8(a)&&(a.nD=!1)};
_.g.sU=function(a){var b=d8(this,a);if(b&&!c8(b)){try{this.Vu(a)}catch(c){this.reportError(c,"am","oc")}e8(this,b)}};_.g.tU=function(a,b,c,d,e,f){(a=d8(this,a))&&!c8(a)&&(this.reportError(c,d,e,a,b,f),a.kf=4,this.wt(a.Ef))};_.g.vU=function(a,b){(a=d8(this,a))&&!c8(a)&&b>=2&&b<=4&&!c8(a)&&(g8(a),a.kf=2)};var e8=function(a,b){g8(b);b.kf=5;a=a.Gj;for(var c in a)a[c]==b&&delete a[c]},d8=function(a,b){return a.Gj[b.n]},V7=function(){this.zd=window.gbar.wg};_.g=V7.prototype;
_.g.kK=function(a,b,c){return this.zd.rc(a,b,c)};_.g.nT=function(a,b){return this.zd.fc(a,b)};_.g.dN=function(a){this.zd.sc(a)};_.g.Vu=function(a){this.zd.hc(a)};_.g.wt=function(a){this.zd.cc(a)};_.g.xU=function(a){this.zd.os(a)};_.g.wU=function(a,b){this.zd.or(a,b)};_.g.uU=function(a){this.zd.oh(a)};_.g.sU=function(a){this.zd.oc(a)};_.g.tU=function(a,b,c,d,e,f){this.zd.oe(a,b,c,d,e,f)};_.g.vU=function(a,b,c,d){this.zd.oi(a,b,c,d)};_.eb(U7,T7);U7.prototype.reportError=function(){};var Zda={fka:"https://www.google.com",Ina:"https://support.google.com",bma:"https://play.google.com"},h8=function(){var a=this;this.AK=[];this.BK=[];this.initialize=Tda(function(){return _.XN(function(b){if(b.Pc==1)return typeof document==="undefined"||document.requestStorageAccessFor===void 0||navigator.permissions===void 0||navigator.permissions.query===void 0||location.hostname.match(".+\\.google\\.com$")?b.return(Promise.resolve()):_.PN(b,$da(a),2);a.AK.length>0&&document.addEventListener("click",
a.hZ);b.Pc=0})});this.hZ=function(){if(!(a.BK.length>0)){for(var b=_.Ca(a.AK),c=b.next();!c.done;c=b.next()){c=c.value;try{a.BK.push(document.requestStorageAccessFor(c))}catch(d){}}Promise.all(a.BK).then(function(){}).catch(function(){}).finally(function(){a.reset()})}}};h8.prototype.reset=function(){document.removeEventListener("click",this.hZ)};
var $da=function(a){var b,c,d,e;return _.XN(function(f){switch(f.Pc){case 1:b=_.Ca(Object.values(Zda)),c=b.next();case 2:if(c.done){f.Xg(0);break}d=c.value;f.vn=5;return _.PN(f,navigator.permissions.query({name:"top-level-storage-access",requestedOrigin:d}),7);case 7:e=f.Wj;e.state!=="granted"&&a.AK.push(d);f.Pc=3;f.vn=0;break;case 5:_.QN(f);f.Xg(0);break;case 3:c=b.next(),f.Xg(2)}})};(new h8).initialize();var aea=function(a){this.Cfa=a;this.Ab=null;a.then((0,_.z)(function(){},this),function(){},this)},bea=function(a,b){a.Cfa.then(function(c){var d=c.startFeedback;if(!d)throw Error("ed`startFeedback");return d.apply(c,b)})},cea=function(a,b,c){for(var d=Array(arguments.length-2),e=2;e<arguments.length;e++)d[e-2]=arguments[e];e=i8(a,b).then(function(f){return f.apply(null,d)},function(f){f=Error("fd`"+b+"`"+a,{cause:f});delete j8[b];return _.Ck(f)});return new aea(e)},j8={},i8=function(a,b){var c=j8[b];
if(c)return c;c=(c=_.wb(b))?_.Bk(c):(new _.xk(function(d,e){var f=(new _.Wd(document)).createElement("SCRIPT");f.async=!0;_.Fh(f,_.hc(_.Xt(a)));f.onload=f.onreadystatechange=function(){f.readyState&&f.readyState!="loaded"&&f.readyState!="complete"||d()};f.onerror=function(h){e(Error("gd`"+b+"`"+a,{cause:h}))};(document.head||document.getElementsByTagName("head")[0]).appendChild(f)})).then(function(){var d=_.wb(b);if(!d)throw Error("hd`"+b+"`"+a);return d});return j8[b]=c};var k8=function(a){this.tr=a;this.Ab=null};k8.prototype.Cga=function(a){bea(this.tr,arguments)};var l8=new _.Wt(_.Ut,"https://www.gstatic.com/feedback/js/help/prod/service/lazy.min.js");i8(l8,"help.service.Lazy.create").vD(function(){});var m8={contactid:!0,cdu:!0,cmp:!0,email:!0,hl:!0,n:!0,m:!0,p:!0,src:!0,userid:!0,sp:!0,ytid:!0};_.gj({nm:!0,s:!0,pr:!0,v:!0},m8);var n8=function(){this.Zu=_.Hk();this.Zu.promise.then(function(a){_.xs(a.getIframeEl(),{border:"none","margin-bottom":"-4px"});_.xs(a.getSiteEl(),{"border-radius":"4px",overflow:"hidden","box-shadow":"rgba(0, 0, 0, 0.3) 0px 4px 16px"})})};_.g=n8.prototype;_.g.SH=function(a){_.an.open(a,this.Zu.resolve)};_.g.cV=function(){this.Zu.promise.then(function(a){return a.close()})};_.g.RH=function(a,b,c,d){this.Zu.promise.then(function(e){return e.send(a,c,d,b)})};
_.g.QH=function(a,b,c){this.Zu.promise.then(function(d){return d.restyle({updateContainer:{visible:a,x:b,y:c}})})};_.g.addOnOpenerHandler=function(a,b,c){_.an.addOnOpenerHandler(a,c,b)};var Vda=RegExp("(?:^|\\s)g-(?:hovercard|profile)(?:$|\\s)"),Z7={loadDelay:400,hoverDelay:500,closeDelay:500},Uda=0;var o8=function(a){this.qt=a;this.Ef=new _.U3(this);this.Ja=null;this.jI=!1;this.nW=0;this.nt=!1};_.g=o8.prototype;
_.g.load=function(a){Xda(this.qb);a=_.ei(a,function(f,h){return m8[h]&&f!=null});a.origin=window.location.protocol+"//"+window.location.host;var b=this,c=this.qt,d={_event:function(f){if(!(f.timestamp<c.vI)){if(f.event=="sgcp_ams")c.MA=!0,c.rB=!1;else if(c.MA&&f.event=="mouseover")c.rB=!0;else if(c.MA&&f.event=="mouseout")c.rB=!1;else if(f.event=="sgcp_amh")c.MA=!1,c.rB||p8(c);else{var h=!1;switch(f.event){case "calendar":h=c.Gi.scheduleEventHandler;break;case "chat":h=c.Gi.sendChatHandler;break;
case "email":h=c.Gi.sendEmailHandler;break;case "feedback":h=c.Gi.feedbackHandler;break;case "videoChat":h=c.Gi.videoChatHandler}h&&h(c.Gi["hovercard-id"]||c.Gi.userid||c.Gi.email)}if(f.event=="mouseover"||f.event=="sgcp_ams")window.clearTimeout(c.oj),c.oj=null;if(f.cpid){for(h=document.getElementById(f.cpid);h&&h.parentNode.tagName!="BODY";)h=h.parentNode;c.xI=h}f.fromCard&&f.event=="mouseout"&&p8(c)}},_ready:(0,_.z)(this.Qd,this),version:function(f){c.Cf(c.Vk,{type:"circles_changed",version:f.v})},
loaded:function(f){f.ri==b.nW&&b.fca()},rendered:function(){var f=b.qt.Mo,h=_.qs(document);b.nt&&(b.ol(!0,f.x+h.x,f.y+h.y),b.nt=!1,f=b.qt,f.Cf(f.Vk,{type:"show",frame:b.Ja}))},renderfailed:function(){b.nt=!1;b.ol(!1,0,0)},disposed:function(){b.Ja.close()},cardAction:function(f){q8(c,f)}},e=":card";!_.ci("iframes/card/url")&&_.ci("iframes/hovercard/url")&&(e=":hovercard");a=_.Gm(_.ek(_.dk(new _.Co({disableMultiLevelParentRelay:!0,hover:!0}),d),_.dn),a).Ei("hover").setUrl(e);_.ci("card/relayOpenTop")&&
(_.Hm(a,-1),(new _.jo(a.T)).RK("_default"));_.an.open(a.value(),(0,_.z)(function(f){this.Ja=f;_.xs(f.getIframeEl(),{border:"none","margin-bottom":"-4px"});_.xs(f.getSiteEl(),{"border-radius":"4px",overflow:"hidden","box-shadow":"rgba(0, 0, 0, 0.3) 0px 4px 16px"})},this))};_.g.Qd=function(){this.jI=!0;this.ol(!1,0,0);this.qb.onReady({});var a=this.qt;a.ye&&a.JI(a.ye)};_.g.ki=function(){return this.jI};_.g.wp=function(a){this.qb=a};_.g.O9=function(a){this.Ja.send("getHealthc",void 0,a,_.dn)};
_.g.u$=function(a,b){this.Ja.send("getVarc",a,b,_.dn)};_.g.ol=function(a,b,c){this.Ja.updateContainer?this.Ja.updateContainer(a,b,c):this.Ja.restyle({updateContainer:{visible:a,x:b,y:c}})};_.g.show=function(){this.ol(!0,0,-1E4);this.Ja.send("render",void 0,void 0,_.dn);this.nt=!0};_.g.hide=function(){this.Ja.send("hide",void 0,void 0,_.dn);this.ol(!1,0,0);var a=this.qt;a.Cf(a.Vk,{type:"hide"});a.Vk=null;a.Gi=null;this.nt=!1};_.g.close=function(){this.Ja.send("dispose",void 0,void 0,_.dn)};
_.g.enable=function(){};_.g.disable=function(){};var r8=function(){this.qX=0;this.qH=[];this.fk={};this.By={};this.qc={};this.Vc=this.ye=this.Zf=null;this.FB=_.cn;this.vI=this.oj=this.Yg=this.Wq=this.Gi=this.Vk=null;this.Mo={x:0,y:0};this.rB=this.MA=!1;this.xI=null;this.dQ=new Map},s8=function(a,b,c,d){var e=Y7(b);e=a.qc[e]||(a.qc[e]={});e[c]||(e[c]=d=(0,_.z)(d,a),b.addEventListener?b.addEventListener(c,d,c=="focus"||c=="blur"):(c=="focus"?c="focusin":c=="blur"&&(c="focusout"),b.attachEvent("on"+c,d)))};
r8.prototype.Nk=function(a,b){var c=this.qc[Y7(a)];c&&c[b]&&(a.addEventListener?a.removeEventListener(b,c[b],b=="focus"||b=="blur"):a.detachEvent(b=="focus"?"onfocusin":b=="blur"?"onfocusout":"on"+b,c[b]),delete c[b])};var t8=function(a,b){var c=a.qc[b.id];if(c)for(var d in c)c.hasOwnProperty(d)&&a.Nk(b,d)};_.g=r8.prototype;
_.g.wx=function(a,b,c){if(a=a||document.body){this.qX++;var d=Y7(a);b&&(this.fk[d]=b);c&&(this.By[d]=c);s8(this,a,"mouseover",this.rU);s8(this,a,"mouseout",this.BH);s8(this,a,"mousedown",this.qU);s8(this,a,"focus",this.rU);s8(this,a,"blur",this.BH);s8(this,document.body,"mouseout",this.BH);s8(this,document.body,"mousedown",this.qU);c&&c.preload&&(b=this.ye=document.createElement("div"),this.Vc=$7(b,this.By),u8(this))}else window.setTimeout((0,_.z)(this.wx,this),100)};
_.g.DN=function(a){if(a=a||document.body)if(p8(this,0),a!=document.body?t8(this,a):this.Nk(document.body,"mouseover"),a=Y7(a),delete this.fk[a],delete this.By[a],!(--this.qX>0)){t8(this,document.body);var b=this.Zf;this.KH();this.Zf=null;window.setTimeout(function(){var c=W7();c&&b&&c.wt(b.Ef)},100)}};_.g.ze=function(a){this.qH.push(a)};_.g.gp=function(a){_.ej(this.qH,a)};_.g.Cf=function(a,b){for(var c=[];a;){var d=X7(a);d&&this.fk[d]&&c.push(this.fk[d]);a=a.parentNode}_.Ch(c,this.qH);_.Bb(c,function(e){e(b)})};
_.g.rU=function(a){this.vI=Date.now();var b=a.target||a.srcElement;if(b&&b.tagName!="IFRAME"){for(;b&&!Wda(b);)if(b=b.parentNode,!b||b.nodeType!=1)return;if(b)if(this.Vk==b||this.ye==b)this.oj&&(window.clearTimeout(this.oj),this.oj=null);else{this.ye=b;s8(this,b,"mousemove",this.haa);a.type=="focus"||a.type=="focusin"?(a=_.nt(b),this.Mo.x=a.x,this.Mo.y=a.y+b.offsetHeight):(this.Mo.x=a.clientX,this.Mo.y=a.clientY);this.Wq=Date.now();a=this.Vc=$7(b,this.By);var c=a8("hoverDelay",a);this.Zf?this.Zf.ki()&&
(window.clearTimeout(this.Yg),this.Yg=window.setTimeout((0,_.z)(this.JI,this,b),c-a8("loadDelay",a))):u8(this)}}};_.g.BH=function(a){this.vI=Date.now();if(a.type!="blur"||a.target==this.Vk||a.target==this.ye){if(a=a.relatedTarget||a.toElement){if(a.tagName=="IFRAME")return;if(this.xI)for(;a&&a.tagName!="BODY";){if(a==this.xI)return;a=a.parentNode}}p8(this)}};_.g.qU=function(){p8(this,0)};_.g.haa=function(a){this.Mo.x=a.clientX;this.Mo.y=a.clientY};
var u8=function(a){a.Yg&&(window.clearTimeout(a.Yg),a.Yg=null);if(a.ye&&(a.Cf(a.ye,{type:"hover",config:a.Vc}),!a.Zf)){var b=a.Zf=new o8(a);Sda((0,_.z)(function(c){c.kK("card",dea++,b.Ef)&&b.load(this.Vc||{})},a))}};
r8.prototype.JI=function(a){this.Yg&&(window.clearTimeout(this.Yg),this.Yg=null);if(this.ye==a){var b=this.Vc||{},c=a8("hoverDelay",b)-a8("loadDelay",b)-Date.now()+this.Wq;c>0?this.Yg=window.setTimeout((0,_.z)(this.JI,this,a),c):(this.Cf(a,{type:"hover",config:b}),this.Vc.feedbackHandler||(this.Vc.feedbackHandler=this.Dga),this.Vc.overrideFeedback=!0,this.Vc.scheduleEventHandler&&(this.Vc.overrideCalendar=!0),this.Vc.sendChatHandler&&(this.Vc.overrideChat=!0),this.Vc.sendEmailHandler&&(this.Vc.overrideEmail=
!0),this.Vc.videoChatHandler&&(this.Vc.overrideVideoChat=!0),c=this.Zf,a=this.Z_.bind(this,a),c.jI&&(c.fca=a,b.ri=++c.nW,c.Ja.send("loadData",b,void 0,_.dn)))}};
r8.prototype.Dga=function(){var a={};a={apiKey:a.apiKey||a.apiKey,asxUiUri:a.asxUiUri||a.asxUiUri,environment:a.environment||a.environment,flow:a.flow||a.flow,frdProductData:a.frdProductData||a.frdProductData,frdProductDataSerializedJspb:a.rqa||a.frdProductDataSerializedJspb,helpCenterPath:a.helpCenterPath||a.helpCenterPath,locale:a.locale||a.locale||"en".replace(/-/g,"_"),nonce:a.nonce||a.nonce,productData:a.productData||a.productData,receiverUri:a.receiverUri||a.receiverUri,renderApiUri:a.renderApiUri||
a.renderApiUri,theme:a.theme||a.theme,window:a.window||a.window};a=cea(l8,"help.service.Lazy.create","5003140",a);(new k8(a)).Cga({productVersion:"gapi",customZIndex:2000000002})};r8.prototype.Z_=function(a){if(this.ye===a&&this.Zf&&this.Zf.ki()&&this.Wq){var b=a8("hoverDelay",this.Vc||{})-Date.now()+this.Wq;b>0?window.setTimeout((0,_.z)(this.Z_,this,a),b):(this.KH(),this.Vk=this.ye,this.Gi=this.Vc,this.Nk(this.ye,"mousemove"),this.Wq=this.Vc=this.ye=null,W7().dN(this.Zf.Ef))}};
var p8=function(a,b){a.ye&&a.Nk(a.ye,"mousemove");a.ye=null;a.Vc=null;a.Wq=null;a.Yg&&(window.clearTimeout(a.Yg),a.Yg=null);!a.oj&&a.Vk&&(a.oj=window.setTimeout((0,_.z)(a.KH,a),typeof b==="number"?b:a8("closeDelay",a.Gi||{})))};r8.prototype.KH=function(){this.oj&&(window.clearTimeout(this.oj),this.oj=null);this.Vk&&W7().Vu(this.Zf.Ef)};var q8=function(a,b){a.Cf(null,b);a.GJ&&a.GJ.send("cardAction",b,void 0,a.FB)};
r8.prototype.lF=function(a,b,c){var d={};d.frame=a;d.filter=b||_.cn;d.callback=c||function(){};this.dQ.set(String(_.rh(a)),d);a.register("cardAction",(0,_.z)(function(e){q8(this,e);d.callback(e)},this),d.filter)};r8.prototype.mF=function(a){var b=this;this.FB=a||_.cn;_.an.addOnOpenerHandler(function(c){b.GJ=c;b.GJ.register("cardAction",function(){return b.ly},b.FB)},void 0,this.FB)};
r8.prototype.ly=function(a){this.dQ.forEach(function(b){return b.frame.send("cardAction",a,void 0,b.filter)});this.Zf&&this.Zf.Ja.send("cardAction",a,void 0,_.dn)};var dea=0;_.v8=function(){var a={},b=new r8,c=new n8;a.wx=function(d,e,f){b.wx(d,e,f)};a.DN=function(d){b.DN(d)};a.ze=function(d){b.ze(d)};a.gp=function(d){b.gp(d)};a.lF=function(d,e,f){b.lF(d,e,f)};a.mF=function(d){b.mF(d)};a.ly=function(d){b.ly(d)};a.va=function(d,e){e.origin=window.location.protocol+"//"+window.location.host;var f=_.an.openChild({url:":card",where:document.getElementById(d),queryParams:e,messageHandlers:{_ready:function(){f.send("loadData",e,void 0,_.dn)},loaded:function(){f.send("render",
void 0,void 0,_.dn)}},messageHandlersFilter:_.dn})};a.cV=function(){c.cV()};a.QH=function(d,e,f){c.QH(d,e,f)};a.RH=function(d,e,f,h){c.RH(d,e,f,h)};a.SH=function(d){c.SH(d)};a.jba=function(d,e,f){c.addOnOpenerHandler(d,e,f)};a.lba=function(){return _.dn};a.mba=function(){return _.cn};return a}();_.t("gapi.card.watch",_.v8.wx);_.t("gapi.card.unwatch",_.v8.DN);_.t("gapi.card.addCallback",_.v8.ze);_.t("gapi.card.removeCallback",_.v8.gp);_.t("gapi.card.render",_.v8.va);_.t("gapi.card.connectChild",_.v8.lF);_.t("gapi.card.connectOpener",_.v8.mF);_.t("gapi.card.broadcast",_.v8.ly);_.t("gapi.card.iframeClose",_.v8.close);_.t("gapi.card.iframeRestyle",_.v8.QH);_.t("gapi.card.iframeSend",_.v8.RH);_.t("gapi.card.iframeSetup",_.v8.SH);_.t("gapi.card.iframeAddOnOpenerHandler",_.v8.jba);
_.t("gapi.card.iframeGetCrossOriginFilter",_.v8.lba);_.t("gapi.card.iframeGetSameOriginFilter",_.v8.mba);
});
// Google Inc.
